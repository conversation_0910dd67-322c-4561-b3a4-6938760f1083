/*!
 * html2canvas 1.1.4 <https://html2canvas.hertzen.com>
 * Copyright (c) 2021 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 */
!function(A,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define("html2canvasModule",e):(A="undefined"!=typeof globalThis?globalThis:A||self).html2canvas=e()}(this,function(){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var r=function(A,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])})(A,e)};function A(A,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=A}r(A,e),A.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t)}var C=function(){return(C=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t])Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A}).apply(this,arguments)};function c(A,s,o,i){return new(o=o||Promise)(function(t,e){function r(A){try{B(i.next(A))}catch(A){e(A)}}function n(A){try{B(i.throw(A))}catch(A){e(A)}}function B(A){var e;A.done?t(A.value):((e=A.value)instanceof o?e:new o(function(A){A(e)})).then(r,n)}B((i=i.apply(A,s||[])).next())})}function H(t,r){var n,B,s,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},A={next:e(0),throw:e(1),return:e(2)};return"function"==typeof Symbol&&(A[Symbol.iterator]=function(){return this}),A;function e(e){return function(A){return function(e){if(n)throw new TypeError("Generator is already executing.");for(;o;)try{if(n=1,B&&(s=2&e[0]?B.return:e[0]?B.throw||((s=B.return)&&s.call(B),0):B.next)&&!(s=s.call(B,e[1])).done)return s;switch(B=0,(e=s?[2&e[0],s.value]:e)[0]){case 0:case 1:s=e;break;case 4:return o.label++,{value:e[1],done:!1};case 5:o.label++,B=e[1],e=[0];continue;case 7:e=o.ops.pop(),o.trys.pop();continue;default:if(!(s=0<(s=o.trys).length&&s[s.length-1])&&(6===e[0]||2===e[0])){o=0;continue}if(3===e[0]&&(!s||e[1]>s[0]&&e[1]<s[3])){o.label=e[1];break}if(6===e[0]&&o.label<s[1]){o.label=s[1],s=e;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(e);break}s[2]&&o.ops.pop(),o.trys.pop();continue}e=r.call(t,o)}catch(A){e=[6,A],B=0}finally{n=s=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}([e,A])}}}function t(A,e,t){if(t||2===arguments.length)for(var r,n=0,B=e.length;n<B;n++)!r&&n in e||((r=r||Array.prototype.slice.call(e,0,n))[n]=e[n]);return A.concat(r||e)}var g=(n.prototype.add=function(A,e,t,r){return new n(this.left+A,this.top+e,this.width+t,this.height+r)},n.fromClientRect=function(A){return new n(A.left,A.top,A.width,A.height)},n);function n(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}for(var E=function(A){return g.fromClientRect(A.getBoundingClientRect())},a=function(A){for(var e=[],t=0,r=A.length;t<r;){var n,B=A.charCodeAt(t++);55296<=B&&B<=56319&&t<r?56320==(64512&(n=A.charCodeAt(t++)))?e.push(((1023&B)<<10)+(1023&n)+65536):(e.push(B),t--):e.push(B)}return e},Q=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,B="";++n<t;){var s=A[n];s<=65535?r.push(s):(s-=65536,r.push(55296+(s>>10),s%1024+56320)),(n+1===t||16384<r.length)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",u="undefined"==typeof Uint8Array?[]:new Uint8Array(256),B=0;B<e.length;B++)u[e.charCodeAt(B)]=B;function s(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))}var o=(i.prototype.get=function(A){var e;if(0<=A){if(A<55296||56319<A&&A<=65535)return e=this.index[A>>5],this.data[e=(e<<2)+(31&A)];if(A<=65535)return e=this.index[2048+(A-55296>>5)],this.data[e=(e<<2)+(31&A)];if(A<this.highStart)return e=this.index[e=2080+(A>>11)],e=this.index[e+=A>>5&63],this.data[e=(e<<2)+(31&A)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},i);function i(A,e,t,r,n,B){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=B}function w(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n)for(var B=t;B<=r.length;){if((o=r[++B])===e)return 1;if(o!==N)break}if(n===N)for(B=t;0<B;){var s=r[--B];if(Array.isArray(A)?-1!==A.indexOf(s):A===s)for(var o,i=t;i<=r.length;){if((o=r[++i])===e)return 1;if(o!==N)break}if(s!==N)break}}function l(A,e){for(var t=A;0<=t;){var r=e[t];if(r!==N)return r;t--}return 0}function U(t,A){var e=(n=function(A,r){void 0===r&&(r="strict");var n=[],B=[],s=[];return A.forEach(function(A,e){var t=q.get(A);if(50<t?(s.push(!0),t-=50):s.push(!1),-1!==["normal","auto","loose"].indexOf(r)&&-1!==[8208,8211,12316,12448].indexOf(A))return B.push(e),n.push(16);if(4!==t&&11!==t)return B.push(e),31===t?n.push("strict"===r?O:X):t===W||29===t?n.push(_):43===t?131072<=A&&A<=196605||196608<=A&&A<=262141?n.push(X):n.push(_):void n.push(t);if(0===e)return B.push(e),n.push(_);t=n[e-1];return-1===eA.indexOf(t)?(B.push(B[e-1]),n.push(t)):(B.push(e),n.push(_))}),[B,n,s]}(t,(A=A||{lineBreak:"normal",wordBreak:"normal"}).lineBreak))[0],r=n[1],n=n[2];return[e,r="break-all"===A.wordBreak||"break-word"===A.wordBreak?r.map(function(A){return-1!==[S,_,W].indexOf(A)?X:A}):r,"keep-all"===A.wordBreak?n.map(function(A,e){return A&&19968<=t[e]&&t[e]<=40959}):void 0]}var h,F,d,f,p,N=10,K=13,I=15,T=17,m=18,R=19,L=20,O=21,b=22,D=24,S=25,M=26,v=27,y=28,_=30,x=32,P=33,V=34,z=35,X=37,G=38,J=39,k=40,W=42,Y="×",q=(d=function(A){var e,t,r,n,B=.75*A.length,s=A.length,o=0;"="===A[A.length-1]&&(B--,"="===A[A.length-2]&&B--);for(var B=new("undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?ArrayBuffer:Array)(B),i=Array.isArray(B)?B:new Uint8Array(B),a=0;a<s;a+=4)e=u[A.charCodeAt(a)],t=u[A.charCodeAt(a+1)],r=u[A.charCodeAt(a+2)],n=u[A.charCodeAt(a+3)],i[o++]=e<<2|t>>4,i[o++]=(15&t)<<4|r>>2,i[o++]=(3&r)<<6|63&n;return B}(h="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"),f=Array.isArray(d)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(d):new Uint32Array(d),p=Array.isArray(d)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(d):new Uint16Array(d),h=s(p,12,f[4]/2),F=2===f[5]?s(p,(24+f[4])/2):(d=f,p=Math.ceil((24+f[4])/4),d.slice?d.slice(p,F):new Uint32Array(Array.prototype.slice.call(d,p,F))),new o(f[0],f[1],f[2],f[3],h,F)),Z=[_,36],j=[1,2,3,5],$=[N,8],AA=[v,M],eA=j.concat($),tA=[G,J,k,V,z],rA=[I,K],nA=(BA.prototype.slice=function(){return Q.apply(void 0,this.codePoints.slice(this.start,this.end))},BA);function BA(A,e,t,r){this.codePoints=A,this.required="!"===e,this.start=t,this.end=r}function sA(A,e){var t=a(A),r=(e=U(t,e))[0],n=e[1],B=e[2],s=t.length,o=0,i=0;return{next:function(){if(s<=i)return{done:!0,value:null};for(var A=Y;i<s&&(A=function(A,e,t,r,n){if(0===t[r])return Y;var B=r-1;if(Array.isArray(n)&&!0===n[B])return Y;var s=B-1,o=1+B,i=e[B],r=0<=s?e[s]:0,n=e[o];if(2===i&&3===n)return Y;if(-1!==j.indexOf(i))return"!";if(-1!==j.indexOf(n))return Y;if(-1!==$.indexOf(n))return Y;if(8===l(B,e))return"÷";if(11===q.get(A[B])&&(n===X||n===x||n===P))return Y;if(7===i||7===n)return Y;if(9===i)return Y;if(-1===[N,K,I].indexOf(i)&&9===n)return Y;
// LB13 Do not break before ‘]’ or ‘!’ or ‘;’ or ‘/’, even after spaces.
if(-1!==[T,m,R,D,y].indexOf(n))return Y;if(l(B,e)===b)return Y;if(w(23,b,B,e))return Y;if(w([T,m],O,B,e))return Y;if(w(12,12,B,e))return Y;if(i===N)return"÷";if(23===i||23===n)return Y;if(16===n||16===i)return"÷";if(-1!==[K,I,O].indexOf(n)||14===i)return Y;if(36===r&&-1!==rA.indexOf(i))return Y;if(i===y&&36===n)return Y;if(n===L&&-1!==Z.concat(L,R,S,X,x,P).indexOf(i))return Y;if(-1!==Z.indexOf(n)&&i===S||-1!==Z.indexOf(i)&&n===S)return Y;if(i===v&&-1!==[X,x,P].indexOf(n)||-1!==[X,x,P].indexOf(i)&&n===M)return Y;if(-1!==Z.indexOf(i)&&-1!==AA.indexOf(n)||-1!==AA.indexOf(i)&&-1!==Z.indexOf(n))return Y;if(-1!==[v,M].indexOf(i)&&(n===S||-1!==[b,I].indexOf(n)&&e[1+o]===S)||-1!==[b,I].indexOf(i)&&n===S||i===S&&-1!==[S,y,D].indexOf(n))return Y;if(-1!==[S,y,D,T,m].indexOf(n))for(var a=B;0<=a;){if((c=e[a])===S)return Y;if(-1===[y,D].indexOf(c))break;a--}if(-1!==[v,M].indexOf(n))for(var c,a=-1!==[T,m].indexOf(i)?s:B;0<=a;){if((c=e[a])===S)return Y;if(-1===[y,D].indexOf(c))break;a--}if(G===i&&-1!==[G,J,V,z].indexOf(n)||-1!==[J,V].indexOf(i)&&-1!==[J,k].indexOf(n)||-1!==[k,z].indexOf(i)&&n===k)return Y;if(-1!==tA.indexOf(i)&&-1!==[L,M].indexOf(n)||-1!==tA.indexOf(n)&&i===v)return Y;if(-1!==Z.indexOf(i)&&-1!==Z.indexOf(n))return Y;if(i===D&&-1!==Z.indexOf(n))return Y;if(-1!==Z.concat(S).indexOf(i)&&n===b||-1!==Z.concat(S).indexOf(n)&&i===m)return Y;if(41===i&&41===n){for(var Q=t[B],u=1;0<Q&&41===e[--Q];)u++;if(u%2!=0)return Y}return i===x&&n===P?Y:"÷"}(t,n,r,++i,B))===Y;);if(A===Y&&i!==s)return{done:!0,value:null};var e=new nA(t,A,o,i);return o=i,{value:e,done:!1}}}}var oA;(je=oA=oA||{})[je.STRING_TOKEN=0]="STRING_TOKEN",je[je.BAD_STRING_TOKEN=1]="BAD_STRING_TOKEN",je[je.LEFT_PARENTHESIS_TOKEN=2]="LEFT_PARENTHESIS_TOKEN",je[je.RIGHT_PARENTHESIS_TOKEN=3]="RIGHT_PARENTHESIS_TOKEN",je[je.COMMA_TOKEN=4]="COMMA_TOKEN",je[je.HASH_TOKEN=5]="HASH_TOKEN",je[je.DELIM_TOKEN=6]="DELIM_TOKEN",je[je.AT_KEYWORD_TOKEN=7]="AT_KEYWORD_TOKEN",je[je.PREFIX_MATCH_TOKEN=8]="PREFIX_MATCH_TOKEN",je[je.DASH_MATCH_TOKEN=9]="DASH_MATCH_TOKEN",je[je.INCLUDE_MATCH_TOKEN=10]="INCLUDE_MATCH_TOKEN",je[je.LEFT_CURLY_BRACKET_TOKEN=11]="LEFT_CURLY_BRACKET_TOKEN",je[je.RIGHT_CURLY_BRACKET_TOKEN=12]="RIGHT_CURLY_BRACKET_TOKEN",je[je.SUFFIX_MATCH_TOKEN=13]="SUFFIX_MATCH_TOKEN",je[je.SUBSTRING_MATCH_TOKEN=14]="SUBSTRING_MATCH_TOKEN",je[je.DIMENSION_TOKEN=15]="DIMENSION_TOKEN",je[je.PERCENTAGE_TOKEN=16]="PERCENTAGE_TOKEN",je[je.NUMBER_TOKEN=17]="NUMBER_TOKEN",je[je.FUNCTION=18]="FUNCTION",je[je.FUNCTION_TOKEN=19]="FUNCTION_TOKEN",je[je.IDENT_TOKEN=20]="IDENT_TOKEN",je[je.COLUMN_TOKEN=21]="COLUMN_TOKEN",je[je.URL_TOKEN=22]="URL_TOKEN",je[je.BAD_URL_TOKEN=23]="BAD_URL_TOKEN",je[je.CDC_TOKEN=24]="CDC_TOKEN",je[je.CDO_TOKEN=25]="CDO_TOKEN",je[je.COLON_TOKEN=26]="COLON_TOKEN",je[je.SEMICOLON_TOKEN=27]="SEMICOLON_TOKEN",je[je.LEFT_SQUARE_BRACKET_TOKEN=28]="LEFT_SQUARE_BRACKET_TOKEN",je[je.RIGHT_SQUARE_BRACKET_TOKEN=29]="RIGHT_SQUARE_BRACKET_TOKEN",je[je.UNICODE_RANGE_TOKEN=30]="UNICODE_RANGE_TOKEN",je[je.WHITESPACE_TOKEN=31]="WHITESPACE_TOKEN",je[je.EOF_TOKEN=32]="EOF_TOKEN";function iA(A){return 48<=A&&A<=57}function aA(A){return iA(A)||65<=A&&A<=70||97<=A&&A<=102}function cA(A){return 10===A||9===A||32===A}function QA(A){return 97<=(t=e=A)&&t<=122||65<=(e=e)&&e<=90||128<=A||95===A;var e,t}function uA(A){return QA(A)||iA(A)||45===A}function wA(A,e){return 92===A&&10!==e}function lA(A,e,t){return 45===A?QA(e)||wA(e,t):!!QA(A)||92===A&&10!==e}function UA(A,e,t){return 43===A||45===A?!!iA(e)||46===e&&iA(t):iA(46===A?e:A)}var CA={type:oA.LEFT_PARENTHESIS_TOKEN},gA={type:oA.RIGHT_PARENTHESIS_TOKEN},EA={type:oA.COMMA_TOKEN},hA={type:oA.SUFFIX_MATCH_TOKEN},FA={type:oA.PREFIX_MATCH_TOKEN},dA={type:oA.COLUMN_TOKEN},HA={type:oA.DASH_MATCH_TOKEN},fA={type:oA.INCLUDE_MATCH_TOKEN},pA={type:oA.LEFT_CURLY_BRACKET_TOKEN},NA={type:oA.RIGHT_CURLY_BRACKET_TOKEN},KA={type:oA.SUBSTRING_MATCH_TOKEN},IA={type:oA.BAD_URL_TOKEN},TA={type:oA.BAD_STRING_TOKEN},mA={type:oA.CDO_TOKEN},RA={type:oA.CDC_TOKEN},LA={type:oA.COLON_TOKEN},OA={type:oA.SEMICOLON_TOKEN},bA={type:oA.LEFT_SQUARE_BRACKET_TOKEN},DA={type:oA.RIGHT_SQUARE_BRACKET_TOKEN},SA={type:oA.WHITESPACE_TOKEN},MA={type:oA.EOF_TOKEN},vA=(yA.prototype.write=function(A){this._value=this._value.concat(a(A))},yA.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==MA;)A.push(e),e=this.consumeToken();return A},yA.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(uA(e)||wA(t,r)){var n=lA(e,t,r)?2:1,B=this.consumeName();return{type:oA.HASH_TOKEN,value:B,flags:n}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),hA;break;case 39:return this.consumeStringToken(39);case 40:return CA;case 41:return gA;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),KA;break;case 43:if(UA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 44:return EA;case 45:var n=A,s=this.peekCodePoint(0),o=this.peekCodePoint(1);if(UA(n,s,o))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(lA(n,s,o))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(45===s&&62===o)return this.consumeCodePoint(),this.consumeCodePoint(),RA;break;case 46:if(UA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var i=this.consumeCodePoint();if(42===i&&47===(i=this.consumeCodePoint()))return this.consumeToken();if(-1===i)return this.consumeToken()}break;case 58:return LA;case 59:return OA;case 60:if(33===this.peekCodePoint(0)&&45===this.peekCodePoint(1)&&45===this.peekCodePoint(2))return this.consumeCodePoint(),this.consumeCodePoint(),mA;break;case 64:var s=this.peekCodePoint(0),o=this.peekCodePoint(1),a=this.peekCodePoint(2);if(lA(s,o,a)){B=this.consumeName();return{type:oA.AT_KEYWORD_TOKEN,value:B}}break;case 91:return bA;case 92:if(wA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case 93:return DA;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),FA;break;case 123:return pA;case 125:return NA;case 117:case 85:a=this.peekCodePoint(0),B=this.peekCodePoint(1);return 43!==a||!aA(B)&&63!==B||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),HA;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),dA;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),fA;break;case-1:return MA}return cA(A)?(this.consumeWhiteSpace(),SA):iA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):QA(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:oA.DELIM_TOKEN,value:Q(A)}},yA.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},yA.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},yA.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},yA.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();aA(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;63===e&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t){var r=parseInt(Q.apply(void 0,A.map(function(A){return 63===A?48:A})),16),n=parseInt(Q.apply(void 0,A.map(function(A){return 63===A?70:A})),16);return{type:oA.UNICODE_RANGE_TOKEN,start:r,end:n}}r=parseInt(Q.apply(void 0,A),16);if(45===this.peekCodePoint(0)&&aA(this.peekCodePoint(1))){this.consumeCodePoint();for(var e=this.consumeCodePoint(),B=[];aA(e)&&B.length<6;)B.push(e),e=this.consumeCodePoint();n=parseInt(Q.apply(void 0,B),16);return{type:oA.UNICODE_RANGE_TOKEN,start:r,end:n}}return{type:oA.UNICODE_RANGE_TOKEN,start:r,end:r}},yA.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:oA.FUNCTION_TOKEN,value:A}):{type:oA.IDENT_TOKEN,value:A}},yA.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),-1===this.peekCodePoint(0))return{type:oA.URL_TOKEN,value:""};var e,t=this.peekCodePoint(0);if(39===t||34===t){t=this.consumeStringToken(this.consumeCodePoint());return t.type===oA.STRING_TOKEN&&(this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:oA.URL_TOKEN,value:t.value}):(this.consumeBadUrlRemnants(),IA)}for(;;){var r=this.consumeCodePoint();if(-1===r||41===r)return{type:oA.URL_TOKEN,value:Q.apply(void 0,A)};if(cA(r))return this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:oA.URL_TOKEN,value:Q.apply(void 0,A)}):(this.consumeBadUrlRemnants(),IA);if(34===r||39===r||40===r||(0<=(e=r)&&e<=8||11===e||14<=e&&e<=31||127===e))return this.consumeBadUrlRemnants(),IA;if(92===r){if(!wA(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),IA;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},yA.prototype.consumeWhiteSpace=function(){for(;cA(this.peekCodePoint(0));)this.consumeCodePoint()},yA.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(41===A||-1===A)return;wA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},yA.prototype.consumeStringSlice=function(A){for(var e="";0<A;){var t=Math.min(6e4,A);e+=Q.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},yA.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r,n=this._value[t];if(-1===n||void 0===n||n===A)return e+=this.consumeStringSlice(t),{type:oA.STRING_TOKEN,value:e};if(10===n)return this._value.splice(0,t),TA;92!==n||-1!==(r=this._value[t+1])&&void 0!==r&&(10===r?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):wA(n,r)&&(e+=this.consumeStringSlice(t),e+=Q(this.consumeEscapedCodePoint()),t=-1)),t++}},yA.prototype.consumeNumber=function(){var A=[],e=4;for(43!==(t=this.peekCodePoint(0))&&45!==t||A.push(this.consumeCodePoint());iA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());var t=this.peekCodePoint(0),r=this.peekCodePoint(1);if(46===t&&iA(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;iA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1),n=this.peekCodePoint(2);if((69===t||101===t)&&((43===r||45===r)&&iA(n)||iA(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;iA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[function(A){var e=0,t=1;43!==A[e]&&45!==A[e]||(45===A[e]&&(t=-1),e++);for(var r=[];iA(A[e]);)r.push(A[e++]);var n=r.length?parseInt(Q.apply(void 0,r),10):0;46===A[e]&&e++;for(var B=[];iA(A[e]);)B.push(A[e++]);var s=B.length,o=s?parseInt(Q.apply(void 0,B),10):0;69!==A[e]&&101!==A[e]||e++;var i=1;43!==A[e]&&45!==A[e]||(45===A[e]&&(i=-1),e++);for(var a=[];iA(A[e]);)a.push(A[e++]);var c=a.length?parseInt(Q.apply(void 0,a),10):0;return t*(n+o*Math.pow(10,-s))*Math.pow(10,i*c)}(A),e]},yA.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),A=this.peekCodePoint(2);if(lA(r,n,A)){A=this.consumeName();return{type:oA.DIMENSION_TOKEN,number:e,flags:t,unit:A}}return 37===r?(this.consumeCodePoint(),{type:oA.PERCENTAGE_TOKEN,number:e,flags:t}):{type:oA.NUMBER_TOKEN,number:e,flags:t}},yA.prototype.consumeEscapedCodePoint=function(){var A,e=this.consumeCodePoint();if(aA(e)){for(var t=Q(e);aA(this.peekCodePoint(0))&&t.length<6;)t+=Q(this.consumeCodePoint());cA(this.peekCodePoint(0))&&this.consumeCodePoint();var r=parseInt(t,16);return 0===r||55296<=(A=r)&&A<=57343||1114111<r?65533:r}return-1===e?65533:e},yA.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(uA(e))A+=Q(e);else{if(!wA(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=Q(this.consumeEscapedCodePoint())}}},yA);function yA(){this._value=[]}var _A=(xA.create=function(A){var e=new vA;return e.write(A),new xA(e.read())},xA.parseValue=function(A){return xA.create(A).parseComponentValue()},xA.parseValues=function(A){return xA.create(A).parseComponentValues()},xA.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===oA.WHITESPACE_TOKEN;)A=this.consumeToken();if(A.type===oA.EOF_TOKEN)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);for(var e=this.consumeComponentValue();(A=this.consumeToken()).type===oA.WHITESPACE_TOKEN;);if(A.type===oA.EOF_TOKEN)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},xA.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(e.type===oA.EOF_TOKEN)return A;A.push(e),A.push()}},xA.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case oA.LEFT_CURLY_BRACKET_TOKEN:case oA.LEFT_SQUARE_BRACKET_TOKEN:case oA.LEFT_PARENTHESIS_TOKEN:return this.consumeSimpleBlock(A.type);case oA.FUNCTION_TOKEN:return this.consumeFunction(A)}return A},xA.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(t.type===oA.EOF_TOKEN||ne(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},xA.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:oA.FUNCTION};;){var t=this.consumeToken();if(t.type===oA.EOF_TOKEN||t.type===oA.RIGHT_PARENTHESIS_TOKEN)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},xA.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?MA:A},xA.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},xA);function xA(A){this._tokens=A}function PA(A){return A.type===oA.DIMENSION_TOKEN}function VA(A){return A.type===oA.NUMBER_TOKEN}function zA(A){return A.type===oA.IDENT_TOKEN}function XA(A){return A.type===oA.STRING_TOKEN}function GA(A,e){return zA(A)&&A.value===e}function JA(A){return A.type!==oA.WHITESPACE_TOKEN}function kA(A){return A.type!==oA.WHITESPACE_TOKEN&&A.type!==oA.COMMA_TOKEN}function WA(A){var e=[],t=[];return A.forEach(function(A){if(A.type===oA.COMMA_TOKEN){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}A.type!==oA.WHITESPACE_TOKEN&&t.push(A)}),t.length&&e.push(t),e}function YA(A){return A.type===oA.NUMBER_TOKEN||A.type===oA.DIMENSION_TOKEN}function qA(A){return A.type===oA.PERCENTAGE_TOKEN||YA(A)}function ZA(A){return 1<A.length?[A[0],A[1]]:[A[0]]}function jA(A,e,t){var r=A[0],A=A[1];return[ie(r,e),ie(void 0!==A?A:r,t)]}function $A(A){return A.type===oA.DIMENSION_TOKEN&&("deg"===A.unit||"grad"===A.unit||"rad"===A.unit||"turn"===A.unit)}function Ae(A){switch(A.filter(zA).map(function(A){return A.value}).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[Be,Be];case"to top":case"bottom":return ce(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[Be,oe];case"to right":case"left":return ce(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[oe,oe];case"to bottom":case"top":return ce(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[oe,Be];case"to left":case"right":return ce(270)}return 0}function ee(A){return 0==(255&A)}function te(A){var e=255&A,t=255&A>>8,r=255&A>>16,A=255&A>>24;return e<255?"rgba("+A+","+r+","+t+","+e/255+")":"rgb("+A+","+r+","+t+")"}function re(A,e){if(A.type===oA.NUMBER_TOKEN)return A.number;if(A.type!==oA.PERCENTAGE_TOKEN)return 0;var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}var ne=function(A,e){return e===oA.LEFT_CURLY_BRACKET_TOKEN&&A.type===oA.RIGHT_CURLY_BRACKET_TOKEN||(e===oA.LEFT_SQUARE_BRACKET_TOKEN&&A.type===oA.RIGHT_SQUARE_BRACKET_TOKEN||e===oA.LEFT_PARENTHESIS_TOKEN&&A.type===oA.RIGHT_PARENTHESIS_TOKEN)},Be={type:oA.NUMBER_TOKEN,number:0,flags:4},se={type:oA.PERCENTAGE_TOKEN,number:50,flags:4},oe={type:oA.PERCENTAGE_TOKEN,number:100,flags:4},ie=function(A,e){if(A.type===oA.PERCENTAGE_TOKEN)return A.number/100*e;if(PA(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},ae=function(A){if(A.type===oA.DIMENSION_TOKEN)switch(A.unit){case"deg":return Math.PI*A.number/180;case"grad":return Math.PI/200*A.number;case"rad":return A.number;case"turn":return 2*Math.PI*A.number}throw new Error("Unsupported angle type")},ce=function(A){return Math.PI*A/180},Qe=function(A){if(A.type===oA.FUNCTION){var e=Ee[A.name];if(void 0===e)throw new Error('Attempting to parse an unsupported color function "'+A.name+'"');return e(A.values)}if(A.type===oA.HASH_TOKEN){if(3===A.value.length){var t=A.value.substring(0,1),r=A.value.substring(1,2),n=A.value.substring(2,3);return ue(parseInt(t+t,16),parseInt(r+r,16),parseInt(n+n,16),1)}if(4===A.value.length){var t=A.value.substring(0,1),r=A.value.substring(1,2),n=A.value.substring(2,3),B=A.value.substring(3,4);return ue(parseInt(t+t,16),parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16)/255)}if(6===A.value.length){t=A.value.substring(0,2),r=A.value.substring(2,4),n=A.value.substring(4,6);return ue(parseInt(t,16),parseInt(r,16),parseInt(n,16),1)}if(8===A.value.length){t=A.value.substring(0,2),r=A.value.substring(2,4),n=A.value.substring(4,6),B=A.value.substring(6,8);return ue(parseInt(t,16),parseInt(r,16),parseInt(n,16),parseInt(B,16)/255)}}if(A.type===oA.IDENT_TOKEN){A=he[A.value.toUpperCase()];if(void 0!==A)return A}return he.TRANSPARENT},ue=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r)<<0)>>>0},we=function(A){A=A.filter(kA);if(3===A.length){var e=A.map(re),t=e[0],r=e[1],e=e[2];return ue(t,r,e,1)}if(4!==A.length)return 0;A=A.map(re),t=A[0],r=A[1],e=A[2],A=A[3];return ue(t,r,e,A)};function le(A,e,t){return t<0&&(t+=1),1<=t&&--t,t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var Ue,Ce,ge=function(A){var e=A.filter(kA),t=e[0],r=e[1],n=e[2],B=e[3],A=(t.type===oA.NUMBER_TOKEN?ce(t.number):ae(t))/(2*Math.PI),e=qA(r)?r.number/100:0,t=qA(n)?n.number/100:0,r=void 0!==B&&qA(B)?ie(B,1):1;if(0==e)return ue(255*t,255*t,255*t,1);n=t<=.5?t*(1+e):t+e-t*e,B=2*t-n,e=le(B,n,A+1/3),t=le(B,n,A),A=le(B,n,A-1/3);return ue(255*e,255*t,255*A,r)},Ee={hsl:ge,hsla:ge,rgb:we,rgba:we},he={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199};(je=Ue=Ue||{})[je.VALUE=0]="VALUE",je[je.LIST=1]="LIST",je[je.IDENT_VALUE=2]="IDENT_VALUE",je[je.TYPE_VALUE=3]="TYPE_VALUE",je[je.TOKEN_VALUE=4]="TOKEN_VALUE",(ge=Ce=Ce||{})[ge.BORDER_BOX=0]="BORDER_BOX",ge[ge.PADDING_BOX=1]="PADDING_BOX";function Fe(A){var e=Qe(A[0]);return(A=A[1])&&qA(A)?{color:e,stop:A}:{color:e,stop:null}}function de(A,t){var e=A[0],r=A[A.length-1];null===e.stop&&(e.stop=Be),null===r.stop&&(r.stop=oe);for(var n=[],B=0,s=0;s<A.length;s++){var o=A[s].stop;null!==o?(B<(o=ie(o,t))?n.push(o):n.push(B),B=o):n.push(null)}for(var i=null,s=0;s<n.length;s++){var a=n[s];if(null===a)null===i&&(i=s);else if(null!==i){for(var c=s-i,Q=(a-n[i-1])/(1+c),u=1;u<=c;u++)n[i+u-1]=Q*u;i=null}}return A.map(function(A,e){return{color:A.color,stop:Math.max(Math.min(1,n[e]/t),0)}})}function He(A,e,t){var r="number"==typeof A?A:(s=e/2,r=(B=t)/2,s=ie((n=A)[0],e)-s,B=r-ie(n[1],B),(Math.atan2(B,s)+2*Math.PI)%(2*Math.PI)),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),B=e/2,s=t/2,e=n/2,t=Math.sin(r-Math.PI/2)*e,e=Math.cos(r-Math.PI/2)*e;return[n,B-e,B+e,s-t,s+t]}function fe(A,e){return Math.sqrt(A*A+e*e)}function pe(A,e,n,B,s){return[[0,0],[0,e],[A,0],[A,e]].reduce(function(A,e){var t=e[0],r=e[1],r=fe(n-t,B-r);return(s?r<A.optimumDistance:r>A.optimumDistance)?{optimumCorner:e,optimumDistance:r}:A},{optimumDistance:s?1/0:-1/0,optimumCorner:null}).optimumCorner}function Ne(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]}var Ke={name:"background-clip",initialValue:"border-box",prefix:!(ge[ge.CONTENT_BOX=2]="CONTENT_BOX"),type:Ue.LIST,parse:function(A){return A.map(function(A){if(zA(A))switch(A.value){case"padding-box":return Ce.PADDING_BOX;case"content-box":return Ce.CONTENT_BOX}return Ce.BORDER_BOX})}},Ie={name:"background-color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},we=function(A){var t=ce(180),r=[];return WA(A).forEach(function(A,e){if(0===e){e=A[0];if(e.type===oA.IDENT_TOKEN&&-1!==["top","left","right","bottom"].indexOf(e.value))return void(t=Ae(A));if($A(e))return void(t=(ae(e)+ce(270))%ce(360))}A=Fe(A);r.push(A)}),{angle:t,stops:r,type:ve.LINEAR_GRADIENT}},Te=function(A,e,t,r,n){var B="http://www.w3.org/2000/svg",s=document.createElementNS(B,"svg"),B=document.createElementNS(B,"foreignObject");return s.setAttributeNS(null,"width",A.toString()),s.setAttributeNS(null,"height",e.toString()),B.setAttributeNS(null,"width","100%"),B.setAttributeNS(null,"height","100%"),B.setAttributeNS(null,"x",t.toString()),B.setAttributeNS(null,"y",r.toString()),B.setAttributeNS(null,"externalResourcesRequired","true"),s.appendChild(B),B.appendChild(n),s},me=function(r){return new Promise(function(A,e){var t=new Image;t.onload=function(){return A(t)},t.onerror=e,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})},Re={get SUPPORT_RANGE_BOUNDS(){var A=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t);e=e.getBoundingClientRect(),e=Math.round(e.height);if(A.body.removeChild(t),123===e)return!0}}return!1}(document);return Object.defineProperty(Re,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=function(A){var e=new Image,t=A.createElement("canvas"),A=t.getContext("2d");if(!A)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{A.drawImage(e,0,0),t.toDataURL()}catch(A){return!1}return!0}(document);return Object.defineProperty(Re,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?function(t){var A=t.createElement("canvas"),r=100;A.width=r,A.height=r;var n=A.getContext("2d");if(!n)return Promise.reject(!1);n.fillStyle="rgb(0, 255, 0)",n.fillRect(0,0,r,r);var e=new Image,B=A.toDataURL();e.src=B;e=Te(r,r,0,0,e);return n.fillStyle="red",n.fillRect(0,0,r,r),me(e).then(function(A){n.drawImage(A,0,0);var e=n.getImageData(0,0,r,r).data;n.fillStyle="red",n.fillRect(0,0,r,r);A=t.createElement("div");return A.style.backgroundImage="url("+B+")",A.style.height="100px",Ne(e)?me(Te(r,r,0,0,A)):Promise.reject(!1)}).then(function(A){return n.drawImage(A,0,0),Ne(n.getImageData(0,0,r,r).data)}).catch(function(){return!1})}(document):Promise.resolve(!1);return Object.defineProperty(Re,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=void 0!==(new Image).crossOrigin;return Object.defineProperty(Re,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(Re,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Re,"SUPPORT_CORS_XHR",{value:A}),A}},Le=(Oe.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,t([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Oe.prototype.getTime=function(){return Date.now()-this.start},Oe.create=function(A){Oe.instances[A.id]=new Oe(A)},Oe.destroy=function(A){delete Oe.instances[A]},Oe.getInstance=function(A){var e=Oe.instances[A];if(void 0===e)throw new Error("No logger instance found with id "+A);return e},Oe.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,t([this.id,this.getTime()+"ms"],A))},Oe.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,t([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Oe.instances={},Oe);function Oe(A){var e=A.id,A=A.enabled;this.id=e,this.enabled=A,this.start=Date.now()}var be=(De.create=function(A,e){return De._caches[A]=new Se(A,e)},De.destroy=function(A){delete De._caches[A]},De.open=function(A){var e=De._caches[A];if(void 0!==e)return e;throw new Error('Cache with key "'+A+'" not found')},De.getOrigin=function(A){var e=De._link;return e?(e.href=A,e.href=e.href,e.protocol+e.hostname+e.port):"about:blank"},De.isSameOrigin=function(A){return De.getOrigin(A)===De._origin},De.setContext=function(A){De._link=A.document.createElement("a"),De._origin=De.getOrigin(A.location.href)},De.getInstance=function(){var A=De._current;if(null===A)throw new Error("No cache instance attached");return A},De.attachInstance=function(A){De._current=A},De.detachInstance=function(){De._current=null},De._caches={},De._origin="about:blank",De._current=null,De);function De(){}var Se=(Me.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)||(Xe(A)||Pe(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),e},Me.prototype.match=function(A){return this._cache[A]},Me.prototype.loadImage=function(s){return c(this,void 0,void 0,function(){var e,r,t,n,B=this;return H(this,function(A){switch(A.label){case 0:return(e=be.isSameOrigin(s),r=!Ve(s)&&!0===this._options.useCORS&&Re.SUPPORT_CORS_IMAGES&&!e,t=!Ve(s)&&!e&&!Xe(s)&&"string"==typeof this._options.proxy&&Re.SUPPORT_CORS_XHR&&!r,e||!1!==this._options.allowTaint||Ve(s)||Xe(s)||t||r)?(n=s,t?[4,this.proxy(n)]:[3,2]):[2];case 1:n=A.sent(),A.label=2;case 2:return Le.getInstance(this.id).debug("Added image "+s.substring(0,256)),[4,new Promise(function(A,e){var t=new Image;t.onload=function(){return A(t)},t.onerror=e,(ze(n)||r)&&(t.crossOrigin="anonymous"),t.src=n,!0===t.complete&&setTimeout(function(){return A(t)},500),0<B._options.imageTimeout&&setTimeout(function(){return e("Timed out ("+B._options.imageTimeout+"ms) loading image")},B._options.imageTimeout)})];case 3:return[2,A.sent()]}})})},Me.prototype.has=function(A){return void 0!==this._cache[A]},Me.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},Me.prototype.proxy=function(s){var o=this,i=this._options.proxy;if(!i)throw new Error("No proxy defined");var a=s.substring(0,256);return new Promise(function(e,t){var r=Re.SUPPORT_RESPONSE_TYPE?"blob":"text",n=new XMLHttpRequest;n.onload=function(){var A;200===n.status?"text"==r?e(n.response):((A=new FileReader).addEventListener("load",function(){return e(A.result)},!1),A.addEventListener("error",function(A){return t(A)},!1),A.readAsDataURL(n.response)):t("Failed to proxy resource "+a+" with status code "+n.status)},n.onerror=t;var A,B=-1<i.indexOf("?")?"&":"?";n.open("GET",i+B+"url="+encodeURIComponent(s)+"&responseType="+r),"text"!=r&&n instanceof XMLHttpRequest&&(n.responseType=r),o._options.imageTimeout&&(A=o._options.imageTimeout,n.timeout=A,n.ontimeout=function(){return t("Timed out ("+A+"ms) proxying "+a)}),n.send()})},Me);function Me(A,e){this.id=A,this._options=e,this._cache={}}var ve,ye=/^data:image\/svg\+xml/i,_e=/^data:image\/.*;base64,/i,xe=/^data:image\/.*/i,Pe=function(A){return Re.SUPPORT_SVG_DRAWING||!Ge(A)},Ve=function(A){return xe.test(A)},ze=function(A){return _e.test(A)},Xe=function(A){return"blob"===A.substr(0,4)},Ge=function(A){return"svg"===A.substr(-3).toLowerCase()||ye.test(A)},Je="closest-side",ke="farthest-side",We="closest-corner",Ye="farthest-corner",qe="ellipse",Ze="contain",je=function(A){var r=$e.CIRCLE,n=At.FARTHEST_CORNER,B=[],s=[];return WA(A).forEach(function(A,e){var t=!0;0===e?t=A.reduce(function(A,e){if(zA(e))switch(e.value){case"center":return s.push(se),!1;case"top":case"left":return s.push(Be),!1;case"right":case"bottom":return s.push(oe),!1}else if(qA(e)||YA(e))return s.push(e),!1;return A},t):1===e&&(t=A.reduce(function(A,e){if(zA(e))switch(e.value){case"circle":return r=$e.CIRCLE,!1;case qe:return r=$e.ELLIPSE,!1;case Ze:case Je:return n=At.CLOSEST_SIDE,!1;case ke:return n=At.FARTHEST_SIDE,!1;case We:return n=At.CLOSEST_CORNER,!1;case"cover":case Ye:return n=At.FARTHEST_CORNER,!1}else if(YA(e)||qA(e))return(n=!Array.isArray(n)?[]:n).push(e),!1;return A},t)),t&&(A=Fe(A),B.push(A))}),{size:n,shape:r,stops:B,position:s,type:ve.RADIAL_GRADIENT}};(ge=ve=ve||{})[ge.URL=0]="URL",ge[ge.LINEAR_GRADIENT=1]="LINEAR_GRADIENT",ge[ge.RADIAL_GRADIENT=2]="RADIAL_GRADIENT";var $e,At;(ge=$e=$e||{})[ge.CIRCLE=0]="CIRCLE",ge[ge.ELLIPSE=1]="ELLIPSE",(ge=At=At||{})[ge.CLOSEST_SIDE=0]="CLOSEST_SIDE",ge[ge.FARTHEST_SIDE=1]="FARTHEST_SIDE",ge[ge.CLOSEST_CORNER=2]="CLOSEST_CORNER",ge[ge.FARTHEST_CORNER=3]="FARTHEST_CORNER";var et=function(A){if(A.type===oA.URL_TOKEN){var e={url:A.value,type:ve.URL};return be.getInstance().addImage(A.value),e}if(A.type!==oA.FUNCTION)throw new Error("Unsupported image type");e=rt[A.name];if(void 0===e)throw new Error('Attempting to parse an unsupported image function "'+A.name+'"');return e(A.values)};var tt,rt={"linear-gradient":function(A){var t=ce(180),r=[];return WA(A).forEach(function(A,e){if(0===e){e=A[0];if(e.type===oA.IDENT_TOKEN&&"to"===e.value)return void(t=Ae(A));if($A(e))return void(t=ae(e))}A=Fe(A);r.push(A)}),{angle:t,stops:r,type:ve.LINEAR_GRADIENT}},"-moz-linear-gradient":we,"-ms-linear-gradient":we,"-o-linear-gradient":we,"-webkit-linear-gradient":we,"radial-gradient":function(A){var n=$e.CIRCLE,B=At.FARTHEST_CORNER,s=[],o=[];return WA(A).forEach(function(A,e){var t,r=!0;0===e&&(t=!1,r=A.reduce(function(A,e){if(t)if(zA(e))switch(e.value){case"center":return o.push(se),A;case"top":case"left":return o.push(Be),A;case"right":case"bottom":return o.push(oe),A}else(qA(e)||YA(e))&&o.push(e);else if(zA(e))switch(e.value){case"circle":return n=$e.CIRCLE,!1;case qe:return n=$e.ELLIPSE,!1;case"at":return!(t=!0);case Je:return B=At.CLOSEST_SIDE,!1;case"cover":case ke:return B=At.FARTHEST_SIDE,!1;case Ze:case We:return B=At.CLOSEST_CORNER,!1;case Ye:return B=At.FARTHEST_CORNER,!1}else if(YA(e)||qA(e))return(B=!Array.isArray(B)?[]:B).push(e),!1;return A},r)),r&&(A=Fe(A),s.push(A))}),{size:B,shape:n,stops:s,position:o,type:ve.RADIAL_GRADIENT}},"-moz-radial-gradient":je,"-ms-radial-gradient":je,"-o-radial-gradient":je,"-webkit-radial-gradient":je,"-webkit-gradient":function(A){var e=ce(180),r=[],n=ve.LINEAR_GRADIENT,t=$e.CIRCLE,B=At.FARTHEST_CORNER;return WA(A).forEach(function(A,e){var t,A=A[0];if(0===e){if(zA(A)&&"linear"===A.value)return void(n=ve.LINEAR_GRADIENT);if(zA(A)&&"radial"===A.value)return void(n=ve.RADIAL_GRADIENT)}A.type===oA.FUNCTION&&("from"===A.name?(t=Qe(A.values[0]),r.push({stop:Be,color:t})):"to"===A.name?(t=Qe(A.values[0]),r.push({stop:oe,color:t})):"color-stop"!==A.name||2===(A=A.values.filter(kA)).length&&(t=Qe(A[1]),A=A[0],VA(A)&&r.push({stop:{type:oA.PERCENTAGE_TOKEN,number:100*A.number,flags:A.flags},color:t})))}),n===ve.LINEAR_GRADIENT?{angle:(e+ce(180))%ce(360),stops:r,type:n}:{size:B,shape:t,stops:r,position:[],type:n}}},nt={name:"background-image",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(A){if(0===A.length)return[];var e=A[0];return e.type===oA.IDENT_TOKEN&&"none"===e.value?[]:A.filter(function(A){return kA(A)&&((A=A).type!==oA.FUNCTION||!!rt[A.name])}).map(et)}},Bt={name:"background-origin",initialValue:"border-box",prefix:!1,type:Ue.LIST,parse:function(A){return A.map(function(A){if(zA(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},st={name:"background-position",initialValue:"0% 0%",type:Ue.LIST,prefix:!1,parse:function(A){return WA(A).map(function(A){return A.filter(qA)}).map(ZA)}};(je=tt=tt||{})[je.REPEAT=0]="REPEAT",je[je.NO_REPEAT=1]="NO_REPEAT",je[je.REPEAT_X=2]="REPEAT_X";var ot,it={name:"background-repeat",initialValue:"repeat",prefix:!(je[je.REPEAT_Y=3]="REPEAT_Y"),type:Ue.LIST,parse:function(A){return WA(A).map(function(A){return A.filter(zA).map(function(A){return A.value}).join(" ")}).map(at)}},at=function(A){switch(A){case"no-repeat":return tt.NO_REPEAT;case"repeat-x":case"repeat no-repeat":return tt.REPEAT_X;case"repeat-y":case"no-repeat repeat":return tt.REPEAT_Y;default:return tt.REPEAT}};(je=ot=ot||{}).AUTO="auto",je.CONTAIN="contain";var ct,Qt={name:"background-size",initialValue:"0",prefix:!(je.COVER="cover"),type:Ue.LIST,parse:function(A){return WA(A).map(function(A){return A.filter(ut)})}},ut=function(A){return zA(A)||qA(A)},je=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"}},wt=je("top"),lt=je("right"),Ut=je("bottom"),Ct=je("left"),je=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:Ue.LIST,parse:function(A){return ZA(A.filter(qA))}}},gt=je("top-left"),Et=je("top-right"),ht=je("bottom-right"),Ft=je("bottom-left");(je=ct=ct||{})[je.NONE=0]="NONE",je[je.SOLID=1]="SOLID",je[je.DASHED=2]="DASHED",je[je.DOTTED=3]="DOTTED",je[je.DOUBLE=4]="DOUBLE";var dt,je=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"none":return ct.NONE;case"dashed":return ct.DASHED;case"dotted":return ct.DOTTED;case"double":return ct.DOUBLE}return ct.SOLID}}},Ht=je("top"),ft=je("right"),pt=je("bottom"),Nt=je("left"),je=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:Ue.VALUE,prefix:!1,parse:function(A){return PA(A)?A.number:0}}},Kt=je("top"),It=je("right"),Tt=je("bottom"),mt=je("left"),Rt={name:"color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},Lt={name:"display",initialValue:"inline-block",prefix:!1,type:Ue.LIST,parse:function(A){return A.filter(zA).reduce(function(A,e){return A|Ot(e.value)},0)}},Ot=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0};(je=dt=dt||{})[je.NONE=0]="NONE",je[je.LEFT=1]="LEFT",je[je.RIGHT=2]="RIGHT",je[je.INLINE_START=3]="INLINE_START";function bt(A,e){return zA(A)&&"normal"===A.value?1.2*e:A.type===oA.NUMBER_TOKEN?e*A.number:qA(A)?ie(A,e):e}var Dt,St,Mt={name:"float",initialValue:"none",prefix:!(je[je.INLINE_END=4]="INLINE_END"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"left":return dt.LEFT;case"right":return dt.RIGHT;case"inline-start":return dt.INLINE_START;case"inline-end":return dt.INLINE_END}return dt.NONE}},vt={name:"letter-spacing",initialValue:"0",prefix:!1,type:Ue.VALUE,parse:function(A){return!(A.type===oA.IDENT_TOKEN&&"normal"===A.value||A.type!==oA.NUMBER_TOKEN&&A.type!==oA.DIMENSION_TOKEN)?A.number:0}},yt={name:"line-break",initialValue:(je=Dt=Dt||{}).NORMAL="normal",prefix:!(je.STRICT="strict"),type:Ue.IDENT_VALUE,parse:function(A){return"strict"!==A?Dt.NORMAL:Dt.STRICT}},_t={name:"line-height",initialValue:"normal",prefix:!1,type:Ue.TOKEN_VALUE},xt={name:"list-style-image",initialValue:"none",type:Ue.VALUE,prefix:!1,parse:function(A){return A.type===oA.IDENT_TOKEN&&"none"===A.value?null:et(A)}};(je=St=St||{})[je.INSIDE=0]="INSIDE";var Pt,Vt={name:"list-style-position",initialValue:"outside",prefix:!(je[je.OUTSIDE=1]="OUTSIDE"),type:Ue.IDENT_VALUE,parse:function(A){return"inside"!==A?St.OUTSIDE:St.INSIDE}};(je=Pt=Pt||{})[je.NONE=-1]="NONE",je[je.DISC=0]="DISC",je[je.CIRCLE=1]="CIRCLE",je[je.SQUARE=2]="SQUARE",je[je.DECIMAL=3]="DECIMAL",je[je.CJK_DECIMAL=4]="CJK_DECIMAL",je[je.DECIMAL_LEADING_ZERO=5]="DECIMAL_LEADING_ZERO",je[je.LOWER_ROMAN=6]="LOWER_ROMAN",je[je.UPPER_ROMAN=7]="UPPER_ROMAN",je[je.LOWER_GREEK=8]="LOWER_GREEK",je[je.LOWER_ALPHA=9]="LOWER_ALPHA",je[je.UPPER_ALPHA=10]="UPPER_ALPHA",je[je.ARABIC_INDIC=11]="ARABIC_INDIC",je[je.ARMENIAN=12]="ARMENIAN",je[je.BENGALI=13]="BENGALI",je[je.CAMBODIAN=14]="CAMBODIAN",je[je.CJK_EARTHLY_BRANCH=15]="CJK_EARTHLY_BRANCH",je[je.CJK_HEAVENLY_STEM=16]="CJK_HEAVENLY_STEM",je[je.CJK_IDEOGRAPHIC=17]="CJK_IDEOGRAPHIC",je[je.DEVANAGARI=18]="DEVANAGARI",je[je.ETHIOPIC_NUMERIC=19]="ETHIOPIC_NUMERIC",je[je.GEORGIAN=20]="GEORGIAN",je[je.GUJARATI=21]="GUJARATI",je[je.GURMUKHI=22]="GURMUKHI",je[je.HEBREW=22]="HEBREW",je[je.HIRAGANA=23]="HIRAGANA",je[je.HIRAGANA_IROHA=24]="HIRAGANA_IROHA",je[je.JAPANESE_FORMAL=25]="JAPANESE_FORMAL",je[je.JAPANESE_INFORMAL=26]="JAPANESE_INFORMAL",je[je.KANNADA=27]="KANNADA",je[je.KATAKANA=28]="KATAKANA",je[je.KATAKANA_IROHA=29]="KATAKANA_IROHA",je[je.KHMER=30]="KHMER",je[je.KOREAN_HANGUL_FORMAL=31]="KOREAN_HANGUL_FORMAL",je[je.KOREAN_HANJA_FORMAL=32]="KOREAN_HANJA_FORMAL",je[je.KOREAN_HANJA_INFORMAL=33]="KOREAN_HANJA_INFORMAL",je[je.LAO=34]="LAO",je[je.LOWER_ARMENIAN=35]="LOWER_ARMENIAN",je[je.MALAYALAM=36]="MALAYALAM",je[je.MONGOLIAN=37]="MONGOLIAN",je[je.MYANMAR=38]="MYANMAR",je[je.ORIYA=39]="ORIYA",je[je.PERSIAN=40]="PERSIAN",je[je.SIMP_CHINESE_FORMAL=41]="SIMP_CHINESE_FORMAL",je[je.SIMP_CHINESE_INFORMAL=42]="SIMP_CHINESE_INFORMAL",je[je.TAMIL=43]="TAMIL",je[je.TELUGU=44]="TELUGU",je[je.THAI=45]="THAI",je[je.TIBETAN=46]="TIBETAN",je[je.TRAD_CHINESE_FORMAL=47]="TRAD_CHINESE_FORMAL",je[je.TRAD_CHINESE_INFORMAL=48]="TRAD_CHINESE_INFORMAL",je[je.UPPER_ARMENIAN=49]="UPPER_ARMENIAN",je[je.DISCLOSURE_OPEN=50]="DISCLOSURE_OPEN";var zt,Xt={name:"list-style-type",initialValue:"none",prefix:!(je[je.DISCLOSURE_CLOSED=51]="DISCLOSURE_CLOSED"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"disc":return Pt.DISC;case"circle":return Pt.CIRCLE;case"square":return Pt.SQUARE;case"decimal":return Pt.DECIMAL;case"cjk-decimal":return Pt.CJK_DECIMAL;case"decimal-leading-zero":return Pt.DECIMAL_LEADING_ZERO;case"lower-roman":return Pt.LOWER_ROMAN;case"upper-roman":return Pt.UPPER_ROMAN;case"lower-greek":return Pt.LOWER_GREEK;case"lower-alpha":return Pt.LOWER_ALPHA;case"upper-alpha":return Pt.UPPER_ALPHA;case"arabic-indic":return Pt.ARABIC_INDIC;case"armenian":return Pt.ARMENIAN;case"bengali":return Pt.BENGALI;case"cambodian":return Pt.CAMBODIAN;case"cjk-earthly-branch":return Pt.CJK_EARTHLY_BRANCH;case"cjk-heavenly-stem":return Pt.CJK_HEAVENLY_STEM;case"cjk-ideographic":return Pt.CJK_IDEOGRAPHIC;case"devanagari":return Pt.DEVANAGARI;case"ethiopic-numeric":return Pt.ETHIOPIC_NUMERIC;case"georgian":return Pt.GEORGIAN;case"gujarati":return Pt.GUJARATI;case"gurmukhi":return Pt.GURMUKHI;case"hebrew":return Pt.HEBREW;case"hiragana":return Pt.HIRAGANA;case"hiragana-iroha":return Pt.HIRAGANA_IROHA;case"japanese-formal":return Pt.JAPANESE_FORMAL;case"japanese-informal":return Pt.JAPANESE_INFORMAL;case"kannada":return Pt.KANNADA;case"katakana":return Pt.KATAKANA;case"katakana-iroha":return Pt.KATAKANA_IROHA;case"khmer":return Pt.KHMER;case"korean-hangul-formal":return Pt.KOREAN_HANGUL_FORMAL;case"korean-hanja-formal":return Pt.KOREAN_HANJA_FORMAL;case"korean-hanja-informal":return Pt.KOREAN_HANJA_INFORMAL;case"lao":return Pt.LAO;case"lower-armenian":return Pt.LOWER_ARMENIAN;case"malayalam":return Pt.MALAYALAM;case"mongolian":return Pt.MONGOLIAN;case"myanmar":return Pt.MYANMAR;case"oriya":return Pt.ORIYA;case"persian":return Pt.PERSIAN;case"simp-chinese-formal":return Pt.SIMP_CHINESE_FORMAL;case"simp-chinese-informal":return Pt.SIMP_CHINESE_INFORMAL;case"tamil":return Pt.TAMIL;case"telugu":return Pt.TELUGU;case"thai":return Pt.THAI;case"tibetan":return Pt.TIBETAN;case"trad-chinese-formal":return Pt.TRAD_CHINESE_FORMAL;case"trad-chinese-informal":return Pt.TRAD_CHINESE_INFORMAL;case"upper-armenian":return Pt.UPPER_ARMENIAN;case"disclosure-open":return Pt.DISCLOSURE_OPEN;case"disclosure-closed":return Pt.DISCLOSURE_CLOSED;default:return Pt.NONE}}},je=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:Ue.TOKEN_VALUE}},Gt=je("top"),Jt=je("right"),kt=je("bottom"),Wt=je("left");(je=zt=zt||{})[je.VISIBLE=0]="VISIBLE",je[je.HIDDEN=1]="HIDDEN",je[je.SCROLL=2]="SCROLL";var Yt,qt,Zt={name:"overflow",initialValue:"visible",prefix:!(je[je.AUTO=3]="AUTO"),type:Ue.LIST,parse:function(A){return A.filter(zA).map(function(A){switch(A.value){case"hidden":return zt.HIDDEN;case"scroll":return zt.SCROLL;case"auto":return zt.AUTO;default:return zt.VISIBLE}})}},jt={name:"overflow-wrap",initialValue:(je=Yt=Yt||{}).NORMAL="normal",prefix:!(je.BREAK_WORD="break-word"),type:Ue.IDENT_VALUE,parse:function(A){return"break-word"!==A?Yt.NORMAL:Yt.BREAK_WORD}},je=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:Ue.TYPE_VALUE,format:"length-percentage"}},$t=je("top"),Ar=je("right"),er=je("bottom"),tr=je("left");(je=qt=qt||{})[je.LEFT=0]="LEFT",je[je.CENTER=1]="CENTER";var rr,nr={name:"text-align",initialValue:"left",prefix:!(je[je.RIGHT=2]="RIGHT"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"right":return qt.RIGHT;case"center":case"justify":return qt.CENTER;default:return qt.LEFT}}};(je=rr=rr||{})[je.STATIC=0]="STATIC",je[je.RELATIVE=1]="RELATIVE",je[je.ABSOLUTE=2]="ABSOLUTE",je[je.FIXED=3]="FIXED";var Br,sr={name:"position",initialValue:"static",prefix:!(je[je.STICKY=4]="STICKY"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"relative":return rr.RELATIVE;case"absolute":return rr.ABSOLUTE;case"fixed":return rr.FIXED;case"sticky":return rr.STICKY}return rr.STATIC}},or={name:"text-shadow",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(A){return 1===A.length&&GA(A[0],"none")?[]:WA(A).map(function(A){for(var e={color:he.TRANSPARENT,offsetX:Be,offsetY:Be,blur:Be},t=0,r=0;r<A.length;r++){var n=A[r];YA(n)?(0===t?e.offsetX=n:1===t?e.offsetY=n:e.blur=n,t++):e.color=Qe(n)}return e})}};(je=Br=Br||{})[je.NONE=0]="NONE",je[je.LOWERCASE=1]="LOWERCASE",je[je.UPPERCASE=2]="UPPERCASE";var ir,ar={name:"text-transform",initialValue:"none",prefix:!(je[je.CAPITALIZE=3]="CAPITALIZE"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"uppercase":return Br.UPPERCASE;case"lowercase":return Br.LOWERCASE;case"capitalize":return Br.CAPITALIZE}return Br.NONE}},cr={name:"transform",initialValue:"none",prefix:!0,type:Ue.VALUE,parse:function(A){if(A.type===oA.IDENT_TOKEN&&"none"===A.value)return null;if(A.type!==oA.FUNCTION)return null;var e=Qr[A.name];if(void 0===e)throw new Error('Attempting to parse an unsupported transform function "'+A.name+'"');return e(A.values)}},Qr={matrix:function(A){A=A.filter(function(A){return A.type===oA.NUMBER_TOKEN}).map(function(A){return A.number});return 6===A.length?A:null},matrix3d:function(A){var e=A.filter(function(A){return A.type===oA.NUMBER_TOKEN}).map(function(A){return A.number}),t=e[0],r=e[1];e[2],e[3];var n=e[4],B=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var s=e[12],A=e[13];return e[14],e[15],16===e.length?[t,r,n,B,s,A]:null}},je={type:oA.PERCENTAGE_TOKEN,number:50,flags:4},ur=[je,je],wr={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:Ue.LIST,parse:function(A){A=A.filter(qA);return 2!==A.length?ur:[A[0],A[1]]}};(je=ir=ir||{})[je.VISIBLE=0]="VISIBLE",je[je.HIDDEN=1]="HIDDEN";var lr,Ur={name:"visible",initialValue:"none",prefix:!(je[je.COLLAPSE=2]="COLLAPSE"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"hidden":return ir.HIDDEN;case"collapse":return ir.COLLAPSE;default:return ir.VISIBLE}}};(je=lr=lr||{}).NORMAL="normal",je.BREAK_ALL="break-all";var Cr,gr={name:"word-break",initialValue:"normal",prefix:!(je.KEEP_ALL="keep-all"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"break-all":return lr.BREAK_ALL;case"keep-all":return lr.KEEP_ALL;default:return lr.NORMAL}}},Er={name:"z-index",initialValue:"auto",prefix:!1,type:Ue.VALUE,parse:function(A){if(A.type===oA.IDENT_TOKEN)return{auto:!0,order:0};if(VA(A))return{auto:!1,order:A.number};throw new Error("Invalid z-index number parsed")}},hr={name:"opacity",initialValue:"1",type:Ue.VALUE,prefix:!1,parse:function(A){return VA(A)?A.number:1}},Fr={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},dr={name:"text-decoration-line",initialValue:"none",prefix:!1,type:Ue.LIST,parse:function(A){return A.filter(zA).map(function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(A){return 0!==A})}},Hr={name:"font-family",initialValue:"",prefix:!1,type:Ue.LIST,parse:function(A){var e=[],t=[];return A.forEach(function(A){switch(A.type){case oA.IDENT_TOKEN:case oA.STRING_TOKEN:e.push(A.value);break;case oA.NUMBER_TOKEN:e.push(A.number.toString());break;case oA.COMMA_TOKEN:t.push(e.join(" ")),e.length=0}}),e.length&&t.push(e.join(" ")),t.map(function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"})}},fr={name:"font-size",initialValue:"0",prefix:!1,type:Ue.TYPE_VALUE,format:"length"},pr={name:"font-weight",initialValue:"normal",type:Ue.VALUE,prefix:!1,parse:function(A){return VA(A)?A.number:!zA(A)||"bold"!==A.value?400:700}},Nr={name:"font-variant",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(A){return A.filter(zA).map(function(A){return A.value})}};(je=Cr=Cr||{}).NORMAL="normal",je.ITALIC="italic";function Kr(A,e){return 0!=(A&e)}function Ir(A,e,t){return(A=A&&A[Math.min(e,A.length-1)])?t?A.open:A.close:""}var Tr,mr={name:"font-style",initialValue:"normal",prefix:!(je.OBLIQUE="oblique"),type:Ue.IDENT_VALUE,parse:function(A){switch(A){case"oblique":return Cr.OBLIQUE;case"italic":return Cr.ITALIC;default:return Cr.NORMAL}}},Rr={name:"content",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(A){if(0===A.length)return[];var e=A[0];return e.type===oA.IDENT_TOKEN&&"none"===e.value?[]:A}},Lr={name:"counter-increment",initialValue:"none",prefix:!0,type:Ue.LIST,parse:function(A){if(0===A.length)return null;var e=A[0];if(e.type===oA.IDENT_TOKEN&&"none"===e.value)return null;for(var t=[],r=A.filter(JA),n=0;n<r.length;n++){var B=r[n],s=r[n+1];B.type===oA.IDENT_TOKEN&&(s=s&&VA(s)?s.number:1,t.push({counter:B.value,increment:s}))}return t}},Or={name:"counter-reset",initialValue:"none",prefix:!0,type:Ue.LIST,parse:function(A){if(0===A.length)return[];for(var e=[],t=A.filter(JA),r=0;r<t.length;r++){var n=t[r],B=t[r+1];zA(n)&&"none"!==n.value&&(B=B&&VA(B)?B.number:0,e.push({counter:n.value,reset:B}))}return e}},br={name:"quotes",initialValue:"none",prefix:!0,type:Ue.LIST,parse:function(A){if(0===A.length)return null;var e=A[0];if(e.type===oA.IDENT_TOKEN&&"none"===e.value)return null;var t=[],r=A.filter(XA);if(r.length%2!=0)return null;for(var n=0;n<r.length;n+=2){var B=r[n].value,s=r[n+1].value;t.push({open:B,close:s})}return t}},Dr={name:"box-shadow",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(A){return 1===A.length&&GA(A[0],"none")?[]:WA(A).map(function(A){for(var e={color:255,offsetX:Be,offsetY:Be,blur:Be,spread:Be,inset:!1},t=0,r=0;r<A.length;r++){var n=A[r];GA(n,"inset")?e.inset=!0:YA(n)?(0===t?e.offsetX=n:1===t?e.offsetY=n:2===t?e.blur=n:e.spread=n,t++):e.color=Qe(n)}return e})}};(je=Tr=Tr||{})[je.FILL=0]="FILL",je[je.STROKE=1]="STROKE";var Sr={name:"paint-order",initialValue:"normal",prefix:!(je[je.MARKERS=2]="MARKERS"),type:Ue.LIST,parse:function(A){var e=[Tr.FILL,Tr.STROKE,Tr.MARKERS],t=[];return A.filter(zA).forEach(function(A){switch(A.value){case"stroke":t.push(Tr.STROKE);break;case"fill":t.push(Tr.FILL);break;case"markers":t.push(Tr.MARKERS)}}),e.forEach(function(A){-1===t.indexOf(A)&&t.push(A)}),t}},Mr={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},vr={name:"-webkit-text-stroke-width",initialValue:"0",type:Ue.VALUE,prefix:!1,parse:function(A){return PA(A)?A.number:0}},yr=(_r.prototype.isVisible=function(){return 0<this.display&&0<this.opacity&&this.visibility===ir.VISIBLE},_r.prototype.isTransparent=function(){return ee(this.backgroundColor)},_r.prototype.isTransformed=function(){return null!==this.transform},_r.prototype.isPositioned=function(){return this.position!==rr.STATIC},_r.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},_r.prototype.isFloating=function(){return this.float!==dt.NONE},_r.prototype.isInlineLevel=function(){return Kr(this.display,4)||Kr(this.display,33554432)||Kr(this.display,268435456)||Kr(this.display,536870912)||Kr(this.display,67108864)||Kr(this.display,134217728)},_r);function _r(A){this.backgroundClip=zr(Ke,A.backgroundClip),this.backgroundColor=zr(Ie,A.backgroundColor),this.backgroundImage=zr(nt,A.backgroundImage),this.backgroundOrigin=zr(Bt,A.backgroundOrigin),this.backgroundPosition=zr(st,A.backgroundPosition),this.backgroundRepeat=zr(it,A.backgroundRepeat),this.backgroundSize=zr(Qt,A.backgroundSize),this.borderTopColor=zr(wt,A.borderTopColor),this.borderRightColor=zr(lt,A.borderRightColor),this.borderBottomColor=zr(Ut,A.borderBottomColor),this.borderLeftColor=zr(Ct,A.borderLeftColor),this.borderTopLeftRadius=zr(gt,A.borderTopLeftRadius),this.borderTopRightRadius=zr(Et,A.borderTopRightRadius),this.borderBottomRightRadius=zr(ht,A.borderBottomRightRadius),this.borderBottomLeftRadius=zr(Ft,A.borderBottomLeftRadius),this.borderTopStyle=zr(Ht,A.borderTopStyle),this.borderRightStyle=zr(ft,A.borderRightStyle),this.borderBottomStyle=zr(pt,A.borderBottomStyle),this.borderLeftStyle=zr(Nt,A.borderLeftStyle),this.borderTopWidth=zr(Kt,A.borderTopWidth),this.borderRightWidth=zr(It,A.borderRightWidth),this.borderBottomWidth=zr(Tt,A.borderBottomWidth),this.borderLeftWidth=zr(mt,A.borderLeftWidth),this.boxShadow=zr(Dr,A.boxShadow),this.color=zr(Rt,A.color),this.display=zr(Lt,A.display),this.float=zr(Mt,A.cssFloat),this.fontFamily=zr(Hr,A.fontFamily),this.fontSize=zr(fr,A.fontSize),this.fontStyle=zr(mr,A.fontStyle),this.fontVariant=zr(Nr,A.fontVariant),this.fontWeight=zr(pr,A.fontWeight),this.letterSpacing=zr(vt,A.letterSpacing),this.lineBreak=zr(yt,A.lineBreak),this.lineHeight=zr(_t,A.lineHeight),this.listStyleImage=zr(xt,A.listStyleImage),this.listStylePosition=zr(Vt,A.listStylePosition),this.listStyleType=zr(Xt,A.listStyleType),this.marginTop=zr(Gt,A.marginTop),this.marginRight=zr(Jt,A.marginRight),this.marginBottom=zr(kt,A.marginBottom),this.marginLeft=zr(Wt,A.marginLeft),this.opacity=zr(hr,A.opacity);var e=zr(Zt,A.overflow);this.overflowX=e[0],this.overflowY=e[1<e.length?1:0],this.overflowWrap=zr(jt,A.overflowWrap),this.paddingTop=zr($t,A.paddingTop),this.paddingRight=zr(Ar,A.paddingRight),this.paddingBottom=zr(er,A.paddingBottom),this.paddingLeft=zr(tr,A.paddingLeft),this.paintOrder=zr(Sr,A.paintOrder),this.position=zr(sr,A.position),this.textAlign=zr(nr,A.textAlign),this.textDecorationColor=zr(Fr,null!==(e=A.textDecorationColor)&&void 0!==e?e:A.color),this.textDecorationLine=zr(dr,null!==(e=A.textDecorationLine)&&void 0!==e?e:A.textDecoration),this.textShadow=zr(or,A.textShadow),this.textTransform=zr(ar,A.textTransform),this.transform=zr(cr,A.transform),this.transformOrigin=zr(wr,A.transformOrigin),this.visibility=zr(Ur,A.visibility),this.webkitTextStrokeColor=zr(Mr,A.webkitTextStrokeColor),this.webkitTextStrokeWidth=zr(vr,A.webkitTextStrokeWidth),this.wordBreak=zr(gr,A.wordBreak),this.zIndex=zr(Er,A.zIndex)}var xr,Pr=function(A){this.content=zr(Rr,A.content),this.quotes=zr(br,A.quotes)},Vr=function(A){this.counterIncrement=zr(Lr,A.counterIncrement),this.counterReset=zr(Or,A.counterReset)},zr=function(A,e){var t=new vA,e=null!=e?e.toString():A.initialValue;t.write(e);var r=new _A(t.read());switch(A.type){case Ue.IDENT_VALUE:var n=r.parseComponentValue();return A.parse(zA(n)?n.value:A.initialValue);case Ue.VALUE:return A.parse(r.parseComponentValue());case Ue.LIST:return A.parse(r.parseComponentValues());case Ue.TOKEN_VALUE:return r.parseComponentValue();case Ue.TYPE_VALUE:switch(A.format){case"angle":return ae(r.parseComponentValue());case"color":return Qe(r.parseComponentValue());case"image":return et(r.parseComponentValue());case"length":var B=r.parseComponentValue();return YA(B)?B:Be;case"length-percentage":B=r.parseComponentValue();return qA(B)?B:Be}}},Xr=function(A){this.styles=new yr(window.getComputedStyle(A,null)),this.textNodes=[],this.elements=[],null!==this.styles.transform&&Xn(A)&&(A.style.transform="none"),this.bounds=E(A),this.flags=0},Gr=function(A,e){this.text=A,this.bounds=e},Jr=function(A){var e=A.ownerDocument;if(e){var t=e.createElement("html2canvaswrapper");t.appendChild(A.cloneNode(!0));e=A.parentNode;if(e){e.replaceChild(t,A);A=E(t);return t.firstChild&&e.replaceChild(t.firstChild,t),A}}return new g(0,0,0,0)},kr=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");r=r.createRange();return r.setStart(A,e),r.setEnd(A,e+t),g.fromClientRect(r.getBoundingClientRect())},Wr=function(A,e){return 0!==e.letterSpacing?a(A).map(function(A){return Q(A)}):qr(A,e)},Yr=[32,160,4961,65792,65793,4153,4241],qr=function(A,e){for(var t,r=sA(A,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap===Yt.BREAK_WORD?"break-word":e.wordBreak}),n=[];!(t=r.next()).done;)!function(){var A,e;t.value&&(A=t.value.slice(),A=a(A),e="",A.forEach(function(A){-1===Yr.indexOf(A)?e+=Q(A):(e.length&&n.push(e),n.push(Q(A)),e="")}),e.length&&n.push(e))}();return n},Zr=function(A,e){var t,r,n,B,s;this.text=jr(A.data,e.textTransform),this.textBounds=(t=this.text,n=A,t=Wr(t,r=e),B=[],s=0,t.forEach(function(A){var e;r.textDecorationLine.length||0<A.trim().length?Re.SUPPORT_RANGE_BOUNDS?B.push(new Gr(A,kr(n,s,A.length))):(e=n.splitText(A.length),B.push(new Gr(A,Jr(n))),n=e):Re.SUPPORT_RANGE_BOUNDS||(n=n.splitText(A.length)),s+=A.length}),B)},jr=function(A,e){switch(e){case Br.LOWERCASE:return A.toLowerCase();case Br.CAPITALIZE:return A.replace($r,An);case Br.UPPERCASE:return A.toUpperCase();default:return A}},$r=/(^|\s|:|-|\(|\))([a-z])/g,An=function(A,e,t){return 0<A.length?e+t.toUpperCase():A},en=(A(tn,xr=Xr),tn);function tn(A){var e=xr.call(this,A)||this;return e.src=A.currentSrc||A.src,e.intrinsicWidth=A.naturalWidth,e.intrinsicHeight=A.naturalHeight,be.getInstance().addImage(e.src),e}var rn,nn=(A(Bn,rn=Xr),Bn);function Bn(A){var e=rn.call(this,A)||this;return e.canvas=A,e.intrinsicWidth=A.width,e.intrinsicHeight=A.height,e}var sn,on=(A(an,sn=Xr),an);function an(A){var e=sn.call(this,A)||this,t=new XMLSerializer,r=E(A);return A.setAttribute("width",r.width+"px"),A.setAttribute("height",r.height+"px"),e.svg="data:image/svg+xml,"+encodeURIComponent(t.serializeToString(A)),e.intrinsicWidth=A.width.baseVal.value,e.intrinsicHeight=A.height.baseVal.value,be.getInstance().addImage(e.svg),e}var cn,Qn=(A(un,cn=Xr),un);function un(A){var e=cn.call(this,A)||this;return e.value=A.value,e}var wn,ln=(A(Un,wn=Xr),Un);function Un(A){var e=wn.call(this,A)||this;return e.start=A.start,e.reversed="boolean"==typeof A.reversed&&!0===A.reversed,e}var Cn,gn=[{type:oA.DIMENSION_TOKEN,flags:0,unit:"px",number:3}],En=[{type:oA.PERCENTAGE_TOKEN,flags:0,number:50}],hn="checkbox",Fn="radio",dn="password",Hn=707406591,fn=(A(pn,Cn=Xr),pn);function pn(A){var e,t=Cn.call(this,A)||this;switch(t.type=A.type.toLowerCase(),t.checked=A.checked,t.value=0===(A=(e=A).type===dn?new Array(e.value.length+1).join("•"):e.value).length?e.placeholder||"":A,t.type!==hn&&t.type!==Fn||(t.styles.backgroundColor=3739148031,t.styles.borderTopColor=t.styles.borderRightColor=t.styles.borderBottomColor=t.styles.borderLeftColor=2779096575,t.styles.borderTopWidth=t.styles.borderRightWidth=t.styles.borderBottomWidth=t.styles.borderLeftWidth=1,t.styles.borderTopStyle=t.styles.borderRightStyle=t.styles.borderBottomStyle=t.styles.borderLeftStyle=ct.SOLID,t.styles.backgroundClip=[Ce.BORDER_BOX],t.styles.backgroundOrigin=[0],t.bounds=(A=t.bounds).width>A.height?new g(A.left+(A.width-A.height)/2,A.top,A.height,A.height):A.width<A.height?new g(A.left,A.top+(A.height-A.width)/2,A.width,A.width):A),t.type){case hn:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=gn;break;case Fn:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=En}return t}var Nn,Kn=(A(In,Nn=Xr),In);function In(A){var e=Nn.call(this,A)||this,A=A.options[A.selectedIndex||0];return e.value=A&&A.text||"",e}var Tn,mn=(A(Rn,Tn=Xr),Rn);function Rn(A){var e=Tn.call(this,A)||this;return e.value=A.value,e}function Ln(A){return Qe(_A.create(A).parseComponentValue())}var On,bn=(A(Dn,On=Xr),Dn);function Dn(A){var e,t,r=On.call(this,A)||this;r.src=A.src,r.width=parseInt(A.width,10)||0,r.height=parseInt(A.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{A.contentWindow&&A.contentWindow.document&&A.contentWindow.document.documentElement&&(r.tree=_n(A.contentWindow.document.documentElement),e=A.contentWindow.document.documentElement?Ln(getComputedStyle(A.contentWindow.document.documentElement).backgroundColor):he.TRANSPARENT,t=A.contentWindow.document.body?Ln(getComputedStyle(A.contentWindow.document.body).backgroundColor):he.TRANSPARENT,r.backgroundColor=ee(e)?ee(t)?r.styles.backgroundColor:t:e)}catch(A){}return r}function Sn(A){return"STYLE"===A.tagName}var Mn=["OL","UL","MENU"],vn=function(A,e,t){for(var r=A.firstChild;r;r=B){var n,B=r.nextSibling;Vn(r)&&0<r.data.trim().length?e.textNodes.push(new Zr(r,e.styles)):zn(r)&&(tB(r)&&r.assignedNodes?r.assignedNodes().forEach(function(A){return vn(A,e,t)}):(n=yn(r)).styles.isVisible()&&(xn(r,n,t)?n.flags|=4:Pn(n.styles)&&(n.flags|=2),-1!==Mn.indexOf(r.tagName)&&(n.flags|=8),e.elements.push(n),r.slot,r.shadowRoot?vn(r.shadowRoot,n,t):AB(r)||Yn(r)||eB(r)||vn(r,n,t)))}},yn=function(A){return new(jn(A)?en:Zn(A)?nn:Yn(A)?on:Jn(A)?Qn:kn(A)?ln:Wn(A)?fn:eB(A)?Kn:AB(A)?mn:$n(A)?bn:Xr)(A)},_n=function(A){var e=yn(A);return e.flags|=4,vn(A,e,e),e},xn=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||qn(A)&&t.styles.isTransparent()},Pn=function(A){return A.isPositioned()||A.isFloating()},Vn=function(A){return A.nodeType===Node.TEXT_NODE},zn=function(A){return A.nodeType===Node.ELEMENT_NODE},Xn=function(A){return zn(A)&&void 0!==A.style&&!Gn(A)},Gn=function(A){return"object"==typeof A.className},Jn=function(A){return"LI"===A.tagName},kn=function(A){return"OL"===A.tagName},Wn=function(A){return"INPUT"===A.tagName},Yn=function(A){return"svg"===A.tagName},qn=function(A){return"BODY"===A.tagName},Zn=function(A){return"CANVAS"===A.tagName},jn=function(A){return"IMG"===A.tagName},$n=function(A){return"IFRAME"===A.tagName},AB=function(A){return"TEXTAREA"===A.tagName},eB=function(A){return"SELECT"===A.tagName},tB=function(A){return"SLOT"===A.tagName},rB=(nB.prototype.getCounterValue=function(A){A=this.counters[A];return A&&A.length?A[A.length-1]:1},nB.prototype.getCounterValues=function(A){A=this.counters[A];return A||[]},nB.prototype.pop=function(A){var e=this;A.forEach(function(A){return e.counters[A].pop()})},nB.prototype.parse=function(A){var t=this,e=A.counterIncrement,A=A.counterReset,r=!0;null!==e&&e.forEach(function(A){var e=t.counters[A.counter];e&&0!==A.increment&&(r=!1,e[Math.max(0,e.length-1)]+=A.increment)});var n=[];return r&&A.forEach(function(A){var e=t.counters[A.counter];n.push(A.counter),(e=e||(t.counters[A.counter]=[])).push(A.reset)}),n},nB);function nB(){this.counters={}}function BB(r,A,e,n,t,B){return r<A||e<r?CB(r,t,0<B.length):n.integers.reduce(function(A,e,t){for(;e<=r;)r-=e,A+=n.values[t];return A},"")+B}function sB(A,e,t,r){for(var n="";t||A--,n=r(A)+n,e<=(A/=e)*e;);return n}function oB(A,e,t,r,n){var B=t-e+1;return(A<0?"-":"")+(sB(Math.abs(A),B,r,function(A){return Q(Math.floor(A%B)+e)})+n)}function iB(A,e,t){void 0===t&&(t=". ");var r=e.length;return sB(Math.abs(A),r,!1,function(A){return e[Math.floor(A%r)]})+t}function aB(A,e,t,r,n,B){if(A<-9999||9999<A)return CB(A,Pt.CJK_DECIMAL,0<n.length);var s=Math.abs(A),o=n;if(0===s)return e[0]+o;for(var i=0;0<s&&i<=4;i++){var a=s%10;0==a&&Kr(B,1)&&""!==o?o=e[a]+o:1<a||1==a&&0===i||1==a&&1===i&&Kr(B,2)||1==a&&1===i&&Kr(B,4)&&100<A||1==a&&1<i&&Kr(B,8)?o=e[a]+(0<i?t[i-1]:"")+o:1==a&&0<i&&(o=t[i-1]+o),s=Math.floor(s/10)}return(A<0?r:"")+o}var cB,QB={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},uB={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},wB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},lB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},UB="마이너스",CB=function(A,e,t){var r=t?". ":"",n=t?"、":"",B=t?", ":"",s=t?" ":"";switch(e){case Pt.DISC:return"•"+s;case Pt.CIRCLE:return"◦"+s;case Pt.SQUARE:return"◾"+s;case Pt.DECIMAL_LEADING_ZERO:var o=oB(A,48,57,!0,r);return o.length<4?"0"+o:o;case Pt.CJK_DECIMAL:return iB(A,"〇一二三四五六七八九",n);case Pt.LOWER_ROMAN:return BB(A,1,3999,QB,Pt.DECIMAL,r).toLowerCase();case Pt.UPPER_ROMAN:return BB(A,1,3999,QB,Pt.DECIMAL,r);case Pt.LOWER_GREEK:return oB(A,945,969,!1,r);case Pt.LOWER_ALPHA:return oB(A,97,122,!1,r);case Pt.UPPER_ALPHA:return oB(A,65,90,!1,r);case Pt.ARABIC_INDIC:return oB(A,1632,1641,!0,r);case Pt.ARMENIAN:case Pt.UPPER_ARMENIAN:return BB(A,1,9999,uB,Pt.DECIMAL,r);case Pt.LOWER_ARMENIAN:return BB(A,1,9999,uB,Pt.DECIMAL,r).toLowerCase();case Pt.BENGALI:return oB(A,2534,2543,!0,r);case Pt.CAMBODIAN:case Pt.KHMER:return oB(A,6112,6121,!0,r);case Pt.CJK_EARTHLY_BRANCH:return iB(A,"子丑寅卯辰巳午未申酉戌亥",n);case Pt.CJK_HEAVENLY_STEM:return iB(A,"甲乙丙丁戊己庚辛壬癸",n);case Pt.CJK_IDEOGRAPHIC:case Pt.TRAD_CHINESE_INFORMAL:return aB(A,"零一二三四五六七八九","十百千萬","負",n,14);case Pt.TRAD_CHINESE_FORMAL:return aB(A,"零壹貳參肆伍陸柒捌玖","拾佰仟萬","負",n,15);case Pt.SIMP_CHINESE_INFORMAL:return aB(A,"零一二三四五六七八九","十百千萬","负",n,14);case Pt.SIMP_CHINESE_FORMAL:return aB(A,"零壹贰叁肆伍陆柒捌玖","拾佰仟萬","负",n,15);case Pt.JAPANESE_INFORMAL:return aB(A,"〇一二三四五六七八九","十百千万","マイナス",n,0);case Pt.JAPANESE_FORMAL:return aB(A,"零壱弐参四伍六七八九","拾百千万","マイナス",n,7);case Pt.KOREAN_HANGUL_FORMAL:return aB(A,"영일이삼사오육칠팔구","십백천만",UB,B,7);case Pt.KOREAN_HANJA_INFORMAL:return aB(A,"零一二三四五六七八九","十百千萬",UB,B,0);case Pt.KOREAN_HANJA_FORMAL:return aB(A,"零壹貳參四五六七八九","拾百千",UB,B,7);case Pt.DEVANAGARI:return oB(A,2406,2415,!0,r);case Pt.GEORGIAN:return BB(A,1,19999,lB,Pt.DECIMAL,r);case Pt.GUJARATI:return oB(A,2790,2799,!0,r);case Pt.GURMUKHI:return oB(A,2662,2671,!0,r);case Pt.HEBREW:return BB(A,1,10999,wB,Pt.DECIMAL,r);case Pt.HIRAGANA:return iB(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case Pt.HIRAGANA_IROHA:return iB(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case Pt.KANNADA:return oB(A,3302,3311,!0,r);case Pt.KATAKANA:return iB(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case Pt.KATAKANA_IROHA:return iB(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case Pt.LAO:return oB(A,3792,3801,!0,r);case Pt.MONGOLIAN:return oB(A,6160,6169,!0,r);case Pt.MYANMAR:return oB(A,4160,4169,!0,r);case Pt.ORIYA:return oB(A,2918,2927,!0,r);case Pt.PERSIAN:return oB(A,1776,1785,!0,r);case Pt.TAMIL:return oB(A,3046,3055,!0,r);case Pt.TELUGU:return oB(A,3174,3183,!0,r);case Pt.THAI:return oB(A,3664,3673,!0,r);case Pt.TIBETAN:return oB(A,3872,3881,!0,r);default:Pt.DECIMAL;return oB(A,48,57,!0,r)}},gB="data-html2canvas-ignore",EB=(hB.prototype.toIFrame=function(A,r){var e=this,n=HB(A,r);if(!n.contentWindow)return Promise.reject("Unable to find iframe window");var t=A.defaultView.pageXOffset,B=A.defaultView.pageYOffset,s=n.contentWindow,o=s.document,A=pB(n).then(function(){return c(e,void 0,void 0,function(){var e,t;return H(this,function(A){switch(A.label){case 0:return this.scrolledElements.forEach(mB),s&&(s.scrollTo(r.left,r.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||s.scrollY===r.top&&s.scrollX===r.left||(o.documentElement.style.top=-r.top+"px",o.documentElement.style.left=-r.left+"px",o.documentElement.style.position="absolute")),e=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:o.fonts&&o.fonts.ready?[4,o.fonts.ready]:[3,2];case 1:A.sent(),A.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,fB(o)]:[3,4];case 3:A.sent(),A.label=4;case 4:return"function"==typeof e?[2,Promise.resolve().then(function(){return e(o,t)}).then(function(){return n})]:[2,n]}})})});return o.open(),o.write(IB(document.doctype)+"<html></html>"),TB(this.referenceElement.ownerDocument,t,B),o.replaceChild(o.adoptNode(this.documentElement),o.documentElement),o.close(),A},hB.prototype.createElementClone=function(A){if(Zn(A))return this.createCanvasClone(A);if(Sn(A))return this.createStyleClone(A);A=A.cloneNode(!1);return jn(A)&&"lazy"===A.loading&&(A.loading="eager"),A},hB.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce(function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A},""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(A){if(Le.getInstance(this.options.id).error("Unable to access cssRules property",A),"SecurityError"!==A.name)throw A}return A.cloneNode(!1)},hB.prototype.createCanvasClone=function(A){if(this.options.inlineImages&&A.ownerDocument){var e=A.ownerDocument.createElement("img");try{return e.src=A.toDataURL(),e}catch(A){Le.getInstance(this.options.id).info("Unable to clone canvas contents, canvas is tainted")}}e=A.cloneNode(!1);try{e.width=A.width,e.height=A.height;var t=A.getContext("2d"),r=e.getContext("2d");return r&&(t?r.putImageData(t.getImageData(0,0,A.width,A.height),0,0):r.drawImage(A,0,0)),e}catch(A){}return e},hB.prototype.cloneNode=function(A){if(Vn(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var e=A.ownerDocument.defaultView;if(e&&zn(A)&&(Xn(A)||Gn(A))){var t=this.createElementClone(A),r=e.getComputedStyle(A),n=e.getComputedStyle(A,":before"),B=e.getComputedStyle(A,":after");this.referenceElement===A&&Xn(t)&&(this.clonedReferenceElement=t),qn(t)&&bB(t);for(var e=this.counters.parse(new Vr(r)),n=this.resolvePseudoContent(A,t,n,cB.BEFORE),s=A.firstChild;s;s=s.nextSibling)zn(s)&&("SCRIPT"===s.tagName||s.hasAttribute(gB)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(s))||this.options.copyStyles&&zn(s)&&Sn(s)||t.appendChild(this.cloneNode(s));n&&t.insertBefore(n,t.firstChild);B=this.resolvePseudoContent(A,t,B,cB.AFTER);return B&&t.appendChild(B),this.counters.pop(e),r&&(this.options.copyStyles||Gn(A))&&!$n(A)&&KB(r,t),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([t,A.scrollLeft,A.scrollTop]),(AB(A)||eB(A))&&(AB(t)||eB(t))&&(t.value=A.value),t}return A.cloneNode(!1)},hB.prototype.resolvePseudoContent=function(o,A,e,t){var i=this;if(e){var r=e.content,a=A.ownerDocument;if(a&&r&&"none"!==r&&"-moz-alt-content"!==r&&"none"!==e.display){this.counters.parse(new Vr(e));var c=new Pr(e),Q=a.createElement("html2canvaspseudoelement");KB(e,Q),c.content.forEach(function(A){if(A.type===oA.STRING_TOKEN)Q.appendChild(a.createTextNode(A.value));else if(A.type===oA.URL_TOKEN){var e=a.createElement("img");e.src=A.value,e.style.opacity="1",Q.appendChild(e)}else if(A.type===oA.FUNCTION){var t,r,n,B,s;"attr"===A.name?(e=A.values.filter(zA)).length&&Q.appendChild(a.createTextNode(o.getAttribute(e[0].value)||"")):"counter"===A.name?(n=(r=A.values.filter(kA))[0],r=r[1],n&&zA(n)&&(t=i.counters.getCounterValue(n.value),s=r&&zA(r)?Xt.parse(r.value):Pt.DECIMAL,Q.appendChild(a.createTextNode(CB(t,s,!1))))):"counters"===A.name&&(n=(t=A.values.filter(kA))[0],s=t[1],r=t[2],n&&zA(n)&&(n=i.counters.getCounterValues(n.value),B=r&&zA(r)?Xt.parse(r.value):Pt.DECIMAL,s=s&&s.type===oA.STRING_TOKEN?s.value:"",s=n.map(function(A){return CB(A,B,!1)}).join(s),Q.appendChild(a.createTextNode(s))))}else if(A.type===oA.IDENT_TOKEN)switch(A.value){case"open-quote":Q.appendChild(a.createTextNode(Ir(c.quotes,i.quoteDepth++,!0)));break;case"close-quote":Q.appendChild(a.createTextNode(Ir(c.quotes,--i.quoteDepth,!1)));break;default:Q.appendChild(a.createTextNode(A.value))}}),Q.className=RB+" "+LB;t=t===cB.BEFORE?" "+RB:" "+LB;return Gn(A)?A.className.baseValue+=t:A.className+=t,Q}}},hB.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},hB);function hB(A,e){if(this.options=e,this.scrolledElements=[],this.referenceElement=A,this.counters=new rB,this.quoteDepth=0,!A.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(A.ownerDocument.documentElement)}(je=cB=cB||{})[je.BEFORE=0]="BEFORE",je[je.AFTER=1]="AFTER";function FB(e){return new Promise(function(A){!e.complete&&e.src?(e.onload=A,e.onerror=A):A()})}var dB,HB=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(gB,"true"),A.body.appendChild(t),t},fB=function(A){return Promise.all([].slice.call(A.images,0).map(FB))},pB=function(n){return new Promise(function(e,A){var t=n.contentWindow;if(!t)return A("No window assigned for iframe");var r=t.document;t.onload=n.onload=function(){t.onload=n.onload=null;var A=setInterval(function(){0<r.body.childNodes.length&&"complete"===r.readyState&&(clearInterval(A),e(n))},50)}})},NB=["all","d","content"],KB=function(A,e){for(var t=A.length-1;0<=t;t--){var r=A.item(t);-1===NB.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},IB=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},TB=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},mB=function(A){var e=A[0],t=A[1],A=A[2];e.scrollLeft=t,e.scrollTop=A},RB="___html2canvas___pseudoelement_before",LB="___html2canvas___pseudoelement_after",OB='{\n    content: "" !important;\n    display: none !important;\n}',bB=function(A){DB(A,"."+RB+":before"+OB+"\n         ."+LB+":after"+OB)},DB=function(A,e){var t=A.ownerDocument;t&&((t=t.createElement("style")).textContent=e,A.appendChild(t))};(je=dB=dB||{})[je.VECTOR=0]="VECTOR",je[je.BEZIER_CURVE=1]="BEZIER_CURVE";function SB(A,t){return A.length===t.length&&A.some(function(A,e){return A===t[e]})}var MB=(vB.prototype.add=function(A,e){return new vB(this.x+A,this.y+e)},vB.prototype.reverse=function(){return this},vB);function vB(A,e){this.type=dB.VECTOR,this.x=A,this.y=e}function yB(A,e,t){return new MB(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)}var _B=(xB.prototype.subdivide=function(A,e){var t=yB(this.start,this.startControl,A),r=yB(this.startControl,this.endControl,A),n=yB(this.endControl,this.end,A),B=yB(t,r,A),r=yB(r,n,A),A=yB(B,r,A);return e?new xB(this.start,t,B,A):new xB(A,r,n,this.end)},xB.prototype.add=function(A,e){return new xB(this.start.add(A,e),this.startControl.add(A,e),this.endControl.add(A,e),this.end.add(A,e))},xB.prototype.reverse=function(){return new xB(this.end,this.endControl,this.startControl,this.start)},xB);function xB(A,e,t,r){this.type=dB.BEZIER_CURVE,this.start=A,this.startControl=e,this.endControl=t,this.end=r}function PB(A){return A.type===dB.BEZIER_CURVE}var VB,zB=function(A){var e=A.styles,t=A.bounds,r=(C=jA(e.borderTopLeftRadius,t.width,t.height))[0],n=C[1],B=(g=jA(e.borderTopRightRadius,t.width,t.height))[0],s=g[1],o=(E=jA(e.borderBottomRightRadius,t.width,t.height))[0],i=E[1],a=(h=jA(e.borderBottomLeftRadius,t.width,t.height))[0],c=h[1];(F=[]).push((r+B)/t.width),F.push((a+o)/t.width),F.push((n+c)/t.height),F.push((s+i)/t.height),1<(d=Math.max.apply(Math,F))&&(r/=d,n/=d,B/=d,s/=d,o/=d,i/=d,a/=d,c/=d);var Q=t.width-B,u=t.height-i,w=t.width-o,l=t.height-c,U=e.borderTopWidth,C=e.borderRightWidth,g=e.borderBottomWidth,E=e.borderLeftWidth,h=ie(e.paddingTop,A.bounds.width),F=ie(e.paddingRight,A.bounds.width),d=ie(e.paddingBottom,A.bounds.width),A=ie(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=0<r||0<n?JB(t.left+E/3,t.top+U/3,r-E/3,n-U/3,VB.TOP_LEFT):new MB(t.left+E/3,t.top+U/3),this.topRightBorderDoubleOuterBox=0<r||0<n?JB(t.left+Q,t.top+U/3,B-C/3,s-U/3,VB.TOP_RIGHT):new MB(t.left+t.width-C/3,t.top+U/3),this.bottomRightBorderDoubleOuterBox=0<o||0<i?JB(t.left+w,t.top+u,o-C/3,i-g/3,VB.BOTTOM_RIGHT):new MB(t.left+t.width-C/3,t.top+t.height-g/3),this.bottomLeftBorderDoubleOuterBox=0<a||0<c?JB(t.left+E/3,t.top+l,a-E/3,c-g/3,VB.BOTTOM_LEFT):new MB(t.left+E/3,t.top+t.height-g/3),this.topLeftBorderDoubleInnerBox=0<r||0<n?JB(t.left+2*E/3,t.top+2*U/3,r-2*E/3,n-2*U/3,VB.TOP_LEFT):new MB(t.left+2*E/3,t.top+2*U/3),this.topRightBorderDoubleInnerBox=0<r||0<n?JB(t.left+Q,t.top+2*U/3,B-2*C/3,s-2*U/3,VB.TOP_RIGHT):new MB(t.left+t.width-2*C/3,t.top+2*U/3),this.bottomRightBorderDoubleInnerBox=0<o||0<i?JB(t.left+w,t.top+u,o-2*C/3,i-2*g/3,VB.BOTTOM_RIGHT):new MB(t.left+t.width-2*C/3,t.top+t.height-2*g/3),this.bottomLeftBorderDoubleInnerBox=0<a||0<c?JB(t.left+2*E/3,t.top+l,a-2*E/3,c-2*g/3,VB.BOTTOM_LEFT):new MB(t.left+2*E/3,t.top+t.height-2*g/3),this.topLeftBorderStroke=0<r||0<n?JB(t.left+E/2,t.top+U/2,r-E/2,n-U/2,VB.TOP_LEFT):new MB(t.left+E/2,t.top+U/2),this.topRightBorderStroke=0<r||0<n?JB(t.left+Q,t.top+U/2,B-C/2,s-U/2,VB.TOP_RIGHT):new MB(t.left+t.width-C/2,t.top+U/2),this.bottomRightBorderStroke=0<o||0<i?JB(t.left+w,t.top+u,o-C/2,i-g/2,VB.BOTTOM_RIGHT):new MB(t.left+t.width-C/2,t.top+t.height-g/2),this.bottomLeftBorderStroke=0<a||0<c?JB(t.left+E/2,t.top+l,a-E/2,c-g/2,VB.BOTTOM_LEFT):new MB(t.left+E/2,t.top+t.height-g/2),this.topLeftBorderBox=0<r||0<n?JB(t.left,t.top,r,n,VB.TOP_LEFT):new MB(t.left,t.top),this.topRightBorderBox=0<B||0<s?JB(t.left+Q,t.top,B,s,VB.TOP_RIGHT):new MB(t.left+t.width,t.top),this.bottomRightBorderBox=0<o||0<i?JB(t.left+w,t.top+u,o,i,VB.BOTTOM_RIGHT):new MB(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=0<a||0<c?JB(t.left,t.top+l,a,c,VB.BOTTOM_LEFT):new MB(t.left,t.top+t.height),this.topLeftPaddingBox=0<r||0<n?JB(t.left+E,t.top+U,Math.max(0,r-E),Math.max(0,n-U),VB.TOP_LEFT):new MB(t.left+E,t.top+U),this.topRightPaddingBox=0<B||0<s?JB(t.left+Math.min(Q,t.width-C),t.top+U,Q>t.width+C?0:Math.max(0,B-C),Math.max(0,s-U),VB.TOP_RIGHT):new MB(t.left+t.width-C,t.top+U),this.bottomRightPaddingBox=0<o||0<i?JB(t.left+Math.min(w,t.width-E),t.top+Math.min(u,t.height-g),Math.max(0,o-C),Math.max(0,i-g),VB.BOTTOM_RIGHT):new MB(t.left+t.width-C,t.top+t.height-g),this.bottomLeftPaddingBox=0<a||0<c?JB(t.left+E,t.top+Math.min(l,t.height-g),Math.max(0,a-E),Math.max(0,c-g),VB.BOTTOM_LEFT):new MB(t.left+E,t.top+t.height-g),this.topLeftContentBox=0<r||0<n?JB(t.left+E+A,t.top+U+h,Math.max(0,r-(E+A)),Math.max(0,n-(U+h)),VB.TOP_LEFT):new MB(t.left+E+A,t.top+U+h),this.topRightContentBox=0<B||0<s?JB(t.left+Math.min(Q,t.width+E+A),t.top+U+h,Q>t.width+E+A?0:B-E+A,s-(U+h),VB.TOP_RIGHT):new MB(t.left+t.width-(C+F),t.top+U+h),this.bottomRightContentBox=0<o||0<i?JB(t.left+Math.min(w,t.width-(E+A)),t.top+Math.min(u,t.height+U+h),Math.max(0,o-(C+F)),i-(g+d),VB.BOTTOM_RIGHT):new MB(t.left+t.width-(C+F),t.top+t.height-(g+d)),this.bottomLeftContentBox=0<a||0<c?JB(t.left+E+A,t.top+l,Math.max(0,a-(E+A)),c-(g+d),VB.BOTTOM_LEFT):new MB(t.left+E+A,t.top+t.height-(g+d))};(je=VB=VB||{})[je.TOP_LEFT=0]="TOP_LEFT",je[je.TOP_RIGHT=1]="TOP_RIGHT",je[je.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",je[je.BOTTOM_LEFT=3]="BOTTOM_LEFT";function XB(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]}function GB(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]}var JB=function(A,e,t,r,n){var B=(Math.sqrt(2)-1)/3*4,s=t*B,o=r*B,i=A+t,a=e+r;switch(n){case VB.TOP_LEFT:return new _B(new MB(A,a),new MB(A,a-o),new MB(i-s,e),new MB(i,e));case VB.TOP_RIGHT:return new _B(new MB(A,e),new MB(A+s,e),new MB(i,a-o),new MB(i,a));case VB.BOTTOM_RIGHT:return new _B(new MB(i,e),new MB(i,e+o),new MB(A+s,a),new MB(A,a));default:VB.BOTTOM_LEFT;return new _B(new MB(i,a),new MB(i-s,a),new MB(A,e+o),new MB(A,e))}},kB=function(A,e,t){this.type=0,this.target=6,this.offsetX=A,this.offsetY=e,this.matrix=t},WB=function(A,e){this.type=1,this.target=e,this.path=A},YB=function(A){this.type=2,this.target=6,this.opacity=A},qB=function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]},ZB=(jB.prototype.getParentEffects=function(){var A,e,t=this.effects.slice(0);return this.container.styles.overflowX!==zt.VISIBLE&&(A=XB(this.curves),e=GB(this.curves),SB(A,e)||t.push(new WB(e,6))),t},jB);function jB(A,e){var t,r;this.container=A,this.effects=e.slice(0),this.curves=new zB(A),A.styles.opacity<1&&this.effects.push(new YB(A.styles.opacity)),null!==A.styles.transform&&(t=A.bounds.left+A.styles.transformOrigin[0].number,e=A.bounds.top+A.styles.transformOrigin[1].number,r=A.styles.transform,this.effects.push(new kB(t,e,r))),A.styles.overflowX!==zt.VISIBLE&&(r=XB(this.curves),A=GB(this.curves),SB(r,A)?this.effects.push(new WB(r,6)):(this.effects.push(new WB(r,2)),this.effects.push(new WB(A,4))))}function $B(A,e){switch(e){case 0:return is(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return is(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return is(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return is(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}}function As(A){var e=A.bounds,A=A.styles;return e.add(A.borderLeftWidth,A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth),-(A.borderTopWidth+A.borderBottomWidth))}function es(A){var e=A.styles,t=A.bounds,r=ie(e.paddingLeft,t.width),n=ie(e.paddingRight,t.width),B=ie(e.paddingTop,t.width),A=ie(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,B+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+B+A))}function ts(A,e,t){var r=(n=cs(A.styles.backgroundOrigin,e),B=A,0===n?B.bounds:(2===n?es:As)(B)),n=(s=cs(A.styles.backgroundClip,e),o=A,s===Ce.BORDER_BOX?o.bounds:(s===Ce.CONTENT_BOX?es:As)(o)),B=as(cs(A.styles.backgroundSize,e),t,r),s=B[0],o=B[1],t=jA(cs(A.styles.backgroundPosition,e),r.width-s,r.height-o);return[Qs(cs(A.styles.backgroundRepeat,e),t,B,r,n),Math.round(r.left+t[0]),Math.round(r.top+t[1]),s,o]}function rs(A){return zA(A)&&A.value===ot.AUTO}function ns(A){return"number"==typeof A}var Bs=function(a,c,Q,u){a.container.elements.forEach(function(A){var e=Kr(A.flags,4),t=Kr(A.flags,2),r=new ZB(A,a.getParentEffects());Kr(A.styles.display,2048)&&u.push(r);var n,B,s,o,i=Kr(A.flags,8)?[]:u;e||t?(n=e||A.styles.isPositioned()?Q:c,t=new qB(r),A.styles.isPositioned()||A.styles.opacity<1||A.styles.isTransformed()?(B=A.styles.zIndex.order)<0?(s=0,n.negativeZIndex.some(function(A,e){return B>A.element.container.styles.zIndex.order?(s=e,!1):0<s}),n.negativeZIndex.splice(s,0,t)):0<B?(o=0,n.positiveZIndex.some(function(A,e){return B>=A.element.container.styles.zIndex.order?(o=e+1,!1):0<o}),n.positiveZIndex.splice(o,0,t)):n.zeroOrAutoZIndexOrTransformedOrOpacity.push(t):(A.styles.isFloating()?n.nonPositionedFloats:n.nonPositionedInlineLevel).push(t),Bs(r,t,e?t:Q,i)):((A.styles.isInlineLevel()?c.inlineLevel:c.nonInlineLevel).push(r),Bs(r,c,Q,i)),Kr(A.flags,8)&&ss(A,i)})},ss=function(A,e){for(var t=A instanceof ln?A.start:1,r=A instanceof ln&&A.reversed,n=0;n<e.length;n++){var B=e[n];B.container instanceof Qn&&"number"==typeof B.container.value&&0!==B.container.value&&(t=B.container.value),B.listValue=CB(t,B.container.styles.listStyleType,!0),t+=r?-1:1}},os=function(A,e){var t=[];return PB(A)?t.push(A.subdivide(.5,!1)):t.push(A),PB(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},is=function(A,e,t,r){var n=[];return PB(A)?n.push(A.subdivide(.5,!1)):n.push(A),PB(t)?n.push(t.subdivide(.5,!0)):n.push(t),PB(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),PB(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},as=function(A,e,t){var r=e[0],n=e[1],B=e[2],s=A[0],o=A[1];if(!s)return[0,0];if(qA(s)&&o&&qA(o))return[ie(s,t.width),ie(o,t.height)];var i=ns(B);if(zA(s)&&(s.value===ot.CONTAIN||s.value===ot.COVER))return ns(B)?t.width/t.height<B!=(s.value===ot.COVER)?[t.width,t.width/B]:[t.height*B,t.height]:[t.width,t.height];var a=ns(r),e=ns(n),A=a||e;if(rs(s)&&(!o||rs(o)))return a&&e?[r,n]:i||A?A&&i?[a?r:n*B,e?n:r/B]:[a?r:t.width,e?n:t.height]:[t.width,t.height];if(i){var c=0,Q=0;return qA(s)?c=ie(s,t.width):qA(o)&&(Q=ie(o,t.height)),rs(s)?c=Q*B:o&&!rs(o)||(Q=c/B),[c,Q]}c=null,Q=null;if(qA(s)?c=ie(s,t.width):o&&qA(o)&&(Q=ie(o,t.height)),null!==(c=null!==(Q=null!==c&&(!o||rs(o))?a&&e?c/r*n:t.height:Q)&&rs(s)?a&&e?Q/n*r:t.width:c)&&null!==Q)return[c,Q];throw new Error("Unable to calculate background-size for element")},cs=function(A,e){e=A[e];return void 0===e?A[0]:e},Qs=function(A,e,t,r,n){var B=e[0],s=e[1],o=t[0],i=t[1];switch(A){case tt.REPEAT_X:return[new MB(Math.round(r.left),Math.round(r.top+s)),new MB(Math.round(r.left+r.width),Math.round(r.top+s)),new MB(Math.round(r.left+r.width),Math.round(i+r.top+s)),new MB(Math.round(r.left),Math.round(i+r.top+s))];case tt.REPEAT_Y:return[new MB(Math.round(r.left+B),Math.round(r.top)),new MB(Math.round(r.left+B+o),Math.round(r.top)),new MB(Math.round(r.left+B+o),Math.round(r.height+r.top)),new MB(Math.round(r.left+B),Math.round(r.height+r.top))];case tt.NO_REPEAT:return[new MB(Math.round(r.left+B),Math.round(r.top+s)),new MB(Math.round(r.left+B+o),Math.round(r.top+s)),new MB(Math.round(r.left+B+o),Math.round(r.top+s+i)),new MB(Math.round(r.left+B),Math.round(r.top+s+i))];default:return[new MB(Math.round(n.left),Math.round(n.top)),new MB(Math.round(n.left+n.width),Math.round(n.top)),new MB(Math.round(n.left+n.width),Math.round(n.height+n.top)),new MB(Math.round(n.left),Math.round(n.height+n.top))]}},us="Hidden Text",ws=(ls.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),B=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",B.appendChild(t),r.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(us)),t.appendChild(n),t.appendChild(r);e=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(us)),t.style.lineHeight="normal",r.style.verticalAlign="super";r=r.offsetTop-t.offsetTop+2;return B.removeChild(t),{baseline:e,middle:r}},ls.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},ls);function ls(A){this._data={},this._document=A}var Us=(Cs.prototype.applyEffects=function(A,e){for(var t=this;this._activeEffects.length;)this.popEffect();A.filter(function(A){return Kr(A.target,e)}).forEach(function(A){return t.applyEffect(A)})},Cs.prototype.applyEffect=function(A){this.ctx.save(),2===A.type&&(this.ctx.globalAlpha=A.opacity),0===A.type&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),1===A.type&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},Cs.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},Cs.prototype.renderStack=function(e){return c(this,void 0,void 0,function(){return H(this,function(A){switch(A.label){case 0:return e.element.container.styles.isVisible()?[4,this.renderStackContent(e)]:[3,2];case 1:A.sent(),A.label=2;case 2:return[2]}})})},Cs.prototype.renderNode=function(e){return c(this,void 0,void 0,function(){return H(this,function(A){switch(A.label){case 0:return e.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(e)]:[3,3];case 1:return A.sent(),[4,this.renderNodeContent(e)];case 2:A.sent(),A.label=3;case 3:return[2]}})})},Cs.prototype.renderTextWithLetterSpacing=function(t,A,r){var n=this;0===A?this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+r):a(t.text).map(function(A){return Q(A)}).reduce(function(A,e){return n.ctx.fillText(e,A,t.bounds.top+r),A+n.ctx.measureText(e).width},t.bounds.left)},Cs.prototype.createFontStyle=function(A){var e=A.fontVariant.filter(function(A){return"normal"===A||"small-caps"===A}).join(""),t=A.fontFamily.join(", "),r=PA(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},Cs.prototype.renderTextNode=function(i,a){return c(this,void 0,void 0,function(){var e,t,r,n,B,s,o=this;return H(this,function(A){return r=this.createFontStyle(a),e=r[0],t=r[1],r=r[2],this.ctx.font=e,this.ctx.textBaseline="alphabetic",r=this.fontMetrics.getMetrics(t,r),n=r.baseline,B=r.middle,s=a.paintOrder,i.textBounds.forEach(function(t){s.forEach(function(A){switch(A){case Tr.FILL:o.ctx.fillStyle=te(a.color),o.renderTextWithLetterSpacing(t,a.letterSpacing,n);var e=a.textShadow;e.length&&t.text.trim().length&&(e.slice(0).reverse().forEach(function(A){o.ctx.shadowColor=te(A.color),o.ctx.shadowOffsetX=A.offsetX.number*o.options.scale,o.ctx.shadowOffsetY=A.offsetY.number*o.options.scale,o.ctx.shadowBlur=A.blur.number,o.renderTextWithLetterSpacing(t,a.letterSpacing,n)}),o.ctx.shadowColor="",o.ctx.shadowOffsetX=0,o.ctx.shadowOffsetY=0,o.ctx.shadowBlur=0),a.textDecorationLine.length&&(o.ctx.fillStyle=te(a.textDecorationColor||a.color),a.textDecorationLine.forEach(function(A){switch(A){case 1:o.ctx.fillRect(t.bounds.left,Math.round(t.bounds.top+n),t.bounds.width,1);break;case 2:o.ctx.fillRect(t.bounds.left,Math.round(t.bounds.top),t.bounds.width,1);break;case 3:o.ctx.fillRect(t.bounds.left,Math.ceil(t.bounds.top+B),t.bounds.width,1)}}));break;case Tr.STROKE:a.webkitTextStrokeWidth&&t.text.trim().length&&(o.ctx.strokeStyle=te(a.webkitTextStrokeColor),o.ctx.lineWidth=a.webkitTextStrokeWidth,o.ctx.lineJoin=window.chrome?"miter":"round",o.ctx.strokeText(t.text,t.bounds.left,t.bounds.top+n)),o.ctx.strokeStyle="",o.ctx.lineWidth=0,o.ctx.lineJoin="miter"}})}),[2]})})},Cs.prototype.renderReplacedElement=function(A,e,t){var r;t&&0<A.intrinsicWidth&&0<A.intrinsicHeight&&(r=es(A),e=GB(e),this.path(e),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore())},Cs.prototype.renderNodeContent=function(w){return c(this,void 0,void 0,function(){var e,t,r,n,B,s,o,i,a,c,Q,u;return H(this,function(A){switch(A.label){case 0:this.applyEffects(w.effects,4),e=w.container,t=w.curves,r=e.styles,n=0,B=e.textNodes,A.label=1;case 1:return n<B.length?(s=B[n],[4,this.renderTextNode(s,r)]):[3,4];case 2:A.sent(),A.label=3;case 3:return n++,[3,1];case 4:if(!(e instanceof en))return[3,8];A.label=5;case 5:return A.trys.push([5,7,,8]),[4,this.options.cache.match(e.src)];case 6:return a=A.sent(),this.renderReplacedElement(e,t,a),[3,8];case 7:return A.sent(),Le.getInstance(this.options.id).error("Error loading image "+e.src),[3,8];case 8:if(e instanceof nn&&this.renderReplacedElement(e,t,e.canvas),!(e instanceof on))return[3,12];A.label=9;case 9:return A.trys.push([9,11,,12]),[4,this.options.cache.match(e.svg)];case 10:return a=A.sent(),this.renderReplacedElement(e,t,a),[3,12];case 11:return A.sent(),Le.getInstance(this.options.id).error("Error loading svg "+e.svg.substring(0,255)),[3,12];case 12:return e instanceof bn&&e.tree?[4,new Cs({id:this.options.id,scale:this.options.scale,backgroundColor:e.backgroundColor,x:0,y:0,scrollX:0,scrollY:0,width:e.width,height:e.height,cache:this.options.cache,windowWidth:e.width,windowHeight:e.height}).render(e.tree)]:[3,14];case 13:s=A.sent(),e.width&&e.height&&this.ctx.drawImage(s,0,0,e.width,e.height,e.bounds.left,e.bounds.top,e.bounds.width,e.bounds.height),A.label=14;case 14:if(e instanceof fn&&(i=Math.min(e.bounds.width,e.bounds.height),e.type===hn?e.checked&&(this.ctx.save(),this.path([new MB(e.bounds.left+.39363*i,e.bounds.top+.79*i),new MB(e.bounds.left+.16*i,e.bounds.top+.5549*i),new MB(e.bounds.left+.27347*i,e.bounds.top+.44071*i),new MB(e.bounds.left+.39694*i,e.bounds.top+.5649*i),new MB(e.bounds.left+.72983*i,e.bounds.top+.23*i),new MB(e.bounds.left+.84*i,e.bounds.top+.34085*i),new MB(e.bounds.left+.39363*i,e.bounds.top+.79*i)]),this.ctx.fillStyle=te(Hn),this.ctx.fill(),this.ctx.restore()):e.type===Fn&&e.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(e.bounds.left+i/2,e.bounds.top+i/2,i/4,0,2*Math.PI,!0),this.ctx.fillStyle=te(Hn),this.ctx.fill(),this.ctx.restore())),gs(e)&&e.value.length){switch(c=this.createFontStyle(r),Q=c[0],i=c[1],c=this.fontMetrics.getMetrics(Q,i).baseline,this.ctx.font=Q,this.ctx.fillStyle=te(r.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=hs(e.styles.textAlign),u=es(e),o=0,e.styles.textAlign){case qt.CENTER:o+=u.width/2;break;case qt.RIGHT:o+=u.width}i=u.add(o,0,0,-u.height/2+1),this.ctx.save(),this.path([new MB(u.left,u.top),new MB(u.left+u.width,u.top),new MB(u.left+u.width,u.top+u.height),new MB(u.left,u.top+u.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Gr(e.value,i),r.letterSpacing,c),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!Kr(e.styles.display,2048))return[3,20];if(null===e.styles.listStyleImage)return[3,19];if((c=e.styles.listStyleImage).type!==ve.URL)return[3,18];a=void 0,c=c.url,A.label=15;case 15:return A.trys.push([15,17,,18]),[4,this.options.cache.match(c)];case 16:return a=A.sent(),this.ctx.drawImage(a,e.bounds.left-(a.width+10),e.bounds.top),[3,18];case 17:return A.sent(),Le.getInstance(this.options.id).error("Error loading list-style-image "+c),[3,18];case 18:return[3,20];case 19:w.listValue&&e.styles.listStyleType!==Pt.NONE&&(Q=this.createFontStyle(r)[0],this.ctx.font=Q,this.ctx.fillStyle=te(r.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",u=new g(e.bounds.left,e.bounds.top+ie(e.styles.paddingTop,e.bounds.width),e.bounds.width,bt(r.lineHeight,r.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Gr(w.listValue,u),r.letterSpacing,bt(r.lineHeight,r.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),A.label=20;case 20:return[2]}})})},Cs.prototype.renderStackContent=function(C){return c(this,void 0,void 0,function(){var e,t,r,n,B,s,o,i,a,c,Q,u,w,l,U;return H(this,function(A){switch(A.label){case 0:return[4,this.renderNodeBackgroundAndBorders(C.element)];case 1:A.sent(),e=0,t=C.negativeZIndex,A.label=2;case 2:return e<t.length?(U=t[e],[4,this.renderStack(U)]):[3,5];case 3:A.sent(),A.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(C.element)];case 6:A.sent(),r=0,n=C.nonInlineLevel,A.label=7;case 7:return r<n.length?(U=n[r],[4,this.renderNode(U)]):[3,10];case 8:A.sent(),A.label=9;case 9:return r++,[3,7];case 10:B=0,s=C.nonPositionedFloats,A.label=11;case 11:return B<s.length?(U=s[B],[4,this.renderStack(U)]):[3,14];case 12:A.sent(),A.label=13;case 13:return B++,[3,11];case 14:o=0,i=C.nonPositionedInlineLevel,A.label=15;case 15:return o<i.length?(U=i[o],[4,this.renderStack(U)]):[3,18];case 16:A.sent(),A.label=17;case 17:return o++,[3,15];case 18:a=0,c=C.inlineLevel,A.label=19;case 19:return a<c.length?(U=c[a],[4,this.renderNode(U)]):[3,22];case 20:A.sent(),A.label=21;case 21:return a++,[3,19];case 22:Q=0,u=C.zeroOrAutoZIndexOrTransformedOrOpacity,A.label=23;case 23:return Q<u.length?(U=u[Q],[4,this.renderStack(U)]):[3,26];case 24:A.sent(),A.label=25;case 25:return Q++,[3,23];case 26:w=0,l=C.positiveZIndex,A.label=27;case 27:return w<l.length?(U=l[w],[4,this.renderStack(U)]):[3,30];case 28:A.sent(),A.label=29;case 29:return w++,[3,27];case 30:return[2]}})})},Cs.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.ctx.restore(),this.formatPath(A.slice(0).reverse().map(function(A){return A.reverse()})),this.ctx.closePath()},Cs.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},Cs.prototype.formatPath=function(A){var r=this;A.forEach(function(A,e){var t=PB(A)?A.start:A;0===e?r.ctx.moveTo(t.x,t.y):r.ctx.lineTo(t.x,t.y),PB(A)&&r.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)})},Cs.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},Cs.prototype.resizeImage=function(A,e,t){if(A.width===e&&A.height===t)return A;var r=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return r.width=Math.max(1,e),r.height=Math.max(1,t),r.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),r},Cs.prototype.renderBackgroundImage=function(d){return c(this,void 0,void 0,function(){var h,e,F,t,r,n;return H(this,function(A){switch(A.label){case 0:h=d.styles.backgroundImage.length-1,e=function(e){var t,r,n,B,s,o,i,a,c,Q,u,w,l,U,C,g,E;return H(this,function(A){switch(A.label){case 0:if(e.type!==ve.URL)return[3,5];t=void 0,r=e.url,A.label=1;case 1:return A.trys.push([1,3,,4]),[4,F.options.cache.match(r)];case 2:return t=A.sent(),[3,4];case 3:return A.sent(),Le.getInstance(F.options.id).error("Error loading background-image "+r),[3,4];case 4:return t&&(n=ts(d,h,[t.width,t.height,t.width/t.height]),o=n[0],u=n[1],w=n[2],c=n[3],Q=n[4],s=F.ctx.createPattern(F.resizeImage(t,c,Q),"repeat"),F.renderRepeat(o,s,u,w)),[3,6];case 5:e.type===ve.LINEAR_GRADIENT?(E=ts(d,h,[null,null,null]),o=E[0],u=E[1],w=E[2],c=E[3],Q=E[4],C=He(e.angle,c,Q),U=C[0],n=C[1],i=C[2],g=C[3],a=C[4],(E=document.createElement("canvas")).width=c,E.height=Q,C=E.getContext("2d"),B=C.createLinearGradient(n,g,i,a),de(e.stops,U).forEach(function(A){return B.addColorStop(A.stop,te(A.color))}),C.fillStyle=B,C.fillRect(0,0,c,Q),0<c&&0<Q&&(s=F.ctx.createPattern(E,"repeat"),F.renderRepeat(o,s,u,w))):e.type===ve.RADIAL_GRADIENT&&(g=ts(d,h,[null,null,null]),o=g[0],i=g[1],a=g[2],c=g[3],Q=g[4],U=0===e.position.length?[se]:e.position,u=ie(U[0],c),w=ie(U[U.length-1],Q),C=function(A,e,t,r,n){var B,s,o,i,a=0,c=0;switch(A.size){case At.CLOSEST_SIDE:A.shape===$e.CIRCLE?a=c=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):A.shape===$e.ELLIPSE&&(a=Math.min(Math.abs(e),Math.abs(e-r)),c=Math.min(Math.abs(t),Math.abs(t-n)));break;case At.CLOSEST_CORNER:A.shape===$e.CIRCLE?a=c=Math.min(fe(e,t),fe(e,t-n),fe(e-r,t),fe(e-r,t-n)):A.shape===$e.ELLIPSE&&(B=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),o=(s=pe(r,n,e,t,!0))[0],i=s[1],c=B*(a=fe(o-e,(i-t)/B)));break;case At.FARTHEST_SIDE:A.shape===$e.CIRCLE?a=c=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):A.shape===$e.ELLIPSE&&(a=Math.max(Math.abs(e),Math.abs(e-r)),c=Math.max(Math.abs(t),Math.abs(t-n)));break;case At.FARTHEST_CORNER:A.shape===$e.CIRCLE?a=c=Math.max(fe(e,t),fe(e,t-n),fe(e-r,t),fe(e-r,t-n)):A.shape===$e.ELLIPSE&&(B=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r)),o=(s=pe(r,n,e,t,!1))[0],i=s[1],c=B*(a=fe(o-e,(i-t)/B)))}return Array.isArray(A.size)&&(a=ie(A.size[0],r),c=2===A.size.length?ie(A.size[1],n):a),[a,c]}(e,u,w,c,Q),E=C[0],g=C[1],0<E&&(l=F.ctx.createRadialGradient(i+u,a+w,0,i+u,a+w,E),de(e.stops,2*E).forEach(function(A){return l.addColorStop(A.stop,te(A.color))}),F.path(o),F.ctx.fillStyle=l,E!==g?(U=d.bounds.left+.5*d.bounds.width,C=d.bounds.top+.5*d.bounds.height,E=1/(g=g/E),F.ctx.save(),F.ctx.translate(U,C),F.ctx.transform(1,0,0,g,0,0),F.ctx.translate(-U,-C),F.ctx.fillRect(i,E*(a-C)+C,c,Q*E),F.ctx.restore()):F.ctx.fill())),A.label=6;case 6:return h--,[2]}})},F=this,t=0,r=d.styles.backgroundImage.slice(0).reverse(),A.label=1;case 1:return t<r.length?(n=r[t],[5,e(n)]):[3,4];case 2:A.sent(),A.label=3;case 3:return t++,[3,1];case 4:return[2]}})})},Cs.prototype.renderSolidBorder=function(e,t,r){return c(this,void 0,void 0,function(){return H(this,function(A){return this.path($B(r,t)),this.ctx.fillStyle=te(e),this.ctx.fill(),[2]})})},Cs.prototype.renderDoubleBorder=function(t,r,n,B){return c(this,void 0,void 0,function(){var e;return H(this,function(A){switch(A.label){case 0:return r<3?[4,this.renderSolidBorder(t,n,B)]:[3,2];case 1:return A.sent(),[2];case 2:return e=function(A,e){switch(e){case 0:return is(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return is(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return is(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return is(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}}(B,n),this.path(e),this.ctx.fillStyle=te(t),this.ctx.fill(),e=function(A,e){switch(e){case 0:return is(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return is(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return is(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return is(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}}(B,n),this.path(e),this.ctx.fill(),[2]}})})},Cs.prototype.renderNodeBackgroundAndBorders=function(Q){return c(this,void 0,void 0,function(){var e,t,r,n,o,B,s,i,a,c=this;return H(this,function(A){switch(A.label){case 0:return(this.applyEffects(Q.effects,2),e=Q.container.styles,t=!ee(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],n=Es(cs(e.backgroundClip,0),Q.curves),t||e.boxShadow.length)?(this.ctx.save(),this.path(n),this.ctx.clip(),ee(e.backgroundColor)||(this.ctx.fillStyle=te(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(Q.container)]):[3,2];case 1:A.sent(),this.ctx.restore(),o=XB(Q.curves),e.boxShadow.slice(0).reverse().forEach(function(A){c.ctx.save();var t,r,n,B,e=A.inset?0:1e4,s=(t=A.offsetX.number-e+(A.inset?1:-1)*A.spread.number,r=A.offsetY.number+(A.inset?1:-1)*A.spread.number,n=A.spread.number*(A.inset?-2:2),B=A.spread.number*(A.inset?-2:2),o.map(function(A,e){switch(e){case 0:return A.add(t,r);case 1:return A.add(t+n,r);case 2:return A.add(t+n,r+B);case 3:return A.add(t,r+B)}return A}));A.inset?(c.path(o),c.ctx.clip(),c.mask(s)):(c.mask(o),c.ctx.clip(),c.path(s)),c.ctx.shadowOffsetX=e,c.ctx.shadowOffsetY=0,c.ctx.shadowColor=te(A.color),c.ctx.shadowBlur=A.blur.number,c.ctx.fillStyle=A.inset?te(A.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()}),A.label=2;case 2:s=B=0,i=r,A.label=3;case 3:return s<i.length?(a=i[s]).style!==ct.NONE&&!ee(a.color)&&0<a.width?a.style!==ct.DASHED?[3,5]:[4,this.renderDashedDottedBorder(a.color,a.width,B,Q.curves,ct.DASHED)]:[3,11]:[3,13];case 4:return A.sent(),[3,11];case 5:return a.style!==ct.DOTTED?[3,7]:[4,this.renderDashedDottedBorder(a.color,a.width,B,Q.curves,ct.DOTTED)];case 6:return A.sent(),[3,11];case 7:return a.style!==ct.DOUBLE?[3,9]:[4,this.renderDoubleBorder(a.color,a.width,B,Q.curves)];case 8:return A.sent(),[3,11];case 9:return[4,this.renderSolidBorder(a.color,B,Q.curves)];case 10:A.sent(),A.label=11;case 11:B++,A.label=12;case 12:return s++,[3,3];case 13:return[2]}})})},Cs.prototype.renderDashedDottedBorder=function(u,w,l,U,C){return c(this,void 0,void 0,function(){var e,t,r,n,B,s,o,i,a,c,Q;return H(this,function(A){return this.ctx.save(),a=function(A,e){switch(e){case 0:return os(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return os(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return os(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return os(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}}(U,l),e=$B(U,l),C===ct.DASHED&&(this.path(e),this.ctx.clip()),s=PB(e[0])?(t=e[0].start.x,e[0].start.y):(t=e[0].x,e[0].y),o=PB(e[1])?(r=e[1].end.x,e[1].end.y):(r=e[1].x,e[1].y),n=0===l||2===l?Math.abs(t-r):Math.abs(s-o),this.ctx.beginPath(),C===ct.DOTTED?this.formatPath(a):this.formatPath(e.slice(0,2)),B=w<3?3*w:2*w,s=w<3?2*w:w,C===ct.DOTTED&&(s=B=w),o=!0,n<=2*B?o=!1:n<=2*B+s?(B*=i=n/(2*B+s),s*=i):(a=Math.floor((n+s)/(B+s)),i=(n-a*B)/(a-1),s=(a=(n-(a+1)*B)/a)<=0||Math.abs(s-i)<Math.abs(s-a)?i:a),o&&(C===ct.DOTTED?this.ctx.setLineDash([0,B+s]):this.ctx.setLineDash([B,s])),C===ct.DOTTED?(this.ctx.lineCap="round",this.ctx.lineWidth=w):this.ctx.lineWidth=2*w+1.1,this.ctx.strokeStyle=te(u),this.ctx.stroke(),this.ctx.setLineDash([]),C===ct.DASHED&&(PB(e[0])&&(c=e[3],Q=e[0],this.ctx.beginPath(),this.formatPath([new MB(c.end.x,c.end.y),new MB(Q.start.x,Q.start.y)]),this.ctx.stroke()),PB(e[1])&&(c=e[1],Q=e[2],this.ctx.beginPath(),this.formatPath([new MB(c.end.x,c.end.y),new MB(Q.start.x,Q.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},Cs.prototype.render=function(n){return c(this,void 0,void 0,function(){return H(this,function(A){switch(A.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=te(this.options.backgroundColor),this.ctx.fillRect(this.options.x-this.options.scrollX,this.options.y-this.options.scrollY,this.options.width,this.options.height)),t=new ZB(e=n,[]),r=new qB(t),Bs(t,r,r,e=[]),ss(t.container,e),[4,this.renderStack(r)];case 1:return A.sent(),this.applyEffects([],2),[2,this.canvas]}var e,t,r})})},Cs);function Cs(A){this._activeEffects=[],this.canvas=A.canvas||document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),(this.options=A).canvas||(this.canvas.width=Math.floor(A.width*A.scale),this.canvas.height=Math.floor(A.height*A.scale),this.canvas.style.width=A.width+"px",this.canvas.style.height=A.height+"px"),this.fontMetrics=new ws(document),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-A.x+A.scrollX,-A.y+A.scrollY),this.ctx.textBaseline="bottom",this._activeEffects=[],Le.getInstance(A.id).debug("Canvas renderer initialized ("+A.width+"x"+A.height+" at "+A.x+","+A.y+") with scale "+A.scale)}var gs=function(A){return A instanceof mn||(A instanceof Kn||A instanceof fn&&A.type!==Fn&&A.type!==hn)},Es=function(A,e){switch(A){case Ce.BORDER_BOX:return XB(e);case Ce.CONTENT_BOX:return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox];default:Ce.PADDING_BOX;return GB(e)}},hs=function(A){switch(A){case qt.CENTER:return"center";case qt.RIGHT:return"right";default:qt.LEFT;return"left"}},Fs=(ds.prototype.render=function(t){return c(this,void 0,void 0,function(){var e;return H(this,function(A){switch(A.label){case 0:return e=Te(Math.max(this.options.windowWidth,this.options.width)*this.options.scale,Math.max(this.options.windowHeight,this.options.height)*this.options.scale,this.options.scrollX*this.options.scale,this.options.scrollY*this.options.scale,t),[4,fs(e)];case 1:return e=A.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=te(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(e,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},ds);function ds(A){this.canvas=A.canvas||document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=A,this.canvas.width=Math.floor(A.width*A.scale),this.canvas.height=Math.floor(A.height*A.scale),this.canvas.style.width=A.width+"px",this.canvas.style.height=A.height+"px",this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-A.x+A.scrollX,-A.y+A.scrollY),Le.getInstance(A.id).debug("EXPERIMENTAL ForeignObject renderer initialized ("+A.width+"x"+A.height+" at "+A.x+","+A.y+") with scale "+A.scale)}function Hs(A){return Qe(_A.create(A).parseComponentValue())}var fs=function(r){return new Promise(function(A,e){var t=new Image;t.onload=function(){A(t)},t.onerror=e,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})};"undefined"!=typeof window&&be.setContext(window);var ps=1,Ns=function(l,U){return c(void 0,void 0,void 0,function(){var e,t,r,n,B,s,o,i,a,c,Q,u,w;return H(this,function(A){switch(A.label){case 0:if(!l||"object"!=typeof l)return[2,Promise.reject("Invalid element provided as first argument")];if(!(e=l.ownerDocument))throw new Error("Element is not attached to a Document");if(!(t=e.defaultView))throw new Error("Document is not attached to a Window");return r="#"+ps++,i=qn(l)||"HTML"===l.tagName?function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");A=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),t=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new g(0,0,A,t)}(e):E(l),B=i.width,s=i.height,n=i.left,o=i.top,i=C(C({},{allowTaint:!1,imageTimeout:15e3,proxy:void 0,useCORS:!1}),U),o={backgroundColor:"#ffffff",cache:U.cache||be.create(r,i),logging:!0,removeContainer:!0,foreignObjectRendering:!1,scale:t.devicePixelRatio||1,windowWidth:t.innerWidth,windowHeight:t.innerHeight,scrollX:t.pageXOffset,scrollY:t.pageYOffset,x:n,y:o,width:Math.ceil(B),height:Math.ceil(s),id:r},B=C(C(C({},o),i),U),s=new g(B.scrollX,B.scrollY,B.windowWidth,B.windowHeight),Le.create({id:r,enabled:B.logging}),Le.getInstance(r).debug("Starting document clone"),o=new EB(l,{id:r,onclone:B.onclone,ignoreElements:B.ignoreElements,inlineImages:B.foreignObjectRendering,copyStyles:B.foreignObjectRendering}),(i=o.clonedReferenceElement)?[4,o.toIFrame(e,s)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return(a=A.sent(),Q=e.documentElement?Hs(getComputedStyle(e.documentElement).backgroundColor):he.TRANSPARENT,c=e.body?Hs(getComputedStyle(e.body).backgroundColor):he.TRANSPARENT,w=U.backgroundColor,w="string"==typeof w?Hs(w):null===w?he.TRANSPARENT:4294967295,c=l===e.documentElement?ee(Q)?ee(c)?w:c:Q:w,Q={id:r,cache:B.cache,canvas:B.canvas,backgroundColor:c,scale:B.scale,x:B.x,y:B.y,scrollX:B.scrollX,scrollY:B.scrollY,width:B.width,height:B.height,windowWidth:B.windowWidth,windowHeight:B.windowHeight},B.foreignObjectRendering)?(Le.getInstance(r).debug("Document cloned, using foreign object rendering"),[4,new Fs(Q).render(i)]):[3,3];case 2:return u=A.sent(),[3,5];case 3:return Le.getInstance(r).debug("Document cloned, using computed rendering"),be.attachInstance(B.cache),Le.getInstance(r).debug("Starting DOM parsing"),w=_n(i),be.detachInstance(),c===w.styles.backgroundColor&&(w.styles.backgroundColor=he.TRANSPARENT),Le.getInstance(r).debug("Starting renderer"),[4,new Us(Q).render(w)];case 4:u=A.sent(),A.label=5;case 5:return!0===B.removeContainer&&(EB.destroy(a)||Le.getInstance(r).error("Cannot detach cloned iframe as it is not in the DOM anymore")),Le.getInstance(r).debug("Finished rendering"),Le.destroy(r),be.destroy(r),[2,u]}})})};return function(A,e){return Ns(A,e=void 0===e?{}:e)}});
