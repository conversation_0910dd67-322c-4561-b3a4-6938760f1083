import { MbscDatetimeOptions } from '../presets/datetimebase';
import { DateTime } from '../presets/datetime';
export { DateTime, MbscDatetimeOptions };
export { IMobiscroll } from '../core/core';
export { mobiscroll as default } from '../core/core';
declare global {
    interface MobiscrollBundle {
        [index: number]: JQuery;
               datetime(options?: MbscDatetimeOptions): JQuery;
               date(options?: MbscDatetimeOptions): JQuery;
               time(options?: MbscDatetimeOptions): JQuery;
    }
    interface JQuery {
        mobiscroll(): MobiscrollBundle;
        mobiscroll(option: string): any;
    }
}
