/* eslint-disable */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],t):(e=e||self).mobiscroll=t(e.jQuery)}(this,function(e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;var w=w||{},t={},ie={},n=e.extend,i={};function o(e,s,a){var n=e;return"object"==typeof s?e.each(function(){new s.component(this,s)}):("string"==typeof s&&e.each(function(){var e,t=w.instances[this.id];if(t&&t[s]&&void 0!==(e=t[s].apply(this,Array.prototype.slice.call(a,1))))return n=e,!1}),n)}function s(t,s,a){i[t]=function(e){return o(this,n(e,{component:s,preset:!1===a?void 0:t}),arguments)}}(w.$=e).mobiscroll=w,e.fn.mobiscroll=function(e){return n(this,i),o(this,e,arguments)};var a,r,c,l,d=[],u="undefined"!=typeof window,_=u&&window.matchMedia&&window.matchMedia("(prefers-color-scheme:dark)").matches,m=u?navigator.userAgent:"",f=u?navigator.platform:"",h=u?navigator.maxTouchPoints:0,p=/Safari/.test(m),b=m.match(/Android|iPhone|iPad|iPod|Windows Phone|Windows|MSIE/i),ue=u&&window.requestAnimationFrame||function(e){return setTimeout(e,20)},me=u&&window.cancelAnimationFrame||function(e){clearTimeout(e)};function ee(){}function fe(e){return 0<=e-parseFloat(e)}function he(e){return"string"==typeof e}function pe(e,t,s){return Math.max(t,Math.min(e,s))}function oe(e,t){for(e+="",t=t||2;e.length<t;)e="0"+e;return e}/Android/i.test(b)?(a="android",(r=m.match(/Android\s+([\d.]+)/i))&&(d=r[0].replace("Android ","").split("."))):/iPhone|iPad|iPod/i.test(b)||/iPhone|iPad|iPod/i.test(f)||"MacIntel"===f&&1<h?(a="ios",(r=m.match(/OS\s+([\d_]+)/i))&&(d=r[0].replace(/_/g,".").replace("OS ","").split("."))):/Windows Phone/i.test(b)?a="wp":/Windows|MSIE/i.test(b)&&(a="windows"),c=d[0],l=d[1];var v,g=0;function te(){g++,setTimeout(function(){g--},500)}function be(e,t,s){var a=e.originalEvent||e,n=(s?"page":"client")+t;return a.targetTouches&&a.targetTouches[0]?a.targetTouches[0][n]:a.changedTouches&&a.changedTouches[0]?a.changedTouches[0][n]:e[n]}function x(t,e,s,a,n,i){var o,r,c,l,d,u=(0,w.$)(e);n=n||9,t.settings.tap&&u.on("touchstart.mbsc",function(e){c||(a&&e.preventDefault(),c=this,o=be(e,"X"),r=be(e,"Y"),l=!1,d=new Date)}).on("touchcancel.mbsc",function(){c=!1}).on("touchmove.mbsc",function(e){c&&!l&&(Math.abs(be(e,"X")-o)>n||Math.abs(be(e,"Y")-r)>n)&&(l=!0)}).on("touchend.mbsc",function(e){c&&(i&&new Date-d<100||!l?function(e,t){if(!t.mbscClick){var s=(e.originalEvent||e).changedTouches[0],a=document.createEvent("MouseEvents");a.initMouseEvent("click",!0,!0,window,1,s.screenX,s.screenY,s.clientX,s.clientY,!1,!1,!1,!1,0,null),a.isMbscTap=!0,a.isIonicTap=!0,v=!0,t.mbscChange=!0,t.mbscClick=!0,t.dispatchEvent(a),v=!1,te(),setTimeout(function(){delete t.mbscClick})}}(e,e.target):te(),c=!1)}),u.on("click.mbsc",function(e){a&&e.preventDefault(),s.call(this,e,t)})}function y(e){if(g&&!v&&!e.isMbscTap&&("TEXTAREA"!=e.target.nodeName||"mousedown"!=e.type))return e.stopPropagation(),e.preventDefault(),!1}u&&(["mouseover","mousedown","mouseup","click"].forEach(function(e){document.addEventListener(e,y,!0)}),"android"==a&&c<5&&document.addEventListener("change",function(e){g&&"checkbox"==e.target.type&&!e.target.mbscChange&&(e.stopPropagation(),e.preventDefault()),delete e.target.mbscChange},!0)),w.uid="7d49ff51";var C,ve=w.$,T=+new Date,k={},M={},D={},S={xsmall:0,small:576,medium:768,large:992,xlarge:1200},re=ve.extend;re(t,{getCoord:be,preventClick:te,vibrate:function(e){"vibrate"in navigator&&navigator.vibrate(e||50)}}),C=re(w,{$:ve,version:"4.10.3",autoTheme:"mobiscroll",themes:{form:{},page:{},frame:{},scroller:{},listview:{},navigation:{},progress:{},card:{}},platform:{name:a,majorVersion:c,minorVersion:l},i18n:{},instances:k,classes:M,util:t,settings:{},setDefaults:function(e){re(this.settings,e)},customTheme:function(e,t){var s,a=w.themes,n=["frame","scroller","listview","navigation","form","page","progress","card"];for(s=0;s<n.length;s++)a[n[s]][e]=re({},a[n[s]][t],{baseTheme:t})}});function ge(r,c){var l,d,u,m,f,h,p,b,v,g=this;function y(e){var s,t,a=D;return f.responsive&&(s=e||((t=l)[0].innerWidth||t.innerWidth()),ve.each(f.responsive,function(e,t){s>=(t.breakpoint||S[e])&&(a=t)})),a}g.settings={},g.element=r,g._init=ee,g._destroy=ee,g._processSettings=ee,g._checkResp=function(e){if(g&&g._responsive){var t=y(e);if(m!==t)return m=t,g.init({}),!0}},g._getRespCont=function(){return ve(f.context)[0]},g.init=function(e,t){var s,a,n,i,o;for(s in e&&g.getVal&&(a=g.getVal()),g.settings)delete g.settings[s];f=g.settings,re(c,e),g._hasDef&&(v=C.settings),re(f,g._defaults,v,c),g._hasTheme&&(i=(n=f).theme,o=n.themeVariant,"auto"!=i&&i||(i=C.autoTheme),"default"==i&&(i="mobiscroll"),("dark"===o||_&&"auto"===o)&&C.themes.form[i+"-dark"]&&(i+="-dark"),p=i,c.theme=p,h=C.themes[g._class]?C.themes[g._class][p]:{}),g._hasLang&&(d=C.i18n[f.lang]),re(f,h,d,v,c),l=g._getRespCont(),g._responsive&&(m=m||y(),re(f,m)),g._processSettings(m||{}),g._presets&&(u=g._presets[f.preset])&&(u=u.call(r,g,c),re(f,u,c,m)),g._init(e),e&&g.setVal&&g.setVal(void 0===t?a:t,!0),b("onInit")},g.destroy=function(){g&&(g._destroy(),b("onDestroy"),delete k[r.id],g=null)},g.tap=function(e,t,s,a,n){x(g,e,t,s,a,n)},g.trigger=function(e,t){var s,a,n,i=[v,h,u,c];for(a=0;a<4;a++)(n=i[a])&&n[e]&&(s=n[e].call(r,t||{},g));return s},g.option=function(e,t,s){var a={},n=["data","invalid","valid","readonly"];/calendar|eventcalendar|range/.test(f.preset)&&n.push("marked","labels","colors"),"object"==typeof e?a=e:a[e]=t,n.forEach(function(e){c[e]=f[e]}),g.init(a,s)},g.getInst=function(){return g},c=c||{},b=g.trigger,g.__ready||(ve(r).addClass("mbsc-comp"),r.id?k[r.id]&&k[r.id].destroy():r.id="mobiscroll"+ ++T,(k[r.id]=g).__ready=!0)}function ce(e,t,s,a,n,i,o){var r=new Date(e,t,s,a||0,n||0,i||0,o||0);return 23==r.getHours()&&0===(a||0)&&r.setHours(r.getHours()+2),r}function le(s,e,t){if(!e)return null;function n(e){for(var t=0;o+1<s.length&&s.charAt(o+1)==e;)t++,o++;return t}function a(e,t,s){var a=""+t;if(n(e))for(;a.length<s;)a="0"+a;return a}function i(e,t,s,a){return n(e)?a[t]:s[t]}var o,r,c=re({},Me,t),l="",d=!1;for(o=0;o<s.length;o++)if(d)"'"!=s.charAt(o)||n("'")?l+=s.charAt(o):d=!1;else switch(s.charAt(o)){case"d":l+=a("d",c.getDay(e),2);break;case"D":l+=i("D",e.getDay(),c.dayNamesShort,c.dayNames);break;case"o":l+=a("o",(e.getTime()-new Date(e.getFullYear(),0,0).getTime())/864e5,3);break;case"m":l+=a("m",c.getMonth(e)+1,2);break;case"M":l+=i("M",c.getMonth(e),c.monthNamesShort,c.monthNames);break;case"y":r=c.getYear(e),l+=n("y")?r:(r%100<10?"0":"")+r%100;break;case"h":var u=e.getHours();l+=a("h",12<u?u-12:0===u?12:u,2);break;case"H":l+=a("H",e.getHours(),2);break;case"i":l+=a("i",e.getMinutes(),2);break;case"s":l+=a("s",e.getSeconds(),2);break;case"a":l+=11<e.getHours()?c.pmText:c.amText;break;case"A":l+=11<e.getHours()?c.pmText.toUpperCase():c.amText.toUpperCase();break;case"'":n("'")?l+="'":d=!0;break;default:l+=s.charAt(o)}return l}function de(s,i,e){var t=re({},Me,e),a=we(t.defaultValue||new Date);if(!s||!i)return a;if(i.getTime)return i;i="object"==typeof i?i.toString():i+"";function o(e){var t=l+1<s.length&&s.charAt(l+1)==e;return t&&l++,t}function n(e){o(e);var t=new RegExp("^\\d{1,"+("@"==e?14:"!"==e?20:"y"==e?4:"o"==e?3:2)+"}"),s=i.substr(w).match(t);return s?(w+=s[0].length,parseInt(s[0],10)):0}function r(e,t,s){var a,n=o(e)?s:t;for(a=0;a<n.length;a++)if(i.substr(w,n[a].length).toLowerCase()==n[a].toLowerCase())return w+=n[a].length,a+1;return 0}function c(){w++}var l,d=t.shortYearCutoff,u=t.getYear(a),m=t.getMonth(a)+1,f=t.getDay(a),h=-1,p=a.getHours(),b=a.getMinutes(),v=0,g=-1,y=!1,w=0;for(l=0;l<s.length;l++)if(y)"'"!=s.charAt(l)||o("'")?c():y=!1;else switch(s.charAt(l)){case"d":f=n("d");break;case"D":r("D",t.dayNamesShort,t.dayNames);break;case"o":h=n("o");break;case"m":m=n("m");break;case"M":m=r("M",t.monthNamesShort,t.monthNames);break;case"y":u=n("y");break;case"H":p=n("H");break;case"h":p=n("h");break;case"i":b=n("i");break;case"s":v=n("s");break;case"a":g=r("a",[t.amText,t.pmText],[t.amText,t.pmText])-1;break;case"A":g=r("A",[t.amText,t.pmText],[t.amText,t.pmText])-1;break;case"'":o("'")?c():y=!0;break;default:c()}if(u<100&&(u+=(new Date).getFullYear()-(new Date).getFullYear()%100+(u<=("string"!=typeof d?d:(new Date).getFullYear()%100+parseInt(d,10))?0:-100)),-1<h){m=1,f=h;do{var _=32-new Date(u,m-1,32,12).getDate();_<f&&(m++,f-=_)}while(_<f)}p=-1==g?p:g&&p<12?p+12:g||12!=p?p:0;var x=t.getDate(u,m-1,f,p,b,v);return t.getYear(x)!=u||t.getMonth(x)+1!=m||t.getDay(x)!=f?a:x}function V(e,t,s){var a,n,i={y:1,m:2,d:3,h:4,i:5,s:6,u:7,tz:8};if(s)for(a in i)(n=e[i[a]-t])&&(s[a]="tz"==a?n:1)}function ye(e,t,s){var a,n,i,o,r=window.moment||t.moment,c=t.returnFormat;if(e){if("moment"==c&&r)return r(e);if("locale"==c)return le(s,e,t);if("iso8601"==c)return a=e,n=t.isoParts,o=i="",a&&(n.h&&(o+=oe(a.getHours())+":"+oe(a.getMinutes()),n.s&&(o+=":"+oe(a.getSeconds())),n.u&&(o+="."+oe(a.getMilliseconds(),3)),n.tz&&(o+=n.tz)),n.y?(i+=a.getFullYear(),n.m&&(i+="-"+oe(a.getMonth()+1),n.d&&(i+="-"+oe(a.getDate())),n.h&&(i+="T"+o))):n.h&&(i=o)),i}return e}function we(e,t,s,a){var n;return e?e.getTime?e:e.toDate?e.toDate():("string"==typeof e&&(e=e.trim()),(n=Ce.exec(e))?(V(n,2,a),new Date(1970,0,1,n[2]?+n[2]:0,n[3]?+n[3]:0,n[4]?+n[4]:0,n[5]?+n[5]:0)):(n=n||P.exec(e))?(V(n,0,a),new Date(n[1]?+n[1]:1970,n[2]?n[2]-1:0,n[3]?+n[3]:1,n[4]?+n[4]:0,n[5]?+n[5]:0,n[6]?+n[6]:0,n[7]?+n[7]:0)):de(t,e,s)):null}var se,A,_e,Y,W,xe,I,P=/^(\d{4}|[+-]\d{6})(?:-(\d{2})(?:-(\d{2}))?)?(?:T(\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?((Z)|([+-])(\d{2})(?::(\d{2}))?)?)?$/,Ce=/^((\d{2}):(\d{2})(?::(\d{2})(?:\.(\d{3}))?)?(?:(Z)|([+-])(\d{2})(?::(\d{2}))?)?)?$/,Te=/^\d{1,2}(\/\d{1,2})?$/,ke=/^w\d$/i,Me={shortYearCutoff:"+10",monthNames:["January","February","March","April","May","June","July","August","September","October","November","December"],monthNamesShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayNames:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayNamesShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],dayNamesMin:["S","M","T","W","T","F","S"],amText:"am",pmText:"pm",getYear:function(e){return e.getFullYear()},getMonth:function(e){return e.getMonth()},getDay:function(e){return e.getDate()},getDate:ce,getMaxDayOfMonth:function(e,t){return 32-new Date(e,t,32,12).getDate()},getWeekNumber:function(e){(e=new Date(e)).setHours(0,0,0),e.setDate(e.getDate()+4-(e.getDay()||7));var t=new Date(e.getFullYear(),0,1);return Math.ceil(((e-t)/864e5+1)/7)}};function L(e){var t;for(t in e)if(void 0!==A[e[t]])return!0;return!1}function De(e,t){if("touchstart"==e.type)ve(t).attr("data-touch","1");else if(ve(t).attr("data-touch"))return ve(t).removeAttr("data-touch"),!1;return!0}function Se(e,t){var s,a=getComputedStyle(e[0]);return ve.each(["t","webkitT","MozT","OT","msT"],function(e,t){if(void 0!==a[t+"ransform"])return s=a[t+"ransform"],!1}),s=s.split(")")[0].split(", "),t?s[13]||s[5]:s[12]||s[4]}t.datetime={formatDate:le,parseDate:de},u&&(I=window,A=document.createElement("modernizr").style,_e=function(){var e,t=["Webkit","Moz","O","ms"];for(e in t)if(L([t[e]+"Transform"]))return"-"+t[e].toLowerCase()+"-";return""}(),xe=_e.replace(/^-/,"").replace(/-$/,"").replace("moz","Moz"),se=void 0!==A.animation?"animationend":"webkitAnimationEnd",A.transition,W=(Y="ios"===a&&!p)&&I.webkit&&I.webkit.messageHandlers,A.touchAction);var ae,H="position:absolute;left:0;top:0;",E=H+"right:0;bottom:0;overflow:hidden;z-index:-1;",ne='<div style="'+E+'"><div style="'+H+'"></div></div><div style="'+E+'"><div style="'+H+'width:200%;height:200%;"></div></div>',Ve=0;function Ae(e){e.preventDefault()}function R(s,a,e){var k,d,u,M,x,D,S,V,A,C,t,Y,T,W,I,P,m,L,H,E,O,f,F,$,N,n,h,X,R,z,U,q,j,B=this,G=ve(s),i=[],o=new Date;function J(e){t&&t.removeClass("mbsc-active"),(t=ve(this)).hasClass("mbsc-disabled")||t.hasClass("mbsc-fr-btn-nhl")||t.addClass("mbsc-active"),"mousedown"===e.type?ve(document).on("mouseup",Z):"pointerdown"===e.type&&ve(document).on("pointerup",Z)}function Z(e){t&&(t.removeClass("mbsc-active"),t=null),"mouseup"===e.type?ve(document).off("mouseup",Z):"pointerup"===e.type&&ve(document).off("pointerup",Z)}function p(e){w.activeInstance==B&&(13!=e.keyCode||ve(e.target).is('textarea,button,input[type="button"],input[type="submit"]')&&!e.shiftKey?27==e.keyCode&&B.cancel():B.select())}function K(e){e||Pe||!B._activeElm||(o=new Date,B._activeElm.focus())}function r(e){var t=Ye,s=X.focusOnClose;B._markupRemove(),M.remove(),I&&(Y.mbscModals--,X.scrollLock&&Y.mbscLock--,Y.mbscLock||u.removeClass("mbsc-fr-lock"),f&&(Y.mbscIOSLock--,Y.mbscIOSLock||(u.removeClass("mbsc-fr-lock-ios"),k.css({top:"",left:""}),V.scrollLeft(Y.mbscScrollLeft),V.scrollTop(Y.mbscScrollTop))),Y.mbscModals||u.removeClass("mbsc-fr-lock-ctx"),Y.mbscModals&&!h||e||(t=t||G,setTimeout(function(){void 0===s||!0===s?(We=!0,t[0].focus()):s&&ve(s)[0].focus()},200))),h=void 0,P=!1,U("onHide")}function Q(){clearTimeout(n),n=setTimeout(function(){B.position(!0)&&(N.style.visibility="hidden",N.offsetHeight,N.style.visibility="")},200)}function b(e){w.activeInstance==B&&e.target.nodeType&&!$.contains(e.target)&&100<new Date-o&&(o=new Date,B._activeElm.focus())}function v(e,t){if(B._isVisible){if(I)M.appendTo(k);else if(G.is("div")&&!B._hasContent)G.empty().append(M);else if(G.hasClass("mbsc-control")){var s=G.closest(".mbsc-control-w");M.insertAfter(s),s.hasClass("mbsc-select")&&s.addClass("mbsc-select-inline")}else M.insertAfter(G);var a,n,i,o,r,c,l,d,u,m,f,h,p,b,v;if(P=!0,B._markupInserted(M),U("onMarkupInserted",{target:L}),I&&X.closeOnOverlayTap)x.on("touchstart mousedown",function(e){n||e.target!=$||(a=!(n=!0),i=be(e,"X"),o=be(e,"Y"))}).on("touchmove mousemove",function(e){n&&!a&&(9<Math.abs(be(e,"X")-i)||9<Math.abs(be(e,"Y")-o))&&(a=!0)}).on("touchcancel",function(){n=!1}).on("touchend click",function(e){n&&!a&&(B.cancel(),"touchend"==e.type&&te()),n=!1});if(M.on("mousedown",".mbsc-btn-e,.mbsc-fr-btn-e",Ae).on("touchstart mousedown",function(e){X.stopProp&&e.stopPropagation()}).on("keydown",".mbsc-fr-btn-e",function(e){32==e.keyCode&&(e.preventDefault(),e.stopPropagation(),this.click())}).on("keydown",function(e){if(32!=e.keyCode||ve(e.target).is(He)){if(9==e.keyCode&&I&&X.focusTrap){var t=M.find('input,select,textarea,button,[tabindex="0"]').filter(function(){return 0<this.offsetWidth||0<this.offsetHeight}),s=t.index(ve(":focus",M)),a=t.length-1,n=0;e.shiftKey&&(a=0,n=-1),s===a&&(t.eq(n)[0].focus(),e.preventDefault())}}else e.preventDefault()}).on("touchstart mousedown pointerdown",".mbsc-fr-btn-e",J).on("touchend",".mbsc-fr-btn-e",Z),L.addEventListener("touchstart",function(){z||(z=!0,k.find(".mbsc-no-touch").removeClass("mbsc-no-touch"))},!0),ve.each(C,function(e,t){B.tap(ve(".mbsc-fr-btn"+e,M),function(e){t=he(t)?B.buttons[t]:t,(he(t.handler)?B.handlers[t.handler]:t.handler).call(this,e,B)},!0)}),B._attachEvents(M),!1!==B.position())(I||B._checkSize)&&(r=L,c=Q,l=X.zone,v=0,window.ResizeObserver?(ae=ae||new ResizeObserver(function(e){var t=e,s=Array.isArray(t),a=0;for(t=s?t:t[Symbol.iterator]();;){var n;if(s){if(a>=t.length)break;n=t[a++]}else{if((a=t.next()).done)break;n=a.value}n.target.__mbscResize()}}),Ve++,r.__mbscResize=c,ae.observe(r)):((m=document.createElement("div")).innerHTML=ne,m.dir="ltr",p=m.childNodes[1],d=m.childNodes[0],u=d.childNodes[0],r.appendChild(m),d.addEventListener("scroll",w),p.addEventListener("scroll",w),l?l.runOutsideAngular(function(){ue(y)}):ue(y)),F={detach:function(){ae?(Ve--,ae.unobserve(r),Ve||(ae=null)):(r.removeChild(m),b=!0)}}),I&&(M.removeClass("mbsc-fr-pos"),T&&!e?M.addClass("mbsc-anim-in mbsc-anim-trans mbsc-anim-trans-"+T).on(se,function e(){M.off(se,e).removeClass("mbsc-anim-in mbsc-anim-trans mbsc-anim-trans-"+T).find(".mbsc-fr-popup").removeClass("mbsc-anim-"+T),K(t)}).find(".mbsc-fr-popup").addClass("mbsc-anim-"+T):K(t)),U("onShow",{target:L,valueText:B._tempValue})}function g(){u.style.width="100000px",u.style.height="100000px",d.scrollLeft=1e5,d.scrollTop=1e5,p.scrollLeft=1e5,p.scrollTop=1e5}function y(){var e=new Date;f=0,b||(200<e-v&&!d.scrollTop&&!d.scrollLeft&&(v=e,g()),f=f||ue(y))}function w(){h=h||ue(_)}function _(){h=0,g(),c()}}function c(e,t){B._isVisible||(e&&e(),!1!==B.show()&&(Ye=t))}function l(){B._fillValue(),U("onSet",{valueText:B._value})}function g(){U("onCancel",{valueText:B._value})}function y(){B.setVal(null,!0)}ge.call(this,s,a,!0),B.position=function(e){var t,s,a,n,i,o,r,c,l,d,u,m,f,h,p,b,v,g,y,w={},_=0,x=0,C=0,T=0;if(!P)return!1;if(b=q,p=j,f=Math.min(L.offsetHeight,W?1/0:window.innerHeight),(h=Math.min(L.offsetWidth,W?1/0:window.innerWidth))&&f&&(q!==h||j!==f||!e)){if(B._checkResp(h))return!1;if(q=h,j=f,B._isFullScreen||/top|bottom/.test(X.display)?S.width(h):I&&A.width(""),B._position(M),!B._isFullScreen&&/center|bubble/.test(X.display)&&(ve(".mbsc-w-p",M).each(function(){v=this.getBoundingClientRect().width,T+=v,C=C<v?v:C}),m=h-16<T||!0===X.tabs,A.css({width:B._isLiquid?Math.min(X.maxPopupWidth,h-16):Math.ceil(m?C:T),"white-space":m?"":"nowrap"})),!1!==U("onPosition",{target:L,popup:N,hasTabs:m,oldWidth:b,oldHeight:p,windowWidth:h,windowHeight:f})&&I)return O&&(_=V.scrollLeft(),x=V.scrollTop(),q&&D.css({width:"",height:""})),H=N.offsetWidth,E=N.offsetHeight,R=E<=f&&H<=h,"center"==X.display?(y=Math.max(0,_+(h-H)/2),g=Math.max(0,x+(f-E)/2)):"bubble"==X.display?(t=void 0===X.anchor?G:ve(X.anchor),r=ve(".mbsc-fr-arr-i",M)[0],i=(n=t.offset()).top+(W?x-k.offset().top:0),o=n.left+(W?_-k.offset().left:0),s=t[0].offsetWidth,a=t[0].offsetHeight,c=r.offsetWidth,l=r.offsetHeight,y=pe(o-(H-s)/2,_+3,_+h-H-3),x+f<(g=i+a+l/2)+E+8&&x<i-E-l/2?(S.removeClass("mbsc-fr-bubble-bottom").addClass("mbsc-fr-bubble-top"),g=i-E-l/2):S.removeClass("mbsc-fr-bubble-top").addClass("mbsc-fr-bubble-bottom"),ve(".mbsc-fr-arr",M).css({left:pe(o+s/2-(y+(H-c)/2),0,c)}),R=x<g&&_<y&&g+E<=x+f&&y+H<=_+h):(y=_,g="top"==X.display?x:Math.max(0,x+f-E)),O&&(d=Math.max(g+E,W?Y.scrollHeight:ve(document).height()),u=Math.max(y+H,W?Y.scrollWidth:ve(document).width()),D.css({width:u,height:d}),X.scroll&&"bubble"==X.display&&(x+f<g+E+8||x+f<i||i+a<x)&&V.scrollTop(Math.min(i,g+E-f+8,d-f))),w.top=Math.floor(g),w.left=Math.floor(y),S.css(w),!0}},B.attachShow=function(e,t){var s,a=ve(e).off(".mbsc"),n=a.prop("readonly");"inline"!==X.display&&((X.showOnFocus||X.showOnTap)&&a.is("input,select")&&(a.prop("readonly",!0).on("mousedown.mbsc",function(e){e.preventDefault()}).on("focus.mbsc",function(){B._isVisible&&this.blur()}),(s=ve('label[for="'+a.attr("id")+'"]')).length||(s=a.closest("label"))),a.is("select")||(X.showOnFocus&&a.on("focus.mbsc",function(){We?We=!1:c(t,a)}),X.showOnTap&&(a.on("keydown.mbsc",function(e){32!=e.keyCode&&13!=e.keyCode||(e.preventDefault(),e.stopPropagation(),c(t,a))}),B.tap(a,function(e){e.isMbscTap&&(z=!0),c(t,a)}),s&&s.length&&B.tap(s,function(e){e.preventDefault(),e.target!==a[0]&&c(t,a)}))),i.push({readOnly:n,el:a,lbl:s}))},B.select=function(){I?B.hide(!1,"set",!1,l):l()},B.cancel=function(){I?B.hide(!1,"cancel",!1,g):g()},B.clear=function(){B._clearValue(),U("onClear"),I&&B._isVisible&&!B.live?B.hide(!1,"clear",!1,y):y()},B.enable=function(){X.disabled=!1,ve.each(i,function(e,t){t.el.is("input,select")&&(t.el[0].disabled=!1)})},B.disable=function(){X.disabled=!0,ve.each(i,function(e,t){t.el.is("input,select")&&(t.el[0].disabled=!0)})},B.show=function(e,t){var s,a,n,i;if(!X.disabled&&!B._isVisible){if(B._readValue(),!1===U("onBeforeShow"))return!1;if(Ye=null,T=X.animate,C=X.buttons||[],O=W||"bubble"==X.display,f=Ie&&!O&&X.scrollLock,s=0<C.length,!1!==T&&("top"==X.display?T=T||"slidedown":"bottom"==X.display?T=T||"slideup":"center"!=X.display&&"bubble"!=X.display||(T=T||"pop")),I&&(j=q=0,f&&!u.hasClass("mbsc-fr-lock-ios")&&(Y.mbscScrollTop=i=Math.max(0,V.scrollTop()),Y.mbscScrollLeft=n=Math.max(0,V.scrollLeft()),k.css({top:-i+"px",left:-n+"px"})),u.addClass((X.scrollLock?"mbsc-fr-lock":"")+(f?" mbsc-fr-lock-ios":"")+(W?" mbsc-fr-lock-ctx":"")),ve(document.activeElement).is("input,textarea")&&document.activeElement.blur(),h=w.activeInstance,w.activeInstance=B,Y.mbscModals=(Y.mbscModals||0)+1,f&&(Y.mbscIOSLock=(Y.mbscIOSLock||0)+1),X.scrollLock&&(Y.mbscLock=(Y.mbscLock||0)+1)),a='<div lang="'+X.lang+'" class="mbsc-fr mbsc-'+X.theme+(X.baseTheme?" mbsc-"+X.baseTheme:"")+" mbsc-fr-"+X.display+" "+(X.cssClass||"")+" "+(X.compClass||"")+(B._isLiquid?" mbsc-fr-liq":"")+(I?" mbsc-fr-pos"+(X.showOverlay?"":" mbsc-fr-no-overlay"):"")+(m?" mbsc-fr-pointer":"")+(Le?" mbsc-fr-hb":"")+(z?"":" mbsc-no-touch")+(f?" mbsc-platform-ios":"")+(s?3<=C.length?" mbsc-fr-btn-block ":"":" mbsc-fr-nobtn")+'">'+(I?'<div class="mbsc-fr-persp">'+(X.showOverlay?'<div class="mbsc-fr-overlay"></div>':"")+'<div role="dialog" class="mbsc-fr-scroll">':"")+'<div class="mbsc-fr-popup'+(X.rtl?" mbsc-rtl":" mbsc-ltr")+(X.headerText?" mbsc-fr-has-hdr":"")+'">'+("bubble"===X.display?'<div class="mbsc-fr-arr-w"><div class="mbsc-fr-arr-i"><div class="mbsc-fr-arr"></div></div></div>':"")+(I?'<div class="mbsc-fr-focus" tabindex="-1"></div>':"")+'<div class="mbsc-fr-w">'+(X.headerText?'<div class="mbsc-fr-hdr">'+(he(X.headerText)?X.headerText:"")+"</div>":"")+'<div class="mbsc-fr-c">',a+=B._generateContent(),a+="</div>",s){var o,r,c,l=C.length;for(a+='<div class="mbsc-fr-btn-cont">',r=0;r<C.length;r++)c=X.btnReverse?l-r-1:r,"set"===(o=he(o=C[c])?B.buttons[o]:o).handler&&(o.parentClass="mbsc-fr-btn-s"),"cancel"===o.handler&&(o.parentClass="mbsc-fr-btn-c"),a+="<div"+(X.btnWidth?' style="width:'+100/C.length+'%"':"")+' class="mbsc-fr-btn-w '+(o.parentClass||"")+'"><div tabindex="0" role="button" class="mbsc-fr-btn'+c+" mbsc-fr-btn-e "+(void 0===o.cssClass?X.btnClass:o.cssClass)+(o.icon?" mbsc-ic mbsc-ic-"+o.icon:"")+'">'+(o.text||"")+"</div></div>";a+="</div>"}M=ve(a+="</div></div></div></div>"+(I?"</div></div>":"")),D=ve(".mbsc-fr-persp",M),x=ve(".mbsc-fr-scroll",M),A=ve(".mbsc-fr-w",M),S=ve(".mbsc-fr-popup",M),d=ve(".mbsc-fr-hdr",M),L=M[0],$=x[0],N=S[0],B._activeElm=ve(".mbsc-fr-focus",M)[0],B._markup=M,B._isVisible=!0,B.markup=L,B._markupReady(M),U("onMarkupReady",{target:L}),I&&(ve(window).on("keydown",p),X.scrollLock&&M.on("touchmove mousewheel wheel",function(e){R&&e.preventDefault()}),X.focusTrap&&V.on("focusin",b)),I?setTimeout(function(){v(e,t)},f?100:0):v(e,t)}},B.hide=function(t,e,s,a){if(!B._isVisible||!s&&!B._isValid&&"set"==e||!s&&!1===U("onBeforeClose",{valueText:B._tempValue,button:e}))return!1;B._isVisible=!1,F&&(F.detach(),F=null),I&&(ve(document.activeElement).is("input,textarea")&&N.contains(document.activeElement)&&document.activeElement.blur(),w.activeInstance==B&&(w.activeInstance=h),ve(window).off("keydown",p),V.off("focusin",b)),M&&(I&&T&&!t?M.addClass("mbsc-anim-out mbsc-anim-trans mbsc-anim-trans-"+T).on(se,function e(){M.off(se,e),r(t)}).find(".mbsc-fr-popup").addClass("mbsc-anim-"+T):r(t),B._detachEvents(M)),a&&a(),G.trigger("blur"),U("onClose",{valueText:B._value})},B.isVisible=function(){return B._isVisible},B.setVal=ee,B.getVal=ee,B._generateContent=ee,B._attachEvents=ee,B._detachEvents=ee,B._readValue=ee,B._clearValue=ee,B._fillValue=ee,B._markupReady=ee,B._markupInserted=ee,B._markupRemove=ee,B._position=ee,B.__processSettings=ee,B.__init=ee,B.__destroy=ee,B._destroy=function(){B.hide(!0,!1,!0),G.off(".mbsc"),ve.each(i,function(e,t){t.el.off(".mbsc").prop("readonly",t.readOnly),t.lbl&&t.lbl.off(".mbsc")}),B.__destroy()},B._updateHeader=function(){var e=X.headerText,t=e?"function"==typeof e?e.call(s,B._tempValue):e.replace(/\{value\}/i,B._tempValue):"";d.html(t||"&nbsp;")},B._getRespCont=function(){return W="body"!=X.context,V=ve(W?X.context:window),"inline"==X.display?G.is("div")?G:G.parent():V},B._processSettings=function(e){var t,s;for(B.__processSettings(e),(m=!X.touchUi)&&(X.display=e.display||a.display||"bubble",X.buttons=e.buttons||a.buttons||[],X.showOverlay=e.showOverlay||a.showOverlay||!1),X.buttons=X.buttons||("inline"!==X.display?["cancel","set"]:[]),X.headerText=void 0===X.headerText?"inline"!==X.display&&"{value}":X.headerText,C=X.buttons||[],I="inline"!==X.display,k=ve(X.context),u=W?k:ve("body,html"),Y=k[0],B.live=!0,s=0;s<C.length;s++)"ok"!=(t=C[s])&&"set"!=t&&"set"!=t.handler||(B.live=!1);B.buttons.set={text:X.setText,icon:X.setIcon,handler:"set"},B.buttons.cancel={text:X.cancelText,icon:X.cancelIcon,handler:"cancel"},B.buttons.close={text:X.closeText,icon:X.closeIcon,handler:"cancel"},B.buttons.clear={text:X.clearText,icon:X.clearIcon,handler:"clear"},B._isInput=G.is("input")},B._init=function(e){var t=B._isVisible,s=t&&!M.hasClass("mbsc-fr-pos");t&&B.hide(!0,!1,!0),G.off(".mbsc"),B.__init(e),B._isLiquid="liquid"==X.layout,I?(B._readValue(),B._hasContent||X.skipShow||B.attachShow(G),t&&B.show(s)):B.show(),G.removeClass("mbsc-cloak").filter("input, select, textarea").on("change.mbsc",function(){B._preventChange||B.setVal(G.val(),!0,!1),B._preventChange=!1})},B.buttons={},B.handlers={set:B.select,cancel:B.cancel,clear:B.clear},B._value=null,B._isValid=!0,B._isVisible=!1,X=B.settings,U=B.trigger,e||B.init()}var Ye,We,O=w.themes,Ie=/(iphone|ipod)/i.test(m)&&7<=c,Pe="android"==a,F="ios"==a,Le=F&&7<c,He="input,select,textarea,button";R.prototype._defaults={lang:"en",setText:"Set",selectedText:"{count} selected",closeText:"Close",cancelText:"Cancel",clearText:"Clear",context:"body",maxPopupWidth:600,disabled:!1,closeOnOverlayTap:!0,showOnFocus:Pe||F,showOnTap:!0,display:"center",scroll:!0,scrollLock:!0,showOverlay:!0,tap:!0,touchUi:!0,btnClass:"mbsc-fr-btn",btnWidth:!0,focusTrap:!0,focusOnClose:!(F&&8==c)},M.Frame=R,O.frame.mobiscroll={headerText:!1,btnWidth:!1},O.scroller.mobiscroll=re({},O.frame.mobiscroll,{rows:5,showLabel:!1,selectedLineBorder:1,weekDays:"min",checkIcon:"ion-ios7-checkmark-empty",btnPlusClass:"mbsc-ic mbsc-ic-arrow-down5",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up5",btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left5",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right5"}),u&&ve(window).on("focus",function(){Ye&&(We=!0)});function z(n,e,t){var a,s,i,o,l,r,c,d,u,m,f,h,p,b,v,g,y,w,_,x,C,T,k,M,D,S,V,A,Y,W,I,P,L,H,E,O,F,$,N,X,R,z,U,q,j,B=this,G=0,J=1,Z=e,K=ve(n);function Q(e){U("onStart",{domEvent:e}),Z.stopProp&&e.stopPropagation(),Z.prevDef&&e.preventDefault(),Z.readonly||Z.lock&&C||De(e,this)&&!x&&(a&&a.removeClass("mbsc-active"),v=!1,C||(a=ve(e.target).closest(".mbsc-btn-e",this)).length&&!a.hasClass("mbsc-disabled")&&(v=!0,o=setTimeout(function(){a.addClass("mbsc-active")},100)),T=Y=!(x=!0),B.scrolled=C,F=be(e,"X"),$=be(e,"Y"),h=F,d=c=r=0,O=new Date,E=+Se(X,q)||0,C&&de(E,Ee?0:1),"mousedown"===e.type&&ve(document).on("mousemove",ee).on("mouseup",se))}function ee(e){x&&(Z.stopProp&&e.stopPropagation(),h=be(e,"X"),p=be(e,"Y"),r=h-F,c=p-$,d=q?c:r,v&&(Math.abs(c)>Z.thresholdY||Math.abs(r)>Z.thresholdX)&&(clearTimeout(o),a.removeClass("mbsc-active"),v=!1),(B.scrolled||!T&&Math.abs(d)>z)&&(Y||U("onGestureStart",b),B.scrolled=Y=!0,M||(M=!0,k=ue(te))),q||Z.scrollLock?e.preventDefault():B.scrolled?e.preventDefault():7<Math.abs(c)&&(T=!0,B.scrolled=!0,se()))}function te(){w&&(d=pe(d,-L*w,L*w)),de(pe(E+d,_-f,y+f)),M=!1}function se(e){if(x){var t,s=new Date-O;Z.stopProp&&e&&e.stopPropagation(),me(k),M=!1,!T&&B.scrolled&&(Z.momentum&&s<300&&(t=d/s,d=Math.max(Math.abs(d),t*t/Z.speedUnit)*(d<0?-1:1)),le(d)),v&&(clearTimeout(o),a.addClass("mbsc-active"),setTimeout(function(){a.removeClass("mbsc-active")},100),T||B.scrolled||U("onBtnTap",{target:a[0],domEvent:e})),e&&"mouseup"==e.type&&ve(document).off("mousemove",ee).off("mouseup",se),x=!1}}function ae(e){if(e=e.originalEvent||e,d=q?null==e.deltaY?e.wheelDelta||e.detail:e.deltaY:e.deltaX,U("onStart",{domEvent:e}),Z.stopProp&&e.stopPropagation(),d){if(e.preventDefault(),e.deltaMode&&1==e.deltaMode&&(d*=15),d=pe(-d,-I,I),E=j,Z.readonly)return;if(Y||ce(),E+d<_&&(E=_,d=0),y<E+d&&(E=y,d=0),M||(M=!0,k=ue(te)),!d&&Y)return;Y=!0,clearTimeout(W),W=setTimeout(function(){me(k),Y=M=!1,le(d)},200)}}function ne(e){U("onStart",{domEvent:e}),Z.readonly||(e.stopPropagation(),E=j,Y=!1,e.target==D?($=be(e,"Y",!0),ve(document).on("mousemove",ie).on("mouseup",oe)):($=s.offset().top,ie(e),oe()))}function ie(e){var t=(be(e,"Y",!0)-$)/l;d=g?pe(d=-(w*L*2+l)*t,-L*w,L*w):(_-y-l)*t,Y||ce(),Y=!0,de(pe(E+d,_-f,y+f))}function oe(){E=j,le(0),ve(document).off("mousemove",ie).off("mouseup",oe)}function re(e){e.stopPropagation()}function ce(){U("onGestureStart",b={posX:q?0:j,posY:q?j:0,originX:q?0:E,originY:q?E:0,direction:0<d?q?270:360:q?90:180})}function le(e){var t,s,a;if(w&&(e=pe(e,-L*w,L*w)),a=pe(Math.round((E+e)/L)*L,_,y),H){if(e<0){for(t=H.length-1;0<=t;t--)if(Math.abs(a)+l>=H[t].breakpoint){J=2,a=H[G=t].snap2;break}}else if(0<=e)for(t=0;t<H.length;t++)if(Math.abs(a)<=H[t].breakpoint){J=1,a=H[G=t].snap1;break}a=pe(a,_,y)}s=Z.time||(j<_||y<j?1e3:Math.max(1e3,Math.abs(a-j)*Z.timeUnit)),b.destinationX=q?0:a,b.destinationY=q?a:0,b.duration=s,b.transitionTiming=m,U("onGestureEnd",b),B.scroll(a,s)}function de(t,e,s,a){function n(){clearInterval(P),clearTimeout(R),C=!1,j=t,b.posX=q?0:t,b.posY=q?t:0,o&&U("onMove",b),r&&U("onAnimationEnd",b),a&&a()}var i,o=t!=j,r=1<e,c=e?_e+"transform "+Math.round(e)+"ms "+m:"";b={posX:q?0:j,posY:q?j:0,originX:q?0:E,originY:q?E:0,direction:0<t-j?q?270:360:q?90:180},j=t,r&&(b.destinationX=q?0:t,b.destinationY=q?t:0,b.duration=e,b.transitionTiming=m,U("onAnimationStart",b)),N[xe+"Transition"]=c,N[xe+"Transform"]="translate3d("+(q?"0,"+t+"px,":t+"px,0,")+"0)",D&&S&&(i=g?(V-t)/(w*L*2):(t-y)/(_-y),D.style[xe+"Transition"]=c,D.style[xe+"Transform"]="translate3d(0,"+Math.max(0,Math.min((l-S)*i,l-S))+"px,0)"),!o&&!C||!e||e<=1?n():e&&(C=!s,clearInterval(P),P=setInterval(function(){var e=+Se(X,q)||0;b.posX=q?0:e,b.posY=q?e:0,U("onMove",b),Math.abs(e-t)<2&&n()},100),clearTimeout(R),R=setTimeout(function(){n()},e)),Z.sync&&Z.sync(t,e,m)}ge.call(this,n,e,!0),B.scrolled=!1,B.scroll=function(e,t,s,a){e=pe(e=fe(e)?Math.round(e/L)*L:Math.ceil((ve(e,n).length?Math.round(X.offset()[u]-ve(e,n).offset()[u]):j)/L)*L,_,y),G=Math.round(e/L),E=j,V=w*L+e,de(e,t,s,a)},B.refresh=function(e){var t;for(l=(void 0===Z.contSize?q?K.height():K.width():Z.contSize)||0,y=(void 0===Z.maxScroll?0:Z.maxScroll)||0,_=Math.min(y,void 0===Z.minScroll?Math.min(0,q?l-X.height():l-X.width()):Z.minScroll)||0,H=null,!q&&Z.rtl&&(t=y,y=-_,_=-t),he(Z.snap)&&(H=[],X.find(Z.snap).each(function(){var e=q?this.offsetTop:this.offsetLeft,t=q?this.offsetHeight:this.offsetWidth;H.push({breakpoint:e+t/2,snap1:-e,snap2:l-e-t})})),L=fe(Z.snap)?Z.snap:1,w=Z.snap?Z.maxSnapScroll:0,m=Z.easing,f=Z.elastic?fe(Z.snap)?L:fe(Z.elastic)?Z.elastic:0:0,I=L;44<I;)I/=2;I=Math.round(44/I)*I,D&&(g=_==-1/0||y==1/0,S=_<y?Math.max(20,l*l/(y-_+l)):0,D.style.height=S+"px",A.style.height=S?"":0),void 0===j&&(j=Z.initialPos,G=Math.round(j/L)),e||B.scroll(Z.snap?H?H[G]["snap"+J]:G*L:j)},B._processSettings=function(){q="Y"==Z.axis,u=q?"top":"left",X=Z.moveElement||K.children().eq(0),N=X[0].style,z=q?Z.thresholdY:Z.thresholdX,Z.scrollbar&&(i=Z.scrollbar,s=i.find(".mbsc-sc-bar"),D=s[0],A=i[0])},B._init=function(){B.refresh(),K.on("touchstart mousedown",Q).on("touchmove",ee).on("touchend touchcancel",se),Z.mousewheel&&K.on("wheel mousewheel",ae),D&&i.on("mousedown",ne).on("click",re),n.addEventListener("click",function(e){B.scrolled&&(B.scrolled=!1,e.stopPropagation(),e.preventDefault())},!0)},B._destroy=function(){clearInterval(P),K.off("touchstart mousedown",Q).off("touchmove",ee).off("touchend touchcancel",se).off("wheel mousewheel",ae),D&&i.off("mousedown",ne).off("click",re)},Z=B.settings,U=B.trigger,t||B.init()}var Ee="ios"==a;z.prototype={_defaults:{speedUnit:.0022,timeUnit:3,initialPos:0,axis:"Y",thresholdX:10,thresholdY:5,easing:"cubic-bezier(0.190, 1.000, 0.220, 1.000)",stopProp:!0,momentum:!0,mousewheel:!0,elastic:!0}};var $={},N=u?window.CSS:null,U=N&&N.supports&&N.supports("(transform-style: preserve-3d)");function q(e){return(e+"").replace('"',"___")}function X(p,t,e){var s,m,g,b,y,c,x,w,_,l,d,v,C,T,k,M,i,D=40,S=1e3,V=this,o=ve(p);function A(e){var t,s,a=+ve(this).attr("data-index");38==e.keyCode?(t=!0,s=-1):40==e.keyCode?(t=!0,s=1):32==e.keyCode&&(t=!0,r(a,ve(e.target))),t&&(e.stopPropagation(),e.preventDefault(),s&&x.start(a,s,e))}function Y(){x.stop()}function r(e,t){var s=M[e],a=+t.attr("data-index"),n=L(s,a),i=V._tempSelected[e],o=fe(s.multiple)?s.multiple:1/0;!1!==T("onItemTap",{target:t[0],index:e,value:n,selected:t.hasClass("mbsc-sc-itm-sel")})&&(s.multiple&&!s._disabled[n]&&(void 0!==i[n]?(t.removeClass(y).removeAttr("aria-selected"),delete i[n]):(1==o&&(V._tempSelected[e]=i={},s._$markup.find(".mbsc-sc-itm-sel").removeClass(y).removeAttr("aria-selected")),function(e){var t,s=[];for(t in e)s.push(e[t]);return s}(i).length<o&&(t.addClass(y).attr("aria-selected","true"),i[n]=n))),N(s,e,a,S,s._index<a?1:2,!0,s.multiple),V.live&&(!s.multiple||1===s.multiple&&C.tapSelect)&&(!0===C.setOnTap||C.setOnTap[e])&&setTimeout(function(){V.select()},C.tapSelect?0:200))}function f(e){return-(e.max-e._offset-(e.multiple&&!b?Math.floor(C.rows/2):0))*_}function h(e){return-(e.min-e._offset+(e.multiple&&!b?Math.floor(C.rows/2):0))*_}function W(e,t){return(e._array?e._map[t]:+e.getIndex(t,V))||0}function I(e,t){var s=e.data;if(t>=e.min&&t<=e.max)return e._array?e.circular?ve(s).get(t%e._length):s[t]:ve.isFunction(s)?s(t,V):""}function P(e){return ve.isPlainObject(e)?void 0!==e.value?e.value:e.display:e}function L(e,t){return P(I(e,t))}function H(e,t,s){var a=M[e];N(a,e,a._index+t,C.delay+100,1==t?1:2,!1,!1,"keydown"==s.type)}function E(e){return ve.isArray(C.readonly)?C.readonly[e]:C.readonly}function u(s,e,t){var a=s._index-s._batch;return s.data=s.data||[],s.key=void 0!==s.key?s.key:e,s.label=void 0!==s.label?s.label:e,s._map={},s._array=ve.isArray(s.data),s._array&&(s._length=s.data.length,ve.each(s.data,function(e,t){s._map[P(t)]=e})),s.circular=void 0===C.circular?void 0===s.circular?s._array&&s._length>C.rows:s.circular:ve.isArray(C.circular)?C.circular[e]:C.circular,s.min=s._array?s.circular?-1/0:0:void 0===s.min?-1/0:s.min,s.max=s._array?s.circular?1/0:s._length-1:void 0===s.max?1/0:s.max,s._nr=e,s._index=W(s,w[e]),s._disabled={},s._batch=0,s._current=s._index,s._first=s._index-D,s._last=s._index+D,s._offset=s._first,t?(s._offset-=s._margin/_+(s._index-a),s._margin+=(s._index-a)*_):s._margin=0,s._refresh=function(e){re(s._scroller.settings,{minScroll:f(s),maxScroll:h(s)}),s._scroller.refresh(e)},i[s.key]=s}function O(e,t,s,a,n){var i,o,r,c,l,d,u,m,f,h,p="",b=V._tempSelected[t],v=e._disabled||{};for(i=s;i<=a;i++)r=I(e,i),f=r,l=void 0===(h=ve.isPlainObject(f)?f.display:f)?"":h,c=P(r),o=r&&void 0!==r.cssClass?r.cssClass:"",d=r&&void 0!==r.label?r.label:"",u=r&&r.invalid,m=void 0!==c&&c==w[t]&&!e.multiple,p+='<div role="option" tabindex="-1" aria-selected="'+!!b[c]+'" class="mbsc-sc-itm '+(n?"mbsc-sc-itm-3d ":"")+o+" "+(m?"mbsc-sc-itm-sel ":"")+(b[c]?y:"")+(void 0===c?" mbsc-sc-itm-ph":" mbsc-btn-e")+(u?" mbsc-sc-itm-inv-h mbsc-disabled":"")+(v[c]?" mbsc-sc-itm-inv mbsc-disabled":"")+'" data-index="'+i+'" data-val="'+q(c)+'"'+(d?' aria-label="'+d+'"':"")+(m?' aria-selected="true"':"")+' style="height:'+_+"px;line-height:"+_+"px;"+(n?_e+"transform:rotateX("+(e._offset-i)*g%360+"deg) translateZ("+_*C.rows/2+"px);":"")+'">'+(1<k?'<div class="mbsc-sc-itm-ml" style="line-height:'+Math.round(_/k)+"px;font-size:"+Math.round(_/k*.8)+'px;">':"")+l+(1<k?"</div>":"")+"</div>";return p}function F(e,t,s,a){var n,i=M[e],o=a||i._disabled,r=W(i,t),c=L(i,r),l=c,d=c,u=0,m=0;if(!0===o[c]){for(n=0;r-u>=i.min&&o[l]&&n<100;)n++,l=L(i,r-++u);for(n=0;r+m<i.max&&o[d]&&n<100;)n++,d=L(i,r+ ++m);c=(m<u&&m&&2!==s||!u||r-u<0||1==s)&&!o[d]?d:l}return c}function $(a,n,i,e,o,t,r){var c,l,d,u,s,m,f,h=V._isVisible;v=!0,u=C.validate.call(p,{values:w.slice(0),index:n,direction:i},V)||{},v=!1,u.valid&&(V._tempWheelArray=w=u.valid.slice(0)),t||ve.each(M,function(e,s){if(h&&s._$markup.find(".mbsc-sc-itm-inv").removeClass("mbsc-sc-itm-inv mbsc-disabled"),s._disabled={},u.disabled&&u.disabled[e]&&ve.each(u.disabled[e],function(e,t){s._disabled[t]=!0,h&&s._$markup.find('.mbsc-sc-itm[data-val="'+q(t)+'"]').addClass("mbsc-sc-itm-inv mbsc-disabled")}),w[e]=s.multiple?w[e]:F(e,w[e],i),h){if(s.multiple&&void 0!==n||s._$markup.find(".mbsc-sc-itm-sel").removeClass(y).removeAttr("aria-selected"),l=W(s,w[e]),c=l-s._index+s._batch,Math.abs(c)>2*D+1&&(d=c+(2*D+1)*(0<c?-1:1),s._offset+=d,s._margin-=d*_,s._refresh()),s._index=l+s._batch,s.multiple){if(void 0===n)for(var t in V._tempSelected[e])s._$markup.find('.mbsc-sc-itm[data-val="'+q(t)+'"]').addClass(y).attr("aria-selected","true")}else s._$markup.find('.mbsc-sc-itm[data-val="'+q(w[e])+'"]').addClass("mbsc-sc-itm-sel").attr("aria-selected","true");s._$active&&s._$active.attr("tabindex",-1),s._$active=s._$markup.find('.mbsc-sc-itm[data-index="'+s._index+'"]').eq(b&&s.multiple?1:0).attr("tabindex",0),r&&n===e&&s._$active.length&&(s._$active[0].focus(),s._$scroller.parent().scrollTop(0)),s._scroller.scroll(-(l-s._offset+s._batch)*_,n===e||void 0===n?a:S,o)}}),T("onValidated",{index:n,time:a}),V._tempValue=C.formatValue.call(p,w,V),h&&V._updateHeader(),V.live&&(m=t,(f=M[s=n])&&(!f.multiple||1!==f.multiple&&m&&(!0===C.setOnTap||C.setOnTap[s])))&&(V._hasValue=e||V._hasValue,X(e,e,0,!0),e&&T("onSet",{valueText:V._value})),e&&T("onChange",{index:n,valueText:V._tempValue})}function N(e,t,s,a,n,i,o,r){var c=L(e,s);void 0!==c&&(w[t]=c,e._batch=e._array?Math.floor(s/e._length)*e._length:0,e._index=s,setTimeout(function(){$(a,t,n,!0,i,o,r)},10))}function X(e,t,s,a,n){if(a?V._tempValue=C.formatValue.call(p,V._tempWheelArray,V):$(s),!n){V._wheelArray=[];for(var i=0;i<w.length;i++)V._wheelArray[i]=M[i]&&M[i].multiple?Object.keys(V._tempSelected[i]||{})[0]:w[i];V._value=V._hasValue?V._tempValue:null,V._selected=re(!0,{},V._tempSelected)}e&&(V._isInput&&o.val(V._hasValue?V._tempValue:""),T("onFill",{valueText:V._hasValue?V._tempValue:"",change:t}),t&&(V._preventChange=!0,o.trigger("change")))}R.call(this,p,t,!0),V.setVal=V._setVal=function(e,t,s,a,n){V._hasValue=null!=e,V._tempWheelArray=w=ve.isArray(e)?e.slice(0):C.parseValue.call(p,e,V)||[],X(t,void 0===s?t:s,n,!1,a)},V.getVal=V._getVal=function(e){var t=V._hasValue||e?V[e?"_tempValue":"_value"]:null;return fe(t)?+t:t},V.setArrayVal=V.setVal,V.getArrayVal=function(e){return e?V._tempWheelArray:V._wheelArray},V.changeWheel=function(e,t,s){var a,n;ve.each(e,function(e,t){(n=i[e])&&(a=n._nr,re(n,t),u(n,a,!0),V._isVisible&&(b&&n._$3d.html(O(n,a,n._first+D-m+1,n._last-D+m,!0)),n._$scroller.html(O(n,a,n._first,n._last)).css("margin-top",n._margin+"px"),n._refresh(v)))}),!V._isVisible||V._isLiquid||v||V.position(),v||$(t,void 0,void 0,s)},V.getValidValue=F,V._generateContent=function(){var s,a=0,n="",i=b?_e+"transform: translateZ("+(_*C.rows/2+3)+"px);":"",o='<div class="mbsc-sc-whl-l" style="'+i+"height:"+_+"px;margin-top:-"+(_/2+(C.selectedLineBorder||0))+'px;"></div>',r=0;return ve.each(C.wheels,function(e,t){n+='<div class="mbsc-w-p mbsc-sc-whl-gr-c'+(b?" mbsc-sc-whl-gr-3d-c":"")+(C.showLabel?" mbsc-sc-lbl-v":"")+'">'+o+'<div class="mbsc-sc-whl-gr'+(b?" mbsc-sc-whl-gr-3d":"")+(c?" mbsc-sc-cp":"")+(C.width||C.maxWidth?'"':'" style="max-width:'+C.maxPopupWidth+'px;"')+">",ve.each(t,function(e,t){V._tempSelected[r]=re({},V._selected[r]),M[r]=u(t,r),a+=C.maxWidth?C.maxWidth[r]||C.maxWidth:C.width?C.width[r]||C.width:0,s=void 0!==t.label?t.label:e,n+='<div class="mbsc-sc-whl-w '+(t.cssClass||"")+(t.multiple?" mbsc-sc-whl-multi":"")+'" style="'+(C.width?"width:"+(C.width[r]||C.width)+"px;":(C.minWidth?"min-width:"+(C.minWidth[r]||C.minWidth)+"px;":"")+(C.maxWidth?"max-width:"+(C.maxWidth[r]||C.maxWidth)+"px;":""))+'">'+(d?'<div class="mbsc-sc-bar-c"><div class="mbsc-sc-bar"></div></div>':"")+'<div class="mbsc-sc-whl-o" style="'+i+'"></div>'+o+'<div aria-live="off" aria-label="'+s+'"'+(t.multiple?' aria-multiselectable="true"':"")+' role="listbox" data-index="'+r+'" class="mbsc-sc-whl" style="height:'+C.rows*_*(b?1.1:1)+'px;">'+(c?'<div data-index="'+r+'" data-step="1" class="mbsc-sc-btn mbsc-sc-btn-plus '+(C.btnPlusClass||"")+'"></div><div data-index="'+r+'" data-step="-1" class="mbsc-sc-btn mbsc-sc-btn-minus '+(C.btnMinusClass||"")+'"></div>':"")+'<div class="mbsc-sc-lbl">'+s+'</div><div class="mbsc-sc-whl-c" style="height:'+l+"px;margin-top:-"+(l/2+1)+"px;"+i+'"><div class="mbsc-sc-whl-sc" style="top:'+(l-_)/2+'px;">',n+=O(t,r,t._first,t._last)+"</div></div>",b&&(n+='<div class="mbsc-sc-whl-3d" style="height:'+_+"px;margin-top:-"+_/2+'px;">',n+=O(t,r,t._first+D-m+1,t._last-D+m,!0),n+="</div>"),n+="</div></div>",r++}),n+="</div></div>"}),a&&(C.maxPopupWidth=a),n},V._attachEvents=function(e){function t(e){var t;m=ve(this),y=+m.attr("data-step"),h=+m.attr("data-index"),f=!0,d&&e.stopPropagation(),"touchstart"==e.type&&m.closest(".mbsc-no-touch").removeClass("mbsc-no-touch"),"mousedown"==e.type&&e.preventDefault(),t="keydown"!=e.type?(v=be(e,"X"),g=be(e,"Y"),De(e,this)):32===e.keyCode,p||!t||m.hasClass("mbsc-disabled")||(i(h,y,e)&&(m.addClass("mbsc-active"),u&&u.addRipple(m.find(".mbsc-segmented-content"),e)),"mousedown"==e.type&&ve(document).on("mousemove",s).on("mouseup",a))}function s(e){(7<Math.abs(v-be(e,"X"))||7<Math.abs(g-be(e,"Y")))&&(f=!0,n())}function a(e){"touchend"==e.type&&e.preventDefault(),n(),"mouseup"==e.type&&ve(document).off("mousemove",s).off("mouseup",a)}function n(){p=!1,clearInterval(w),m&&(m.removeClass("mbsc-active"),u&&setTimeout(function(){u.removeRipple()},100))}function i(e,t,s){return p||_(e)||(h=e,y=t,b=s,f=!(p=!0),setTimeout(o,100)),p}function o(){m&&m.hasClass("mbsc-disabled")?n():(!p&&f||(f=!0,c(h,y,b,o)),p&&l&&(clearInterval(w),w=setInterval(function(){c(h,y,b)},l)))}var r,c,l,d,u,m,f,h,p,b,v,g,y,w,_;r=ve(".mbsc-sc-btn",e),c=H,l=C.delay,d=!0,_=E||ee,r.on("touchstart mousedown keydown",t).on("touchmove",s).on("touchend touchcancel keyup",a),x={start:i,stop:n,destroy:function(){r.off("touchstart mousedown keydown",t).off("touchmove",s).off("touchend touchcancel keyup",a)}},ve(".mbsc-sc-whl",e).on("keydown",A).on("keyup",Y)},V._detachEvents=function(){x.stop();for(var e=0;e<M.length;e++)M[e]._scroller.destroy()},V._markupReady=function(e){ve(".mbsc-sc-whl-w",s=e).each(function(d){var n,e=ve(this),u=M[d];u._$markup=e,u._$scroller=ve(".mbsc-sc-whl-sc",this),u._$3d=ve(".mbsc-sc-whl-3d",this),u._scroller=new z(this,{mousewheel:C.mousewheel,moveElement:u._$scroller,scrollbar:ve(".mbsc-sc-bar-c",this),initialPos:(u._first-u._index)*_,contSize:C.rows*_,snap:_,minScroll:f(u),maxScroll:h(u),maxSnapScroll:D,prevDef:!0,stopProp:!0,timeUnit:3,easing:"cubic-bezier(0.190, 1.000, 0.220, 1.000)",sync:function(e,t,s){var a=t?_e+"transform "+Math.round(t)+"ms "+s:"";b&&(u._$3d[0].style[xe+"Transition"]=a,u._$3d[0].style[xe+"Transform"]="rotateX("+-e/_*g+"deg)")},onStart:function(e,t){t.settings.readonly=E(d)},onGestureStart:function(){e.addClass("mbsc-sc-whl-a mbsc-sc-whl-anim"),T("onWheelGestureStart",{index:d})},onGestureEnd:function(e){var t=90==e.direction?1:2,s=e.duration,a=e.destinationY;n=Math.round(-a/_)+u._offset,N(u,d,n,s,t)},onAnimationStart:function(){e.addClass("mbsc-sc-whl-anim")},onAnimationEnd:function(){e.removeClass("mbsc-sc-whl-a mbsc-sc-whl-anim"),T("onWheelAnimationEnd",{index:d}),u._$3d.find(".mbsc-sc-itm-del").remove()},onMove:function(e){var t,s,a,n,i,o,r,c,l;t=u,s=d,a=e.posY,n=Math.round(-a/_)+t._offset,i=n-t._current,o=t._first,r=t._last,c=o+D-m+1,l=r-D+m,i&&(t._first+=i,t._last+=i,t._current=n,0<i?(t._$scroller.append(O(t,s,Math.max(r+1,o+i),r+i)),ve(".mbsc-sc-itm",t._$scroller).slice(0,Math.min(i,r-o+1)).remove(),b&&(t._$3d.append(O(t,s,Math.max(l+1,c+i),l+i,!0)),ve(".mbsc-sc-itm",t._$3d).slice(0,Math.min(i,l-c+1)).attr("class","mbsc-sc-itm-del"))):i<0&&(t._$scroller.prepend(O(t,s,o+i,Math.min(o-1,r+i))),ve(".mbsc-sc-itm",t._$scroller).slice(Math.max(i,o-r-1)).remove(),b&&(t._$3d.prepend(O(t,s,c+i,Math.min(c-1,l+i),!0)),ve(".mbsc-sc-itm",t._$3d).slice(Math.max(i,c-l-1)).attr("class","mbsc-sc-itm-del"))),t._margin+=i*_,t._$scroller.css("margin-top",t._margin+"px"))},onBtnTap:function(e){r(d,ve(e.target))}})}),$()},V._fillValue=function(){X(V._hasValue=!0,!0,0,!0)},V._clearValue=function(){ve(".mbsc-sc-whl-multi .mbsc-sc-itm-sel",s).removeClass(y).removeAttr("aria-selected")},V._readValue=function(){var e=o.val()||"",s=0;""!==e&&(V._hasValue=!0),V._tempWheelArray=w=V._hasValue&&V._wheelArray?V._wheelArray.slice(0):C.parseValue.call(p,e,V)||[],V._tempSelected=re(!0,{},V._selected),ve.each(C.wheels,function(e,t){ve.each(t,function(e,t){M[s]=u(t,s),s++})}),X(!1,!1,0,!0),T("onRead")},V.__processSettings=function(e){C=V.settings,T=V.trigger,k=C.multiline,y="mbsc-sc-itm-sel mbsc-ic mbsc-ic-"+C.checkIcon,(d=!C.touchUi)&&(C.tapSelect=!0,C.circular=!1,C.rows=e.rows||t.rows||7)},V.__init=function(e){e&&(V._wheelArray=null),M=[],i={},c=C.showScrollArrows,b=C.scroll3d&&U&&!c&&!d&&("ios"==C.theme||"ios"==C.baseTheme),_=C.height,l=b?2*Math.round((_-.03*(_*C.rows/2+3))/2):_,m=Math.round(1.8*C.rows),g=360/(2*m),c&&(C.rows=Math.max(3,C.rows))},V._getItemValue=P,V._tempSelected={},V._selected={},e||V.init()}X.prototype={_hasDef:!0,_hasTheme:!0,_hasLang:!0,_responsive:!0,_class:"scroller",_presets:$,_defaults:re({},R.prototype._defaults,{minWidth:80,height:40,rows:3,multiline:1,delay:200,readonly:!1,showLabel:!0,setOnTap:!1,wheels:[],preset:"",speedUnit:.0012,timeUnit:.08,checkIcon:"checkmark",compClass:"mbsc-sc",validate:function(){},formatValue:function(e){return e.join(" ")},parseValue:function(e,s){var a,n,i=[],o=[],r=0;return null!=e&&(i=(e+"").split(" ")),ve.each(s.settings.wheels,function(e,t){ve.each(t,function(e,t){n=t.data,a=s._getItemValue(n[0]),ve.each(n,function(e,t){if(i[r]==s._getItemValue(t))return a=s._getItemValue(t),!1}),o.push(a),r++})}),o}})},M.Scroller=X;function j(g){function e(e){var t,s,a,n,i=[];if(e){for(t=0;t<e.length;t++)if((s=e[t]).start&&s.end&&!Ce.test(s.start))for(a=new Date(we(s.start,D,P)),n=new Date(we(s.end,D,P));a<=n;)i.push(ce(a.getFullYear(),a.getMonth(),a.getDate())),a.setDate(a.getDate()+1);else i.push(s);return i}return e}function Y(e,t,s,a){return Math.min(a,Math.floor(e/t)*t+s)}function t(e,t,s){return Math.floor((s-t)/e)*e+t}function i(e){return e.getFullYear()+"-"+oe(e.getMonth()+1)+"-"+oe(e.getDate())}function r(e,t,s,a){var n;return void 0===k[t]||(n=+e[k[t]],isNaN(n))?s?ne[t](s):void 0!==o[t]?o[t]:ne[t](a):n}function y(e){var t,s=new Date((new Date).setHours(0,0,0,0));if(null===e)return e;void 0!==k.dd&&(t=e[k.dd].split("-"),t=new Date(t[0],t[1]-1,t[2])),void 0!==k.tt&&(t=t||s,t=new Date(t.getTime()+e[k.tt]%86400*1e3));var a=r(e,"y",t,s),n=r(e,"m",t,s),i=Math.min(r(e,"d",t,s),P.getMaxDayOfMonth(a,n)),o=r(e,"h",t,s);return P.getDate(a,n,i,X&&r(e,"a",t,s)?o+12:o,r(e,"i",t,s),r(e,"s",t,s),r(e,"u",t,s))}function w(e,t){var s,a,n=["y","m","d","a","h","i","s","u","dd","tt"],i=[];if(null==e)return e;for(s=0;s<n.length;s++)void 0!==k[a=n[s]]&&(i[k[a]]=ne[a](e)),t&&(o[a]=ne[a](e));return i}function s(e,t){return t?Math.floor(new Date(e)/864e5):e.getMonth()+12*(e.getFullYear()-1970)}function m(e){return{value:e,display:(/yy/i.test(A)?e:(e+"").substr(2,2))+(P.yearSuffix||"")}}function f(e){return e}function h(a){var n=/d/i.test(a);return{label:"",cssClass:"mbsc-dt-whl-date",min:F?s(i(F),n):void 0,max:$?s(i($),n):void 0,data:function(e){var t=new Date((new Date).setHours(0,0,0,0)),s=n?new Date(864e5*e):new Date(1970,e,1);return n&&(s=new Date(s.getUTCFullYear(),s.getUTCMonth(),s.getUTCDate())),{invalid:n&&!x(s,!0),value:i(s),display:t.getTime()==s.getTime()?P.todayText:le(a,s,P)}},getIndex:function(e){return s(e,n)}}}function p(e){var t,s,a,n=[];for(/s/i.test(e)?s=j:/i/i.test(e)?s=60*q:/h/i.test(e)&&(s=3600*U),W=ae.tt=s,t=0;t<86400;t+=s)a=new Date((new Date).setHours(0,0,0,0)+1e3*t),n.push({value:t,display:le(e,a,P)});return{label:"",cssClass:"mbsc-dt-whl-time",data:n}}function _(e,t){return P.getYear(e)===P.getYear(t)&&P.getMonth(e)===P.getMonth(t)}function x(e,t){return!(!t&&e<F)&&(!(!t&&$<e)&&(!!a(e,O)||!a(e,E)))}function a(e,t){var s,a,n;if(t)for(a=0;a<t.length;a++)if(n=(s=t[a])+"",!s.start)if(ke.test(n)){if((n=+n.replace("w",""))==e.getDay())return!0}else if(Te.test(n)){if((n=n.split("/"))[1]){if(n[0]-1==e.getMonth()&&n[1]==e.getDate())return!0}else if(n[0]==e.getDate())return!0}else if(s=we(s,D,P),e.getFullYear()==s.getFullYear()&&e.getMonth()==s.getMonth()&&e.getDate()==s.getDate())return!0;return!1}function C(e,t,s,a,n,i,o){var r,c,l,d;if(e)for(c=0;c<e.length;c++)if(d=(r=e[c])+"",!r.start)if(ke.test(d))for(l=(d=+d.replace("w",""))-a;l<n;l+=7)0<=l&&(i[l+1]=o);else Te.test(d)?(d=d.split("/"))[1]?d[0]-1==s&&(i[d[1]]=o):i[d[0]]=o:(r=we(r,D,P),P.getYear(r)==t&&P.getMonth(r)==s&&(i[P.getDay(r)]=o))}function T(e,t,s,a,n,i,o,r){var c,l,d,u,m,f,h,p,b,v,g,y,w,_,x,C,T,k,M,D,S={},V=P.getDate(a,n,i),A=["a","h","i","s"];if(e){for(h=0;h<e.length;h++)(g=e[h]).start&&(g.apply=!1,k=(T=(d=g.d)+"").split("/"),d&&(d.getTime&&a==P.getYear(d)&&n==P.getMonth(d)&&i==P.getDay(d)||!ke.test(T)&&(k[1]&&i==k[1]&&n==k[0]-1||!k[1]&&i==k[0])||ke.test(T)&&V.getDay()==+T.replace("w",""))&&(g.apply=!0,S[V]=!0));for(h=0;h<e.length;h++)if(g=e[h],C=c=0,p=te[s],b=se[s],l=!(x=_=!0),g.start&&(g.apply||!g.d&&!S[V])){for(y=g.start.split(":"),w=g.end.split(":"),v=0;v<3;v++)void 0===y[v]&&(y[v]=0),void 0===w[v]&&(w[v]=59),y[v]=+y[v],w[v]=+w[v];if("tt"==s)p=Y(Math.round((new Date(V).setHours(y[0],y[1],y[2])-new Date(V).setHours(0,0,0,0))/1e3),W,0,86400),b=Y(Math.round((new Date(V).setHours(w[0],w[1],w[2])-new Date(V).setHours(0,0,0,0))/1e3),W,0,86400);else{for(y.unshift(11<y[0]?1:0),w.unshift(11<w[0]?1:0),X&&(12<=y[1]&&(y[1]=y[1]-12),12<=w[1]&&(w[1]=w[1]-12)),v=0;v<t;v++)void 0!==I[v]&&(M=Y(y[v],ae[A[v]],te[A[v]],se[A[v]]),D=Y(w[v],ae[A[v]],te[A[v]],se[A[v]]),f=m=u=0,X&&1==v&&(u=y[0]?12:0,m=w[0]?12:0,f=I[0]?12:0),_||(M=0),x||(D=se[A[v]]),(_||x)&&M+u<I[v]+f&&I[v]+f<D+m&&(l=!0),I[v]!=M&&(_=!1),I[v]!=D&&(x=!1));if(!r)for(v=t+1;v<4;v++)0<y[v]&&(c=ae[s]),w[v]<se[A[v]]&&(C=ae[s]);l||(M=Y(y[t],ae[s],te[s],se[s])+c,D=Y(w[t],ae[s],te[s],se[s])-C,_&&(p=M),x&&(b=D))}if(_||x||l)for(v=p;v<=b;v+=ae[s])o[v]=!r}}}var W,b,n,k={},o={},v={},I=[],c=function(e){var t,s,a,n={};if(e.is("input")){switch(e.attr("type")){case"date":t="yy-mm-dd";break;case"datetime":t="yy-mm-ddTHH:ii:ssZ";break;case"datetime-local":t="yy-mm-ddTHH:ii:ss";break;case"month":t="yy-mm",n.dateOrder="mmyy";break;case"time":t="HH:ii:ss"}n.format=t,s=e.attr("min"),a=e.attr("max"),s&&"undefined"!=s&&(n.min=de(t,s)),a&&"undefined"!=a&&(n.max=de(t,a))}return n}(ve(this)),l=re({},g.settings),d=ie[l.calendarSystem],P=re(g.settings,Me,d,Oe,c,l),M=P.preset,u="datetime"==M?P.dateFormat+P.separator+P.timeFormat:"time"==M?P.timeFormat:P.dateFormat,D=c.format||u,S=P.dateWheels||P.dateFormat,V=P.timeWheels||P.timeFormat,A=P.dateWheels||P.dateDisplay,L=V,H=P.baseTheme||P.theme,E=e(P.invalid),O=e(P.valid),F=we(P.min,D,P),$=we(P.max,D,P),N=/time/i.test(M),X=/h/.test(L),R=/D/.test(A),z=P.steps||{},U=z.hour||P.stepHour||1,q=z.minute||P.stepMinute||1,j=z.second||P.stepSecond||1,B=z.zeroBased,G=B||!F?0:F.getHours()%U,J=B||!F?0:F.getMinutes()%q,Z=B||!F?0:F.getSeconds()%j,K=t(U,G,X?11:23),Q=t(q,J,59),ee=t(q,J,59),te={y:F?F.getFullYear():-1/0,m:0,d:1,h:G,i:J,s:Z,a:0,tt:0},se={y:$?$.getFullYear():1/0,m:11,d:31,h:K,i:Q,s:ee,a:1,tt:86400},ae={y:1,m:1,d:1,h:U,i:q,s:j,a:1,tt:1},ne={y:function(e){return P.getYear(e)},m:function(e){return P.getMonth(e)},d:function(e){return P.getDay(e)},h:function(e){var t=e.getHours();return Y(t=X&&12<=t?t-12:t,U,G,K)},i:function(e){return Y(e.getMinutes(),q,J,Q)},s:function(e){return Y(e.getSeconds(),j,Z,ee)},u:function(e){return e.getMilliseconds()},a:function(e){return 11<e.getHours()?1:0},dd:i,tt:function(e){return Y(Math.round((e.getTime()-new Date(e).setHours(0,0,0,0))/1e3),W||1,0,86400)}};return g.getVal=function(e){return g._hasValue||e?ye(y(g.getArrayVal(e)),P,D):null},g.getDate=function(e){return g._hasValue||e?y(g.getArrayVal(e)):null},g.setDate=function(e,t,s,a,n){g.setArrayVal(w(e,!0),t,n,a,s)},n=function(){var e,t,s,a,n,i,o,r,c=0,l=[],d=[],u=[];if(/date/i.test(M)){for(e=S.split(/\|/.test(S)?"|":""),a=0;a<e.length;a++)if(i=0,(s=e[a]).length)if(/y/i.test(s)&&(v.y=1,i++),/m/i.test(s)&&(v.y=1,v.m=1,i++),/d/i.test(s)&&(v.y=1,v.m=1,v.d=1,i++),1<i&&void 0===k.dd)k.dd=c,c++,d.push(h(s)),u=d,b=!0;else if(/y/i.test(s)&&void 0===k.y)k.y=c,c++,d.push({cssClass:"mbsc-dt-whl-y",label:P.yearText,min:F?P.getYear(F):void 0,max:$?P.getYear($):void 0,data:m,getIndex:f});else if(/m/i.test(s)&&void 0===k.m){for(k.m=c,o=[],c++,n=0;n<12;n++)r=A.replace(/[dy|]/gi,"").replace(/mm/,oe(n+1)+(P.monthSuffix||"")).replace(/m/,n+1+(P.monthSuffix||"")),o.push({value:n,display:/MM/.test(r)?r.replace(/MM/,'<span class="mbsc-dt-month">'+P.monthNames[n]+"</span>"):r.replace(/M/,'<span class="mbsc-dt-month">'+P.monthNamesShort[n]+"</span>")});d.push({cssClass:"mbsc-dt-whl-m",label:P.monthText,data:o})}else if(/d/i.test(s)&&void 0===k.d){for(k.d=c,o=[],c++,n=1;n<32;n++)o.push({value:n,display:(/dd/i.test(A)?oe(n):n)+(P.daySuffix||"")});d.push({cssClass:"mbsc-dt-whl-d",label:P.dayText,data:o})}l.push(d)}if(/time/i.test(M)){for(t=V.split(/\|/.test(V)?"|":""),a=0;a<t.length;a++)if(i=0,(s=t[a]).length&&(/h/i.test(s)&&(v.h=1,i++),/i/i.test(s)&&(v.i=1,i++),/s/i.test(s)&&(v.s=1,i++),/a/i.test(s)&&i++),1<i&&void 0===k.tt)k.tt=c,c++,u.push(p(s));else if(/h/i.test(s)&&void 0===k.h){for(o=[],k.h=c,v.h=1,c++,n=G;n<(X?12:24);n+=U)o.push({value:n,display:X&&0===n?12:/hh/i.test(L)?oe(n):n});u.push({cssClass:"mbsc-dt-whl-h",label:P.hourText,data:o})}else if(/i/i.test(s)&&void 0===k.i){for(o=[],k.i=c,v.i=1,c++,n=J;n<60;n+=q)o.push({value:n,display:/ii/i.test(L)?oe(n):n});u.push({cssClass:"mbsc-dt-whl-i",label:P.minuteText,data:o})}else if(/s/i.test(s)&&void 0===k.s){for(o=[],k.s=c,v.s=1,c++,n=Z;n<60;n+=j)o.push({value:n,display:/ss/i.test(L)?oe(n):n});u.push({cssClass:"mbsc-dt-whl-s",label:P.secText,data:o})}else/a/i.test(s)&&void 0===k.a&&(k.a=c,c++,u.push({cssClass:"mbsc-dt-whl-a",label:P.ampmText,data:/A/.test(s)?[{value:0,display:P.amText.toUpperCase()},{value:1,display:P.pmText.toUpperCase()}]:[{value:0,display:P.amText},{value:1,display:P.pmText}]}));u!=d&&l.push(u)}return l}(),P.isoParts=v,g._format=u,g._order=k,g.handlers.now=function(){g.setDate(new Date,g.live,1e3,!0,!0)},g.buttons.now={text:P.nowText,icon:P.nowIcon,handler:"now"},{minWidth:b&&N?{bootstrap:46,ios:50,material:46,mobiscroll:46,windows:50}[H]:void 0,compClass:"mbsc-dt mbsc-sc",wheels:n,headerText:!!P.headerText&&function(){return le(u,y(g.getArrayVal(!0)),P)},formatValue:function(e){return le(D,y(e),P)},parseValue:function(e){return e||(o={},g._hasValue=!1),w(we(e||P.defaultValue||new Date,D,P,v),!!e)},validate:function(e){var t,r,s,a,n=e.values,i=e.index,o=e.direction,c=P.wheels[0][k.d],l=function(e,t){var s,a,n=!1,i=!1,o=0,r=0,c=F?y(w(F)):-1/0,l=$?y(w($)):1/0;if(x(e))return e;if(e<c&&(e=c),l<e&&(e=l),a=s=e,2!==t)for(n=x(s,!0);!n&&s<l&&o<100;)n=x(s=new Date(s.getTime()+864e5),!0),o++;if(1!==t)for(i=x(a,!0);!i&&c<a&&r<100;)i=x(a=new Date(a.getTime()-864e5),!0),r++;return 1===t&&n?s:2===t&&i?a:_(e,s)?s:_(e,a)?a:r<=o&&i?a:s}(y(n),o),d=w(l),u=[],m={},f=ne.y(l),h=ne.m(l),p=P.getMaxDayOfMonth(f,h),b=!0,v=!0;if(ve.each(["dd","y","m","d","tt","a","h","i","s"],function(e,s){var t=te[s],a=se[s],n=ne[s](l);if(u[k[s]]=[],b&&F&&(t=ne[s](F)),v&&$&&(a=ne[s]($)),n<t&&(n=t),a<n&&(n=a),"dd"!==s&&"tt"!==s&&(b=b&&n==t,v=v&&n==a),void 0!==k[s]){if("y"!=s&&"dd"!=s)for(r=te[s];r<=se[s];r+=ae[s])(r<t||a<r)&&u[k[s]].push(r);if("d"==s){var i=P.getDate(f,h,1).getDay(),o={};C(E,f,h,i,p,o,1),C(O,f,h,i,p,o,0),ve.each(o,function(e,t){t&&u[k[s]].push(e)})}}}),N&&ve.each(["a","h","i","s","tt"],function(e,s){var t=ne[s](l),a=ne.d(l),n={};void 0!==k[s]&&(T(E,e,s,f,h,a,n,0),T(O,e,s,f,h,a,n,1),ve.each(n,function(e,t){t&&u[k[s]].push(e)}),I[e]=g.getValidValue(k[s],t,o,n))}),c&&(c._length!==p||R&&(void 0===i||i===k.y||i===k.m))){for((m[k.d]=c).data=[],t=1;t<=p;t++)a=P.getDate(f,h,t).getDay(),s=A.replace(/[my|]/gi,"").replace(/dd/,(t<10?"0"+t:t)+(P.daySuffix||"")).replace(/d/,t+(P.daySuffix||"")),c.data.push({value:t,display:/DD/.test(s)?s.replace(/DD/,'<span class="mbsc-dt-day">'+P.dayNames[a]+"</span>"):s.replace(/D/,'<span class="mbsc-dt-day">'+P.dayNamesShort[a]+"</span>")});g._tempWheelArray[k.d]=d[k.d],g.changeWheel(m)}return{disabled:u,valid:d}}}}var Oe={separator:" ",dateFormat:"mm/dd/yy",dateDisplay:"MMddyy",timeFormat:"h:ii A",dayText:"Day",monthText:"Month",yearText:"Year",hourText:"Hours",minuteText:"Minutes",ampmText:"&nbsp;",secText:"Seconds",nowText:"Now",todayText:"Today"};$.date=j,$.time=j,$.datetime=j,s("date",X),s("time",X),s("datetime",X);var B=w.themes;B.frame.ios={display:"bottom",headerText:!1,btnWidth:!1,deleteIcon:"ios-backspace",scroll3d:"wp"!=a&&("android"!=a||7<c)},B.scroller.ios=re({},B.frame.ios,{rows:5,height:34,minWidth:55,selectedLineHeight:!0,selectedLineBorder:1,showLabel:!1,useShortLabels:!0,btnPlusClass:"mbsc-ic mbsc-ic-arrow-down5",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up5",checkIcon:"ion-ios7-checkmark-empty",filterClearIcon:"ion-close-circled",dateDisplay:"MMdyy",btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left5",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right5"}),B.listview.ios={leftArrowClass:"mbsc-ic-ion-ios7-arrow-back",rightArrowClass:"mbsc-ic-ion-ios7-arrow-forward"},B.form.ios={};var G=w.themes;function J(e,t){var s=be(t,"X",!0),a=be(t,"Y",!0),n=e[0],i=e.offset(),o=s-i.left,r=a-i.top,c=Math.max(o,n.offsetWidth-o),l=Math.max(r,n.offsetHeight-r),d=2*Math.sqrt(Math.pow(c,2)+Math.pow(l,2));Z(Fe),Fe=ve('<span class="mbsc-ripple"></span>').css({backgroundColor:getComputedStyle(n).color,width:d,height:d,top:a-i.top-d/2,left:s-i.left-d/2}).appendTo(e),setTimeout(function(){Fe.addClass("mbsc-ripple-scaled mbsc-ripple-visible")},10)}function Z(e){setTimeout(function(){e&&(e.removeClass("mbsc-ripple-visible"),setTimeout(function(){e.remove()},2e3))},100)}function K(e,t,s,a){var n,i;e.off(".mbsc-ripple").on("touchstart.mbsc-ripple mousedown.mbsc-ripple",t,function(e){De(e,this)&&(n=be(e,"X"),i=be(e,"Y"),(Q=ve(this)).hasClass(s)||Q.hasClass(a)?Q=null:J(Q,e))}).on("touchmove.mbsc-ripple mousemove.mbsc-ripple",t,function(e){(Q&&9<Math.abs(be(e,"X")-n)||9<Math.abs(be(e,"Y")-i))&&(Z(Fe),Q=null)}).on("touchend.mbsc-ripple touchcancel.mbsc-ripple mouseleave.mbsc-ripple mouseup.mbsc-ripple",t,function(){Q&&(setTimeout(function(){Z(Fe)},100),Q=null)})}G.frame.bootstrap={disabledClass:"disabled",selectedClass:"btn-primary",selectedTabClass:"active",tabLink:!0,todayClass:"text-primary mbsc-cal-today",onMarkupInserted:function(e){var t=ve(e.target),s=ve(".mbsc-cal-tabs",t);ve(".mbsc-fr-popup",t).addClass("popover"),ve(".mbsc-fr-w",t).addClass("popover-content"),ve(".mbsc-fr-hdr",t).addClass("popover-title popover-header"),ve(".mbsc-fr-arr-i",t).addClass("popover"),ve(".mbsc-fr-arr",t).addClass("arrow"),ve(".mbsc-fr-btn",t).addClass("btn btn-default btn-secondary"),ve(".mbsc-fr-btn-s .mbsc-fr-btn",t).removeClass("btn-default btn-secondary").addClass("btn btn-primary"),s.addClass("nav nav-tabs"),s.find(".mbsc-cal-tab").addClass("nav-item"),s.find("a").addClass("nav-link"),s.find(".mbsc-cal-tab.active .nav-link").addClass("active"),ve(".mbsc-cal-picker",t).addClass("popover"),ve(".mbsc-range-btn",t).addClass("btn btn-sm btn-small btn-default"),ve(".mbsc-np-btn",t).addClass("btn btn-default"),ve(".mbsc-sel-filter-cont",t).removeClass("mbsc-input"),ve(".mbsc-sel-filter-input",t).addClass("form-control")},onTabChange:function(e,t){ve(".mbsc-cal-tabs .nav-link",t._markup).removeClass("active"),ve(".mbsc-cal-tab.active .nav-link",t._markup).addClass("active")},onPosition:function(e){setTimeout(function(){ve(".mbsc-fr-bubble-top, .mbsc-fr-bubble-top .mbsc-fr-arr-i",e.target).removeClass("bottom bs-popover-bottom").addClass("top bs-popover-top"),ve(".mbsc-fr-bubble-bottom, .mbsc-fr-bubble-bottom .mbsc-fr-arr-i",e.target).removeClass("top bs-popover-top").addClass("bottom  bs-popover-bottom")},10)}},G.scroller.bootstrap=re({},G.frame.bootstrap,{dateDisplay:"Mddyy",btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left5",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right5",btnPlusClass:"mbsc-ic mbsc-ic-arrow-down5 btn-light",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up5 btn-light",selectedLineHeight:!0,onEventBubbleShow:function(e){var t=ve(e.eventList);ve(".mbsc-cal-event-list",t).addClass("list-group"),ve(".mbsc-cal-event",t).addClass("list-group-item")}}),G.navigation.bootstrap={wrapperClass:"popover panel panel-default",groupClass:"btn-group",activeClass:"btn-primary",disabledClass:"disabled",itemClass:"btn btn-default"},G.form.bootstrap={},w.customTheme("mobiscroll-dark","mobiscroll");var Q,Fe,$e=w.themes;$e.frame.material={headerText:!1,btnWidth:!1,deleteIcon:"material-backspace",onMarkupReady:function(e){K(ve(e.target),".mbsc-fr-btn-e","mbsc-disabled","mbsc-fr-btn-nhl")}},$e.scroller.material=re({},$e.frame.material,{showLabel:!1,selectedLineBorder:2,weekDays:"min",icon:{filled:"material-star",empty:"material-star-outline"},checkIcon:"material-check",btnPlusClass:"mbsc-ic mbsc-ic-material-keyboard-arrow-down",btnMinusClass:"mbsc-ic mbsc-ic-material-keyboard-arrow-up",btnCalPrevClass:"mbsc-ic mbsc-ic-material-keyboard-arrow-left",btnCalNextClass:"mbsc-ic mbsc-ic-material-keyboard-arrow-right"}),$e.listview.material={leftArrowClass:"mbsc-ic-material-keyboard-arrow-left",rightArrowClass:"mbsc-ic-material-keyboard-arrow-right",onItemActivate:function(e){J(ve(e.target),e.domEvent)},onItemDeactivate:function(){Z(Fe)},onSlideStart:function(e){ve(".mbsc-ripple",e.target).remove()},onSortStart:function(e){ve(".mbsc-ripple",e.target).remove()}},$e.navigation.material={onInit:function(){K(ve(this),".mbsc-ms-item.mbsc-btn-e","mbsc-disabled","mbsc-btn-nhl")},onMarkupInit:function(){ve(".mbsc-ripple",this).remove()},onDestroy:function(){ve(this).off(".mbsc-ripple")}},$e.form.material={addRipple:function(e,t){J(e,t)},removeRipple:function(){Z(Fe)}},w.customTheme("material-dark","material"),w.customTheme("ios-dark","ios");var Ne=w.themes;Ne.frame.windows={headerText:!1,deleteIcon:"backspace4",btnReverse:!0},Ne.scroller.windows=re({},Ne.frame.windows,{rows:6,minWidth:88,height:44,btnPlusClass:"mbsc-ic mbsc-ic-arrow-down5",btnMinusClass:"mbsc-ic mbsc-ic-arrow-up5",checkIcon:"material-check",dateDisplay:"MMdyy",showLabel:!1,showScrollArrows:!0,btnCalPrevClass:"mbsc-ic mbsc-ic-arrow-left5",btnCalNextClass:"mbsc-ic mbsc-ic-arrow-right5",dayNamesShort:["Su","Mo","Tu","We","Th","Fr","Sa"],useShortLabels:!0}),Ne.form.windows={},w.customTheme("windows-dark","windows");var Xe=w.themes,Re="mobiscroll";return"android"==a?Re="material":"ios"==a?Re="ios":"wp"==a&&(Re="windows"),ve.each(Xe.frame,function(e,t){if(Re&&t.baseTheme==Re&&e!=Re+"-dark")return w.autoTheme=e,!1;e==Re&&(w.autoTheme=e)}),w});
