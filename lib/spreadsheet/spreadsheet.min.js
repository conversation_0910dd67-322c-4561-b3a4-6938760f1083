/**
 * @license
 * Webix SpreadSheet v.7.3.7
 * This software is covered by Webix Commercial License.
 * Usage without proper license is prohibited.
 * (c) XB Software Ltd.
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t():"function"==typeof define&&define.amd?define(t):t()}(0,function(){"use strict";function l(e){var t=new Date(e.getFullYear(),0,0),n=e-t+60*(t.getTimezoneOffset()-e.getTimezoneOffset())*1e3;return Math.floor(n/864e5)}function w(e){var t=new Date(Math.round(86400*(e-25569)*1e3));return new Date(t.getTime()+6e4*t.getTimezoneOffset())}function o(e){return(25569+(e.getTime()-6e4*e.getTimezoneOffset())/864e5).toString()}function s(e){return!(!e&&0!==e||(e*=1,isNaN(e)))&&e}function a(e){for(var t=[],n=0;n<e.length;n++)t=t.concat(e[n]);return t}function t(e){return!e&&0!==e||e instanceof Date||isNaN(e)||(e=w(e)),e}function n(e){return e&&e instanceof Date&&(e=o(e)),e}function g(e){return(g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function r(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function c(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&f(e,t)}function d(e){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function f(e,t){return(f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function v(e,t){return!t||"object"!=typeof t&&"function"!=typeof t?function n(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function S(e,t){return function n(e){if(Array.isArray(e))return e}(e)||function c(e,t){var n=[],i=!0,o=!1,r=undefined;try{for(var a,l=e[Symbol.iterator]();!(i=(a=l.next()).done)&&(n.push(a.value),!t||n.length!==t);i=!0);}catch(s){o=!0,r=s}finally{try{i||null==l["return"]||l["return"]()}finally{if(o)throw r}}return n}(e,t)||function i(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function k(e){return function i(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function t(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function n(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}webix.i18n.spreadsheet={labels:{common:"Common",currency:"Currency",number:"Number",percent:"Percent",date:"Date",string:"Text","custom-format":"Custom","decimal-places":"Decimal places",separator:"Group separator",negative:"Negative number","date-format":"Date format","format-docs":"How to create a format","undo-redo":"Undo/Redo",font:"Font",text:"Text",cell:"Cell",align:"Align",format:"Number",column:"Column",borders:"Borders",px:"px",apply:"Apply",cancel:"Cancel",save:"Save",sheet:"Sheet","conditional-format":"Conditional Format",condition:"Condition","conditional-style":"Style","conditional-operator":"Operator","conditional-between":"between","conditional-not-equal":"not equal",range:"Range","range-title":"Named ranges","range-name":"Name","range-cells":"Range","image-or":"or","image-title":"Add image","image-upload":"Select file for upload","image-url":"URL (e.g. http://*)","sparkline-title":"Add sparkline","sparkline-type":"Type","sparkline-range":"Range","sparkline-color":"Color","sparkline-positive":"Positive","sparkline-negative":"Negative","format-title":"Set format","format-pattern":"Format pattern","dropdown-empty":"Empty cells","dropdown-title":"Add dropdown","dropdown-range":"Range",ok:"OK","import-title":"Import",
"import-not-support":"Sorry, your browser does not support import","export-title":"Export","export-name":"Name of xslx file","export-all-sheets":"Export all sheets","link-title":"Add Link","link-name":"Text","link-url":"URL",image:"Image","add-image-cell":"Add to cell","add-image-top":"Add above cells",graph:"Graph","add-sparkline":"Add to cell","add-chart":"Add above cells",display:"Display",value:"Value","range-remove-confirm":"Are you sure you want to remove the range permanently?","sheet-remove-confirm":"Are you sure you want to remove the sheet permanently?","merge-cell-confirm":"Only the left top value will remain after merging. Continue?","error-range":"The range is incorrect!",print:"Print","print-title":"Before you print..","print-settings":"General settings","print-paper":"Paper size","print-layout":"Layout","current-sheet":"Current sheet","all-sheets":"All sheets",selection:"Selected cells",borderless:"Hide gridlines","sheet-names":"Show sheet names","skip-rows":"Skip empty rows",margin:"Hide margins","page-letter":"Letter","page-a4":"A4 (210x297mm)","page-a3":"A3 (297x420mm)","page-width":"Page width","page-actual":"Actual Size","page-portrait":"Portrait","page-landscape":"Landscape",comment:"Comment",width:"Width",height:"Height","fit-content":"Fit to content","edit-view":"Edit","remove-view":"Remove","line-chart":"Line","area-chart":"Area","bar-chart":"Bar","donut-chart":"Donut","pie-chart":"Pie","radar-chart":"Radar","spline-chart":"Spline","splinearea-chart":"Spline Area","stacked-chart":"Stacked","chart-range":"Range","chart-type":"Type","chart-config":"Add chart","chart-col-axis":"Use left column as xAxis","chart-row-legend":"Use top row as legend","chart-col-legend":"Use left column as legend","chart-pie-text":"Show values inside"},tooltips:{color:"Font color",background:"Background color","font-family":"Font family","font-size":"Font size","text-align":"Horizontal align","vertical-align":"Vertical align",borders:"Borders","borders-no":"Clear borders","borders-left":"Left border","borders-top":"Top border","borders-right":"Right border","borders-bottom":"Bottom border","borders-all":"All borders","borders-outer":"Outer borders","borders-top-bottom":"Top and bottom borders","borders-color":"Border color","align-left":"Left align","align-center":"Center align","align-right":"Right align","align-top":"Top align","align-middle":"Middle align","align-bottom":"Bottom align",span:"Merge",wrap:"Text wrap",undo:"Undo",redo:"Redo",format:"Number format","increase-decimals":"Increase decimal places","decrease-decimals":"Decrease decimal places","font-weight":"Bold","font-style":"Italic","text-decoration":"Underline","hide-gridlines":"Hide/show gridlines","hide-headers":"Hide/show headers","create-filter":"Create/remove filters","freeze-columns":"Freeze/unfreeze columns","add-range":"Set name for the selected range",conditional:"Conditional formatting","add-sheet":"Add Sheet","lock-cell":"Lock/unlock cell",clear:"Clear","add-link":"Add link",row:"Rows",column:"Columns",sheet:"Sheet","add-image":"Image","add-sparkline":"Graph","add-comment":"Comment"},menus:{"remove-sheet":"Remove sheet","rename-sheet":"Rename sheet",file:"File","new":"New","new-sheet":"New sheet","excel-import":"Import from Excel","excel-export":"Export to Excel",sheet:"Sheets","copy-sheet":"Copy to new sheet",edit:"Edit",undo:"Undo",redo:"Redo",columns:"Columns","insert-column":"Insert column","delete-column":"Delete column","show-column":"Show column","hide-column":"Hide column","resize-column":"Resize column",rows:"Rows","insert-row":"Insert row","delete-row":"Delete row","show-row":"Show row","hide-row":"Hide row","resize-row":"Resize row",insert:"Insert","conditional-format":"Conditional format",clear:"Clear","clear-value":"Values","clear-style":"Styles","clear-conditional-formats":"Conditional formats","clear-dropdown-editors":"Dropdowns and filters","clear-comments":"Comments","clear-all":"All",image:"Image","add-image-cell":"Add to cell","add-image-top":"Add above cells",graph:"Graph","add-sparkline":"Add to cell",
"add-chart":"Add above cells",data:"Data","add-link":"Add link","add-range":"Named ranges",sort:"Sort","sort-asc":"Sort A to Z","sort-desc":"Sort Z to A",view:"View","freeze-columns":"Freeze/unfreeze columns","freeze-rows":"Freeze/unfreeze rows","hide-gridlines":"Hide/show gridlines","hide-headers":"Hide/show headers","create-filter":"Create/remove filters","add-dropdown":"Add dropdown","lock-cell":"Lock/unlock cell",print:"Print","add-comment":"Comment"},table:{"math-error":"ERROR","format-error":"INCORRECT FORMAT"},liveEditor:{edit:"Edit:"},formats:{dateFormat:"mm/dd/yyyy",timeFormat:"hh:mm AM/PM",longDateFormat:"dd mmmm yyyy",fullDateFormat:"mm/dd/yyyy hh:mm AM/PM",parseDateTime:"%m/%d/%Y %G:%i:%s",parseDate:"%m/%d/%Y"}},webix.protoUI({name:"ssheet-align",$cssName:"richselect",$init:function(e){e.options={view:"datasuggest",body:{view:"ssheet-icons",tooltip:{template:"#tooltip#"},xCount:3,yCount:1},data:e.data}}},webix.ui.richselect),webix.protoUI({name:"ssheet-borders-suggest",defaults:{width:300},$init:function(e){e.body={margin:6,cols:[{view:"ssheet-icons",scroll:!1,select:!0,xCount:4,yCount:2,tooltip:{template:function(e){return webix.i18n.spreadsheet.tooltips["borders-"+e.id]}},on:{onAfterSelect:function(){this.getParentView().getParentView().updateMasterValue(!0)}},template:function(e){return"<span class='"+("webix_ssheet_button_icon webix_ssheet_icon ssi-borders-"+e.value)+"'></span>"},data:e.data},{view:"ssheet-separator"},{rows:[{view:"ssheet-color",css:e.css,name:e.name,width:68,value:"#000000",tooltip:webix.i18n.spreadsheet.tooltips["borders-color"],title:"<span class='webix_icon wxi-pencil'></span>",on:{onChange:function(){this.getParentView().getParentView().getParentView().updateMasterValue(!1)}}},{}]}]}},updateMasterValue:function(e){var t=this.getValue();webix.$$(this.config.master).setValue(t),e&&this.hide()},setValue:function(e){e[0]&&this.getList().select(e[0]),e[1]&&this.getColorView().setValue(e[1])},getValue:function(){return[this.getList().getSelectedId(),this.getColorView().getValue()||""]},getList:function(){return this.getBody().getChildViews()[0]},getColorView:function(){return this.getBody().getChildViews()[2].getChildViews()[0]},getItemText:function(){return"<span class='webix_ssheet_button_icon webix_ssheet_icon ssi-borders-all'>"}},webix.ui.suggest),webix.protoUI({name:"ssheet-borders",$cssName:"richselect",$init:function(e){e.options={view:"ssheet-borders-suggest",fitMaster:!1,data:e.data},this.$ready.push(webix.bind(function(){this.getPopup().config.master=this.config.id},this))},setValue:function(e){return webix.isArray(e)&&(this.config.value&&e[0]==this.config.value[0]&&e[1]==this.config.value[1]||this.getPopup().setValue(e),(this.config.value=e)[0]&&this.callEvent("onChange")),e},getValue:function(){return this.getPopup().getValue().join(",")},getList:function(){return this.getPopup().getBody().getChildViews()[0]},getColorView:function(){return this.getPopup().getBody().getChildViews()[1].getChildViews()[0]}},webix.ui.richselect),webix.protoUI({$cssName:"colorboard",name:"ssheet-colorboard",defaults:{css:"webix_ssheet_colorboard",palette:[["#000000","#434343","#666666","#999999","#b7b7b7","#cccccc","#d9d9d9","#efefef","#f3f3f3","#ffffff"],["#980000","#ff0000","#ff9900","#ffff00","#00ff00","#00ffff","#4a86e8","#0000ff","#9900ff","#ff00ff"],["#e6b8af","#f4cccc","#fce5cd","#fff2cc","#d9ead3","#d0e0e3","#c9daf8","#cfe2f3","#d9d2e9","#ead1dc"],["#dd7e6b","#ea9999","#f9cb9c","#ffe599","#b6d7a8","#a2c4c9","#a4c2f4","#9fc5e8","#b4a7d6","#d5a6bd"],["#cc4125","#e06666","#f6b26b","#ffd966","#93c47d","#76a5af","#6d9eeb","#6fa8dc","#8e7cc3","#c27ba0"],["#a61c00","#cc0000","#e69138","#f1c232","#6aa84f","#45818e","#3c78d8","#3d85c6","#674ea7","#a64d79"],["#85200c","#990000","#b45f06","#bf9000","#38761d","#134f5c","#1155cc","#0b5394","#351c75","#741b47"],["#5b0f00","#660000","#783f04","#7f6000","#274e13","#0c343d","#1c4587","#073763","#20124d","#4c1130"]]}},webix.ui.colorboard),webix.protoUI({$cssName:"richselect",name:"ssheet-color",defaults:{css:"webix_ssheet_color",icon:"wxi-menu-down",suggest:{
borderless:!0,body:{view:"ssheet-colorboard",height:202,on:{onSelect:function(e){this.getParentView().setMasterValue({value:e})}}}}},$init:function(){this.$view.className+=" webix_ssheet_color"},$renderInput:function(o,e,t){var r=this.renderColor.call(this);return e=e.replace(/([^>]>)(.*)(<\/div)/,function(e,t,n,i){return t+o.title+r+i}),webix.ui.colorpicker.prototype.$renderInput.call(this,o,e,t)},$setValue:function(e){e=e||"",webix.$$(this.config.popup.toString()).getBody().setValue(e),this.config.value=e,this.getColorNode().style.backgroundColor=e},renderColor:function(){return"<div class='webix_ssheet_cp_color' style='background-color:"+this.config.value+";'> </div>"},getColorNode:function(){return this.$view.firstChild.firstChild.childNodes[1]},$renderIcon:function(){return webix.ui.text.prototype.$renderIcon.apply(this,arguments)}},webix.ui.colorpicker),webix.protoUI({$cssName:"datepicker",name:"ssheet-datepicker",getValue:function(){return n(webix.ui.datepicker.prototype.getValue.apply(this))||""},$prepareValue:function(e){return e=t(e),webix.ui.datepicker.prototype.$prepareValue.apply(this,[e])}},webix.ui.datepicker),webix.protoUI({$cssName:"daterangepicker",name:"ssheet-daterangepicker",getValue:function(){var e=webix.ui.datepicker.prototype.getValue.apply(this);return e&&((e=webix.copy(e)).start=n(e.start),e.end=n(e.end)),e||""},$prepareValue:function(e){return e?e.start||e.end||(e={start:e}):e={start:null,end:null},e.start=t(e.start),e.end=t(e.end),webix.ui.daterangepicker.prototype.$prepareValue.apply(this,[e])}},webix.ui.daterangepicker),webix.protoUI({$cssName:"window",name:"ssheet-dialog",$init:function(e){this.$view.className+=" webix_ssheet_dialog",this.config.buttons=e.buttons},getHeaderConfig:function(e){return{paddingX:10,paddingY:5,height:60,cols:[{view:"label",label:e},{view:"icon",css:"webix_ssheet_hide_icon",icon:"webix_ssheet_icon ssi-close",width:50,click:function(){this.getTopParentView().callEvent("onHideClick",[])}}]}},getBodyConfig:function(e){return e.borderless=!0,{view:"form",css:"webix_ssheet_form",paddingY:0,elements:this.getFormElements(e)}},getFormElements:function(e){var t;return webix.isArray(e)?t=e:(t=[]).push(e),this.config.buttons&&(t.push({height:1}),t.push({margin:10,cols:[{},{view:"button",css:"ssheet_cancel_button",label:webix.i18n.spreadsheet.labels.cancel,autowidth:!0,click:function(){this.getTopParentView().callEvent("onCancelClick",[])}},{view:"button",label:webix.i18n.spreadsheet.labels.save,autowidth:!0,click:function(){this.getTopParentView().callEvent("onSaveClick",[])}}]})),t.push({height:10,borderless:!0}),t},body_setter:function(e){return"object"==g(e)&&(e.paddingY=e.paddingY||0,"form"==e.view&&e.elements?(e.elements=this.getFormElements(e.elements),e.css||(e.css="webix_ssheet_form")):e=this.getBodyConfig(e)),webix.ui.window.prototype.body_setter.call(this,e)},head_setter:function(e){return e&&(e=this.getHeaderConfig(e)),webix.ui.window.prototype.head_setter.call(this,e)},defaults:{padding:0,move:!0,head:!0,buttons:!0,autofocus:!1,width:350}},webix.ui.window,webix.IdSpace),webix.protoUI({$cssName:"datatable",name:"ssheet-dialog-table",$init:function(e){e.headerRowHeight||(e.headerRowHeight=34),this.$view.className+=" webix_ssheet_table webix_ssheet_dialog_table"}},webix.ui.datatable),webix.protoUI({name:"ssheet-form-popup",defaults:{padding:0,borderless:!0},$init:function(){this.$view.className+=" webix_ssheet_form"}},webix.ui.suggest),webix.protoUI({name:"ssheet-form-suggest",defaults:{padding:0,borderless:!0},$init:function(){this.$view.className+=" webix_ssheet_suggest"}},webix.ui.suggest),webix.protoUI({$cssName:"colorpicker",name:"ssheet-colorpicker",$init:function(){this.$view.className+=" webix_ssheet_colorpicker"},defaults:{icon:"wxi-menu-down",suggest:{borderless:!0,body:{view:"ssheet-colorboard",height:202,on:{onSelect:function(e){this.getParentView().setMasterValue({value:e})}}}}}},webix.ui.colorpicker),webix.protoUI({name:"formlate",setValue:function(e){return this.setHTML(e)},getValue:function(){return""}},webix.ui.template),webix.protoUI({
name:"ssheet-icons",$cssName:"dataview",$init:function(){this.$view.className+=" webix_ssheet_dataview"},defaults:{borderless:!0,template:"<span class='webix_ssheet_button_icon #css#' ></span>",type:{width:36,height:36}}},webix.ui.dataview),webix.protoUI({name:"ssheet-suggest",defaults:{padding:0,css:"webix_ssheet_suggest"}},webix.ui.contextmenu),webix.protoUI({name:"suggest-formula",defaults:{fitMaster:!1,width:200,filter:function(e,t){var n=webix.$$(this.config.master),i=n.getInputNode().selectionStart,o=n.getValue();if("="===o.charAt(0)){var r=o.substring(0,i).match(/([a-zA-Z]+)$/),a=o.charAt(i).search(/[^A-Za-z0-9]/);return!r||i!==o.length&&0!==a||(t=r[0]),0===e.value.toString().toLowerCase().indexOf(t.toLowerCase())}}},$init:function(){var s=this;this.attachEvent("onBeforeShow",function(e){if(e.tagName){var t=webix.$$(s.config.master),n=t.getValue();if(!n||"="!==n.charAt(0))return!1;var i=webix.html.offset(e),o=t.getInputNode().selectionStart,r=webix.html.getTextSize(n.substring(0,o),"webix_ssheet_formula").width,a=i.y+i.height,l=i.x+r;return webix.ui.popup.prototype.show.apply(s,[{x:l,y:a}]),!1}})},setMasterValue:function(e,t){var n=e.id?this.getItemText(e.id):e.text||e.value;webix.$$(this.config.master).setValueHere(n),t||this.hide(!0),this.callEvent("onValueSuggest",[e,n])},$enterKey:function(){if(this.isVisible())return webix.ui.suggest.prototype.$enterKey.apply(this,arguments)}},webix.ui.suggest);var m={value:null,set:function(e,t){this.value?e.call(t):(this.start(),e.call(t),this.end())},start:function(){this.value=webix.uid()},end:function(){this.value=null}};function p(e,t,n){var i,o=e.config.save;if(o){if("all"==t){if(!o.all)return;n={data:e.serialize({sheets:!0})},i=o.all}else i="string"==typeof o?o+"/"+t:o[t];if(i=webix.proxy.$parse(i)){if("function"==typeof i)return i(t,n);if(i.$proxy&&i.save)return i.save(e,n,null,null);var r=webix.ajax();"all"===t&&r.headers({"Content-type":"application/json"}),r.post(i,n)}}}for(var b=Object.freeze({init:function Kr(o){for(var r,t=[{name:"onSheetAdd",type:"insert"},{name:"onSheetRemove",type:"remove"},{name:"onSheetRename",type:"rename"},{name:"onCellChange"},{name:"onStyleChange"},{name:"onAction"},{name:"onRowOperation"},{name:"onColumnOperation"},{name:"onAfterConditionSet"},{name:"onAfterRangeSet"},{name:"onAfterSpan"},{name:"onAfterSplit"},{name:"onAfterFilter",view:"cells"},{name:"onRowResize",view:"cells"},{name:"onColumnResize",view:"cells"}],e=function(e){var i=t[e];(i.view?o.$$(i.view):o).attachEvent(i.name,function(e,t){var n;o._loading_data||(n=i.type?"rename"==i.type?[i.type,t,e]:[i.type,e]:["update",o.getActiveSheet()],clearTimeout(r),r=webix.delay(function(){return o.callEvent("onChange",n)}))})},n=0;n<t.length;n++)e(n);o.attachEvent("onChange",function(){p(o,"all")})},save:p}),x={},$={},e="ABCDEFGHIJKLMNOPQRSTUVWXYZ",_=1;_<1e3;_++){var y=parseInt((_-1)/e.length),C=(y?e[y-1]:"")+e[(_-1)%e.length];x[C]=_,$[_]=C}function E(e){var t=e.charCodeAt(1)<59?1:2,n=e.substr(0,t);return[1*e.substr(t),x[n]]}function V(e,t){var n=e.indexOf("!"),i="";-1!==n&&("'"===(i=e.substr(0,n))[0]&&(i=i.substr(1,i.length-2)),e=e.substr(n+1));var o=e.split(":");if(2!=o.length&&t){if(!(e=t.ranges.getCode(e,i)))return null;o=e.split(":")}var r=E(o[0]),a=E(o[1]);return[r[0],r[1],a[0],a[1],i]}$[0]=$[1];var z=/[^A-Za-z0-9]/;function R(e){return z.test(e)?"'".concat(e,"'"):e}function I(e,t,n,i,o){return(o?R(o)+"!":"")+$[t]+e+":"+$[i]+n}function A(e,t){if("object"===g(e))return e;var n=V(e,t);return{start:{row:n[0],column:n[1]},end:{row:n[2],column:n[3]},sheet:n[4]}}function F(e,t,n,i){for(var o=A(e),r=o.start.row;r<=o.end.row;r++)for(var a=o.start.column;a<=o.end.column;a++)n(t,{row:r,column:a},i)}var O=/(([A-Za-z0-9]+|'[^']+')!|)[A-Z]+[0-9]+:[A-Z]+[0-9]+/;function M(e){return O.test(e)}function D(e,t,n,i){var o=!1;if(M(e)){var r=S(V(e),5),a=r[0],l=r[1],s=r[2],c=r[3],u=r[4];if(s<a){var h=[s,a];a=h[0],s=h[1]}if(c<l){var d=[c,l];l=d[0],c=d[1]}if("row"===t&&i.row<=s){if((n<0&&i.row<a||0<n&&i.row<=a)&&(a+=n),(s+=n)<a)return"";o=!0}else if("column"===t&&i.column<=c){if((
n<0&&i.column<l||0<n&&i.column<=l)&&(l+=n),(c+=n)<l)return"";o=!0}o&&(e=I(a,l,s,c,u))}return e}var N={width:38,margin:7,paddingY:1,sectorPadding:5,sectorMargin:0},U={color:"#666666",background:"#ffffff","font-family":"'PT Sans', Tahoma","font-size":"15px","text-align":"left","vertical-align":"middle","white-space":"nowrap",format:"common"};var T,j=["color","background","text-align","font-family","font-size","font-style","text-decoration","font-weight","vertical-align","wrap","borders","format","border-right","border-bottom","border-left","border-top"],B={"text-align":{left:function(){return"justify-content:flex-start;"},center:function(){return"justify-content:center;"},right:function(){return"justify-content:flex-end;"}},"vertical-align":{top:function(){return"align-items:flex-start;"},middle:function(){return"align-items:center;"},bottom:function(){return"align-items:flex-end;"}},wrap:{wrap:function(){return"white-space: normal !important;"}},"border-left":function(e){return e["border-left"]?"border-left: 1px solid "+e["border-left"]+" !important;":""},"border-top":function(e){return e["border-top"]?"border-top: 1px solid "+e["border-top"]+" !important;":""},"border-right":function(e){return e["border-right"]?"border-right: 1px solid "+e["border-right"]+" !important;":""},"border-bottom":function(e){return e["border-bottom"]?"border-bottom: 1px solid "+e["border-bottom"]+" !important;":""},"font-weight":function(e){return"bold"!=e["font-weight"]?"":"font-weight:"+("'Roboto', sans-serif"==(e["font-family"]||U["font-family"])?500:700)+";"}},H={"border-left":function(e,t,n){return e.column==t.start.column||"no"==n},"border-right":function(e,t,n){return e.column==t.end.column||"all"==n||"no"==n},"border-top":function(e,t,n){return e.row==t.start.row||"no"==n},"border-bottom":function(e,t,n){return e.row==t.end.row||"all"==n||"no"==n}},P={borders:function(e,t,n,i){var o=e.$$("cells").getSelectArea(),r=(n=n.split(","))[0],a=n[1],l=["border-left","border-right","border-bottom","border-top"];"no"==r?a="":"top-bottom"==r?l=["border-top","border-bottom"]:"all"!=r&&"outer"!=r&&(l=["border-"+r]);for(var s=0;s<l.length;s++){var c=l[s];(!0===e.callEvent("onAction",["check-borders",{row:i.row,column:i.column,area:o,type:r,mode:c}])||H[c](i,o,r))&&(t=q(e,t,c,a,i))}return t}};function L(e){e._styles={},e._styles_cache={},e._styles_max=1;var t=".wss_"+e.$index;webix.html.removeStyle(t)}function W(e,t){var n=e.getRow(t.row).$cellCss;if(n){var i=n[t.column];if(i)return e._styles[i]}return null}function K(e,t,n){var i={props:ne(";;;;;;;;;;;;;;;")};if(n)for(var o in n.props)i.props[o]=n.props[o];for(var r in t)i.props[r]=t[r];i.text=te(i);var a=e._styles_cache[i.text];return a||(ee(e,i),i)}function Y(e,t){if(t.styles)for(var n=0;n<t.styles.length;n++){var i=t.styles[n];ee(e,{id:i[0],text:i[1],props:ne(i[1])},!0)}for(var o=0;o<t.data.length;o++){var r=S(t.data[o],4),a=r[0],l=r[1],s=r[3];s&&G(e,a,l,e._styles[s])}}function q(e,t,n,i,o){if(P[n])return P[n](e,t,i,o);if(t&&t.props[n]==i)return t;var r={text:"",id:0,props:t?webix.copy(t.props):{}};r.props[n]=i,r.text=te(r);var a=e._styles_cache[r.text];return a||(ee(e,r),r)}function G(e,t,n,i){var o=e.getRow(t);o.$cellCss=o.$cellCss||{},o.$cellFormat=o.$cellFormat||{},i?(o.$cellCss[n]=i.id,o.$cellFormat[n]=i.props.format||null):(delete o.$cellCss[n],delete o.$cellFormat[n])}function Z(e,t,n){return J(e,t,n,W(e,t))}function X(e,t,n){F(t,e,J,n)}function Q(e,t,n,i){T.innerHTML=t,T.style.width=i?i+"px":"auto",T.className="webix_table_cell webix_cell "+n,e._table.$view.appendChild(T);var o=Math.max(0,T.offsetWidth+1),r=Math.max(0,T.offsetHeight+1);return e._table.$view.removeChild(T),T.innerHTML="",{width:o,height:r}}function J(i,o,r,a){m.set(function(){if(i.callEvent("onBeforeStyleChange",[o.row,o.column,r,a])){var e=r&&r.props.format||null,t=a&&a.props.format||null,n=e!=t;(!n||n&&i.callEvent("onBeforeFormatChange",[o.row,o.column,e,t]))&&(G(i,o.row,o.column,r),i.callEvent("onStyleChange",[o.row,o.column,r,a]),n&&i.callEvent("onFormatChange",[o.row,o.column,e,t]),i.saveCell(o.row,o.column))}})}
function ee(e,t,n){for(e._styles_cache[t.text]=t;!t.id||e._styles[t.id];)t.id="wss"+e._styles_max++;var i=function r(e){var t="";for(var n in e)e[n]&&(B[n]?B[n][e[n]]?t+=B[n][e[n]](e):"function"==typeof B[n]&&(t+=B[n](e)):t+=n+":"+e[n]+";");return t}((e._styles[t.id]=t).props),o=".wss_"+e.$index;webix.html.addStyle(o+" ."+t.id+"{"+i+"}",o),n||p(e,"styles",{name:t.id,text:t.text})}function te(e){for(var t=[],n=0;n<j.length;n++)t.push(e.props[j[n]]);return t.join(";")}function ne(e){for(var t=e.split(";"),n={},i=0;i<j.length;i++)n[j[i]]=t[i];return n}function ie(e,t){m.set(function(){F(t,e,function(e,t){W(e,t)&&Z(e,t,null)})})}function oe(e){for(var t=e.serialize(),n=t.data,i=t.styles,o={},r=0;r<n.length;r++){var a=n[r][3];a&&(o[a]=1)}for(var l=[],s=0;s<i.length;s++){o[i[s][0]]&&l.push(i[s])}t.styles=l,L(e),Y(e,t)}var re=Object.freeze({style_names:j,init:function Yr(r){r.attachEvent("onStyleSet",function(e,t){return function o(t,n,i){m.set(function(){t.eachSelectedCell(function(e){!function a(e,t,n,i){var o=W(e,t),r=q(e,o,n,i,t);r&&r!=o&&J(e,t,r,o)}(t,e,n,i)})}),t.refresh()}(r,e,t)}),r.attachEvent("onDataParse",function(e){return Y(r,e)}),r.attachEvent("onDataSerialize",function(e){return function o(e,t){var n=[];for(var i in e._styles_cache)n.push([e._styles_cache[i].id,i]);t.styles=n}(r,e)}),r.attachEvent("onReset",function(){return L(r)}),r.attachEvent("onUndo",function(e,t,n,i){"style"==e&&function o(e,t,n,i){Z(e,{row:t,column:n},i)}(r,t,n,i)}),L(r),T||(T=webix.html.create("DIV",{style:"visibility:hidden; position:absolute; top:0px; left:0px; height:auto;"},""))},getStyle:W,addStyle:K,setStyle:Z,setRangeStyle:X,getTextSize:Q,clearRangeStyle:ie,compactStyles:oe});var ae,le,se,ce,ue={};function he(){var n=webix.i18n.spreadsheet.formats;for(var e in ae={},le=0,se={},ce={},ue={price:{getFormat:function(e,t){return t.css="webix_ssheet_format_price",webix.i18n.priceFormat(e)},values:webix.extend({zeros:webix.i18n.priceSettings.decimalSize,symbol:webix.i18n.priceFormat(""),separator:1,negative:1,type:"price"},de("price"))},"int":{getFormat:function(e,t){return t.css="webix_ssheet_format_int",webix.i18n.numberFormat(e)},values:webix.extend({zeros:webix.i18n.decimalSize,separator:1,negative:1,type:"int"},de("int"))},percent:{getFormat:function(e,t){return t.css="webix_ssheet_format_percent",Math.round(100*e)+"%"},values:webix.extend({zeros:0,separator:0,negative:1,type:"percent"},de("percent"))},date:{getFormat:function(e,t){return t.css="webix_ssheet_format_date",be(n.dateFormat,de("date"))(e)},values:{type:"date",date:n.dateFormat}},string:{getFormat:function(e,t){return t.css="webix_ssheet_format_text",e},values:{type:"string"}}})se[e]=Re(ze(e,ue[e].values)),ce[se[e]]=e}function de(e){return"price"==e?{groupSign:webix.i18n.priceSettings.groupDelimiter,decimalSign:webix.i18n.priceSettings.decimalDelimiter}:{groupSign:webix.i18n.groupDelimiter,decimalSign:webix.i18n.decimalDelimiter}}function fe(e){return ue[e]?ue[e].getFormat:ue[e]}function ve(e,t){return t?Re(Ie({values:ue[e].values,format:se[e]}),!0):se[e]}function me(e,t){if(ce[e])return ce[e];var n="object"==g(t)?"fmt"+le:t;return le++,ae[n]=e,t=webix.extend("object"==g(t)?t:{format:e,type:"custom"},de("custom")),ue[n]={getFormat:be(e,{decimalSign:t.decimalSign,groupSign:t.groupSign}),values:t},se[n]=e,ce[e]=n}function ge(e,t,n,i,o){o||(o={format:i,type:"custom"});var r=e.getStyle(t,n),a=K(e,{format:me(i,o)},r);e.setStyle(t,n,a)}function we(e,t,n){var i=K(e,{format:""},e.getStyle(t,n));e.setStyle(t,n,i)}function pe(e){var t=e.match(/.*\[[><=].*/g),n=e.split(";");return t||1<n.length&&(n[0]=(2<n.length?"[>0]":"[>=0]")+n[0],n[1]="[<0]"+n[1],n[2]&&(n[2]="[=0]"+n[2])),n}function be(e,t){for(var n=webix.i18n.spreadsheet.table["format-error"],i=pe(e),o=['var spaces = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";'],r=0;r<i.length;r++){var a=Fe(i[r]);o.push(xe(a.condition,a.color.toLowerCase(),a.fmt,r,0<i.length,t))}return new Function("val","extra","try{ ".concat(o.join("\n"),' return val; } catch(err) { return "').concat(n,'"; }'))}function xe(e,t,
n,i,o,r){var a="";return e&&(a+=i?"else if":"if","="===e[0]&&(e="="+e),a+="(val"+e+"){",o&&(a+="val = Math.abs(val);")),t&&(a+='extra.css = "webix_ssheet_format_'+t+'";'),(a+=function g(e,t){if(!e)return"return val;";for(var n,i,o,r,a='return ""',l="",s=1,c=[],u="date",h=0;h<e.length;h++){var d=Oe(e[h-1])&&Oe(e[h+1]);if('"'!=e[h]){if(webix.isUndefined(n))if("%"==e[h]&&(s*=100),/[a-zA-Z]/.test(e[h])){var f=Se(e.substr(h),u);if(f){if(o||r)return'throw "unexpected number placeholder";';c.push(f.format),a+="/*date*/",u=f.type,h+=f.length-1;continue}a+='+"'.concat(e[h],'"')}else if(e[h]===t.decimalSign&&d)l+=t.decimalSign;else if("@"==e[h]){if(o||c.length)return'throw "unexpected number placeholder";';r||(a+="+fmt",r=!0),l+=e[h]}else if(Oe(e[h])){if(r||c.length)return'throw "unexpected text placeholder";';o||(a+="+fmt",o=!0),l+=e[h]}else e[h]===t.groupSign&&d?i=!0:a+='+"'.concat(e[h],'"')}else n=webix.isUndefined(n)?h:(a+='+"'.concat(e.substr(n+1,h-n-1),'"'),undefined)}if(c.length){-1!=c.indexOf("%A")&&(c=c.map(function(e){switch(e){case"%G":return"%g";case"%H":return"%h";default:return e}}));for(var v=0;v<c.length;v++)a=a.replace("/*date*/","+dateParts[".concat(v,"]"))}return"\n\t\t".concat(c.length?"val = (".concat(w,')(val); var dateParts = webix.Date.dateToStr("').concat(c.join(";"),'")(val).split(";");'):"","\n\t\t").concat(l?"".concat(function m(e,t,n,i){var o='if(/^@+$/.test("'.concat(e,'") || isNaN(val)){ var fmt = val; }else {'),r=n.decimalSign,a=S(Me(e,r),2),l=a[0],s=a[1];s=(s||"").split("").reverse().join("");var c=l.indexOf("0");0<=c&&(c=l.length-c);var u=l.indexOf("?");0<=u&&(u=l.length-u);var h=s.indexOf("0");0<=h&&(h=s.length-h);var d=s.indexOf("?");0<=d&&(d=s.length-d);var f=s.length;return(o+="\n\tval = val*".concat(i,";\n\tvar parts = val.toFixed(").concat(f,').split(".");\n\tvar left = parts[0];\n\tvar lsize = left.length; \n\tvar right = parts[1] || "";\n\tif (left.length < ').concat(c,') left = "0000000000".substr(0, ').concat(c," - left.length)+left;\n\tif (left.length < ").concat(u,") left = spaces.substr(0, 6*(").concat(u," - left.length))+left;\n\tif (").concat(t,') {\n\t\tvar buf = [];\n\t\tvar start = 3;\n\t\twhile (lsize > start) { buf.push(left.substr(left.length-start,3)); start+=3; }\n\t\tvar last = left.substr(0,left.length-start+3);\n\t\tif (last !== "-")\n\t\t\tbuf.push(last);\n\t\telse\n\t\t\tbuf.push("-"+buf.pop());\n\n\t\tleft = buf.reverse().join("').concat(n.groupSign,'");\n\t}\n\tif (right){\n\t\tvar zpoint = right.length-1;\n\t\twhile (zpoint >= ').concat(h,'){\n\t\t\tif (right[zpoint] !== "0"){\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\tzpoint--;\n\t\t}\n\n\t\tif (zpoint <= right.length)\n\t\t\tright = right.substr(0, zpoint+1);\n\n\t\tif (right.length < ').concat(d,"){\n\t\t\tright += spaces.substr(0, 6*(").concat(d,' - right.length));\n\t\t}\n\t}\n\tvar fmt = left+(right?"').concat(r,'":"")+right;\n\t'))+"}\n"}(l,i,t,s),";"):"","\n\t\t").concat(a,";")}(n,r))+(e?"}":"")}var _e={yy:"%y",yyyy:"%Y",m:"%n",mm:"%m",mmm:"%M",mmmm:"%F",mmmmm:"%F",d:"%j",dd:"%d",ddd:"%D",dddd:"%l"},ye={h:"%G",hh:"%H",m:"%i",mm:"%i",s:"%s",ss:"%s","AM/PM":"%A"},Ce=Object.keys(_e).sort(function(e,t){return t.length-e.length}),$e=Object.keys(ye).sort(function(e,t){return t.length-e.length});function Se(e,t){var n=[{name:"date",order:Ce,types:_e},{name:"time",order:$e,types:ye}];/^m{1,2}(?!m)/.test(e)&&"date"!=t&&n.reverse();for(var i=0;i<n.length;i++)for(var o=n[i],r=0;r<o.order.length;r++)if(0==e.indexOf(o.order[r]))return{format:o.types[o.order[r]],length:o.order[r].length,type:o.name}}function ke(t,n,e,i){var o=function c(e,t,n){{if(t&&n){if("object"!==g(t)||"object"!==g(n))return[{row:t,column:n}];for(var i=[],o=t.row;o<=n.row;o++)for(var r=t.column;r<=n.column;r++)i.push({row:o,column:r});return i}return e.getSelectedId(!0)}}(t,e,i);if(0!=o.length){var r=t.$$("cells").getItem(o[0].row)[o[0].column],a=r?r.toString():r,l=t.getStyle(o[0].row,o[0].column);if(webix.rules.isNumber(a)||l&&l.props.format){m.set(function(){for(var e=0;e<o.length;e++)Ee(t,n,o[e])}),t.refresh();var s=t.getStyle(o[0].row,o[0].column
).props.format||"common";s=Ve(s)?"custom":s,t.callEvent("onCommand",[{id:"toolbar-update",name:"format",value:s}])}}}function Ee(e,t,n){var i,o=e.getStyle(n.row,n.column),r=e.$$("cells").getItem(n.row)[n.column];if(r=r?r.toString():"",o&&o.props.format){var a=o.props.format;(i=webix.copy({values:ue[a].values,format:ve(a)})).format=function l(e,t,n){for(var i=Ie(e),o=0;o<i.length;o++){if(i[o].decimals)0<t?i[o].right.placeholders+=Array(t+1).join("0"):i[o].right.placeholders=i[o].right.placeholders.slice(0,t);else{var r=!i[o].left.text&&!i[o].right.text,a=!i[o].left.placeholders&&!i[o].right.placeholders;t<0&&r&&a&&webix.rules.isNumber(n)&&-1<n.indexOf(".")?(i[o].left.placeholders="0",i[o].right.placeholders=Array(n.split(".")[1].length).join(Array(t+1).join("0"))):0<t&&(i[o].left.placeholders||r)&&(i[o].left.placeholders=i[o].left.placeholders||"0",i[o].right.placeholders=Array(t+1).join("0"),i[o].decimals=!0)}i[o].decimals=0<i[o].right.placeholders.length}return Re(i)}(i,t,r),i.values&&i.values.hasOwnProperty("zeros")&&(i.values.zeros=Math.max(i.values.zeros+t,0))}else{(i={values:{negative:1,zeros:webix.rules.isNumber(r)&&r.split(".")[1]?Math.max(r.split(".")[1].length+t,0):Math.max(t,0),separator:0,type:"int"}}).format=Re(ze(i.values.type,i.values))}ge(e,n.row,n.column,i.format,i.values)}function Ve(e){return"common"!=e&&(-1!=e.indexOf("fmt")||"custom"==ue[e].values.type)}function ze(e,t){if("string"==e)return[{left:{text:"@"}}];if(t.date)return[{left:{text:t.date}}];var n=[{condition:">=0",left:{text:""},right:{text:""}},{condition:"<0",left:{text:""},right:{text:""}}];if(webix.extend(n,de(e)),n[0].right.placeholders=n[1].right.placeholders=t.zeros?Array(t.zeros+1).join("0"):"",n[0].separator=n[1].separator=t.separator&&"percent"!=e,n[0].left.placeholders=n[1].left.placeholders=n[0].separator?"#"+n.groupSign+"0":"0",n[0].decimals=n[1].decimals=!!t.zeros,n[1].color=1!=t.negative?"red":"",n[1].left.text+=t.negative<3?"-":"","percent"===e)n[0].right.text=n[1].right.text="%";else if("price"===e){var i=ue.price.values.symbol,o=0==i.indexOf(" ");n[1][o?"right":"left"].text+=i,n[0][o?"right":"left"].text+=i}return n}function Re(e,t){for(var n="",i=0;i<e.length;i++){if(e[i].condition&&(n+="["+e[i].condition+"]"),e[i].color&&(n+="["+e[i].color+"]"),e[i].left){if(e[i].left.text)n+=e[i].left.text;if(e[i].left.placeholders){var o=e[i].left.placeholders;n+=t?o.replace(e.groupSign,","):o}}if(e[i].decimals&&(n+=t?".":e.decimalSign),e[i].right)if(e[i].right.placeholders&&(n+=e[i].right.placeholders),e[i].right.text)n+=e[i].right.text;i!=e.length-1&&(n+=";")}return n}function Ie(e){for(var t=e.format.split(";"),n=[],i=0;i<t.length;i++){var o=Fe(t[i]);n[i]={color:o.color,condition:o.condition,left:{},right:{}};var r=o.fmt;n.groupSign=e.values.groupSign,n.decimalSign=e.values.decimalSign,-1<r.indexOf(n.groupSign)&&(n[i].separator=!0);var a=Me(r,n.decimalSign),l=a[0],s=a[1]||"";a[1]&&(n[i].decimals=!0),l=Ae(l,"left",n.groupSign),s=Ae(s,"right",n.groupSign),n[i].left={text:l.leftText,placeholders:l.placeholders},n[i].right={text:l.rightText+s.rightText,placeholders:s.placeholders}}return n}function Ae(e,t,n){var i;"left"==t&&(i=(i=e.match(/[#?0]/))?i.index:Infinity);for(var o="",r="",a="",l=0;l<e.length;l++)if(Oe(e[l]))a+=e[l];else{if(e[l]==n&&Oe(e[l+1])&&Oe(e[l-1])){a+=e[l];continue}if("left"==t&&l<i){o+=e[l];continue}r+=e[l]}return{leftText:o,rightText:r,placeholders:a}}function Fe(e){var t="",n="",i=e.indexOf("[");if(-1!==i&&e[1].match(/[><=]/)){var o=e.indexOf("]");n=e.substr(i+1,o-i-1),e=e.substr(o+1)}if(-1!==(i=e.indexOf("["))){var r=e.indexOf("]");t=e.substr(i+1,r-i-1),e=e.substr(r+1)}return{condition:n,color:t,fmt:e}}function Oe(e){return"0"===e||"#"===e||"?"===e}function Me(e,t){var n=e.match("[#?0][/"+t+"][#?0]");return n?[e.substring(0,n.index+1),e.substring(n.index+2)]:[e]}function De(e,t,n){if(e=e&&e.fmt?e.fmt:ve(e)){var i,o=Fe(pe(e)[0]).fmt;if(o)for(var r=0;r<o.length;r++)if('"'==o[r]&&(i=!i),!i){if("date"==t&&/[a-zA-Z]/.test(o)){var a=Se(o.substr(r),"date");if(a){if(!n)return!0;if("time"==a.type)return!0}}if(
"string"==t&&"@"==o[r])return!0}}}function Ne(e,t,n){var i=e.getRow(t);return i.$cellFormat&&i.$cellFormat[n]}function Ue(e){return e<-99974430.925||100025569.125<e}function Te(e,t){var n=e.$cellFormat[t],i=webix.i18n.spreadsheet.formats,o=De(n,"date",!0);return webix.Date.dateToStr(i[o?"parseDateTime":"parseDate"])}var je=Object.freeze({init:function qr(e){e.attachEvent("onDataParse",function(e){return function o(e,t){var n,i=t.formats;if(i)for(n=0;n<i.length;n++)me(i[n][1],i[n][0])}(0,e)}),e.attachEvent("onDataSerialize",function(e,t){return function r(e,t){var n=[],i=["percent","int","price","date","string"];for(var o in se)-1==i.indexOf(o)&&n.push([o,se[o]]);t.formats=n}(0,e)})},get formatHelpers(){return ue},setDefaultFormats:he,getDelimiters:de,getFormat:fe,getFormatSource:ve,addFormat:ge,removeFormat:we,format2code:be,changeAreaDecimals:ke,changeCellDecimals:Ee,isCustom:Ve,formToValues:ze,serializeFormat:Re,checkFormat:De,getFormatName:Ne,isDateInvalid:Ue,getDateEditFormat:Te});function Be(e){var t=e.charCodeAt(0);return 65<=t&&t<=122||35==t||36==t}function He(e,t,n){for(var i,o=e.length,r=n,a=!1,l=t;l<o;l++){var s=e[l];if("'"!==s){if(!a)if("!"==s){if("#"==e[t])continue;"'"===(r=e.substr(t,l-t))[0]&&(r=r.substr(1,r.length-2)),t=l+1}else if(!(Be(s)||(void 0,48<=(i=s.charCodeAt(0))&&i<=57)))return[e.substr(t,l-t),l,t,r]}else a=!a}return[e.substr(t),l,t,r]}var Pe=/^[A-Z$]+[0-9]+$/;function Le(e){return Pe.test(e)}function We(e){for(var t=0,n=0,i=1,o=1,r=0,a=!1,l=e.length-1;0<=l;l--){var s=e[l].charCodeAt(0);36!==s?s<58?(n+=(s-48)*i,i*=10):(a||(t=n,a=!(n=0),o+=i=1),n+=(s-64)*i,i*=26):r+=o}return[t,n,r]}function Ke(e){return'this.e("'.concat(e,'")')}function Ye(e,t,n,i){var o=t.ranges.getCode(n,i);if(!o)return"";var r=o.indexOf("!");-1!==r&&("'"===(i=o.substr(0,r))[0]&&(i=i.substr(1,i.length-2)),o=o.substr(r+1));var a=S(o.split(":"),2);return qe(e,a[0],a[1],i)}function qe(e,t,n,i){var o=S(We(t),2),r=o[0],a=o[1],l=S(We(n),2),s=l[0],c=l[1];if(s<r){var u=r;r=s,s=u}if(c<a){var h=a;a=c,c=h}return""===i?(e.push([r,a,s,c,""]),"this.r(".concat(r,",").concat(a,",").concat(s,",").concat(c,")")):(e.push([r,a,s,c,i]),'this.rs("'.concat(i,'",').concat(r,",").concat(a,",").concat(s,",").concat(c,")"))}function Ge(e,t,n){for(var i=[],o=0,r=!1,a=!1,l="="===e[l]?1:0;l<e.length;l++){var s=e[l];if('"'==s)r=!r;else if(!r)if("{"==s&&"{"==e[l+1])a=!0;else if("}"==s&&"}"==e[l+1])a=!1;else if(!a){var c="'"===s;if(c||Be(s)){var u=S(He(e,l,""),4),h=u[0],d=u[1],f=u[3],v=d-1;!f||!t&&f?(f&&(l+=f.length+(c?3:1)),"("!==e[v+1]&&(Le(h)||n&&n[h])&&(Ze(i,e,l,o,t?h:We(h)),o=v+1)):t&&(Ze(i,e,l,o,[f,h,c]),o=v+1),l=v}}}return o!=e.length&&i.push(e.substr(o)),i}function Ze(e,t,n,i,o){0!==n&&e.push(t.substr(i,n-i)),e.push(o)}function Xe(e){return{view:"ssheet-suggest",css:"webix_ssheet_suggest",autowidth:!0,template:function(e){var t="";return e.icon&&(t+="<span class='webix_ssheet_button_icon webix_ssheet_icon ssi-"+e.icon+"'></span> "),t+=e.value||webix.i18n.spreadsheet.menus[e.id]||e.id},data:e,on:{onItemClick:function(e){var t=this.config.master;if(t){var n=webix.$$(t).getTopParentView(),i=[this.getItem(e)];if(webix.$$(t).config.area){var o=n.$$("cells").getSelectArea();o&&(i=i.concat([o.start,o.end]))}n.callEvent("onCommand",i)}}}}}webix.protoUI({name:"live-editor",$cssName:"texthighlight",$init:function(e){var n=this;e.suggest={view:"suggest-formula",data:e.suggestData},e.highlight=this.highlight,this._highlightedCells={},this.$view.className+=" webix_ssheet_formula",this.attachEvent("onKeyPress",function(e,t){delete n._update_range,13===e&&(300<new Date-(n._last_value_set||0)&&(n.showActiveSheet(),n.updateCellValue(),webix.delay(function(){n.getTopParentView()._table.moveSelection("down")})),webix.html.preventEvent(t))}),this.attachEvent("onAfterRender",function(){var t=this;webix.eventRemove(this.keydownEv),this.keydownEv=webix.event(this.getInputNode(),"keydown",function(e){t.endEdit(e)})})},highlight:function(e){var t="",n=this.getTopParentView(),i=webix.template.escape;for(var o in this._highlightedCells){var r=this._highlightedCells[o]
;this.changeCellCss(r[0],r[1],r[2],!0)}if(this._highlightedCells={},e&&"="==e[0])for(var a=n.ranges._ranges,l=Ge(e,!0,a),s=n.getActiveSheet(),c=1,u=0;u<l.length;u++)if(u%2==0)t+=i(l[u]);else{var h=webix.isArray(l[u]),d=h?this.prepareSheet(l[u][0],l[u][2]):"",f=this._activeMath,v=!(h||f&&f!=s),m=We(h?l[u][1]:l[u]),g=h?d+l[u][1]:l[u],w=/^[A-Za-z]+$/.test(l[u]);if(v||h&&l[u][0]==s){var p=void 0;if(":"==l[u+1]&&l[u+2]||w){var b=void 0,x=void 0;if(w){var _=a[l[u]].split(":");m=We(_[0]),b=We(_[1])}else b=We(h?l[u+2][1]:l[u+2]),x=h?d+l[u+2][1]:l[u+2],u+=2;p=this.setColor(m[0],m[1],b[0],b[1],c),t+='<span class="webix_ssheet_highlight_color_'.concat(p||c,'">').concat(w?l[u]:g+":"+x,"</span>")}else p=this.setColor(m[0],m[1],m[0],m[1],c),t+='<span class="webix_ssheet_highlight_color_'.concat(p||c,'">').concat(g,"</span>");p||(c+=7==c?-6:1)}else t+=g}else t=i(e);return n.$$("cells").refresh(),document.activeElement==this.getInputNode()&&this.paintValue(),t},showActiveSheet:function(){var e=this,t=this.getTopParentView();if(this._activeMath){if(this._activeMath!=t.getActiveSheet()){var n=this.getValue(),i=this.config.activeCell;this.define({activeCell:null}),t.showSheet(this._activeMath),webix.delay(function(){t._table.select(i.row,i.column),e.setValue(n),e.updateCellValue()})}delete this._activeMath}},changeCellCss:function(e,t,n,i){for(var o="webix_ssheet_highlight_background_".concat(n),r=e.start;r<=e.end;r++)for(var a=t.start;a<=t.end;a++)this.getTopParentView().$$("cells")[i?"removeCellCss":"addCellCss"](r,a,o,!0)},endEdit:function(e){var t=e.which||e.keyCode;if(9==t||36<t&&t<41&&"="!==this.getValue().charAt(0)){var n=40==t?"down":39===t?"right":37==t?"left":"up";9===t&&(n="right"),this.updateCellValue(),this.getTopParentView()._table.moveSelection(n)}},paintValue:function(){var e=this.getTopParentView();if(!this._activeMath||this._activeMath==e.getActiveSheet()){var t=this.config.activeCell,n=e._table.getItemNode(t);t&&n&&(n.innerHTML="<div>".concat(webix.template.escape(this.getValue()),"</div>"))}},updateCellValue:function(){var e=this.getValue(),t=this.getTopParentView(),n=this.config.activeCell;n?e!=t.getCellValue(n.row,n.column)&&(this.config.value=e,t.setCellValue(n.row,n.column,e),t.refresh()):this.setValue("");delete t.$handleSelection},$setValueHere:function(e){this.setValueHere(e)},setValueHere:function(e){this._last_value_set=new Date;var t=this.getValue();if(t&&"="===t.charAt(0)){var n=this.getInputNode().selectionStart,i=t.substring(0,n),o=t.substring(n);i=(/[a-zA-Z]+\(?$/.test(i)?i.replace(/[a-zA-Z]+\(?$/,e):i+e)+("("==o[0]?"":"("),this.setValue(i+o),this.getInputNode().setSelectionRange(i.length,i.length)}},expectRange:function(){var e=this.getValue().trim();return e.substr(e.length-1,1).match(/[(,]/)},expectOperator:function(){var e=this.getValue(),t=this.getInputNode().selectionStart;return"="==e[0]&&e[t-1]&&e[t-1].match(/[+&\-/*%=(:,]/)},setRange:function(e,t){var n,i,o=this.getInputNode().selectionStart,r=this.getValue();t&&(t=this._update_range&&o==this._update_range.pos+this._update_range.len);var a=t?this._update_range.pos:o;n=r.substring(0,a)+e,i=r.substring(o),this._update_range={pos:a,len:e.length},this.setValue(n+i),this.getInputNode().setSelectionRange(n.length,n.length)},prepareSheet:function(e,t){return((webix.isUndefined(t)?-1!=e.indexOf(" "):t)?"'".concat(e,"'"):e)+"!"},prepareArea:function(e,t){return{start:Math.min(e,t),end:Math.max(e,t)}},setColor:function(e,t,n,i,o){var r=this.prepareArea(e,n),a=this.prepareArea(t,i),l=[r.start,r.end,a.start,a.end].join(","),s=this._highlightedCells[l];if(s)return s[2];this._highlightedCells[l]=[r,a,o],this.changeCellCss(r,a,o)}},webix.ui.texthighlight),webix.protoUI({name:"ssheet-separator",defaults:{css:"webix_ssheet_toolbar_spacer",template:" ",width:1,borderless:!0}},webix.ui.view),webix.protoUI({name:"sheets",defaults:{layout:"x",borderless:!0,css:"ssheet_list_sheets",select:!0,drag:"order",dragscroll:"x",scroll:!1}},webix.EditAbility,webix.ui.list),webix.protoUI({$cssName:"toggle",name:"ssheet-toggle",toggle:function(){var e=this.getValue(
)==this.config.onValue;this.setValue(!e)},$setValue:function(e){e==this.config.offValue?e=!1:e==this.config.onValue&&(e=!0),webix.ui.toggle.prototype.$setValue.call(this,e)},getValue:function(){var e=this.config,t=e.value;return t&&t!=e.offValue?e.onValue||!0:e.offValue||!1},defaults:{template:function(e,t){var n=!0===e.value||e.value==e.onValue?" webix_pressed":"",i=t.$renderInput(e,t);return"<div class='webix_el_box"+n+"' style='width:"+e.awidth+"px; height:"+e.aheight+"px'>"+i+"</div>"}}},webix.ui.toggle),webix.protoUI({name:"ssheet-toggle-silent",$cssName:"toggle",$allowsClear:!1,setValue:function(){this.blockEvent(),webix.ui.toggle.prototype.setValue.apply(this,arguments),this.unblockEvent()}},webix.ui.toggle),webix.protoUI({name:"ssheet-bar-title",defaults:{borderless:!0},$init:function(){this.$view.className+=" webix_ssheet_subbar_title"}},webix.ui.template),webix.protoUI({name:"ssheet-button",$cssName:"button",defaults:{type:"htmlbutton",width:40},$init:function(e){if(this.$view.className+=" webix_ssheet_button"+(e.arrow?" webix_ssheet_button_menu":""),e.label||e.icon){var t=e.icon||e.name,n=e.label||"";e.arrow&&(n+="<span class='webix_ssheet_button_icon webix_ssheet_icon_arrow webix_icon wxi-menu-down'></span>"),e.label="<span class='webix_ssheet_button_icon webix_ssheet_icon ssi-"+t+"'></span> "+n,e.tooltip=webix.i18n.spreadsheet.tooltips[e.name]||""}e.options&&!e.popup&&(e.popup=Xe(e.options))}},webix.ui.button),webix.protoUI({name:"ssheet-button-icon-top",$cssName:"button",defaults:{type:"htmlbutton",width:70,height:70},$init:function(e){if(this.$view.className+=" ssheet_button_icon_top",e.label){var t=e.icon||e.name||"",n=e.label;e.label="<span class='webix_ssheet_button_icon webix_ssheet_icon ssi-"+t+"'></span><br/>",e.label+="<span class='ssheet_button_icon_top_text'>"+n+"</span>",e.arrow&&(e.label+="<br/><span class='ssheet_button_icon_top_arrow webix_icon wxi-menu-down'></span>"),e.tooltip=webix.i18n.spreadsheet.tooltips[e.name]||""}e.options&&!e.popup&&(e.popup=Xe(e.options))}},webix.ui.button),webix.protoUI({name:"multicheckbox",defaults:{padding:0,type:"clean",borderless:!0,elementsConfig:{labelWidth:10}},$init:function(e){e.rows=[{height:1e-6}],this.$ready.push(function(){this._initOnChange()})},_initOnChange:function(){this.attachEvent("onChange",function(e){var t=this.$eventSource.config.name;if(this.blockEvent(),"$all"==t){for(var n in this.elements)this.elements[n].setValue(e);e||this._getCheckboxes()[0].setValue(1)}else{var i=this.getValue().length;this.elements.$all.setValue(i==this._count?1:0),i||this._getCheckboxes()[0].setValue(1)}this.unblockEvent()})},setValue:function(e){if(this.elements={},this._count=e.length,1<e.length){var t=[],n=[];e.forEach(function(e){n.push({view:"checkbox",labelRight:e.name,name:e.name,value:e.active})});var i=n.length*webix.skin.$active.inputHeight,o=400<i;t.push({view:"scrollview",body:{rows:n},scroll:o,height:o?400:i}),t.push({template:"<div class='ss_sep_line'></div>",height:10}),t.push({view:"checkbox",labelRight:webix.i18n.spreadsheet.labels["export-all-sheets"],name:"$all",value:0}),webix.ui(t,this)}else 1<this.getChildViews().length&&webix.ui([{height:1e-6}],this)},getValue:function(){for(var e=this._getCheckboxes(),t=[],n=0;n<e.length;n++)e[n].getValue()&&t.push(e[n].config.name);return t},_getCheckboxes:function(){var e=this.queryView("scrollview");return e?e.getBody().getChildViews():[]}},webix.ui.form),webix.protoUI({name:"formlist",defaults:{paddingY:5,height:120},$init:function(e){var t=this;e.cols=[{css:"webix_inp_label",template:e.label,width:e.labelWidth,borderless:!0},{view:"list",data:e.data,css:e.css,template:e.template,select:!0,scroll:!1,borderless:!0,on:{onSelectChange:function(){t.getParentView().callEvent("onChange",[])}}}]},setValue:function(e){this.getChildViews()[1].select(e)},getValue:function(){return this.getChildViews()[1].getSelectedId()},refresh:function(){this.getChildViews()[1].refresh()},focus:function(){}},webix.ui.layout),webix.protoUI({name:"ssheet-ui",defaults:{move:!0,css:"webix_shadow_none webix_ssheet_ui",head:!1,
resize:!0,toFront:!0,minWidth:50,minHeight:200,escHide:!1,autofit:!1},$init:function(){this.$view.setAttribute("webixignore","1"),this.getMenu(),this.$ready.push(function(){webix.UIManager.addHotKey("delete",webix.bind(this._removeView,this)),webix.UIManager.addHotKey("backspace",webix.bind(this._removeView,this))}),this.attachEvent("onDestruct",function(){webix.eventRemove(this._clickEv),this._menu.destructor()})},move_setter:function(e){return e&&webix.DragControl.addDrag(this.$view,this),e},$dragPos:function(e,t){return this.callEvent("onViewMove",[e,t]),e},$dragCreate:function(e,t){var n=t.target;if(n.getAttribute&&!n.getAttribute("webix_disable_drag")&&!n.getAttribute("webixignore")){this.getMenu().hide();var i=webix.html.offset(e),o=webix.html.pos(t),r=webix.html.offset(e.parentNode),a=webix.$$(this.config.master).getScrollState();return webix.DragControl.top=i.y-o.y-r.y,webix.DragControl.left=i.x-o.x-r.x+a.x,webix.toNode(this.$view)}},$dragDestroy:function(e,t){var n=this.config.left=parseInt(t.style.left,10),i=this.config.top=parseInt(t.style.top,10);webix.DragControl.top=webix.DragControl.left=0,this.callEvent("onViewMoveEnd",[n,i])},getMenu:function(){var t=this;if(!this._menu){this._menu=webix.ui({view:"contextmenu",data:[{id:"edit",value:webix.i18n.spreadsheet.labels["edit-view"]},{id:"del",value:webix.i18n.spreadsheet.labels["remove-view"]}],on:{onItemClick:function(e){return t._menuActions(e)}}});var e=webix.html.create("span",{"class":"webix_ssheet_view_menu webix_icon wxi-dots"});this._clickEv=webix.event(e,"click",function(){t._menu.isVisible()?t._menu.hide():t._menu.show(e)}),this.$view.firstChild.appendChild(e)}return this._menu},_menuActions:function(e){"del"==e?this.callEvent("onViewRemove",[]):"edit"==e&&this.callEvent("onViewEdit",[])},_removeView:function(e){e&&e.getTopParentView()==this&&this.callEvent("onViewRemove",[])}},webix.ui.window),webix.editors.excel_date=webix.extend({createPopup:function(){var e=webix.editors.excel_date;if(!e.popup){var t=webix.ui({view:"popup",body:{view:"calendar",icons:!0,timepicker:!0,on:{onChange:function(e){e=e[0],this.getParentView()._last_input_target.value=e?o(e):"",webix.callEvent("onEditEnd",[])}}},on:{onEnter:function(){webix.callEvent("onEditEnd",[])}}});e.popup=t.config.id}return e.popup},render:function(){var e=this,t=webix.html.create("div",{"class":"webix_dt_editor"},"<input type='text'>");return this.popup=this.createPopup(),webix.event(t.firstChild,"click",function(){return e.getPopup().show(t)}),t},getPopup:function(){return webix.$$(this.popup)},showPopup:function(){var e=this.getInputNode(),t=this.getPopup();t._last_input_target=e,t.show(e),e.setAttribute("aria-expanded","true")},updateCalendar:function(e){var t=this.getPopup().getBody();t.blockEvent(),t.setValue(e),t.unblockEvent()},afterRender:function(){this.showPopup()},setValue:function(e){e=""===e||isNaN(1*e)?new Date:w(e),this.updateCalendar(e);var t=Te(this.config.row,this.config.id);this.getInputNode().value=t(e)},getValue:function(){var e=this.getInputNode().value;return this.getPopup().hide(),e}},webix.editors.text),webix.editors.ss_richselect=webix.extend({getFormat:function(e){var t,n=this.getInputNode();return e&&n.exists(e)&&(t=n.getItem(e)),t&&t.format},popupInit:function(e){webix.editors.richselect.popupInit.apply(this,arguments),e.getList().define("template",function(e){return e.$value||e.value})}},webix.editors.richselect);var Qe={span:function(e){var t=e.$$("cells").getSelectArea();if(t)if(function a(e,t){var n,i,o=t.start,r=t.end;for(n=1*o.row;n<=1*r.row;n++)for(i=1*o.column;i<=1*r.column;i++)if(e.$$("cells").getSpan(n,i))return!0;return!1}(e,t))e.splitCell();else{for(var n=[],i=t.start.row;i<=t.end.row;i++){for(var o=t.start.column;o<=t.end.column;o++){var r=e.getCellValue(i,o);if(r&&n.push(r),1<n.length)break}if(1<n.length)break}1<n.length?e.confirm({text:webix.i18n.spreadsheet.labels["merge-cell-confirm"]}).then(function(){return e.combineCells()}):e.combineCells()}},undo:function(e){e.undo()},redo:function(e){e.redo()},"hide-gridlines":function(e){e.hideGridlines("toggle")
},"hide-headers":function(e){e.hideHeaders("toggle")},"freeze-columns":function(e){var t=e.$$("cells").getSelectedId();e.freezeColumns(t&&"rowId"!=t.column?t.column:0)},"freeze-rows":function(e){var t=e.$$("cells").getSelectedId();e.freezeRows(t?t.row:0)},"increase-decimals":function(e){ke(e,1)},"decrease-decimals":function(e){ke(e,-1)}};var Je=[{id:"Arial",value:"Arial"},{id:"'Roboto', sans-serif",value:"Roboto"},{id:"'PT Sans', Tahoma",value:"PT Sans"},{id:"Tahoma",value:"Tahoma"},{id:"Verdana",value:"Verdana"},{id:"Calibri, Tahoma",value:"Calibri"}];function et(e){var t=webix.i18n.spreadsheet.menus,n=[{id:"add",group:"column",value:t["insert-column"]},{id:"del",group:"column",value:t["delete-column"]},{id:"show",group:"column",value:t["show-column"]},{id:"hide",group:"column",value:t["hide-column"]}];return 0!=e.config.resizeCell&&n.push({id:"resize",group:"column",value:t["resize-column"]}),n}function tt(e){var t=webix.i18n.spreadsheet.menus,n=[{id:"add",group:"row",value:t["insert-row"]},{id:"del",group:"row",value:t["delete-row"]},{id:"show",group:"row",value:t["show-row"]},{id:"hide",group:"row",value:t["hide-row"]}];return 0!=e.config.resizeCell&&n.push({id:"resize",group:"row",value:t["resize-row"]}),n}function nt(){return[{id:"clear-value",value:ot("value")},{id:"clear-style",value:ot("style")},{id:"clear-conditional-formats",value:ot("conditional-formats")},{id:"clear-dropdown-editors",value:ot("dropdown-editors")},{id:"clear-comments",value:ot("comments")},{$template:"Separator"},{id:"clear-all",value:ot("all")}]}var it=[{id:"no",value:"no"},{id:"left",value:"left"},{id:"top",value:"top"},{id:"right",value:"right"},{id:"bottom",value:"bottom"},{id:"all",value:"all"},{id:"outer",value:"outer"},{id:"top-bottom",value:"top-bottom"}];function ot(e){var t=webix.i18n.spreadsheet.menus;return t.clear+" "+t["clear-"+e].toLowerCase()}var rt={"font-weight":"bold","font-style":"italic","text-decoration":"underline"},at={button:function(e){return{view:"ssheet-toggle",width:e.width||N.width,id:e.name,name:e.name,label:e.label,css:e.css||"",onValue:rt[e.name],offValue:"normal",tooltip:webix.i18n.spreadsheet.tooltips[e.name]||""}},colorButton:function(e){return{view:"ssheet-color",css:e.css,name:e.name,width:e.width||N.width+24,title:"<span class='webix_ssheet_button_icon webix_ssheet_color_button_icon webix_ssheet_icon ssi-"+e.name+"' ></span>",tooltip:webix.i18n.spreadsheet.tooltips[e.name]||""}},toggleButton:function(e){return{view:"ssheet-toggle-silent",width:N.width,id:e.name,name:e.name,label:"<span class='webix_ssheet_button_icon webix_ssheet_icon ssi-"+e.name+"'></span>",tooltip:webix.i18n.spreadsheet.tooltips[e.name]||""}},iconButton:function(e){var t=webix.copy(e);return webix.extend(t,{view:"button",width:N.width,id:e.name,label:"<span class='webix_ssheet_button_icon webix_ssheet_icon ssi-"+e.name+"'></span>",css:"",tooltip:webix.i18n.spreadsheet.tooltips[e.name]||webix.i18n.spreadsheet.menus[e.name]||""}),e.onValue&&webix.extend(t,{view:"ssheet-toggle",onValue:e.onValue,offValue:e.offValue},!0),t},segmented:function(e){return{view:"segmented",name:e.name,css:e.css||"",width:e.width||N.width+76,options:e.options}},select:function(e){return webix.extend(e,{view:"richselect",id:e.name,value:U[e.name],suggest:{css:"webix_ssheet_suggest",padding:0,data:e.options}}),e.tooltip=webix.i18n.spreadsheet.tooltips[e.name]||"",e.popupWidth&&(e.suggest.fitMaster=!1,e.suggest.width=e.popupWidth),e.popupTemplate&&(e.suggest.body={template:e.popupTemplate}),e.popupEvents&&(e.suggest.body=e.suggest.body||{},e.suggest.body.on=e.popupEvents),e},separator:function(){return{view:"ssheet-separator"}},title:function(e){var t=e.title;return 0===t.indexOf("$")&&(t=""),{template:t=webix.i18n.spreadsheet.labels[e.title]||t,view:"ssheet-bar-title",height:N.titleHeight}},borders:function(e){return{view:"ssheet-borders",width:e.width||N.width+24,data:it,id:e.name,name:e.name,tooltip:webix.i18n.spreadsheet.tooltips[e.name]}},align:function(e){return{view:"ssheet-align",value:U[e.name],width:e.width||N.width+24,data:e.options,name:e.name,
tooltip:webix.i18n.spreadsheet.tooltips[e.name]}},condFormat:function(e){return{view:"ssheet-cond-format",width:40,id:e.name,name:e.name}}},lt={"font-family":function(){return at.select({name:"font-family",options:Je,width:100})},"font-size":function(){return at.select({name:"font-size",options:function i(){for(var e=["8","9","10","11","12","14","15","16","18","20","22","24","28","36"],t=[],n=0;n<e.length;n++)t.push({id:e[n]+webix.i18n.spreadsheet.labels.px,value:e[n]});return t}(),width:64})},"font-weight":function(){return at.button({name:"font-weight",label:"B",css:"webix_ssheet_bold"})},"font-style":function(){return at.button({name:"font-style",label:"I",css:"webix_ssheet_italic"})},"text-decoration":function(){return at.button({name:"text-decoration",label:"U",css:"webix_ssheet_underline"})},color:function(){return at.colorButton({name:"color",icon:"font",css:"webix_ssheet_color"})},background:function(){return at.colorButton({name:"background",icon:"paint-brush",css:"webix_ssheet_background",width:64})},borders:function(){return at.borders({name:"borders"})},"text-align":function(){var e=webix.i18n.spreadsheet.tooltips;return at.align({name:"text-align",css:"webix_ssheet_align",options:[{id:"left",css:"webix_ssheet_icon ssi-left",tooltip:e["align-left"]},{id:"center",css:"webix_ssheet_icon ssi-center",tooltip:e["align-center"]},{id:"right",css:"webix_ssheet_icon ssi-right",tooltip:e["align-right"]}]})},"vertical-align":function(){var e=webix.i18n.spreadsheet.tooltips;return at.align({name:"vertical-align",css:"webix_ssheet_align",options:[{id:"top",css:"webix_ssheet_icon ssi-top",tooltip:e["align-top"]},{id:"middle",css:"webix_ssheet_icon ssi-middle",tooltip:e["align-middle"]},{id:"bottom",css:"webix_ssheet_icon ssi-bottom",tooltip:e["align-bottom"]}]})},wrap:function(){return at.iconButton({name:"wrap",onValue:"wrap",offValue:"nowrap"})},format:function(){return at.select({name:"format",width:106,options:function t(){var e=webix.i18n.spreadsheet.labels;return[{id:"common",value:e.common},{id:"price",value:e.currency,example:"98.20"},{id:"int",value:e.number,example:"2120.02"},{id:"percent",value:e.percent,example:"0.5"},{id:"date",value:e.date,example:"45000"},{id:"string",value:e.string,example:"012345"},{id:"custom",value:e["custom-format"]}]}(),popupWidth:180,popupTemplate:function(e){var t=ue[e.id]?ue[e.id].getFormat:"",n={css:""},i=t&&e.example?t(e.example,n):"";return e.value+(t?"<span class='webix_ssheet_right"+(n.css?" "+n.css:"")+"'>"+i+"</span>":"")},popupEvents:{onItemClick:function(e){"custom"==e&&this.getTopParentView().callEvent("onCommand",[{id:e}])}}})},column:function(e){return{name:"column",view:"ssheet-button",icon:"column",arrow:!0,area:!0,width:58,options:et(e)}},row:function(e){return{name:"row",view:"ssheet-button",icon:"row",arrow:!0,area:!0,width:58,options:tt(e)}},clear:function(){return{name:"clear",view:"ssheet-button",icon:"clear-styles",arrow:!0,area:!0,width:58,options:nt()}},image:function(){return{name:"image",view:"ssheet-button",icon:"add-image",arrow:!0,options:[{id:"add-image-cell",value:webix.i18n.spreadsheet.labels["add-image-cell"]},{id:"add-image-top",value:webix.i18n.spreadsheet.labels["add-image-top"]}]}},graph:function(){return{name:"graph",view:"ssheet-button",icon:"add-sparkline",arrow:!0,options:[{id:"add-sparkline",value:webix.i18n.spreadsheet.labels["add-sparkline"]},{id:"add-chart",value:webix.i18n.spreadsheet.labels["add-chart"]}]}},comment:function(){return{name:"comment",view:"ssheet-button",icon:"comments",arrow:!0,area:!0,width:55}},"create-filter":function(){return at.toggleButton({name:"create-filter"})},"hide-gridlines":function(){return at.toggleButton({name:"hide-gridlines"})},"hide-headers":function(){return at.toggleButton({name:"hide-headers"})}};function st(e){var t=webix.i18n.spreadsheet;return t.menus[e]||t.labels[e]||t.tooltips[e]||e}function ct(){var e=function r(){var e,t,n,i={undo:["undo","redo"],insert:["add-sparkline","add-image","add-comment"]},o={};for(t in i)for(e=o[t]=0;e<i[t].length;e++)n=webix.html.getTextSize(st(i[t][e]),
"webix_ssheet_button_measure").width+7,o[t]=Math.max(n,o[t]);return o}(),t=N.titleHeight;return[{padding:3,margin:0,rows:[{margin:2,cols:[{name:"sheet",view:"ssheet-button-icon-top",label:st("sheet"),arrow:!0,options:[{id:"new-sheet"},{id:"copy-sheet"},{id:"remove-sheet"}]},{rows:[{$button:"excel-import"},{$button:"excel-export"}]},{rows:[{$button:"print"}]}]},{},{template:st("file"),view:"ssheet-bar-title",height:t,width:85}]},{view:"ssheet-separator"},{padding:3,rows:[{$button:"undo",view:"ssheet-button",label:st("undo"),width:e.undo},{$button:"redo",view:"ssheet-button",label:st("redo"),width:e.undo},{template:st("undo-redo"),view:"ssheet-bar-title",height:t}]},{view:"ssheet-separator"},{padding:3,rows:[{margin:2,cols:[{margin:2,cols:[{$button:"font-family",width:3*(webix.skin.$active.inputHeight+2)+4},{$button:"font-size"}]},{$button:"borders"}]},{margin:2,cols:[{margin:2,cols:[{$button:"font-weight"},{$button:"font-style"},{$button:"text-decoration"}]},{$button:"background"},{$button:"color"}]},{template:st("font"),view:"ssheet-bar-title",height:t}]},{view:"ssheet-separator"},{padding:3,rows:[{margin:2,cols:[{$button:"text-align"},{$button:"span"}]},{margin:2,cols:[{$button:"vertical-align"},{$button:"wrap"}]},{template:st("align"),view:"ssheet-bar-title",height:t}]},{view:"ssheet-separator"},{padding:3,rows:[{$button:"format"},{margin:2,cols:[{$button:"increase-decimals"},{$button:"decrease-decimals"}]},{template:st("number"),view:"ssheet-bar-title",height:t}]},{view:"ssheet-separator"},{padding:3,rows:[{cols:[{$button:"sort-asc"},{$button:"create-filter"},{$button:"conditional-format"},{$button:"add-link"},{$button:"clear"}]},{cols:[{$button:"sort-desc"},{$button:"add-range"},{$button:"lock-cell"},{$button:"add-dropdown"}]},{template:st("edit"),view:"ssheet-bar-title",height:t}]},{view:"ssheet-separator"},{padding:3,rows:[{cols:[{$button:"image",view:"ssheet-button",label:st("image"),width:e.insert},{$button:"add-comment",view:"ssheet-button",label:st("comment"),width:e.insert}]},{$button:"graph",view:"ssheet-button",label:st("graph"),width:e.insert},{template:st("insert"),view:"ssheet-bar-title",height:t}]},{view:"ssheet-separator"},{padding:3,rows:[{cols:[{rows:[{$button:"row"},{$button:"column"}]},{rows:[{$button:"hide-gridlines"},{$button:"hide-headers"}]},{rows:[{$button:"freeze-rows"},{$button:"freeze-columns"}]}]},{template:st("view"),view:"ssheet-bar-title",height:t}]},{view:"ssheet-separator"},{}]}var ut={"undo-redo":["undo","redo"],font:["font-family","font-size","font-weight","font-style","text-decoration","color","background","borders"],align:["text-align","vertical-align","wrap","span"],format:["format"]};function ht(i){i.attachEvent("onComponentInit",function(){return function e(t){t.attachEvent("onAfterSelect",function(e){return function i(e,t){var n=function c(e,t){for(var n=["font-family","font-size","text-align","vertical-align","format","background","color"],i=t[0],o=dt(e,i),r=1;r<t.length;r++){for(var a=dt(e,t[r]),l=n.length-1;0<=l;l--){var s=n[l];o[s]!=a[s]&&(delete o[s],n.splice(l,1))}if(!n.length)break}return o}(e,t);e.$$("bar").setValues(n)}(t,e)})}(i)}),i.attachEvent("onCommand",function(e){"toolbar-update"==e.id&&function o(e,t,n){var i=e.$$("bar").elements[t];i&&i.setValue(n)}(i,e.name,e.value)});var e=[];if(i.config.toolbar){var t=i.config.toolbar;"full"==t&&(t=ct(),webix.isUndefined(i.config.bottombar)&&(i.config.bottombar=!0)),e=function a(o,e){var r=function(e){for(var t=0;t<e.length;t++){var n=e[t].$button;if(n){var i=lt[n]?lt[n](o):at.iconButton({name:n});webix.extend(e[t],i)}e[t].rows&&r(e[t].rows),e[t].cols&&r(e[t].cols)}};return r(e),e}(i,t)}else e=function s(e,t){var n,i,o,r=[];for(var a in t)r.push((n=e,o=t[i=a],{rows:[{padding:2,cols:[{margin:2,cols:function l(e,t){for(var n=[],i=0;i<t.length;i++){var o=t[i];if("object"==g(o))n.push(o);else{var r=lt[o]?lt[o](e):at.iconButton({name:o});n.push(r)}}return n}(n,o)}]},at.title({title:i})]})),r.push(at.separator());return r}(i,i.config.buttons||ut);var n={view:"toolbar",css:"webix_ssheet_toolbar webix_layout_toolbar",id:"bar",
padding:0,elements:e,on:{onChange:function(){var e=this.$eventSource,t=e.getValue(),n=e.config.name;"format"==n&&"common"==t&&(t=""),"format"==n&&"custom"==t||i.callEvent("onStyleSet",[n,t])},onItemClick:function(e){var t=i.innerId(e);Qe[t]?Qe[t].call(this,i):i.callEvent("onCommand",[{id:t}])}}};return i.callEvent("onViewInit",["toolbar",n]),n}function dt(e,t){var n=e.$$("cells").getSpan(t.row,t.column);if(n){var i=S(n,2);t.row=i[0],t.column=i[1]}var o=e.getStyle(t.row,t.column),r=o?webix.copy(o.props):{};for(var a in U)r[a]||(r[a]=U[a]);return r.format=Ve(r.format)?"custom":r.format,r}function ft(e,t,n){var i=t.$$("cells").config.columns,o={row:e,column:i["rowId"==i[0].id?1:0].id},r={row:n||e,column:i[i.length-1].id};o.column&&r.column&&xt(o,r,t)}function vt(e){var t=e.$$("cells").data.order,n=e.$$("cells").config.columns,i="rowId"==n[0].id?1:0,o={row:t[0],column:n[i].id},r={row:t[t.length-1],column:n[n.length-1].id};o.column&&r.column&&xt(o,r,e)}function mt(e,t,n){var i=t.$$("cells").data.order;xt({row:i[0],column:e},{row:i[i.length-1],column:n||e},t)}function gt(e,t){var n=t.$$("cells").getSelectArea();return!!(n&&e>=n.start.column&&e<=n.end.column)}function wt(e,t,n){return pt(e,n)&&gt(t,n)}function pt(e,t){var n=t.$$("cells").getSelectArea();return!!(n&&e>=n.start.row&&e<=n.end.row)}function bt(e,t){var n=t.$$("cells").getSelectArea();n?mt(Math.min(n.start.column,n.end.column,e),t,Math.max(n.start.column,n.end.column,e)):mt(e,t)}function xt(e,t,n){n.$$("cells").addSelectArea(e,t)}function _t(a,l){var s=25,c=l._table.getColumnConfig(a).width;l._table.eachRow(function(e){var t=l._table.getSpan(e,a);if(!(t&&1<t[2])){var n=this.getText(e,a),i=l.getStyle(e,a),o=i?i.id:"";if(n.replace(/<\/?[^>]+(>|$)/g,"")){var r=Q(l,n,o);(l.getCellEditor(e,a)||l.getCellFilter(e,a))&&(r.width+=20),i&&"wrap"==i.props.wrap&&r.width>c&&(r.width=c),r.width>s&&(s=r.width)}}}),l._table.setColumnWidth(a,s)}function yt(l,s){var c=25;s._table.eachColumn(function(e){if("rowId"!=e){var t=this.getText(l,e),n=this.getColumnConfig(e).width,i=s._table.getSpan(l,e);i&&(n=s._table.getSpanNode({row:i[0],column:i[1]}).offsetWidth);var o=s.getStyle(l,e),r=o?o.id:"";if(t.replace(/<\/?[^>]+(>|$)/g,"")){var a=Q(s,t,r,n);a.height>c&&(c=a.height)}}}),s._table.setRowHeight(l,c)}function Ct(e,t,n){if(n._table.config.header){var i="webix_highlight",o=e.row,r=t.row,a=e.column,l=t.column;if(r<o){var s=[r,o];o=s[0],r=s[1]}if(l<a){var c=[l,a];a=c[0],l=c[1]}n._table.eachRow(function(e){e<o||r<e?n._table.removeCellCss(e,"rowId",i):n._table.addCellCss(e,"rowId",i)}),n._table.eachColumn(function(e){var t=n._table.getHeaderNode(e);"rowId"!=e&&t&&(e<a||l<e?webix.html.removeCss(t,i):webix.html.addCss(t,i,!0))})}}var $t=Object.freeze({selectRow:ft,selectAll:vt,selectColumn:mt,isColSelected:gt,isCellSelected:wt,isRowSelected:pt,selectColumns:bt,adjustColumn:_t,adjustRow:yt,highlightColRow:Ct});function St(e,t){e.attachEvent("onComponentInit",function(){return function h(o){var r,n,a=o._table,e=webix.event(document.body,"mousemove",function(e){clearTimeout(t),t=webix.delay(function(e){if(!o.comments._activeComment.editStatus){var t=a.locate(e),n=o.comments.commentsView&&o.comments.commentsView.isVisible();if(t&&o.comments.get(t.row,t.column)){var i=o.comments._activeComment.cell;(!i||i.row==t.row&&i.column==t.column)&&n||o.callEvent("onCommand",[{id:"add-comment",cell:t,viewonly:!0}])}else n&&!o.comments.commentsView.$view.contains(e.target)&&o.callEvent("onCommentHide",[])}},!0,[e],250)});var t;o.config.math&&(a.config.editMath=!0);function i(){var e=a.getSelectedId(!0);if(e.length){var t=e[0],n=e[e.length-1]?e[e.length-1]:e[0];Ct(t,n,o)}else{var i={row:0,column:0};Ct(i,i,o)}}a.attachEvent("onBeforeEditStop",function(e,t){e.old===webix.undefined&&""===e.value||(e.value!=e.old&&(o.setCellValue(t.row,t.column,e.value),a.refresh()),e.value=e.old)}),a.attachEvent("onEnter",function(e){a.getEditor()&&webix.delay(function(){a.moveSelection("down")}),webix.html.preventEvent(e)}),o.attachEvent("onBeforeEditStart",function(e,t){return!o.isCellLocked(e,t)}),a.attachEvent(
"onBeforeEditStart",function(e){return o.callEvent("onBeforeEditStart",[e.row,e.column])}),a.attachEvent("onBeforeSelect",function(e){return"rowId"!=e.column}),a.attachEvent("onBeforeBlockSelect",function(e,t,n){return!n||"rowId"===e.column&&"rowId"===t.column||("rowId"===e.column&&(e.column=1),"rowId"===t.column&&(t.column=1)),("rowId"!==e.column||"rowId"!==t.column)&&(Ct(e,t,o),!0)}),a.attachEvent("onSelectChange",function(){return i()}),a.attachEvent("onColumnResize",function(){webix.delay(function(){return i()})}),webix.event(a.$view.firstChild,"mousedown",function(e){var t=a.config.resizeColumn,n=a.locate(e);if(n&&n.cind){if(t){var i=a.getColumnConfig(n.column).width,o=webix.html.posRelative(e);if(o.x<t.size||i-o.x<t.size+1)return}r=n.cind}}),webix.event(a.$view.firstChild,"mousemove",function(e){if(r){var t=a.locate(e);t&&(n=t.cind,Ct({column:a.columnId(r)},{column:a.columnId(n||1)},o))}});var l=webix.event(document.body,"mouseup",function(){r&&(null!=n&&(mt(a.columnId(r),o,a.columnId(n||1)),n=null),r=null)});a.attachEvent("onItemDblClick",function(e){"rowId"===e.column&&yt(e.row,o)});var s=0;a.attachEvent("onHeaderClick",function(e,t){if("rowId"!=e.column){var n=new Date,i=n-s<=300;i?_t(e.column,o):(s=n,t.shiftKey?bt(e.column,o):mt(e.column,o))}else vt(o)});var c=null;a.attachEvent("onItemClick",function(e,t){"rowId"===e.column&&(t.shiftKey&&c?ft(c,o,e.row):ft(e.row,o),c=e.row)}),o.attachEvent("onReset",function(){return Et(o)}),o.attachEvent("onBeforeSheetShow",function(){return a.editStop()}),a.attachEvent("onBlur",function(){webix.delay(function(){var e=document.activeElement;if(!e||"INPUT"!=e.tagName){var t=webix.UIManager.getFocus(),n=t&&t!=a&&t.getTopParentView&&t.getTopParentView()===o;n&&webix.UIManager.setFocus(a)}},this,[],100)}),function u(e){webix.html.addStyle("#"+e._table.$view.id+".webix_dtable .webix_cell { white-space:nowrap;}")}(o),o.attachEvent("onDestruct",function(){webix.eventRemove(e),webix.eventRemove(l)})}(e)});var n={view:"datatable",id:"cells",css:"webix_ssheet_table webix_data_border wss_"+e.$index,headerRowHeight:"contrast"==webix.skin.$name||"flat"==webix.skin.$name?24:20,spans:!0,leftSplit:1,areaselect:!0,minColumnWidth:38,editable:!0,editaction:t.liveEditor?"custom":"dblclick",minRowHeight:1,navigation:!0};return!1!==t.clipboard&&(t.clipboard="custom",t.clipboardDecimalDelimiter&&(t.templateCopy=function(e){return webix.rules.isNumber(e)&&(e=e.toString().replace(".",t.clipboardDecimalDelimiter)),e})),t&&(n=webix.extend(n,t,!0)),n}function kt(e,t,n,i,o,r){var a=o.id,l=t.id,s="",c=""===i||isNaN(i),u=Ne(e,l,a),h=De(u,"date")&&Ue(i),d="";if(r||(d+=e.comments.get(l,a)?"<div class='ssheet_commented_sign'></div>":"",d+=e.getCellFilter(l,a)?"<div class='ssheet_filter_sign'></div>":""),!h&&(!c||De(u,"string")))if(u){var f={css:""},v=fe(u);if(v){var m=v(i,f);m!=webix.i18n.spreadsheet.table["format-error"]?(f.css&&(s='class="'.concat(f.css,'"')),i=m):c||(i*=1)}}else i*=1;return(webix.isUndefined(i)||null===i)&&(i=""),"<div ".concat(s,">").concat(i,"</div>").concat(d)}function Et(o){var e=o.$$("cells");e.clearAll();for(var t=o.config.columnCount,n=o.config.rowCount,i=[{id:"rowId",header:"",width:40,css:"sheet_column_0",template:function(e){return e.id}}],r=1;r<=t;r++)i.push({id:r,editor:"text",header:{text:$[r]},template:function(e,t,n,i){return kt(o,e,0,n,i)}}),o.callEvent("onColumnInit",[i[r]]);e.refreshColumns(i);for(var a=[],l=1;l<=n;l++)a.push({id:l});e.parse(a)}function Vt(i){var o=i.$$("cells"),e=webix.env.isMac?"command":"ctrl";if(webix.UIManager.addHotKey(e+"-a",function(){o.getEditor()||vt(i)},o),!i.config.readonly){webix.UIManager.addHotKey("any",function(e,t){if(16!=(t.which||t.keyCode)){var n=e.getSelectedId(!0);n.length&&(o.$anyKey=!0,o.edit(n[0]))}},o),webix.UIManager.addHotKey("enter",function(e){var t=e.getSelectedId(!0);t.length&&(e.config.liveEditor?o.callEvent("onBeforeLiveEditor",[t[0]]):o.edit(t[0]))},o),webix.UIManager.addHotKey("backspace",function(){return zt(i)},o),webix.UIManager.addHotKey("delete",function(){return zt(i)},o)
;var t=webix.env.isMac?"command-shift-z":"ctrl-y";webix.UIManager.addHotKey(t,function(){return i.redo()},o),webix.UIManager.addHotKey(e+"-z",function(){return i.undo()},o),webix.UIManager.addHotKey(e+"-b",function(){return Rt(i,"font-weight")},o),webix.UIManager.addHotKey(e+"-i",function(){return Rt(i,"font-style")},o),webix.UIManager.addHotKey(e+"-u",function(e,t){return function n(e,t){return Rt(e,"text-decoration"),webix.html.preventEvent(t)}(i,t)},o),webix.UIManager.addHotKey(e+"-p",function(e,t){return function n(e,t){return e.callEvent("onCommand",[{id:"print"}]),webix.html.preventEvent(t)}(i,t)},o)}}function zt(t){m.set(function(){t.eachSelectedCell(function(e){return t.setCellValue(e.row,e.column,"")}),t.refresh()})}function Rt(e,t){var n=e.getSelectedId();if(n){var i=e.getStyle(n.row,n.column),o=rt[t],r=i&&i.props[t]===o?"normal":o;e.callEvent("onStyleSet",[t,r])}}var It=Object.freeze({SUM:function Gr(){for(var e=0,t=a(arguments),n=0;n<t.length;n++){var i=s(t[n]);!1!==i&&(e+=i)}return e},AVERAGE:function Zr(){for(var e=0,t=0,n=a(arguments),i=0;i<n.length;i++){var o=s(n[i]);!1!==o&&(e+=o,t++)}return e/t},COUNT:function Xr(){for(var e=0,t=a(arguments),n=0;n<t.length;n++)!1!==s(t[n])&&e++;return e},COUNTA:function Qr(){for(var e=0,t=a(arguments),n=0;n<t.length;n++)t[n]&&1*t[n]!=0&&e++;return e},COUNTBLANK:function Jr(){for(var e=0,t=a(arguments),n=0;n<t.length;n++)1*!t[n]&&e++;return e},MAX:function ea(){for(var e="",t=a(arguments),n=0;n<t.length;n++){var i=s(t[n]);!1!==i&&(e<i||""===e)&&(e=i)}return e||0},MIN:function ta(){for(var e="",t=a(arguments),n=0;n<t.length;n++){var i=s(t[n]);!1!==i&&(i<e||""===e)&&(e=i)}return e||0},PRODUCT:function na(){for(var e="",t=a(arguments),n=0;n<t.length;n++){var i=s(t[n]);!1!==i&&(e=""===e?i:e*i)}return e},SUMPRODUCT:function ia(e){var t=e[0].length;for(var n in e)if(e[n].length!==t)return;for(var i=0,o=0;o<e[0].length;o++){var r="";for(var a in e){var l=s(e[a][o]);if(!1===l){r=0;break}r=""===r?l:r*l}webix.isUndefined(r)||(i+=r)}return i},SUMSQ:function oa(){for(var e=0,t=a(arguments),n=0;n<t.length;n++){var i=s(t[n]);"number"==typeof i&&(e+=Math.pow(i,2))}return e},VARP:function ra(){for(var e=a(arguments),t=this.COUNT(e),n=this.AVERAGE(e),i=0,o=0;o<e.length;o++){var r=s(e[o]);!1!==r&&(i+=Math.pow(r-n,2))}return i/t},STDEVP:function aa(){var e=a(arguments);return Math.sqrt(this.VARP(e))},POWER:function la(e,t){var n=s(e),i=s(t);if("number"==typeof n&&"number"==typeof i)return Math.pow(n,i)},QUOTIENT:function sa(e,t){var n=s(e),i=s(t);if("number"==typeof n&&"number"==typeof i)return n/i},SQRT:function ca(e){var t=s(e);if(!1!==t&&0<=t)return Math.sqrt(t)},ABS:function ua(e){var t=s(e);if(!1!==t)return Math.abs(t)},RAND:function ha(){return Math.random()},PI:function da(){return Math.PI},INT:function fa(e){var t=s(e);if(!1!==t)return Math.round(t)},ROUND:function va(e,t){var n=s(e),i=s(t)||0;if(!1!==n)return parseFloat(n.toFixed(i))},ROUNDDOWN:function ma(e,t){var n=s(e),i=s(t)||0;if(!1!==n)return Math.floor(n*Math.pow(10,i))/Math.pow(10,i)},ROUNDUP:function ga(e,t){var n=s(e),i=s(t)||0;if(!1!==n)return Math.ceil(n*Math.pow(10,i))/Math.pow(10,i)},TRUNC:function wa(e){var t=s(e);if(!1!==t)return parseInt(t)},EVEN:function pa(e){var t=s(e);if(!1!==t){var n=Math.round(t);return n%2?n+1:n}},ODD:function ba(e){var t=s(e);if(!1!==t){var n=Math.round(t);return n%2?n:n+1}}});var At=Object.freeze({CONCATENATE:function xa(){var e=Array.prototype.slice.call(arguments);return(e=e.map(function(e){return"object"===g(e)?e.join(""):e})).join("")},LEFT:function _a(e,t){return e?e.substring(0,t):""},MID:function ya(e,t,n){return e?e.substring(t,t+n):""},RIGHT:function Ca(e,t){return e?e.substring(e.length-t):""},LOWER:function $a(e){return e?e.toLowerCase():""},UPPER:function Sa(e){return e?e.toUpperCase():""},PROPER:function ka(e){if(!e)return"";var t=e.toLowerCase().split(" ");for(var n in e="",t)e+=(e?" ":"")+t[n].substring(0,1).toUpperCase()+t[n].substring(1);return e},TRIM:function Ea(e){return e?e.trim():""},LEN:function Va(e){return e||0===e?e.toString().length:0}});var Ft=Object.freeze({
DATE:function za(e,t,n){return o(new Date(e,t-1,n))},TIME:function Ra(e,t,n){return o(new Date(1900,0,1,e,t,n))},DAY:function Ia(e){return w(e).getDate()},MONTH:function Aa(e){return w(e).getMonth()+1},YEAR:function Fa(e){return w(e).getFullYear()},NOW:function Oa(){return o(new Date)},DATEDIF:function Ma(e,t,n){switch("D"!=n&&(e=w(e),t=w(t)),n){case"Y":return t.getFullYear()-e.getFullYear();case"M":return 12*(t.getFullYear()-e.getFullYear())+t.getMonth()-e.getMonth();case"D":return Math.floor(t-e);case"MD":var i=t.getDate()-e.getDate();if(i<0){var o=new Date(e.getFullYear(),e.getMonth()+1,0).getDate();return t.getDate()+o-e.getDate()}return i;case"YM":var r=t.getMonth()-e.getMonth();return r<0?12+r:r;case"YD":var a=l(t)-l(e);return a<0?365+a:a}}});var Ot=Object.freeze({IMAGE:function Da(e){return'<img class="webix_ssheet_cimage" src="'.concat(webix.template.escape(e),'">')},SPARKLINE:function Na(e,t,n,i){var o,r={type:t,color:n,negativeColor:i},a="pie"==t?60:150;for(o=0;o<e.length;o++)e[o]=e[o]||0;return webix.Sparklines.getTemplate(r)(e,{width:a,height:35})},HYPERLINK:function Ua(e,t){return t=t||e,'<a target="blank" href="'.concat(webix.template.escape(e),'">').concat(webix.template.escape(t),"</a>")},IF:function Ta(e,t,n){return e?t:n}}),Mt={};function Dt(e,t,n,i){i=o(i),e.setCellValue(t,n,i)}function Nt(e){var t=e?"number":null;return De(e,"string")?t="string":De(e,"date")&&(t="date"),t}function Ut(e,t,n){var i=e.getRow(t).$cellType;if(i&&i[n])return i[n]}function Tt(e,t,n,i,o,r){e._setType=!0;var a=e.getCellValue(t,n),l="string"==typeof a&&"="==a[0],s=!(r&&"date"!=i||e._loading_data&&"date"!=o),c=l&&!i?function f(e,t){for(var n,i=0,o=e.ranges._ranges,r=Ge(t,!0,o),a=0;a<r.length;a++)if(a%2==0){var l=r[a].match(/(([a-zA-Z]*\()|\))(?=(?:[^"]|"[^"]*")*$)/g);l&&l.forEach(function(e){-1<e.indexOf("(")?(i++,n||/^(SUM|AVERAGE|MAX|MIN|INT|ROUND|ROUNDDOWN|ROUNDUP|TRUNC)?\($/gi.test(e)||(n=i)):--i<n&&(n=null)})}else if(!n){if(webix.isArray(r[a])){if(r[a][0]!=e.getActiveSheet())continue;r[a]=r[a][1]}var s=/^[A-Za-z]+$/.test(r[a])?We(o[r[a]].split(":")[0]):We(r[a]),c=Ne(e,s[0],s[1]);if(c)return{fmt:ve(c)}}}(e,a):null,u=Nt(i||c),h=function v(e,t,n,i,o,r,a,l){var s="string";if(!webix.isUndefined(i))if(r)s="number",/^=(now|date|time)\(/i.test(i)&&(s="date");else if(isNaN(i)){if("string"!=a&&o){var c=webix.Date.strToDate(webix.i18n.spreadsheet.formats.parseDateTime);""==(i=c(i))||isNaN(i)||(l?Dt(e,t,n,i):e.ignoreUndo(function(){return Dt(e,t,n,i)}),s="date")}}else s="number";return s}(e,t,n,a,s,l,u,r);(function m(e,t,n,i,o,r,a){var l=e.getCellValue(t,n),s="string"==typeof l&&"="==l[0],c=e.getCellEditor(t,n);if(a)e.setFormat(t,n,a.fmt);else if("date"==i){if("date"!=r){var u=s&&0==l.toUpperCase().indexOf("=TIME("),h=webix.i18n.spreadsheet.formats,d=u?h.timeFormat:h.dateFormat;e.setFormat(t,n,d)}s||c||e.setCellEditor(t,n,{editor:"excel_date"})}else"string"!=r&&"string"==i&&"string"!=o&&e.setFormat(t,n,"@");("date"!=i||s)&&c&&"excel_date"==c.editor&&e.setCellEditor(t,n,null)})(e,t,n,o=function g(e,t,n,i,o){return e=e||t,"string"==t?e="string":n&&(e=n),"date"==e&&!i&&Ue(o)&&(e="number"),e}(o,h,u,l,a),h,u,c);var d=e.getRow(t);d.$cellType||(d.$cellType={}),d.$cellType[n]=o,delete e._setType}webix.extend(Mt,It),webix.extend(Mt,At),webix.extend(Mt,Ft),webix.extend(Mt,Ot);var jt=Object.freeze({init:function ja(e){e.attachEvent("onCellChange",function(e,t,n,i){this._setType||Tt(this,e,t,Ne(this,e,t),i)}),e.attachEvent("onBeforeFormatChange",function(e,t,n){this._setType||Tt(this,e,t,n,null,!0)}),e.attachEvent("onAction",function(e,t){"dropdown"==e&&t.newValue&&"excel_date"==t.newValue.editor&&"date"!=Nt(Ne(this,t.row,t.column))&&this.setFormat(t.row,t.column,webix.i18n.spreadsheet.formats.dateFormat)})},getType:Ut}),Bt=[];for(var Ht in Mt)"__esModule"!=Ht&&Bt.push(Ht);function Pt(t){return t.attachEvent("onComponentInit",function(){return function e(s){var c=s.$$("liveEditor");function n(e,t){var n=c.config.activeCell;n&&n.row==e&&n.column==t&&o({row:e,column:t})}function i(e,t,n){return!(!s.getCellEditor(t.row,t.column)&&o(t,n))||(c.focus(),
n||c.paintValue(),!1)}function r(e,t){var n=s.getCellEditor(e,t)||s.isCellLocked(e,t);return n?c.disable():c.enable(),n}function o(e,t){var n=r(e.row,e.column);return c.config.activeCell=e,c.setValue(t?"":function o(e,t){var n=s.getCellValue(e,t);if("date"==Ut(s,e,t)&&"="!=n[0]){var i=Te(s.getRow(e),t);n=i(w(n))}return n}(e.row,e.column)),s.$handleSelection=n?null:function(e,t,n,i){return a(n,i,e)},!n}function a(e,t,n){var i="";if(c._activeMath){var o=s.getActiveSheet();c._activeMath!=o&&(i=c.prepareSheet(o))}var r=e==t?i+e:"".concat(i+e,":").concat(i+t),a=c._update_range;if(a&&a.pos==c.getInputNode().selectionStart-a.len)c.setRange(r,!0);else{if(!c.expectOperator())return function l(e){c.isEnabled()&&(c.showActiveSheet(),c.updateCellValue(),c.setValue(e?s.getCellValue(e.row,e.column):""));return!0}(n);c.setRange(r)}return webix.delay(function(){c.focus(),c.paintValue()}),!1}function l(){c.define({activeCell:null}),c.setValue(""),c.disable()}s._table.attachEvent("onBeforeEditStart",function(e){var t=s._table.$anyKey;return s._table.$anyKey=!1,i(this,e,t)}),s._table.attachEvent("onBeforeLiveEditor",function(e){return i(this,e,!1)}),s._table.attachEvent("onItemDblClick",function(e){s.getCellEditor(e.row,e.column)&&this.edit(e),c.focus()}),s._table.attachEvent("onAfterScroll",function(){c.$view.contains(document.activeElement)&&c.paintValue()}),s.attachEvent("onCellChange",function(e,t,n){var i=c.config.activeCell;i&&i.row==e&&i.column==t&&c.setValue(n)}),s.attachEvent("onAfterSelect",function(e){if(!s.$handleSelection){var t=e[0];o(t)}}),s.attachEvent("onChange",function(e){if("update"==e&&c.isEnabled()){var t=c.config.activeCell;t&&c.getValue()!=s.getCellValue(t.row,t.column)&&(delete c._update_range,c.showActiveSheet(),c.updateCellValue())}}),s.attachEvent("onBeforeSheetShow",function(t){var e=c.config.activeCell;if(e){var n=c.getInputNode().selectionStart,i=c.getValue();s.getCellValue(e.row,e.column)!=i?(c._activeMath||(c._activeMath=s.getActiveSheet()),webix.delay(function(){if(t==c._activeMath){s.$handleSelection=null;var e=c.config.activeCell;s._table.select(e.row,e.column)}c.setValue(i),c.refresh(),c.focus(),c.getInputNode().setSelectionRange(n,n),c.paintValue(),s.$handleSelection=function(e,t,n,i){return a(n,i,e)}})):l()}}),s.attachEvent("onReset",function(){return s.$handleSelection=null}),s.attachEvent("onAfterLoad",function(){return l()}),s.attachEvent("onAction",function(e,t){var n=c.config.activeCell,i=n&&n.row==t.row&&n.column==t.column;!i||"lock"!=e&&"dropdown"!=e||r(t.row,t.column)}),s.attachEvent("onFormatChange",function(e,t){return n(e,t)}),s.attachEvent("onCellChange",function(e,t){return n(e,t)})}(t)}),{view:"toolbar",css:"webix_ssheet_toolbar",elements:[{view:"label",label:webix.i18n.spreadsheet.liveEditor.edit,width:60},{view:"live-editor",disabled:!0,id:"liveEditor",suggestData:Bt}]}}function Lt(e,t){return e[Wt(e,t)]}function Wt(e,t){for(var n=-1,i=0;n<0&&i<e.length;i++)t(e[i])&&(n=i);return n}var Kt=["rename-sheet","remove-sheet"],Yt={"remove-sheet":function(e){e.confirm({text:webix.i18n.spreadsheet.labels["sheet-remove-confirm"]}).then(function(){Jt(e,e._activeSheet)})},"rename-sheet":function(e){Qt(e,e._activeSheet)},"new-sheet":function(e){Xt(e)},"copy-sheet":function(e){!function t(e){Xt(e,e.serialize())}(e)}};function qt(t){t.attachEvent("onComponentInit",function(){return function e(t){tn(t),t.$$("add-sheet")&&t.$$("add-sheet").attachEvent("onItemClick",function(){Xt(t)});t.attachEvent("onAfterSheetShow",function(e){return function n(e,t){e.$$("sheets")&&(e.$$("sheets").select(t),e.$$("sheets").showItem(t))}(t,e)}),t.attachEvent("onCommand",function(e){Yt[e.id]&&Yt[e.id](this)}),t.$$("sheets")&&(t.$$("sheets").data.attachEvent("onStoreUpdated",function(){return function i(e){var t=e.$$("bottom-toolbar"),n=e.$$("sheets");n.count()<=n.config.width/e.config.sheetTabWidth?t.showBatch("pager",!1):t.showBatch("pager")}(t)}),t.$$("sheets").attachEvent("onAfterScroll",function(){return function o(e){var t=e.$$("sheets"),n=t.getScrollState().x;if(0==n)e.$$("prev-sheet").disable();else{e.$$(
"prev-sheet").enable();var i=e.config.sheetTabWidth;if(n==(t.count()-t.config.width/i)*i)return void e.$$("next-sheet").disable()}e.$$("next-sheet").enable()}(t)}),t.$$("sheets").attachEvent("onAfterDrop",function(e){return function r(e,t,n){for(var i in e._sheets)if(e._sheets[i].name==t){var o=e._sheets.splice(i,1);e._sheets.splice(n,0,o[0]);break}}(t,e.start,e.index)}),t.$$("sheets").attachEvent("onAfterEditStop",function(e){return en(t,e.old,e.value)}))}(t)})}function Gt(n){var t=n.config.readonly,e=n.config.sheetTabWidth,i={view:"toolbar",id:"bottom-toolbar",css:"webix_ssheet_bottom_toolbar webix_layout_toolbar",paddingY:0,height:34,elements:[{view:"button",type:"htmlbutton",width:34,id:"add-sheet",label:"<span class='webix_ssheet_icon_add_sheet'></span>",tooltip:webix.i18n.spreadsheet.tooltips["add-sheet"]||"",hidden:t},{id:"sheets",view:"sheets",width:5*e,editable:!t,editaction:"dblclick",editor:"text",editValue:"value",type:{width:e,height:32,template:function(e){return"<div>"+e.value+"</div>"+(t?"":"<div class='webix_input_icon wxi-menu-down webix_ssheet_sheet_arrow'></div>")},css:"ssheet_order_sheets"},onClick:{webix_ssheet_sheet_arrow:function(e,t){!function i(t,e,n){t.$sheetMenu||(t.$sheetMenu=webix.ui(function o(e){for(var t=[],n=0;n<Kt.length;n++)t.push({id:Kt[n],value:webix.i18n.spreadsheet.menus[Kt[n]]||Kt[n]});var i={view:"ssheet-suggest",data:t};return e.callEvent("onViewInit",["sheet-menu",i]),i}(t)),t._destroy_with_me.push(t.$sheetMenu),t.$sheetMenu.attachEvent("onItemClick",function(e){return function n(e,t){e.$sheetMenu.data.getMark(t,"webix_disabled")||Yt[t](e)}(t,e)}));t.$sheetMenu.getItem("remove-sheet")&&(1==t.$$("sheets").count()?t.$sheetMenu.disableItem("remove-sheet"):t.$sheetMenu.enableItem("remove-sheet"));t.callEvent("onBeforeSheetMenu",[n])&&t.$sheetMenu.show(e)}(n,e,t)}},on:{onItemClick:function(e){e!=n._activeSheet&&n.showSheet(e)}}},{view:"button",type:"htmlbutton",width:34,id:"prev-sheet",disabled:!0,hidden:!0,label:"<span class='webix_icon wxi-menu-left'></span>",batch:"pager",click:function(){Zt(n,-1)}},{view:"button",type:"htmlbutton",width:34,id:"next-sheet",hidden:!0,label:"<span class='webix_icon wxi-menu-right'></span>",batch:"pager",click:function(){Zt(n,1)}}]};return n.callEvent("onViewInit",["bottom-toolbar",i]),i}function Zt(e,t){var n=e.$$("sheets");n.scrollTo(n.getScrollState().x+t*e.config.sheetTabWidth,0)}function Xt(t,e){var n,i,o;return o=-1<(n=Wt(t._sheets,function(e){return e.name==t._activeSheet}))?n+1:t._sheets.length,i=function r(e){var t=e._sheets.length+1;for(;rn(e,webix.i18n.spreadsheet.labels.sheet+t);)t++;return webix.i18n.spreadsheet.labels.sheet+t}(t),e=e||{data:[]},t._sheets.splice(o,0,{name:i,content:e}),t.$$("sheets").add({id:i,value:i},o),t.showSheet(i),t.callEvent("onSheetAdd",[i]),i}function Qt(e,t){e.$$("sheets").edit(t)}function Jt(e,t){var n,i;1<e._sheets.length&&(n=Wt(e._sheets,function(e){return e.name==t}),e._sheets.splice(n,1),e._sheets[n]||(n=0),i=e._sheets[n],e._activeSheet=null,e.$$("sheets")&&(e.$$("sheets").remove(t),e.$$("sheets").refresh()),e.showSheet(i.name),e.callEvent("onSheetRemove",[t]))}function en(e,t,n){if(t!=n){for(var i=1;!n||rn(e,n);)n=webix.i18n.spreadsheet.labels.sheet+i,i++;n=n.replace(/[*?:[\]\\/]/g,"").substring(0,31);var o=rn(e,t),r=e.$$("sheets");if(o.name=n,e._activeSheet==t&&(e._activeSheet=n),r)r.getItem(t).value=n,r.data.changeId(t,n),r.refresh(n);e.callEvent("onSheetRename",[t,n])}}function tn(e,t){t||(t={data:[]}),t.sheets||(t=nn(t));var n=t.sheets[0].name;e._activeSheet="",e._sheets=t.sheets,function o(e,t){var n=e.$$("sheets");if(n){n.clearAll();var i=[];t.sheets.forEach(function(e){i.push({id:e.name,value:e.name})}),n.parse(i)}}(e,t),on(e,n)}function nn(e){return{sheets:[{name:webix.i18n.spreadsheet.labels.sheet+1,content:e}]}}function on(e,t){t!=e._activeSheet&&(e.callEvent("onBeforeSheetShow",[t])&&(e._activeSheet&&(rn(e,e._activeSheet).content=e.serialize()),e._activeSheet=t,ln(e,rn(e,t).content),e.callEvent("onAfterSheetShow",[t])))}function rn(e,t){return Lt(e._sheets,function(e){return e.name==t})}
function an(e){return e._activeSheet}function ln(e,t){var n=S(function r(e,t,n){for(var i=0;i<e.length;i++){var o=e[i];o[0]>t&&(t=o[0]),o[1]>n&&(n=o[1])}return[1*t,1*n]}(t.data,e.config.rowCount,e.config.columnCount),2),i=n[0],o=n[1];e.callEvent("onReset",[]),i==e.config.rowCount&&o==e.config.columnCount||(e.config.rowCount=i,e.config.columnCount=o,e._resetTable()),e._loading_data=!0,e.callEvent("onDataParse",[t]),e._loading_data=!1,e._table.refresh(),e.config.bottombar&&e.$$("sheets").refresh()}var sn=function(){function t(e){i(this,t),this.view=e}return c(t,[{key:"$init",value:function(){}},{key:"$show",value:function(){}},{key:"$hide",value:function(){}},{key:"open",value:function(){var e=this;this.$dialog||(this.$dialog=webix.ui(this.$init()),this.view._destroy_with_me.push(this.$dialog),this.$dialog.attachEvent("onHide",function(){return e.$hide()}));var t=this.$dialog.getBody();this.$dialog.show(),!1===this.$show(this.$dialog,t)&&this.close()}},{key:"close",value:function(){webix.UIManager.setFocus(this.view),this.$dialog.hide()}}]),t}();function cn(e){return e?('"'===(e=e.trim())[0]&&(e=e.substr(1)),'"'===e[e.length-1]&&(e=e.substr(0,e.length-1)),e):""}var un=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"$show",value:function(e,t){if(this.cell=this.view.getSelectedId(),!this.cell)return!1;t.elements.preview.setHTML(""),t.elements.url.setValue("");var n=function i(e){if(e&&0===e.indexOf("=IMAGE("))return{url:cn(e.substr(7,e.length-8))}}(this.view.getCellValue(this.cell.row,this.cell.column));n&&n.url&&t.elements.url.setValue(n.url),t.elements.url.focus()}},{key:"$init",value:function(){var t=this,e=this.view.config.save,n=e&&e.images||null;return{view:"ssheet-dialog",head:webix.i18n.spreadsheet.labels["image-title"],move:!0,position:"center",body:{view:"form",elements:[{view:"text",name:"url",placeholder:webix.i18n.spreadsheet.labels["image-url"],on:{onChange:function(e){return t.showPreview(e)}}},{view:"label",label:webix.i18n.spreadsheet.labels["image-or"],align:"center"},{view:"uploader",label:webix.i18n.spreadsheet.labels["image-upload"],upload:n,on:{onBeforeFileAdd:hn,onFileUpload:dn}},{view:"formlate",name:"preview",borderless:!0,css:"webix_ssheet_preview",template:"",height:50}]},on:{onSaveClick:function(){return t.okClick()},onHideClick:function(){return t.close()},onCancelClick:function(){return t.close()}}}}},{key:"okClick",value:function(){var e=this.cell,t=this.fillUrl(this.$dialog.getBody().elements.url.getValue());this.view.addImage(e.row,e.column,t),this.close()}},{key:"fillUrl",value:function(e){return/^[/]|((https?:\/|data:image)\/)/i.test(e)||(e="http://"+e),e}},{key:"showPreview",value:function(e){e&&(e=webix.template.escape(this.fillUrl(e)),this.$dialog.queryView("form").elements.preview.setHTML("<img class='webix_ssheet_cimage' src='"+e+"'></img>"))}}]),t}();function hn(e){var t=this;if(!this.config.upload){var n=new FileReader;return n.onload=function(e){return t.getFormView().elements.url.setValue(e.target.result)},n.readAsDataURL(e.file),!1}this.getFormView().elements.preview.setHTML("")}function dn(e,t){this.getFormView().elements.url.setValue(t.imageURL)}var fn=Object.freeze({action:"add-image-cell",DialogBox:un}),vn=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,un),c(t,[{key:"open",value:function(e){this.config=e.config,this.viewid=e.viewid,un.prototype.open.apply(this,arguments)}},{key:"$hide",value:function(){this.config=this.viewid=null}},{key:"$show",value:function(e,t){t.elements.preview.setHTML(""),t.elements.url.setValue(""),this.config&&this.config.data&&t.elements.url.setValue(this.config.data),t.elements.url.focus()}},{key:"okClick",value:function(){var e=this.fillUrl(this.$dialog.getBody().elements.url.getValue());this.viewid?this.view.views.update(this.viewid,e):this.view.views.add(null,null,"image",e),this.close()}}]),t}(),mn=Object.freeze({action:"add-image-top",DialogBox:vn}),gn={},wn={};function pn(e,t,n){var i=function $(e,t){var n,i,o,r,a,
l=2<arguments.length&&arguments[2]!==undefined?arguments[2]:"",s="return ",c=[],u=!1,h="",d="";if("="!=e[0])return!1;for(var f=1;f<e.length;f++){var v=e[f];if('"'==v)u=!u;else{if("{"==v&&"{"==e[f+1]){var m=S(He(e,f+2),2),g=m[0];f=m[1]+1,s+="this.p.".concat(g);continue}if(!u&&("'"==v||Be(v))){var w=S(He(e,f,l),4),p=w[0],b=w[1],x=w[2],_=w[3],y=p.toUpperCase();if(f=b-1,"#"==y[0]){s+=Ke(y);break}if("("==e[f+1])s+="this.m.".concat(y);else if(Le(y))":"==e[f+1]?(h=y,d=_,f++):""!==h?(s+=qe(c,h,y,d),h=""):s+=(n=c,i=_,o=S(We(y),2),r=o[0],a=o[1],""!==i?(n.push([r,a,r,a,i]),'this.vs("'.concat(i,'",').concat(r,",").concat(a,")")):(n.push([r,a,r,a,""]),"this.v(".concat(r,",").concat(a,")")));else{var C=Ye(c,t,y,_);if(""==C){s+=Ke("#REF!");break}s+=C}y!==p&&(e=e.substr(0,x)+y+e.substr(b));continue}}u?s+=v:"&"===v&&"&"!==e[f+1]?s+="+":"<"===v&&">"===e[f+1]?(s+="!=",f++):"="===v&&"<"!==e[f-1]&&">"!==e[f-1]?s+="==":s+=v}return{code:s+";",triggers:c,text:e}}(e,t,n);n&&!wn[n]&&(wn[n]={});var o=n?wn[n]:gn;return i.handler=o[e]=o[e]||function r(e){try{return new Function(e)}catch(t){return xn}}(i.code),i}function bn(e){gn={},e||(wn={})}function xn(){return webix.i18n.spreadsheet.table["math-error"]}function _n(e,t,n){for(var i=Ge(e,!0),o=i.length,r=n?n.start:null,a=n?n.end:null,l=t.count,s=t.id,c=1;c<o;c+=2)if(webix.isArray(i[c])){var u=S(i[c],3),h=u[0],d=u[1],f=u[2]?"'":"";i[c]=f+h+f+"!"+d}else{var v=S(We(i[c]),3),m=v[0],g=v[1],w=v[2],p=1&w,b=2&w,x=yn(i,c);if("move"==s&&(!t.cut||m>=r.row&&m<=a.row&&g>=r.column&&g<=a.column))g+=b?0:t.column,m+=p?0:t.row;else if("row"==s||"column"==s){var _="row"==s,y=_?m:g;if(l<0&&"start"==x&&t.start<=y&&We(i[c+2])[_?0:1]<=t.start+Math.abs(l)){i[c]=i[c+2]="#REF!",c+=2;continue}if(t.start<=y){var C=void 0;if(l<0&&t.start+Math.abs(l)>y){if(!x){i[c]="#REF!";continue}y=t.start-("end"==x?1:0),C=!0}C||(y+=l),_?m=y:g=y}}i[c]=g<=0||m<=0?"#REF!":(b?"$":"")+$[g]+(p?"$":"")+m}return i.join("")}function yn(e,t){return":"==e[t+1]&&e[t+2]?"start":":"==e[t-1]&&e[t-2]?"end":void 0}function Cn(e,t,n,i){for(var o=Ge(e,!0),r=0;r<o.length;r++)if(webix.isArray(o[r])){var a=o[r][0]===t;!i.multiSheet&&a&&(i.multiSheet=!0);var l=a?n:o[r][0];o[r]="'"+l+"'!"+o[r][1]}return o.join("")}function $n(e,t){var n=e._mathSheetCache;if(!n[t]){var i=t&&t!=an(e)?function o(c,u){var e=rn(c,u);if(!e)return;var h=e.content,d=[],f=[],v=[];return{init:function(){if(h.ranges)for(var e=0;e<h.ranges.length;e++){var t=h.ranges[e];v[t[0]]=t[1]}if(h.data)for(var n=0;n<h.data.length;n++){var i=S(h.data[n],3),o=i[0],r=i[1],a=i[2];if(d[o]||(d[o]=[]),"="===(d[o][r]=a)[0]){var l=c._mathSheetHelpers.parse(a,c._mathSheetCore,u);d[o][r]=l.handler;for(var s=0;s<l.triggers.length;s++)if(l.triggers[s][5]){f.push(h.data[n]);break}}}},getRangeCode:function(e){return v[e]},getRow:function(e){return d[e]||[]},getCell:function(e,t){var n=this.getRow(e)[t];return"function"==typeof n?c._mathSheetHelpers.execute(n,c._mathSheetCore):n},getRange:function(e,t,n,i){for(var o=[],r=e;r<=n;r++)for(var a=t;a<=i;a++)o.push(this.getCell(r,a));return o},getRangeCols:function(e,t,n,i){for(var o=[],r=0,a=e;a<=n;a++){o[r]={};for(var l=this.getRow(a),s=0,c=t;c<=i;c++)o[r]["data"+s]=l[c],s++;r++}return o}}}(e,t):function r(u){return{init:function(){},getRangeCode:function(e){return u.ranges.getCode(e)},getRow:function(e){return u.getRow(e)},getCell:function(e,t){return u.getRow(e)[t]},getRange:function(e,t,n,i){for(var o=[],r=e;r<=n;r++)for(var a=u.getRow(r),l=t;l<=i;l++)o.push(a[l]);return o},getRangeCols:function(e,t,n,i){for(var o=[],r=0,a=e;a<=n;a++){o[r]={};for(var l=u.getRow(a),s=0,c=t;c<=i;c++)o[r]["data"+s]=l[c],s++;r++}return o}}}(e);if(!i)return;(n[t]=function a(s){return{init:function(){s.init()},getRangeCode:function(e){return s.getRangeCode(e)},getItem:function(e){return s.getRow(e)},getValue:function(e,t){var n=s.getCell(e,t),i=1*n;return isNaN(i)||""===n?n||"":i},getRangeValue:function(e){var t=this.getRangeCode(e)||e;if(-1==t.indexOf(":"))return[];var n=V(t);return this.getRange.apply(this,n)},getRange:function(e,t,n,i){for(var o=s.getRange(e,t,n,i),r=o.length-1;0<=r;r--){
var a=o[r],l=""!==a?1*a:"";o[r]=isNaN(l)?a||"":l}return o},getRangeCols:function(e,t,n,i){return s.getRangeCols(e,t,n,i)}}}(i)).init()}return n[t]}function Sn(r,e,t){var n=[];if("string"==typeof e){var i=V(e,r);if(i){var o=$n(r,i[4]);n=o?function d(e,t,n,i,o,r){for(var a=[],l=t;l<=i;l++)for(var s=this.getItem(l),c=n;c<=o;c++){var u=s[c]||0===s[c]?s[c]:"";u={id:u,value:u},r&&r!=e._activeSheet||(u.$value=kt(e,s,0,s[c],{id:c},!0),u.format=s.$cellFormat&&s.$cellFormat[c]),a.push(u)}return a}.apply(o,[r].concat(k(i))):[]}}else if(webix.isArray(e))for(var a=0;a<e.length;a++){var l=e[a]||0===e[a]?e[a]:"";l="object"===g(l)?webix.copy(l):{id:l,value:l},n.push(l)}if(t){if(t.unique)for(var s={},c=n.length-1;0<=c;c--)s[n[c].id]?n.splice(c,1):s[n[c].id]=!0;for(var u=!1,h=n.length-1;0<=h;h--)n[h].value=String(n[h].value),""===n[h].value&&(n.splice(h,1),u=!0);t.order&&n.sort(function(e,t){var n=r.$$("cells").data.sorting.as,i=e.value,o=t.value;return webix.rules.isNumber(i)&&webix.rules.isNumber(o)?n["int"](i,o):n.string(i,o)}),u&&t.filter&&n.unshift(function f(){return{id:"$empty",value:"",$value:"<span class='webix_ssheet_empty'>"+webix.i18n.spreadsheet.labels["dropdown-empty"]+"</span>"}}()),t.empty&&n.unshift({id:"$empty",$empty:!0,value:""})}return n}var kn=function(){function t(e){i(this,t),this._master=e,this._ranges={}}return c(t,[{key:"clear",value:function(){this._ranges={}}},{key:"add",value:function(e,t){this._master.callEvent("onAfterRangeSet",[e,t]),this._ranges[e]=t,bn(!0),this._master.callEvent("onMathRefresh",[])}},{key:"getCode",value:function(e,t){if(t){var n=$n(this._master,t);return n?n.getRangeCode(e):null}return this._ranges[e]}},{key:"remove",value:function(e){delete this._ranges[e],bn(!0),this._master.callEvent("onMathRefresh",[])}},{key:"getRanges",value:function(){var e=[];for(var t in this._ranges)e.push({name:t,range:this._ranges[t]});return e}},{key:"parse",value:function(e){if(e)for(var t=e.length;t--;){var n=e[t];this._ranges[n[0]]=n[1]}}},{key:"serialize",value:function(){var e,t=[];for(e in this._ranges)t.push([e,this._ranges[e]]);return t}}]),t}(),En=webix.i18n.spreadsheet.labels;var Vn=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"$show",value:function(e,t){if(this.cell=this.view.getSelectedId(),!this.cell)return!1;var o=t.elements;this.view.$handleSelection=function(e,t,n,i){return o.range.setValue(n+":"+i),!1},o.range.setValue("");var n=function i(e){if(e&&0===e.indexOf("=SPARKLINE(")){var t=e.substr(11,e.length-12).split(",");return{range:t[0],type:cn(t[1]),color:cn(t[2]),negativeColor:cn(t[3])}}}(this.view.getCellValue(this.cell.row,this.cell.column));n&&(t.blockEvent(),o.type.setValue(n.type),o.range.setValue(n.range),n.color&&(o.color_def.setValue(n.color),o.color_pos.setValue(n.color)),n.negativeColor&&o.color_neg.setValue(n.negativeColor),t.unblockEvent(),zn(this)),o.range.focus()}},{key:"$hide",value:function(){this.view.$handleSelection=null}},{key:"$init",value:function(){var e=this;return{view:"ssheet-dialog",head:En["sparkline-title"],move:!0,position:"center",body:{view:"form",visibleBatch:1,on:{onChange:function(){return zn(e)}},elements:[{view:"richselect",name:"type",label:En["sparkline-type"],value:"line",labelPosition:"left",suggest:{view:"ssheet-form-suggest",data:function t(){return[{id:"line",value:En["line-chart"]},{id:"spline",value:En["spline-chart"]},{id:"splineArea",value:En["splinearea-chart"]},{id:"area",value:En["area-chart"]},{id:"bar",value:En["bar-chart"]},{id:"pie",value:En["pie-chart"]}]}()},on:{onChange:In}},{view:"text",label:En["sparkline-range"],name:"range"},{view:"ssheet-colorpicker",label:En["sparkline-color"],name:"color_def",id:"add_sparkline_color",value:"#6666FF",batch:"1"},{view:"ssheet-colorpicker",label:En["sparkline-positive"],name:"color_pos",value:"#6666FF",batch:"2"},{view:"ssheet-colorpicker",label:En["sparkline-negative"],name:"color_neg",value:"#FF6666",batch:"2"},{view:"formlate",name:"preview",borderless:!0,css:"webix_ssheet_preview",height:50}]},on:{onSaveClick:function(){
return function i(e){var t=e.$dialog.getBody(),n=Rn(t,t.getValues());e.checkRange(n.range)&&(e.view.addSparkline(e.cell.row,e.cell.column,n),e.close())}(e)},onHideClick:function(){return e.close()},onCancelClick:function(){return e.close()}}}}},{key:"checkRange",value:function(e){if(e&&V(e,this.view))return!0;this.view.alert({text:En["error-range"]})}}]),t}();function zn(e){var t=e.$dialog.getBody(),n=t.getValues();if(n.range&&e.checkRange(n.range)){var i,o=function a(e,t){var n=[];if("string"==typeof t){var i=V(t,e);if(i){var o=$n(e,i[4]);n=o?o.getRange.apply(o,i):[]}}else n=webix.copy(t);return n}(e.view,n.range),r=Rn(t);for(i=0;i<o.length;i++)o[i]=o[i]||0;t.elements.preview.setValue(webix.Sparklines.getTemplate(r)(o,{width:200,height:40}))}}function Rn(e,t){var n=e.getValues();return t=t||{type:n.type},"bar"===n.type?(t.color=n.color_pos,t.negativeColor=n.color_neg):e.elements.color_def.isVisible()&&(t.color=n.color_def),t}function In(e){var t=this.getFormView();switch(e){case"pie":t.showBatch(3);break;case"bar":t.showBatch(2);break;default:t.showBatch(1)}}var An=Object.freeze({action:"add-sparkline",DialogBox:Vn}),Fn=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"$show",value:function(e){var t=e.$$("table"),r=e.$$("form");r.clear(),r.clearValidation(),r.elements.name.focus(),r.elements.range.setValue(this.view.getSelectedRange()),t.clearAll(),t.parse(this.view.ranges.getRanges()),this.view.$handleSelection=function(e,t,n,i){return r.elements.range.setValue(function o(e,t,n){return(n?R(n)+"!":"")+e+":"+t}(n,i,"")),!1}}},{key:"$hide",value:function(){this.view.$handleSelection=null}},{key:"saveClick",value:function(){var e=this.$dialog.$$("form"),t=e.getValues(),n=t.range.indexOf("!");if(t.range=t.range.substr(0,n+1)+t.range.substr(n+1).toUpperCase(),t.name=t.name.toUpperCase(),e.setValues(t),e.validate()){var i=this.$dialog.$$("table");t.id&&i.exists(t.id)?i.updateItem(t.id,t):i.add(t),this.view.ranges.add(t.name,t.range),e.clear()}}},{key:"removeRange",value:function(t){var n=this;this.view.confirm({text:webix.i18n.spreadsheet.labels["range-remove-confirm"]}).then(function(){var e=n.$dialog.$$("table");n.view.ranges.remove(e.getItem(t).name),e.remove(t)})}},{key:"editRange",value:function(e){var t=this.$dialog.$$("form");t.clearValidation(),t.setValues(this.$dialog.$$("table").getItem(e))}},{key:"$init",value:function(){var r=this,e={type:"clean",cols:[{view:"ssheet-dialog-table",id:"table",borderless:!0,columns:[{id:"name",header:webix.i18n.spreadsheet.labels["range-name"],width:120},{id:"range",header:webix.i18n.spreadsheet.labels["range-cells"],width:120},{template:"<div class='webix_icon wxi-pencil'></div>",width:40},{template:"<div class='webix_icon wxi-trash'></div>",width:40}],autowidth:!0,height:150,onClick:{"wxi-trash":function(e,t){return r.removeRange(t)},"wxi-pencil":function(e,t){return r.editRange(t)}}},{width:250,view:"form",id:"form",rules:{name:function(n){var e=/^[A-Za-z]+$/.test(n),t=r.$dialog.$$("table"),i=r.$dialog.$$("form").getValues(),o=!0;return t.eachRow(function(e){var t=this.getItem(e);t.name==n&&t.id!=i.id&&(o=!1)}),e&&o},range:M},elementsConfig:{labelWidth:70},elements:[{view:"text",name:"name",gravity:1,label:webix.i18n.spreadsheet.labels["range-name"]},{view:"text",name:"range",gravity:1,label:webix.i18n.spreadsheet.labels["range-cells"]},{cols:[{},{view:"button",value:"Save",click:function(){return r.saveClick()}}]}]}]};return{view:"ssheet-dialog",move:!0,head:webix.i18n.spreadsheet.labels["range-title"],buttons:!1,autoheight:!0,width:610,position:"center",body:e,on:{onSaveClick:function(){return r.close()},onHideClick:function(){return r.close()},onCancelClick:function(){return r.close()}}}}}]),t}(),On=Object.freeze({action:"add-range",DialogBox:Fn}),Mn=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"$show",value:function(){var o=this.$dialog.$$("form");if(this.cell=this.view.getSelectedId(!0),!this.cell.length)return!1;o.clear();var e=this.view.getCellEditor(
this.cell[0].row,this.cell[0].column);e&&e.options&&o.elements.range.setValue(e.options),this.view.$handleSelection=function(e,t,n,i){return o.elements.range.setValue(n+":"+i),!1},o.elements.range.focus()}},{key:"$hide",value:function(){this.view.$handleSelection=null}},{key:"$init",value:function(){var e=this;return{view:"ssheet-dialog",position:"center",head:webix.i18n.spreadsheet.labels["dropdown-title"],move:!0,body:{view:"form",id:"form",rows:[{view:"text",label:webix.i18n.spreadsheet.labels["dropdown-range"],name:"range"}]},on:{onSaveClick:function(){return e.okClick()},onHideClick:function(){return e.close()},onCancelClick:function(){return e.close()}}}}},{key:"okClick",value:function(){var t=this.$dialog.$$("form").elements.range.getValue();t?V(t,this.view)?(m.set(function(){for(var e=0;e<this.cell.length;e++)this.view.setCellEditor(this.cell[e].row,this.cell[e].column,{editor:"ss_richselect",options:t})},this),this.close()):this.view.alert({text:webix.i18n.spreadsheet.labels["error-range"]}):this.close()}}]),t}(),Dn=Object.freeze({action:"add-dropdown",DialogBox:Mn}),Nn="webix_lock";function Un(n,e,t,i,o){var r=n.$$("cells");"object"===g(e)&&"object"===g(t)?m.set(function(){!function a(e,t,n,i){for(var o=e.row;o<=t.row;o++)for(var r=e.column;r<=t.column;r++)n.call(i||this,o,r)}(e,t,function(e,t){Un(n,e,t,i,!0)})}):(i=!1!==i,((n._locks[e]=n._locks[e]||{})[t]=i)?r.addCellCss(e,t,Nn,!0):r.removeCellCss(e,t,Nn,!0),n.callEvent("onAction",["lock",{row:e,column:t,value:!i,newValue:i}]));o||r.refresh()}function Tn(e,t,n){return!!e._locks[t]&&e._locks[t][n]}var jn=Object.freeze({lockCss:Nn,init:function Ba(o){o._locks={},o.attachEvent("onReset",function(){return function t(e){e._locks={}}(o)}),o.attachEvent("onUndo",function(e,t,n,i){"lock"===e&&Un(o,t,n,!!i,!0)}),o.attachEvent("onCommand",function(e){"lock-cell"===e.id&&function i(e){var t=e.$$("cells").getSelectArea();if(t){var n=e.isCellLocked(t.start.row,t.start.column);e.lockCell(t.start,t.end,!n)}}(o)}),o.attachEvent("onAction",function(e,t){"before-grid-change"==e&&function c(e,t,n,i){var o=n.locked,r=o.length;if(t)for(;r--;){var a=S(o[r],2),l=a[0],s=a[1];(l&&"row"==e&&l>=i.row||s&&"column"==e&&s>=i.column)&&("row"==e?l<i.row-t?o.splice(r,1):o[r][0]=1*l+t:"column"==e&&(s<i.column-t?o.splice(r,1):o[r][1]=1*s+t))}}(t.name,t.inc,t.data,t.start)})},lockCell:Un,isCellLocked:Tn,serialize:function Ha(e,t){var n,i,o=[];for(n in e._locks)for(i in e._locks[n])e._locks[n][i]&&o.push([n,i]);t.locked=o},load:function Pa(e,t){var n,i=t.locked;if(i)for(n=0;n<i.length;n++)Un(e,i[n][0],i[n][1],!0,!0)}});function Bn(e,t,n){var i=e.$$("cells").getSpan()[t];if(i)return i[n]}function Hn(e,t,n,i,o){if(!(n<2&&i<2)&&(o||e.callEvent("onBeforeSpan",[t.row,t.column,[n,i]]))){var r=e.getRow(t.row),a=r.$cellCss&&r.$cellCss[t.column]||"",l=Kn(e,t.row,t.column);l&&(a+=" "+l),function u(e,t,n,i){var o,r,a,l=1*t.row,s=1*t.column,c=!e.getCellValue(l,s);for(o=l;o<l+i;o++)for(r=s;r<s+n;r++)""===(a=e.getCellValue(o,r))||r==s&&o==l||(c&&(c=!1,e.setCellValue(l,s,a)),e.setCellValue(o,r,""))}(e,t,n,i),e.$$("cells").addSpan(t.row,t.column,n,i,null,a),o||(e.callEvent("onAfterSpan",[t.row,t.column,[n,i]]),p(e,"spans",{row:t.row,column:t.column,x:n,y:i}))}}function Pn(e,t){var n=e.$$("cells").getSpan(t.row,t.column);n&&e.callEvent("onBeforeSplit",[t.row,t.column,[n[2],n[3]]])&&(e.$$("cells").removeSpan(t.row,t.column),e.callEvent("onAfterSplit",[t.row,t.column,[n[2],n[3]]]),p(e,"spans",{row:t.row,column:t.column,x:0,y:0}))}function Ln(e){var t,n,i,o;i=o=0,t=n=Infinity;for(var r=0;r<e.length;r++){var a=1*e[r].column,l=1*e[r].row;t=Math.min(a,t),i=Math.max(a,i),n=Math.min(l,n),o=Math.max(l,o)}return{cell:{row:n,column:t},x:i-t+1,y:o-n+1}}function Wn(e,t,n,i,o){var r=Bn(e,t,n);r&&(o?-1==r[3].indexOf(i)&&(r[3]+=" "+i):r[3]=r[3].replace(" "+i,""))}function Kn(e,t,n){return e.getCellEditor(t,n)?"ss_editor":e.getCellFilter(t,n)?"ss_filter":""}var Yn={"border-right":function(e,t,n,i){return i.end.column==t+n[0]-1},"border-bottom":function(e,t,n,i){return i.end.row==e+n[1]-1}};function qn(t,e,n,i,o){
var r="row"==e.group?t[3]:t[2],a="row"==e.group?[1*t[0],1*t[0]+t[3]-1]:[1*t[1],1*t[1]+t[2]-1],l="row"==e.group?[n.row,i.row]:[n.column,i.column],s=l[1]-l[0]+1;l[0]=Math.max(l[0],a[0]),l[1]=Math.min(l[1],a[1]);var c=l[0]==a[0];if(l[0]<=l[1]){if("del"==e.id&&(s=-1*(l[1]-l[0]+1)),"del"==e.id&&c&&-1*s<r){var u=Lt(o,function(e){return e[0]==t[0]&&e[1]==t[1]});u[0]="row"==e.group?u[0]-s:u[0],u[1]="column"==e.group?u[1]-s:u[1];var h=Wt(o,function(e){return e[0]==u[0]&&e[1]==u[1]&&(e[2]!=u[2]||e[3]!=u[3])});-1<h&&o.splice(h,1)}c&&"add"==e.id||(t[2]="column"==e.group?t[2]+s:t[2],t[3]="row"==e.group?t[3]+s:t[3])}return t}function Gn(i,e,t,n,o,r){var a=i._table.getSpan(t,n);if(e&&e.span){var l=e.span;t==1*l[0]+r.row&&n==1*l[1]+r.column&&(i._table.mapCells(t,n,l[3],l[2],function(e,t,n){(a=i._table.getSpan(t,n))&&Pn(i,{row:a[0],column:a[1]})},!0),Hn(i,{row:t,column:n},l[2],l[3]),1===o&&Pn(i,{row:e.row,column:e.col}))}else a&&Pn(i,{row:a[0],column:a[1]})}var Zn=Object.freeze({init:function La(l){l.attachEvent("onStyleChange",function(e,t,n){var i=Bn(l,e,t);if(i){var o=Kn(l,e,t);i[3]=(n?n.id:"")+(o?" "+o:"")}}),l.attachEvent("onDataParse",function(e){if(e.spans)for(var t=0;t<e.spans.length;t++){var n=e.spans[t];Hn(l,{row:n[0],column:n[1]},1*n[2],1*n[3],!0)}}),l.attachEvent("onDataSerialize",function(e){var t=[],n=l.$$("cells").getSpan();if(n){for(var i in n){var o=n[i];for(var r in o){var a=o[r];t.push([1*i,1*r,a[0],a[1]])}}e.spans=t}}),l.attachEvent("onUndo",function(e,t,n,i,o){"span"!=e&&"split"!=e||("span"==e&&(o=!o),function r(e,t,n,i,o){o?Pn(e,{row:t,column:n}):Hn(e,{row:t,column:n},i[0],i[1])}(l,t,n,i,o))}),l.attachEvent("onAction",function(e,t){if("lock"==e)Wn(l,t.row,t.column,Nn,t.newValue);else if("before-grid-change"==e)!function c(e,t,n,i){var o=n.spans;if(t)for(var r=o.length-1;0<=r;r--){var a=S(o[r],2),l=a[0],s=a[1];t<0&&("row"==e&&l<=i.row-t-1?t=i.row-o[r][0]:"column"==e&&s<=i.column-t-1&&(t=i.column-o[r][1])),("row"==e&&l>=i.row||"column"==e&&s>=i.column)&&(o[r][0]="row"==e?1*l+t:l,o[r][1]="column"==e?1*s+t:s)}}(t.name,t.inc,t.data,t.start);else if("check-borders"==e)return function a(e,t,n,i,o){var r=Bn(e,t,n);return!(!r||!Yn[o])&&Yn[o](t,n,r,i)}(l,t.row,t.column,t.area,t.mode)})},addSpan:Hn,removeSpan:Pn,getRange:Ln,setSpanCss:Wn,adjustSpan:qn,pasteSpan:Gn});function Xn(e){e._table._ssFilters={},ii(e)}function Qn(e){var t=e._ssFilters,n=[];for(var i in t)for(var o in t[i])n.push(t[i][o]);return n}function Jn(e,t){var i=!1;F(t,e,function(e,t){var n=e._table._ssFilters[t.row];n&&n[t.column]&&(i=!0,delete n[t.column],e._table.removeCellCss(t.row,t.column,"ss_filter"),e._table.removeCellCss(t.row,t.column,"ss_filter_active"),Wn(e,t.row,t.column,"ss_filter",!1),Wn(e,t.row,t.column,"ss_filter_active",!1))}),i&&e.filterSpreadSheet()}function ei(e,t,n,i){(i||(i={end:{row:t}}),i.end)&&(i=function o(e,t,n,i){if(t===i)for(i=t;i<e.config.rowCount&&e.getCellValue(i+1,n);i++);return I(t+1,n,i,n)}(e,t,n,i.end.row));return i}function ti(e){return!!(e.includes||e.condition&&e.condition.filter)}function ni(e){var t=e._table._ssFilters;for(var n in t)for(var i in t[n])if(t[n][i])return!0;return!1}function ii(e){var t=ni(e);e.callEvent("onCommand",[{id:"toolbar-update",name:"create-filter",value:t}])}function oi(e,t,n){for(var i=t;i<e.config.rowCount;i++){var o=e.getRow(i+1),r=o["$"+n]||o[n];if(r||0===r){var a=Ut(e,i+1,n);return"number"==a||"date"==a?a:"text"}if(""!==r)break}return"text"}function ri(e,t,n,i,o,r){var a=t.filter;a.options=D(a.options,"row",r.row,{column:t.col,row:t.row}),a.options=D(a.options,"column",r.column,{column:t.col,row:t.row}),delete a.value,delete a.handler,e.setCellFilter(n,i,a)}function ai(e,t){for(var n=ui[e],i=0;i<n.length;i++)if(n[i].id==t)return n[i]}function li(e){return webix.copy(ui[e])}function si(e,t){var n=t.includes,i=t.condition,o=ai(e,i.type),r=o?o.handler:function(){return!0};if(n)for(var a=0;a<n.length;a++)n[a]=String(n[a]);return function(e){return n?-1!==n.indexOf(e):""===i.filter||r(e,i.filter)}}function ci(e){return e&&!isNaN(e)&&!Ue(e)}var ui={number:[{id:"greater",batch:"text",
handler:function(e,t){return""!==e&&1*t<1*e}},{id:"less",batch:"text",handler:function(e,t){return""!==e&&1*e<1*t}},{id:"greaterOrEqual",batch:"text",handler:function(e,t){return""!==e&&1*t<=1*e}},{id:"lessOrEqual",batch:"text",handler:function(e,t){return""!==e&&1*e<=1*t}},{id:"equal",batch:"text",handler:function(e,t){return e==t}},{id:"notEqual",batch:"text",handler:function(e,t){return e!=t}},{id:"contains",batch:"text",handler:function(e,t){return-1!==e.toLowerCase().indexOf(t.toLowerCase())}},{id:"notContains",batch:"text",handler:function(e,t){return-1===e.toLowerCase().indexOf(t.toLowerCase())}}],text:[{id:"contains",batch:"text",handler:function(e,t){return-1!==e.toLowerCase().indexOf(t.toLowerCase())}},{id:"notContains",batch:"text",handler:function(e,t){return-1===e.toLowerCase().indexOf(t.toLowerCase())}},{id:"equal",batch:"text",handler:function(e,t){return e.toLowerCase()===t.toLowerCase()}},{id:"notEqual",batch:"text",handler:function(e,t){return e.toLowerCase()!==t.toLowerCase()}},{id:"beginsWith",batch:"text",handler:function(e,t){return 0===e.toLowerCase().lastIndexOf(t.toLowerCase(),0)}},{id:"notBeginsWith",batch:"text",handler:function(e,t){return 0!==e.toLowerCase().lastIndexOf(t.toLowerCase(),0)}},{id:"endsWith",batch:"text",handler:function(e,t){return-1!==e.toLowerCase().indexOf(t.toLowerCase(),e.length-t.length)}},{id:"notEndsWith",batch:"text",handler:function(e,t){return-1===e.toLowerCase().indexOf(t.toLowerCase(),e.length-t.length)}}],date:[{id:"greater",batch:"datepicker",handler:function(e,t){return ci(e)&&t<e}},{id:"less",batch:"datepicker",handler:function(e,t){return ci(e)&&e<t}},{id:"greaterOrEqual",batch:"datepicker",handler:function(e,t){return ci(e)&&t<=e}},{id:"lessOrEqual",batch:"datepicker",handler:function(e,t){return ci(e)&&e<=t}},{id:"equal",batch:"datepicker",handler:function(e,t){return ci(e)&&e==t}},{id:"notEqual",batch:"datepicker",handler:function(e,t){return!ci(e)||e!=t}},{id:"between",batch:"daterangepicker",handler:function(e,t){return!!ci(e)&&((!t.start||e>t.start)&&(!t.end||e<t.end))}},{id:"notBetween",batch:"daterangepicker",handler:function(e,t){return!!ci(e)&&(!t.start||e<=t.start||!t.end||e>=t.end)}}]},hi=Object.freeze({init:function Wa(l){var s=l._table;l.attachEvent("onReset",function(){return Xn(l)}),Xn(l),s.on_click.ssheet_filter_sign=function(e,t){var n=s._ssFilters[t.row][t.column],i=n.mode||oi(l,t.row,t.column),o=[];n.options&&("string"==typeof n.options?o=Sn(l,n.options,{unique:!0,order:!0,filter:!0}):webix.isArray(n.options)&&(o=Sn(l,n.options,{filter:!0}))),l.callEvent("onCommand",[{id:"start-filter",cell:t,mode:i,options:o,filter:n}])},s.on_dblclick.ssheet_filter_sign=function(){return!1},l.attachEvent("onCommand",function(e){"create-filter"===e.id&&function t(n){var i=n._table.getSelectArea();ni(n)?(m.set(function(){n.removeFilters()}),i&&n._table.addSelectArea(i.start,i.end)):i&&m.set(function(){for(var e=i.start.row,t=i.start.column;t<=i.end.column;t++)n.setCellFilter(e,t,{options:i})}),ii(n)}(l)});var c=!1;l.attachEvent("onUndo",function(e,t,n,i){if("filter"==e)i="object"==g(i)?i:null,l.setCellFilter(t,n,i),c=!0;else if("apply-filter"==e){var o=l.getCellFilter(t,n);if(o.value=i,delete o.handler,i&&i.condition){var r=o.mode||oi(l,t,n);o.handler=si(r,o.value)}c=!0;var a=i&&ti(i);s[a?"addCellCss":"removeCellCss"](t,n,"ss_filter_active"),Wn(l,t,n,"ss_filter_active",a)}}),l.attachEvent("onAfterUndo",function(){c&&(l.filterSpreadSheet(),c=!1)}),l.attachEvent("onAction",function(e,t){"filter"==e?(Wn(l,t.row,t.column,"ss_filter",t.newValue),Wn(l,t.row,t.column,"ss_filter_active",t.newValue&&t.newValue.value&&ti(t.newValue.value)),ii(l)):"before-grid-change"==e&&function u(e,t,n,i){var o=n.filters;if(t)for(var r=o.length-1;0<=r;r--){var a=S(o[r],3),l=a[0],s=a[1],c=a[2];if("row"==e&&l>=i.row||"column"==e&&s>=i.column){if("row"==e){if(l<i.row-t){o.splice(r,1);continue}o[r][0]=1*l+t}else if("column"==e){if(s<i.column-t){o.splice(r,1);continue}o[r][1]=1*s+t}c.options=D(c.options,e,t,i)}}}(t.name,t.inc,t.data,t.start)})},getFilters:Qn,serialize:function Ka(e,t){
for(var n=Qn(e._table),i=t.filters=[],o=0;o<n.length;o++){var r=n[o],a=r.row,l=r.column,s=r.options,c=r.mode,u=r.value;u=u&&webix.copy(u),i.push([a,l,{value:u,options:s,mode:c}])}},load:function Ya(e,t){var n=t.filters;if(n){for(var i=0;i<n.length;i++)e.setCellFilter(n[i][0],n[i][1],n[i][2]||{options:""});e.filterSpreadSheet()}},clearFilters:Jn,isFilter:function qa(e,t,n){var i=e._table._ssFilters;return i[t]&&!!i[t][n]},calibrateRange:ei,isValueActive:ti,getFilterMode:oi,pasteFilter:ri,getSingleOption:ai,getConditions:li,getFilterFunction:si}),di=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"open",value:function(e){var t=this;if(this.cell=e.cell,this.$mode=e.mode,this.$filter=e.filter,!this.cell||this.view.isCellLocked(this.cell.row,this.cell.column))return!1;if(!this.$dialog){this.$dialog=webix.ui(this.$init()),this.view._destroy_with_me.push(this.$dialog),this.$dialog.attachEvent("onHide",function(){return t.$hide()});var n=this.view.$$("cells");n.attachEvent("onScrollY",function(){return t.syncScroll()}),n.attachEvent("onScrollX",function(){return t.syncScroll()})}if(!1===this.$show(this.$filter.value,e.options))return this.close();this.$dialog.show(this._getTargetNode())}},{key:"_getTargetNode",value:function(){var e=this.view.$$("cells");return e.getSpan(this.cell.row,this.cell.column)?e.getSpanNode(this.cell):e.getItemNode(this.cell)}},{key:"$show",value:function(e,t){var n=this.getList(),i=this.getFilter();i.config.mode!==this.$mode&&(i.define("conditions",this.getConditions(this.$mode)),i.define("mode",this.$mode));var o=e&&e.conditions&&e.conditions.type;o&&!ai(this.$mode,o)&&(e=""),n.clearAll(),n.data.importData(t),i.config.value="",i.setValue(webix.copy(e||{}))}},{key:"$init",value:function(){var e=this,t=webix.i18n.spreadsheet.labels;return{view:"popup",css:"ssheet_filter",padding:7,relative:"right",body:{margin:4,rows:[{view:"filter",field:"value",mode:"text",conditions:this.getConditions("text"),inputs:["text",{view:"ssheet-datepicker",timepicker:!0,batch:"datepicker",on:{onChange:function(){return e.getFilter().applyFilter()}}},{view:"ssheet-daterangepicker",timepicker:!0,batch:"daterangepicker",on:{onChange:function(){return e.getFilter().applyFilter()}}}],template:function(e){return e.$value||e.value}},{cols:[{},{view:"button",label:t.cancel,autowidth:!0,css:"webix_transparent",on:{onItemClick:function(){return e.close()}}},{view:"button",label:t.apply,autowidth:!0,css:"webix_secondary",on:{onItemClick:function(){return e.applyFilter()}}}]}]}}}},{key:"applyFilter",value:function(){var e=this.getFilter(),t=this.$filter.value,n=this.$filter.value=e.getValue();this.$filter.handler=si(this.$mode,n),this.view.filterSpreadSheet(),this.view.callEvent("onAction",["apply-filter",{row:this.cell.row,column:this.cell.column,value:t,newValue:n}]),this.setCellStyle(this.cell.row,this.cell.column,"ss_filter_active",ti(n)),this.close()}},{key:"setCellStyle",value:function(e,t,n,i){var o=this.view.$$("cells"),r=this.getSpan(o,e,t);r&&(i?-1==r[3].indexOf(n)&&(r[3]+=" "+n):r[3]=r[3].replace(" "+n,"")),o[i?"addCellCss":"removeCellCss"](e,t,n)}},{key:"getSpan",value:function(e,t,n){var i=e.getSpan()[t];if(i)return i[n]}},{key:"getList",value:function(){return this.getFilter().getChildViews()[2]}},{key:"getFilter",value:function(){return this.$dialog.getBody().getChildViews()[0]}},{key:"syncScroll",value:function(){if(this.$dialog.isVisible()){var e=this._getTargetNode(),t=this.view.$$("cells"),n=!1;if(e){var i=webix.html.offset(e),o=webix.html.offset(t.$view.getElementsByClassName("webix_ss_body")[0]),r=i.y-o.y,a=i.x-o.x+i.width;n=r<0||r+i.height>o.height||a<0||a-i.width>o.width-webix.env.scrollSize}!e||n?this.$dialog.show({x:-9999,y:-9999}):this.$dialog.show(e)}}},{key:"getConditions",value:function(e){for(var t=webix.i18n.filter,n=li(e),i=0;i<n.length;i++)n[i].id&&(n[i].value=t[n[i].id]);return n}}]),t}(),fi=Object.freeze({action:"start-filter",DialogBox:di}),vi=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{
key:"$show",value:function(){if(this.form=this.$dialog.$$("form"),this.list=this.$dialog.$$("list"),this.preview=this.$dialog.$$("preview"),this.cell=this.view.getSelectedId(!0),!this.cell.length)return!1;this.fillForm(this.view),this.view.$handleSelection=function(){return!1}}},{key:"$hide",value:function(){this.view.$handleSelection=null}},{key:"$init",value:function(){var o=this,t=webix.i18n.spreadsheet,e=t.labels,n=t.formats,i=de("int"),r=[{name:"zeros",view:"counter",css:"webix_ssheet_counter",label:e["decimal-places"],value:0,batch:"common"},{name:"separator",view:"checkbox",label:e.separator,batch:"common"},{view:"formlist",label:e.negative,name:"negative",batch:"common",css:"webix_ssheet_format_negative",template:function(e){if(o.list){var t=o.getFormat(e.id),n={css:""},i=(t=be(t.fmt,t.delimiters))(-1234.56789,n);return"<span class='"+n.css+"'>"+i+"</span>"}},data:[{id:1},{id:2},{id:3}]}],a=[{view:"text",label:e["format-pattern"],name:"format",labelPosition:"top",batch:"custom",placeholder:"[>100]0"+i.groupSign+"000"+i.decimalSign+"00;[>0]None"},{id:"docs",template:"<a href='https://webix-guides.gitbook.io/spreadsheet-user-guide/formatting_numbers#custom-number-format' target='_blank' class = 'docs'>"+e["format-docs"]+"</a>",borderless:!0,batch:"custom"}],l=[{name:"date",batch:"date",view:"formlist",labelWidth:90,height:160,label:e["date-format"],css:"webix_ssheet_format_date",template:function(e){return be(e.value,i)(45e3)},data:[n.dateFormat,n.timeFormat,n.fullDateFormat,n.longDateFormat]}];return{view:"ssheet-dialog",position:"center",width:460,head:e["format-title"],move:!0,body:{cols:[{view:"list",id:"list",css:"webix_ssheet_format_type",width:120,scroll:!1,data:this.getFormats(),select:!0,on:{onSelectChange:function(e){switch(o.updateForm(),e[0]){case"custom":case"date":o.form.showBatch(e[0]);break;default:o.form.showBatch("common"),o.form.elements.separator.show(),"percent"==e[0]&&o.form.elements.separator.hide()}}}},{view:"form",id:"form",margin:15,height:270,paddingY:0,paddingX:20,elements:[{id:"preview",template:function(e){return e.value==t.table["format-error"]?e.value:"<span class ='"+e.css+"'>"+e.value+"</span>"},css:"webix_ssheet_format_preview",autoheight:!0,borderless:!0}].concat(r,a,l),elementsConfig:{labelWidth:155},on:{onChange:function(){return o.updateForm()}}}]},on:{onSaveClick:function(){return o.okClick()},onHideClick:function(){return o.close()},onCancelClick:function(){return o.closeWin()}}}}},{key:"okClick",value:function(){var t=this,e=this.list.getSelectedId(),n="custom"==e?{format:this.form.getValues().format}:this.form.getValues();n.type=e,m.set(function(){for(var e=0;e<t.cell.length;e++)ge(t.view,t.cell[e].row,t.cell[e].column,n.format,n)}),this.view.$$("cells").refresh(),this.closeWin()}},{key:"closeWin",value:function(){var e=this.view.getStyle(this.cell[0].row,this.cell[0].column),t=e&&e.props.format?e.props.format:"common";Ve(t)||this.view.callEvent("onCommand",[{id:"toolbar-update",name:"format",value:t}]),this.close()}},{key:"getFormat",value:function(e){var t=this.list.getSelectedId(),n=this.form.getValues();return n.negative=e||n.negative,"custom"!=t?("date"!=t&&(n.date=""),{fmt:Re(n=ze(t,n)),delimiters:de(t)}):{fmt:n.format,delimiters:de(t)}}},{key:"fillForm",value:function(e){var t,n=webix.i18n.spreadsheet,i=e.getStyle(this.cell[0].row,this.cell[0].column),o={zeros:0,separator:0,negative:1,date:n.formats.dateFormat,format:""},r=i&&i.props.format?i.props.format:"";o.format=ve(r||"price"),"custom"!=(t=r?"string"==r?r="custom":ue[r].values.type:r="price")&&webix.extend(o,ue[r].values,!0);var a=this.view.$$("cells").getItem(this.cell[0].row),l=this.cell[0].column;this.value=webix.isUndefined(a[l])?"":a[l],this.form.setValues(o),this.list.select(t),this.updateForm()}},{key:"updateForm",value:function(){var e=this.form.elements,t=this.getFormat(),n=this.value,i=""===n||isNaN(n);De(t,"date")&&(i||Ue(n))?n=45e3:i&&!De(t,"string")&&(n=1234.56789);var o={css:""};n=be(t.fmt,t.delimiters)(n,o),this.preview.parse({value:n,css:o.css}),e.negative.refresh(),e.date.refresh(),
e.format.setValue(t.fmt)}},{key:"getFormats",value:function(){var e=webix.i18n.spreadsheet.labels;return[{id:"price",value:e.currency},{id:"int",value:e.number},{id:"percent",value:e.percent},{id:"date",value:e.date},{id:"custom",value:e["custom-format"]}]}}]),t}(),mi=Object.freeze({action:"custom",DialogBox:vi}),gi=[{id:">",value:">"},{id:"<",value:"<"},{id:"=",value:"="},{id:"!=",value:webix.i18n.spreadsheet.labels["conditional-not-equal"]},{id:"<>",value:webix.i18n.spreadsheet.labels["conditional-between"]}];var wi=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"_getCondition",value:function(){var o=this,e=this.view,t=this.$dialog.getBody().getChildViews()[0].getChildViews()[0].getChildViews(),r=[];t.forEach(function(e){if(e.getValues){var t=e.getValues(),n=[];n.push(t.condition),"<>"!==t.condition?n.push(o._normalizeValue(t.value)):n.push([o._normalizeValue(t.value),o._normalizeValue(t.value2)]),n.push(t.style);for(var i=0;i<n.length;i++)if(""===n[i])return!1;r.push(n)}}),e.callEvent("onConditionSet",[r])}},{key:"_safeInt",value:function(e){var t=parseFloat(e);return t==e?t:e}},{key:"_setCondition",value:function(){var i=this,e=this.view.conditions.get(this.cell.row,this.cell.column);if(e){var o=this.$dialog.getBody().getChildViews()[0].getChildViews()[0].getChildViews();e.forEach(function(e,t){var n={};n.condition=e[0],webix.isArray(e[1])?(n.value=i._formatValue(e[1][0]),n.value2=i._formatValue(e[1][1])):n.value=i._formatValue(e[1]),n.style=e[2],o[t+1].setValues(n)})}}},{key:"_formatValue",value:function(e){return this.editFormat&&""!==e&&"="!=e[0]?(e=w(e),this.editFormat(e)):e.toString()}},{key:"_normalizeValue",value:function(e){this.editFormat?isNaN(e)?"="!=e[0]&&(e=webix.Date.strToDate(webix.i18n.spreadsheet.formats.parseDateTime)(e),e=isNaN(e)?"":o(e)):e=Ue(e)?"":e:e=this._safeInt(e,10);return e}},{key:"apply",value:function(){this._getCondition(),this.close()}},{key:"_clean",value:function(){this.$dialog.getBody().getChildViews()[0].getChildViews()[0].getChildViews().forEach(function(e){e.setValues&&e.setValues({condition:"",value:"",value2:"",style:""})})}},{key:"$show",value:function(){var o=this;if(this.cell=this.view.getSelectedId(),!this.cell)return!1;var e=this.view.getRow(this.cell.row),t=e.$cellType;t&&(t=t[this.cell.column]),"date"==t&&(this.editFormat=Te(e,this.cell.column)),this.$dialog.queryView("text","all").forEach(function(e){e.define({placeholder:"date"==t?o._formatValue(45e3):""}),e.refresh()}),this.view.$handleSelection=function(e,t,n,i){return o.activeValue&&n==i&&o.activeValue.setValue("="+n),!1},this._setCondition()}},{key:"$hide",value:function(){this.view.$handleSelection=null,delete this.editFormat,this._clean()}},{key:"getRows",value:function(e,t){for(var n=this,i=[{height:36,padding:{left:12},cols:[{view:"label",width:131,label:webix.i18n.spreadsheet.labels.display},{view:"label",width:116,label:webix.i18n.spreadsheet.labels.condition},{view:"label",label:webix.i18n.spreadsheet.labels.value}]}],o={view:"form",padding:0,borderless:!0,css:"webix_ssheet_cformats",elements:[{margin:10,cols:[{view:"richselect",name:"style",width:120,placeholder:webix.i18n.spreadsheet.labels["conditional-style"],css:"webix_ssheet_cformat_select",suggest:{padding:0,borderless:!0,css:"webix_ssheet_cformat_list",body:{template:function(e){var t="webix_ssheet_cformat "+e.css;return'<div class="'.concat(t,'">').concat(e.name,"</div>")},data:e}}},{view:"richselect",width:105,name:"condition",placeholder:webix.i18n.spreadsheet.labels["conditional-operator"],on:{onChange:function(e){"<>"===e?this.getFormView().elements.value2.show():this.getFormView().elements.value2.hide()}},suggest:{view:"ssheet-form-suggest",body:{data:gi}}},{cols:[{view:"text",on:{onFocus:function(e){n.activeValue=e},onBlur:function(e){var t=e.getValue();""!==t&&(t=n._normalizeValue(t),e.setValue(n._formatValue(t)))}},name:"value"},{view:"text",name:"value2",on:{onFocus:function(e){n.activeValue=e},onBlur:function(e){var t=e.getValue();""!==t&&(t=n._normalizeValue(t),e.setValue(
n._formatValue(t)))}},hidden:!0}]},{view:"icon",css:"webix_ssheet_cformat_clear",icon:"wxi-trash",click:pi}]}]};t--;)i.push(o);return i}},{key:"$init",value:function(){var e=this;return{view:"ssheet-dialog",head:webix.i18n.spreadsheet.labels["conditional-format"],position:"center",width:670,move:!0,buttons:!1,body:{view:"form",borderless:!0,rows:[{rows:this.getRows(function t(e){return e.map(function(e){return e.id=e.css,e})}(this.view.config.conditionStyle),3)},{cols:[{gravity:2},{view:"button",value:webix.i18n.spreadsheet.labels.apply,click:function(){return e.apply()}}]}]},on:{onHideClick:function(){return e.close()}}}}}]),t}();function pi(){this.getFormView().clear()}var bi=Object.freeze({action:"conditional-format",DialogBox:wi}),xi=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"$show",value:function(e,t){this.view.$handleSelection=function(){return!1},t.elements.sheets.setValue(this.getSheets()),t.elements.filename.setValue("Data"),t.elements.filename.getInputNode().select()}},{key:"$hide",value:function(){this.view.$handleSelection=null}},{key:"$init",value:function(){var e=this;return{view:"ssheet-dialog",head:webix.i18n.spreadsheet.labels["export-title"],move:!0,position:"center",body:{view:"form",elements:[{view:"text",name:"filename",placeholder:webix.i18n.spreadsheet.labels["export-name"]},{view:"multicheckbox",name:"sheets"}]},on:{onSaveClick:function(){return function n(e){var t=e.$dialog.getBody().getValues();webix.toExcel(e.view,t),e.close()}(e)},onHideClick:function(){return e.close()},onCancelClick:function(){return e.close()}}}}},{key:"getSheets",value:function(){for(var e=this.view,t=e._sheets,n=[],i=0;i<t.length;i++)n.push({name:t[i].name,active:e._activeSheet===t[i].name?1:0});return n}}]),t}();var _i=Object.freeze({action:"excel-export",DialogBox:xi}),yi=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"$show",value:function(e,t){if(!this.view.getSelectedId())return!1;this.restoreValue(t)||t.clear(),t.elements.name.focus()}},{key:"restoreValue",value:function(e){var t=this.view.getSelectedId();if(t){var n=this.view.getRow(t.row),i=n["$"+t.column]||n[t.column];if(i&&0===i.indexOf("=HYPERLINK")){var o=i.split('"');return e.setValues({name:o[3]||"",url:o[1]||""}),!0}}return!1}},{key:"$init",value:function(){var e=this;return{view:"ssheet-dialog",head:webix.i18n.spreadsheet.labels["link-title"],move:!0,position:"center",body:{view:"form",elements:[{view:"text",name:"name",placeholder:webix.i18n.spreadsheet.labels["link-name"]},{view:"text",name:"url",placeholder:webix.i18n.spreadsheet.labels["link-url"]}]},on:{onSaveClick:function(){return function o(e){var t=e.view.getSelectedId(),n=e.$dialog.getBody().getValues(),i="";n.url&&(n.name=n.name||n.url,/^https?:\/\//i.test(n.url)||(n.url="http://"+n.url),i='=HYPERLINK("'.concat(n.url,'","').concat(n.name,'")'));e.view.setCellValue(t.row,t.column,i),e.view.refresh(),e.close()}(e)},onHideClick:function(){return e.close()},onCancelClick:function(){return e.close()}}}}}]),t}();var Ci=Object.freeze({action:"add-link",DialogBox:yi}),$i=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"$show",value:function(e,t){t.setValues({data:"current",paper:"A4",fit:"page",mode:"landscape",sheetnames:1,margin:0})}},{key:"$init",value:function(){var e=this;return{view:"ssheet-dialog",head:webix.i18n.spreadsheet.labels["print-title"],move:!0,modal:!0,width:530,height:520,position:"center",buttons:!1,body:{view:"form",elements:[{type:"section",template:webix.i18n.spreadsheet.labels["print-settings"]},{cols:[{view:"radio",name:"data",vertical:!0,options:[{id:"current",value:webix.i18n.spreadsheet.labels["current-sheet"]},{id:"all",value:webix.i18n.spreadsheet.labels["all-sheets"]},{id:"selection",value:webix.i18n.spreadsheet.labels.selection}],on:{onChange:function(e){"all"==e&&this.getFormView().elements.sheetnames.setValue(1)}}},{rows:[{view:"checkbox",name:"sheetnames",
labelRight:webix.i18n.spreadsheet.labels["sheet-names"]},{view:"checkbox",name:"borderless",labelRight:webix.i18n.spreadsheet.labels.borderless},{view:"checkbox",name:"skiprows",labelRight:webix.i18n.spreadsheet.labels["skip-rows"]},{view:"checkbox",name:"margin",labelRight:webix.i18n.spreadsheet.labels.margin}]}]},{type:"section",template:webix.i18n.spreadsheet.labels["print-paper"]},{view:"radio",name:"paper",options:[{id:"letter",value:webix.i18n.spreadsheet.labels["page-letter"]},{id:"A4",value:webix.i18n.spreadsheet.labels["page-a4"]},{id:"A3",value:webix.i18n.spreadsheet.labels["page-a3"]}]},{type:"section",template:webix.i18n.spreadsheet.labels["print-layout"]},{cols:[{view:"radio",name:"fit",options:[{id:"page",value:webix.i18n.spreadsheet.labels["page-width"]},{id:"data",value:webix.i18n.spreadsheet.labels["page-actual"]}]},{width:25},{view:"radio",width:220,name:"mode",options:[{id:"portrait",value:webix.i18n.spreadsheet.labels["page-portrait"]},{id:"landscape",value:webix.i18n.spreadsheet.labels["page-landscape"]}]}]},{cols:[{},{view:"button",css:"ssheet_cancel_button",value:webix.i18n.spreadsheet.labels.cancel,autowidth:!0,click:function(){return e.close()}},{view:"button",value:webix.i18n.spreadsheet.labels.print,autowidth:!0,click:function(){return e.apply(e)}}]}]},on:{onHideClick:function(){return e.close()}}}}},{key:"apply",value:function(e){var t=e.$dialog.getBody().getValues();t.margin=t.margin?0:{},this.close(),webix.print(e.view,t)}}]),t}(),Si=Object.freeze({action:"print",DialogBox:$i}),ki=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"open",value:function(e){var t=this;if(this.cell=e.cell?e.cell:this.view.getSelectedId(),!this.cell||this.view.isCellLocked(this.cell.row,this.cell.column)&&!e.viewonly||!this.view.callEvent("onBeforeCommentShow",[this.cell.row,this.cell.column,!e.viewonly]))return!1;this.view.comments._activeComment={editStatus:!e.viewonly,cell:this.cell},this.$dialog||(this.$dialog=webix.ui(this.$init()),this.view._destroy_with_me.push(this.$dialog),this.view.comments.commentsView=this.$dialog,this.$dialog.attachEvent("onHide",function(){return t.$hide()}),this.view.attachEvent("onCommentHide",function(){return t.$dialog.hide()}));var n=this.$dialog.getBody(),i=this.view.$$("cells").getSpan(this.cell.row,this.cell.column),o=i?{row:this.cell.row,column:1*i[1]+i[2]-1}:this.cell;webix.delay(function(){t.$dialog.show(t.view.$$("cells").getItemNode(o)),t._setComment()}),!1===this.$show(this.$dialog,n)&&this.close()}},{key:"$show",value:function(){this.textarea=this.$dialog.queryView({view:"textarea"}),this.template=this.$dialog.queryView({view:"template"})}},{key:"$init",value:function(){var e=this;return{view:"popup",css:"ssheet_comments",minWidth:250,minHeight:150,relative:"right",resize:!0,on:{onViewResize:function(){e.$dialog.hide(),e.$dialog.show()}},body:{animate:!1,cells:[{view:"template",css:"ssheet_comment_view",borderless:!0,scroll:"auto",onClick:{ssheet_comment_view:function(){e.view.config.readonly||e.view.isCellLocked(e.cell.row,e.cell.column)||e.showInput(e.view.comments.get(e.cell.row,e.cell.column))}}},{view:"textarea",on:{onChange:function(){e.addComment()},onFocus:function(){e.view.$$("cells").select(e.cell.row,e.cell.column),e.view.comments._activeComment={editStatus:!0,cell:e.cell}},onBlur:function(){e.showTemplate(e.textarea.getValue()),e.changeTextarea(),e.view.comments._activeComment={}}}}]}}}},{key:"_setComment",value:function(){var e=this.view.comments.get(this.cell.row,this.cell.column);this.view.comments._activeComment.editStatus?this.showInput(e):this.showTemplate(e)}},{key:"showTemplate",value:function(e){this.template.show(),this.template.setHTML(e)}},{key:"showInput",value:function(e){this.changeTextarea(e),this.textarea.show(),this.textarea.focus()}},{key:"addComment",value:function(){this.view.comments.add(this.cell.row,this.cell.column,this.textarea.getValue()),this.$dialog.hide()}},{key:"changeTextarea",value:function(e){this.textarea.blockEvent(),this.textarea.setValue(e||""),
this.textarea.unblockEvent()}}]),t}(),Ei=Object.freeze({action:"add-comment",DialogBox:ki}),Vi=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"open",value:function(e){this.group=e.group,this.header=e.value,sn.prototype.open.apply(this,arguments)}},{key:"$show",value:function(){var e=this.view.getSelectedId();if(!e)return!1;this.view.$handleSelection=function(){return!1};var t=this.$dialog.getBody().elements;this.type=t.type,this.size=t.size,this.$dialog.getHead().getChildViews()[0].setHTML(this.header);var n,i=this.view.$$("cells");n="row"==this.group?i.getItem(e.row).$height||i.config.rowHeight:i.getColumnConfig(e.column).width||i.config.columnWidth,this.type.getOption(1).value=webix.i18n.spreadsheet.labels["row"==this.group?"height":"width"],this.type.setValue(1),this.type.refresh(),this.size.setValue(n),this.size.focus()}},{key:"$hide",value:function(){this.view.$handleSelection=null}},{key:"okClick",value:function(){var e=this.size.isEnabled()?1*this.size.getValue():"auto";"row"==this.group?this.view.setRowHeight(null,e):this.view.setColumnWidth(null,e),this.close()}},{key:"$init",value:function(){var t=this;return{view:"ssheet-dialog",position:"center",width:300,move:!0,body:{cols:[{view:"radio",name:"type",vertical:!0,optionHeight:40,on:{onChange:function(e){2==e?t.size.disable():t.size.enable()}},options:[{id:1,value:""},{id:2,value:webix.i18n.spreadsheet.labels["fit-content"]}]},{rows:[{view:"text",name:"size",width:120}]}]},on:{onSaveClick:function(){return t.okClick()},onHideClick:function(){return t.close()},onCancelClick:function(){return t.close()}}}}}]),t}(),zi=Object.freeze({action:"resize",DialogBox:Vi}),Ri=webix.i18n.spreadsheet.labels;var Ii=function(e){function t(){return i(this,t),v(this,d(t).apply(this,arguments))}return h(t,sn),c(t,[{key:"open",value:function(e){this.config=e.config,this.viewid=e.viewid,sn.prototype.open.apply(this,arguments)}},{key:"$show",value:function(e,o){this.view.$handleSelection=function(e,t,n,i){return o.elements.range.setValue(n+":"+i),!1},o.clear();var t=this.config?webix.extend(this.config.config,{range:this.config.data||""},!0):{type:"line",range:this.view.getSelectedRange()};o.setValues(this.inValues(t)),o.elements.range.focus()}},{key:"$hide",value:function(){this.config=this.viewid=null,this.view.$handleSelection=null}},{key:"$init",value:function(){var t=this;return{view:"ssheet-dialog",position:"center",width:460,head:Ri["chart-config"],move:!0,body:{view:"form",elements:[{view:"richselect",name:"type",value:"line",label:Ri["chart-type"],suggest:{view:"ssheet-form-suggest",data:function e(){return[{id:"line",value:Ri["line-chart"]},{id:"spline",value:Ri["spline-chart"]},{id:"splineArea",value:Ri["splinearea-chart"]},{id:"area",value:Ri["area-chart"]},{id:"bar",value:Ri["bar-chart"]},{id:"radar",value:Ri["radar-chart"]},{id:"pie",value:Ri["pie-chart"]},{id:"donut",value:Ri["donut-chart"]}]}()},on:{onChange:function(e){return t.toggleFields(e)}}},{view:"checkbox",label:Ri["stacked-chart"],name:"stacked"},{view:"text",label:Ri["chart-range"],name:"range"},{view:"checkbox",label:Ri["chart-col-axis"],labelWidth:180,name:"xAxis"},{view:"checkbox",label:Ri["chart-pie-text"],labelWidth:180,name:"pieInnerText",hidden:!0},{view:"checkbox",label:Ri["chart-row-legend"],labelWidth:180,name:"legend"}],elementsConfig:{labelWidth:100}},on:{onSaveClick:function(){return t.okClick()},onHideClick:function(){return t.close()},onCancelClick:function(){return t.close()}}}}},{key:"okClick",value:function(){var e=this.$dialog.queryView("form"),t=this.outValues(e.getValues());this.checkRange(t.range)&&(this.viewid?e.isDirty()&&this.view.views.update(this.viewid,t.range,t):this.view.views.add(null,null,"chart",t.range,t),this.close())}},{key:"checkRange",value:function(e){if(e&&V(e,this.view))return!0;this.view.alert({text:Ri["error-range"]})}},{key:"outValues",value:function(e){return e.stacked&&(e.type="stacked"+e.type.charAt(0).toUpperCase()+e.type.substr(1)),e}},{key:"inValues",value:function(e){return-1!==e.type.indexOf("stacked"
)&&(e.type=e.type.replace("stacked","").toLowerCase()),e}},{key:"toggleFields",value:function(e){var t=this.$dialog.getBody().elements;"area"==e||"bar"==e?t.stacked.show():this.hideField(t.stacked),"pie"==e||"donut"==e?(this.hideField(t.xAxis),t.pieInnerText.show(),t.legend.define("label",Ri["chart-col-legend"])):(t.xAxis.show(),this.hideField(t.pieInnerText),t.legend.define("label",Ri["chart-row-legend"])),t.legend.refresh()}},{key:"hideField",value:function(e){e.setValue(0),e.hide()}}]),t}(),Ai=[mn,fn,An,On,Dn,fi,mi,bi,_i,Ci,Si,Ei,zi,Object.freeze({action:"add-chart",DialogBox:Ii})];var Fi=!1;function Oi(e){ji(e,-1)}function Mi(e){ji(e,1)}function Di(e){Fi||(e._ssUndoHistory=[],e._ssUndoCursor=-1)}function Ni(e,t){e.$skipHistory||(e._ssUndoHistory.splice(e._ssUndoCursor+1),e._ssUndoHistory.push(t),e._ssUndoCursor++)}function Ui(e,t){t.$skipHistory++;try{e.call(t)}finally{t.$skipHistory--}}function Ti(e){Fi=!0,e(),Fi=!1}function ji(e,t){var n=e._ssUndoHistory[0<t?e._ssUndoCursor+t:e._ssUndoCursor];if(n){var i=0<t?n.newValue:n.value,o=0<t,r=[n.action,n.row,n.column,i,o];e._ssUndoCursor+=t;var a=n.group,l=e._ssUndoHistory[0<t?e._ssUndoCursor+t:e._ssUndoCursor];Ui(function(){e.callEvent("onUndo",r),a&&l&&a==l.group||(e.refresh(),e.callEvent("onAfterUndo",[]))},e),l&&a&&a==l.group&&ji(e,t)}}var Bi=Object.freeze({init:function Ga(o){Di(o),function e(o){o.attachEvent("onUndo",function(e,t,n,i){switch(e){case"freeze-row":o.freezeRows(i);break;case"freeze-column":o.freezeColumns(i)}})}(o),o.attachEvent("onHardReset",function(){return Di(o)}),o.attachEvent("onAfterSheetShow",function(){return Di(o)}),o.attachEvent("onBeforeStyleChange",function(e,t,n,i){if(Tn(o,e,t))return!1;Ni(o,{action:"style",row:e,column:t,value:i,newValue:n,group:m.value})}),o.attachEvent("onBeforeValueChange",function(e,t,n,i){if(Tn(o,e,t))return!1;Ni(o,{action:"value",row:e,column:t,value:i,newValue:n,group:m.value})}),o.attachEvent("onBeforeSpan",function(e,t,n){if(Tn(o,e,t))return!1;Ni(o,{action:"span",row:e,column:t,value:n,newValue:n,group:m.value})}),o.attachEvent("onBeforeSplit",function(e,t,n){if(Tn(o,e,t))return!1;Ni(o,{action:"split",row:e,column:t,value:n,newValue:n,group:m.value})}),o.attachEvent("onAction",function(e,t){return Ni(o,{action:e,row:t.row||null,column:t.column||null,value:t.value||null,newValue:t.newValue||null,group:m.value})}),o.$$("cells").attachEvent("onColumnResize",function(e,t,n){return Ni(o,{action:"c-resize",row:0,column:e,value:n,newValue:t,group:m.value})}),o.attachEvent("onColumnOperation",function(e,t,n){"add"!==e.id&&"del"!==e.id&&Ni(o,{action:e,row:0,column:{start:t,end:n}})}),o.attachEvent("onRowOperation",function(e,t,n){"add"!==e.id&&"del"!==e.id&&Ni(o,{action:e,row:{start:t,end:n},column:0})}),o.$$("cells").attachEvent("onRowResize",function(e,t,n){return Ni(o,{action:"r-resize",row:e,column:0,value:n,newValue:t,group:m.value})}),o.attachEvent("onBeforeConditionSet",function(e,t,n,i){if(Tn(o,e,t))return!1;n=n?webix.copy(n):null,Ni(o,{action:"condition",row:e,column:t,value:n,newValue:i,group:m.value})}),o.$skipHistory=0},undo:Oi,redo:Mi,reset:Di,ignoreUndo:Ui,ignoreReset:Ti});function Hi(e,t,n,i,o,r){var a,l,s,c,u,h=0,d=0,f=i._table.getSelectArea(),v=i._table.getScrollState();s=i.serialize({math:!0,viewIds:!0}),c=webix.copy(s);var m=s.spans;for(var g in m){var w=m[g];((w=qn(w,e,t,n,s.data))[2]<=0||w[3]<=0)&&m.splice(g,1)}u=function x(e,t,n){var i=e.group,o="add"==e.id?1:"del"==e.id?-1:0;o&&("row"==i&&(o+=o*(n.row-t.row)),"column"==i&&(o+=o*(n.column-t.column)));return{name:i,inc:o}}(e,t,n),i.$handleSelection=null,o||Ui(function(){i.callEvent("onAction",["before-grid-change",{name:u.name,inc:u.inc,data:s,start:t}])},i),a=s.data,"column"==e.group?(h=n.column-t.column+1,d=t.column,"add"==e.id?function _(e,t,n,i,o,r,a){var l=a.length,s=n.column-t.column+1;i.config.columnCount+=s,i.reset();for(;l--;)a[l][1]>=t.column&&(a[l][1]+=s);o?a.push.apply(a,k(r)):i.callEvent("onColumnOperation",[e,t,n,null])}(e,t,n,i,o,r,a):"del"==e.id&&(!function y(e,t,n,i,o,r,a){var l=a.length,s=n.column-t.column+1;if(
i.config.columnCount===s){if(t.column==n.column)return;n.column--,s--}i.config.columnCount-=s,i.reset();var c=[];for(;l--;)a[l][1]>=t.column&&a[l][1]<=n.column?c.push(a.splice(l,1)[0]):a[l][1]>n.column&&(a[l][1]-=s);o||i.callEvent("onColumnOperation",[e,t,n,c])}(e,t,n,i,o,0,a),h=-h)):"row"==e.group&&(h=n.row-t.row+1,d=t.row,"add"==e.id?function C(e,t,n,i,o,r,a){var l=a.length,s=n.row-t.row+1;i.config.rowCount+=s,i.reset();for(;l--;)a[l][0]>=t.row&&(a[l][0]+=s);o?a.push.apply(a,k(r)):i.callEvent("onRowOperation",[e,t,n,null])}(e,t,n,i,o,r,a):"del"==e.id&&(!function $(e,t,n,i,o,r,a){var l=a.length,s=n.row-t.row+1;if(i.config.rowCount===s){if(t.row==n.row)return;n.row--,s--}i.config.rowCount-=s,i.reset();var c=[];for(;l--;)a[l][0]>=t.row&&a[l][0]<=n.row?c.push(a.splice(l,1)[0]):a[l][0]>n.row&&(a[l][0]-=s);o||i.callEvent("onRowOperation",[e,t,n,c])}(e,t,n,i,o,0,a),h=-h));var p={id:e.group,start:d,count:h};for(l=a.length;l--;)a[l][2]&&"string"==typeof a[l][2]&&"="==a[l][2].substr(0,1)&&(a[l][2]=_n(a[l][2],p));if(i.callEvent("onAction",["grid-change",{value:c,newValue:s}]),Ui(function(){Pi(i,s),i._table.scrollTo(v.x,v.y)},i),f){var b=i.$$("cells");(f=function S(e,t){var n=t.count(),i=t.config.columns.length;return 1*e.start.row>n?null:1*e.end.row>n?null:1*e.start.column>i?null:1*e.end.column>i?null:e}(f,b))&&b.addSelectArea(f.start,f.end)}}function Pi(e,t){Ti(function(){e.$handleSelection=null,e.parse(t)})}var Li=Object.freeze({init:function Za(o){o.attachEvent("onCommand",function(e,t,n){if("add"==e.id||"del"==e.id){var i=o._table.getSelectArea();!i||t&&n||(t=i.start,n=i.end),t&&n&&m.set(function(){Hi(e,t,n,o)})}}),o.attachEvent("onUndo",function(e,t,n,i){"grid-change"==e&&(bn(!0),Pi(o,i))})},process:Hi});function Wi(e,t){var n=Yi(e);return t=Ki(pn(t,n,"").handler,n)}function Ki(e,t){var n,i=webix.i18n.spreadsheet.table["math-error"];try{n=e.call(t)}catch(o){return i}return"number"==typeof n?isFinite(n)?Math.round(1e5*n)/1e5:i:"string"!=typeof n?n?n.toString():i:n}function Yi(r){var e=$n(r);return{rs:function(e,t,n,i,o){return $n(r,e).getRange(t,n,i,o)},r:e.getRange,vs:function(e,t,n){return $n(r,e).getValue(t,n)},v:e.getValue,m:Mt,p:{},e:function(e){throw e}}}var qi=Object.freeze({init:function Xa(l){var c=[],a=[],s=!1;l._mathSheetCache={};var h,d,v=Yi(l);function u(e,t,n){var i=l.getRow(e),o=Ki(n,v);i[t]=o;var r=1e8*t+e,a=d[r]=(d[r]||0)+1;h[r]===o||1e3<a||(h[r]=o,m(e,t))}function m(e,t){var n=c[e];if(n){var i=n[t];if(i)for(var o=0;o<i.length;o++){var r=i[o],a=r.row,l=r.column,s=r.handler;a==e&&l==t||u(a,l,s)}}}function t(){c=[],a=[];var e=l.$$("cells"),t=e.getState(),r=t.ids.concat(t.hidden);e.eachRow(function(e){for(var t=this.getItem(e),n=1;n<r.length;n++){var i=r[n],o=t["$"+i];o&&f(e,i,o)}},!0)}function f(e,t,n){if(s&&n&&"string"==typeof n&&0===n.indexOf("="))l.getRow(e)["$"+t]=n;else{var i=a[e];if(i&&i[t]&&function u(e,t,n,i){var o=t[i][n];t[i][n]=null;for(var r=o.length-1;0<=r;r--)for(var a=o[r],l=e[a[0]][a[1]],s=l.length-1;0<=s;s--){var c=l[s];c.row==i&&c.column==n&&l.splice(s,1)}}(c,a,t,e),n&&1<(n=n.toString()).length&&0===n.indexOf("=")){var o=pn(n,v,""),r=l.getRow(e);r[t]=Ki(o.handler,v),r["$"+t]=o.text,o.triggers.length&&function f(e,t,n,i,o){for(var r=[],a=0;a<n.length;a++){var l=n[a];if(""===l[4]||l[4]===o)for(var s=l[0];s<=l[2];s++){var c=e[s];c||(c=e[s]=[]);for(var u=l[1];u<=l[3];u++){var h=c[u];h||(h=c[u]=[]),r.push([s,u]),h.push(i)}}}!function d(e,t,n,i){var o=e[t];o||(o=e[t]=[]),o[n]=i}(t,i.row,i.column,r)}(c,a,o.triggers,{row:e,column:t,handler:o.handler},l.getActiveSheet())}h={},d={},m(e,t)}}l.ranges=v.ranges=new kn(l),v.p=function e(i){var o={};return i.setPlaceholder=function(e,t){if("object"===g(e))for(var n in e)o[n.toLowerCase()]=e[n];else o[e.toString().toLowerCase()]=t;i.callEvent("onMathRefresh",[])},o}(l),l.attachEvent("onReset",function(){c=[],a=[],s=!0,l.ranges.clear()}),l.attachEvent("onCellChange",function n(e,t){f(e,t,l.getCellValue(e,t))}),l.attachEvent("onMathRefresh",function(){t(),l.$$("cells").refresh()}),l.attachEvent("onDataSerialize",function(e){return function n(e,t){
t.ranges=e.ranges.serialize()}(l,e)}),l.attachEvent("onDataParse",function(e){l.ranges.parse(e.ranges),s=!1,t()}),l.attachEvent("onAction",function(e,t){"before-grid-change"==e&&(bn(!0),function a(e,t,n,i){for(var o=n.ranges,r=0;r<o.length;r++)o[r][1]=D(o[r][1],e,t,i)}(t.name,t.inc,t.data,t.start))}),function i(t,e,n){t.attachEvent("onBeforeSheetShow",function(){delete t._mathSheetCache[an(t)]}),t.attachEvent("onSheetRename",function(a,l){t._sheets.forEach(function(e){var r={multiSheet:!1};["data","editors","conditions","ranges"].forEach(function(o){e.content[o]&&e.content[o].forEach(function(e){switch(o){case"data":"="===e[2][0]&&(e[2]=Cn(e[2],a,l,r));break;case"editors":var t=e[2].options;if(t)if("string"==typeof t)e[2].options=Cn(t,a,l,r);else for(var n=0;n<t.length;n++){var i="string"==typeof t[n]?t[n]:t[n].value;t[n]=Cn(i,a,l,r)}break;case"conditions":"="===e[3][0]&&(e[3]=Cn(e[3],a,l,r));break;case"ranges":e[1]=Cn(e[1],a,l,r)}})}),!0===r.multiSheet&&(delete t._mathSheetCache[e.name],$n(t,e.name),bn())}),delete t._mathSheetCache[a]}),t.attachEvent("onSheetRemove",function(e){delete t._mathSheetCache[e]}),t._mathSheetCore=e,t._mathSheetHelpers=n}(l,v,{parse:pn,execute:Ki})},calculate:Wi});function Gi(e,t){return isNaN(1*t)?"="==t.charAt(0)&&(t=Wi(e,t)):t*=1,t}function Zi(e,t,n,i){return i._empty=!1,e[t]||(e[t]={}),e[t][n]||(e[t][n]=[]),e[t][n]}function Xi(e){if(this._empty=!0,e)for(var t=e.length;t--;){var n=e[t];Zi(this._pull,n[0],n[1],this).push([n[2],n[3],n[4]])}}function Qi(){this._pull={}}function Ji(e,t,n){this.get(e,t)||Zi(this._pull,e,t,this),this._pull[e][t]=n}function eo(e,t){return e?t?this._pull[e]?this._pull[e][t]:null:this._pull[e]:this._pull}function to(e,t,n,i,o){Zi(this._pull,e,t,this).push([n,i,o])}function no(e,t){this.get(e,t)&&delete this._pull[e][t]}function io(){var e,t,n,i,o=[];for(i in this._pull)for(e in this._pull[i])for(n=0;n<this._pull[i][e].length;n++)t=this._pull[i][e][n],o.push([i,e,t[0],t[1],t[2]]);return o}function oo(e,t){m.set(function(){F(t,e,function(e,t){var n=e.conditions.get(t.row,t.column);n&&ao("remove",t.row,t.column,n,null,e)}),e.refresh()})}function ro(e,t,n,i,o){var r=t.condition;ao("update",n,i,e.conditions.get(n,i)||null,r,e),1===o&&ao("remove",t.row,t.col,r,null,e)}function ao(e,t,n,i,o,r){r.callEvent("onBeforeConditionSet",[t,n,i,o])&&(r.conditions[e].apply(r.conditions,[t,n,o]),r.callEvent("onAfterConditionSet",[t,n,i,o]))}var lo=Object.freeze({init:function Qa(r){r.conditions={_empty:!0,_pull:{},handlers:{">":function(e,t){return e>Gi(r,t)},"<":function(e,t){return e<Gi(r,t)},"=":function(e,t){return e==Gi(r,t)},"!=":function(e,t){return e!=Gi(r,t)},"<>":function(e,t){var n=t[0],i=t[1];return webix.isArray(t)&&e<Gi(r,i)&&e>Gi(r,n)}},add:to,remove:no,update:Ji,get:eo,parse:Xi,serialize:io,clear:Qi},r.attachEvent("onConditionSet",function(e){return function t(n,i){m.set(function(){n.eachSelectedCell(function(e){var t=n.conditions.get(e.row,e.column);ao("update",e.row,e.column,t||null,i,n)})}),n.refresh()}(r,e)}),r.attachEvent("onUndo",function(e,t,n,i){"condition"==e&&function o(e,t,n,i){e.conditions.get(t,n)&&e.conditions.remove(t,n),i&&e.conditions.update(t,n,i)}(r,t,n,i)}),r.attachEvent("onDataSerialize",function(e){return function n(e,t){t.conditions=e.conditions.serialize()}(r,e)}),r.attachEvent("onDataParse",function(e){return function n(e,t){e.conditions.parse(t.conditions)}(r,e)}),r.attachEvent("onColumnInit",function(e){return function t(l,e){e.cssFormat=function(e,t,n,i){if(l.conditions._empty)return"";var o,r;if(!(o=l.conditions.get(n,i)))return"";for(r=o.length;r--;){var a=l.conditions.handlers[o[r][0]];if(a&&a(t[i],o[r][1]))return o[r][2]}return""}}(r,e)}),r.attachEvent("onReset",function(){return r.conditions.clear()}),r.attachEvent("onAction",function(e,t){"before-grid-change"==e&&function c(e,t,n,i){var o=n.conditions,r=o.length;if(t)for(;r--;){var a=S(o[r],2),l=a[0],s=a[1];("row"==e&&l>=i.row||"column"==e&&s>=i.column)&&("row"==e?l<i.row-t?o.splice(r,1):o[r][0]=1*l+t:"column"==e&&(s<i.column-t?o.splice(r,1):o[r][1]=1*s+t))}}(t.name,t.inc,t.data,
t.start)}),function t(e){e.conditions.clear()}(r)},clearConditionalFormats:oo,pasteCondition:ro,_changeCondition:ao});function so(e){e._table._ssEditors={}}function co(e,t){e._table.editStop(),F(t,e,function(e,t){var n=e._table._ssEditors[t.row];n&&n[t.column]&&(delete n[t.column],e._table.removeCellCss(t.row,t.column,"ss_editor"),Wn(e,t.row,t.column,"ss_editor",!1))})}function uo(e,t,n,i,o){var r=t.dropdown;e.setCellEditor(n,i,r),1===o&&e.setCellEditor(t.row,t.col,{})}var ho,fo,vo,mo,go,wo=Object.freeze({init:function Ja(a){var e=a._table;a.attachEvent("onReset",function(){return so(a)}),so(a),e.attachEvent("onBeforeEditStart",function(e){var t=this.getColumnConfig(e.column);if(this._ssEditors[e.row]){var n=this._ssEditors[e.row][e.column];n&&(webix.extend(t,n,!0),webix.extend(t,{row:a.getRow(e.row)},!0),n.options&&("string"==typeof n.options?t.options=Sn(a,n.options,{unique:!0,order:!0,empty:!0}):webix.isArray(n.options)&&(t.options=Sn(a,n.options,{empty:!0}))))}}),e.attachEvent("onBeforeEditStop",function(e,t,n){if(!n&&t.getFormat){var i=t.getValue(),o=t.getFormat(i);m.set(function(){a.setCellValue(t.row,t.column,i),o?ge(a,t.row,t.column,ve(o)):we(a,t.row,t.column)});var r=a.getSelectedId();r&&r.row==t.row&&r.column==t.column&&(o=Ve(o=o||"common")?"custom":o,a.callEvent("onCommand",[{id:"toolbar-update",name:"format",value:o}]))}}),e.attachEvent("onAfterEditStop",function(e,t){var n=this.getColumnConfig(t.column);n.editor="text",delete n.popup,delete n.$popup,delete n.row}),a.attachEvent("onUndo",function(e,t,n,i){"dropdown"==e&&a.setCellEditor(t,n,i)}),a.attachEvent("onAction",function(e,t){"dropdown"==e?Wn(a,t.row,t.column,"ss_editor",t.newValue):"before-grid-change"==e&&function u(e,t,n,i){var o=n.editors;if(t)for(var r=o.length-1;0<=r;r--){var a=S(o[r],3),l=a[0],s=a[1],c=a[2];if("row"==e&&l>=i.row||"column"==e&&s>=i.column){if("row"==e){if(l<i.row-t){o.splice(r,1);continue}o[r][0]=1*l+t}else if("column"==e){if(s<i.column-t){o.splice(r,1);continue}o[r][1]=1*s+t}c.options=D(c.options,e,t,i)}}}(t.name,t.inc,t.data,t.start)})},serialize:function el(e,t){var n,i,o=e._table._ssEditors,r=[];for(n in o)for(i in o[n])r.push([n,i,o[n][i]]);t.editors=r},load:function tl(e,t){var n=t.editors;if(n)for(var i=0;i<n.length;i++)e.setCellEditor.apply(e,n[i])},clearEditors:co,isEditor:function nl(e,t,n){var i=e._table._ssEditors;return i[t]&&!!i[t][n]},pasteDropdown:uo});function po(e,t,n,i,o,r){var a=i;if("object"===g(i)){if(a=i.math?_n(i.math,r,vo):i.text,i.style&&i.style.props){var l=e.addStyle(i.style.props);e.setStyle(t,n,l)}var s=i.extra;s&&(s.condition&&ro(e,s,t,n,mo),s.dropdown&&uo(e,s,t,n,mo),s.filter&&go&&ri(e,s,t,n,0,r)),Gn(e,s,t,n,mo,r)}e.setCellValue(t,n,a)}var bo=Object.freeze({init:function il(i){var o=i.$$("cells");o.attachEvent("onKeyPress",function(e,t){67!==e&&88!==e||!t.ctrlKey&&!t.metaKey||!o.getSelectedId()||(ho=function n(c,u){var h,d,f=[];return u.mapSelection(function(e,t,n){t!=d&&(h=[],f.push(h),d=t);var i={text:e,math:u.getItem(t)["$"+n],style:c.getStyle(t,n)},o=c.conditions.get(t,n),r=c.getCellEditor(t,n),a=c.getCellFilter(t,n),l=c._table.getSpan(t,n);if(r||a||o||l){var s={row:t,col:n};o&&(s.condition=o),r&&(s.dropdown=r),a&&(s.filter=a),l&&(s.span=l),i.extra=s}return h.push(i),e}),f}(i,o),fo=document.getElementsByClassName("webix_clipbuffer")[0].value,vo=o.getSelectArea(),(mo=1*(88===e))&&0!==Object.keys(i._table._ssFilters).length&&(go=function u(e,t,n){for(var i=Object.keys(e),o=Math.min.apply(null,i),r=Math.max.apply(null,i),a=n,l=1,s=o;s<=r;s++)if(e[s]){var c=Object.keys(e[s]);a=Math.min(a,Math.min.apply(null,c)),l=Math.max(l,Math.max.apply(null,c))}return t.start.row<=o&&t.end.row>=r&&t.start.column<=a&&t.end.column>=l}(i._table._ssFilters,vo,i.config.columnCount)))}),i.config.readonly||o.attachEvent("onPaste",function(e){!function a(l,n,e){var t=n.getSelectArea();if(t){var i=t.start,o=e===fo,s=o?ho:webix.csv.parse(e,n.config.delimiter);!o&&l.config.clipboardDecimalDelimiter&&(s=function r(e,t){for(var n=0;n<e.length;n++)for(var i=0;i<e[n].length;i++){var o=e[n][i].toString().replace(t,".")
;webix.rules.isNumber(o)&&(e[n][i]=o)}return e}(s,l.config.clipboardDecimalDelimiter));var c={id:"move",column:0,row:0,cut:mo};o?(c.column=i.column-vo.start.column,c.row=i.row-vo.start.row):mo=0,m.set(function(){if(go&&l.removeFilters(),function a(e,t,n){var i=t.row+n.length-1,o=t.column+n[0].length-1;if(i>e.config.rowCount||o>e.config.columnCount){var r={id:"add"};i>e.config.rowCount&&(r.group="row",e.callEvent("onCommand",[r,{row:e.config.rowCount+1},{row:i}])),o>e.config.columnCount&&(r.group="column",e.callEvent("onCommand",[r,{column:e.config.columnCount+1},{column:o}]))}}(l,i,s),1===mo)for(var e=vo.start.row;e<=vo.end.row;e++)for(var t=vo.start.column;t<=vo.end.column;t++)l.setCellValue(e,t,null),l.setStyle(e,t,null);1!=s.length||1!=s[0].length||go?n.mapCells(i.row,i.column,s.length,null,function(e,t,n,i,o){if(s[i]&&s[i].length>o){var r=s[i][o];po(l,t,n,r,0,c)}},!0):l.eachSelectedCell(function(e){var t={id:"move",column:c.column+1*e.column-i.column,row:c.row+1*e.row-i.row,cut:mo};po(l,e.row,e.column,s[0][0],0,t)}),1===mo&&(go=!(mo=2))}),l.refresh()}}(i,o,e)})}});var xo=Object.freeze({init:function ol(r){r.attachEvent("onDataParse",function(e){return function h(e,t){var n=e.$$("cells"),i=t.table||{frozenColumns:0,frozenRows:0};e._frozenColumns=e._frozenRows=0,i&&(webix.isUndefined(i.frozenColumns)||i.frozenColumns+1==n.config.leftSplit||e.freezeColumns(i.frozenColumns),webix.isUndefined(i.frozenRows)||i.frozenRows==n.config.topSplit||e.freezeRows(i.frozenRows)),t.sizes&&n.define("fixedRowHeight",!1);for(var o=0;o<t.data.length;o++){var r=S(t.data[o],5),a=r[0],l=r[1],s=r[2],c=r[4];"string"==typeof s&&(s=s.replace(/^=link\(/i,"=HYPERLINK("));var u=n.getItem(a);u[l]=s,e.ignoreUndo(function(){e.callEvent("onCellChange",[a,l,s,c])})}}(r,e)}),r.attachEvent("onDataSerialize",function(e,t){return function o(s,e,t){var c=!t||!1!==t.math,u=[],n=s.$$("cells"),i=n.getState(),h=i.ids.concat(i.hidden);h.splice(h.indexOf("rowId"),1),n.eachRow(function(e){for(var t=this.getItem(e),n=0;n<h.length;n++){var i=h[n],o=t[i],r=t.$cellCss&&t.$cellCss[i]||"";c&&t["$"+i]&&(o=t["$"+i]);var a=o||0===o;if(a||r){var l=Ut(s,e,i);u.push([1*e,1*i,a?o:"",r,l])}}},!0),e.table={frozenColumns:s._frozenColumns||0,frozenRows:s._frozenRows||0},e.data=u}(r,e,t)}),r.attachEvent("onUndo",function(e,t,n,i){"value"==e&&function o(e,t,n,i){e.setCellValue(t,n,i)}(r,t,n,i)})}});function _o(e,t){t?e["_hidden_"+t+"_hash"]={}:(e._hidden_cols_hash={},e._hidden_rows_hash={})}function yo(e,t){return!e._hidden_cols_hash[t]}function Co(e,t,n,i){if(t){var o=function c(e){return webix.isArray(e)?e:[e,e]}(t),r="row"==i?e._hidden_rows_hash:e._hidden_cols_hash;if(r[o[0]]||!webix.isUndefined(n)&&!n){if(r[o[0]]&&(webix.isUndefined(n)||!n)){var a=1<o[0]?1:-1;("row"==i?Eo:ko)({id:"hide",group:i},u({},i,o[0]+a),u({},i,o[1]+a),e)}}else("row"==i?zo:Vo)({id:"hide",group:i},u({},i,o[0]),u({},i,o[1]),e)}else{var l=e.getSelectedId(!0),s={id:!1===n?"show":"hide",group:i};l.length&&e.callEvent("onCommand",[s,l[0],l[l.length-1]])}}function $o(e,t){return!e._hidden_rows_hash[t]}function So(e){"column"==e.group?"show"==e.id?ko.apply(this,arguments):"hide"==e.id&&Vo.apply(this,arguments):"row"==e.group&&("show"==e.id?Eo.apply(this,arguments):"hide"==e.id&&zo.apply(this,arguments))}function ko(e,t,n,i,o){for(var r=n.column,a=i.$$("cells");r>=t.column;){var l=r;if(o||(l=i._hidden_cols_hash[1*r-1]||i._hidden_cols_hash[1*r+1]||!1),!1!==l){delete i._hidden_cols_hash[l];var s=a.getColumnConfig(l-1||"rowId").header[0];s.css=s.css.replace("webix_ssheet_hide_column",""),a.showColumn(l),t.column=t.column<l?t.column:l,n.column=n.column>l?n.column:l,o||i.callEvent("onColumnOperation",[e,{column:l},{column:l},null])}r--}}function Eo(e,t,n,i,o){var r=n.row,a=i.$$("cells"),l=i._frozenRows;for(l+1>=t.row&&i.freezeRows(0);r>=t.row;){var s=r;if(o||(s=i._hidden_rows_hash[1*r-1]||i._hidden_rows_hash[1*r+1]||!1),!1!==s){if(delete i._hidden_rows_hash[s],s-1==0){var c=a.getColumnConfig("rowId").header[0];c.css=c.css.replace("webix_ssheet_hide_row",""),a.refreshColumns()}else a.removeCellCss(s-1,"rowId",
"webix_ssheet_hide_row");o||i.callEvent("onRowOperation",[e,{row:s},{row:s},null])}r--}i.filterSpreadSheet(),l+1>=t.row&&i.freezeRows(l)}function Vo(e,t,n,i,o){var r=i.$$("cells"),a=n.column;if(i.$handleSelection=null,r.unselect(),n.column<1||t.column<1)delete i._hidden_cols_hash[t.column];else{for(;a>=t.column;){i._hidden_cols_hash[a]=a;var l=r.getColumnConfig(a-1||"rowId").header[0];l.css=(l.css||"")+" webix_ssheet_hide_column",r.hideColumn(a),a--}o||i.callEvent("onColumnOperation",[e,t,n,null])}}function zo(e,t,n,i,o){if(n.row<1||t.row<1)delete i._hidden_rows_hash[t.row];else{var r=i._frozenRows;r>=t.row&&i.freezeRows(0);for(var a=i.$$("cells"),l=n.row;l>=t.row;){if((i._hidden_rows_hash[l]=l)-1==0){var s=a.getColumnConfig("rowId").header[0];s.css=(s.css||"")+" webix_ssheet_hide_row",a.refreshColumns()}else a.addCellCss(l-1,"rowId","webix_ssheet_hide_row");l--}i.filterSpreadSheet(),r>=t.row&&i.freezeRows(r),o||i.callEvent("onRowOperation",[e,t,n,null])}}var Ro=Object.freeze({init:function rl(l){l.attachEvent("onCommand",function(e){if("show"==e.id||"hide"==e.id){var t=l._table.getSelectArea();t&&So(e,t.start,t.end,l)}}),_o(l),l.attachEvent("onUndo",function(e,t,n,i,o){if(!("hide"!=e.id&&"show"!=e.id||"column"!=e.group&&"row"!=e.group)){var r=e.id;o||(r="hide"==r?"show":"hide");var a=t||n;So({id:r,group:e.group},a.start,a.end,l,!0)}}),l.attachEvent("onHardReset",function(){return _o(l)}),l.attachEvent("onAction",function(e,t){"before-grid-change"==e&&function u(e,t,n,i,o){if(i.table&&i.table.hidden&&i.table.hidden[t]&&i.table.hidden[t].length){"column"===t&&_o(e,"cols"),"row"===t&&_o(e,"rows");var r=o[t],a=r+n;if(a<r){var l=[a,r];r=l[0],a=l[1]}for(var s=r;s<a;s++)for(var c=0;c<i.table.hidden[t].length;c++)i.table.hidden[t][c]>=s&&(i.table.hidden[t][c]=1*i.table.hidden[t][c]+(0<n?1:-1))}}(l,t.name,t.inc,t.data,t.start)})},reset:_o,isColumnVisible:yo,setState:Co,isRowVisible:$o,serialize:function al(e,t){var n=[],i=[];for(var o in e._hidden_rows_hash)n.push(o);for(var r in e._hidden_cols_hash)i.push(r);(n.length||i.length)&&(t.table.hidden={}),n.length&&(t.table.hidden.row=n),i.length&&(t.table.hidden.column=i)},load:function ll(e,t){if(_o(e),!webix.isUndefined(t.table)&&!webix.isUndefined(t.table.hidden)){var n=t.table.hidden;if(n.row&&n.row.length)for(var i=0;i<n.row.length;i++)zo({id:"hide",group:"row"},{row:n.row[i]},{row:n.row[i]},e,!0);if(n.column&&n.column.length)for(var o=0;o<n.column.length;o++)Vo({id:"hide",group:"column"},{column:n.column[o]},{column:n.column[o]},e,!0)}}});function Io(a,e){var l=this,t=W(l,{row:a,column:e});if(t&&t.props.wrap){var n=l._table.getItem(a),s=n.$height||l._table.config.rowHeight;l._table.eachColumn(function(e,t){var n=W(l,{row:a,column:e});if(n&&n.props&&"wrap"===n.props.wrap){var i=this.getText(a,e),o=t.width,r=l._table.getSpan(a,e);r&&(o=l._table.getSpanNode({row:r[0],column:r[1]}).offsetWidth),(l.getCellEditor(a,e)||l.getCellFilter(a,e))&&(o+=20),s=Math.max(s,Q(l,i,n.id,o).height)}}),n.$height=s}}var Ao=Object.freeze({init:function sl(h){if(h.config.resizeCell){var e=h.$$("cells");e.define("resizeRow",{headerOnly:!0,size:10}),e.define("resizeColumn",{headerOnly:!0,size:10}),e.define("fixedRowHeight",!1),e.attachEvent("onRowResize",function(e){h.$$("cells").refreshSelectArea(),p(h,"sizes",{row:e,column:0,size:h.getRow(e).$height})}),e.attachEvent("onColumnResize",function(e){h.$$("cells").refreshSelectArea(),p(h,"sizes",{row:0,column:e,size:h.getColumn(e).width})}),h.attachEvent("onUndo",function(e,t,n,i){"c-resize"!=e&&"r-resize"!=e||function o(e,t,n,i){t?(e.$$("cells").getItem(t).$height=i,p(e,"sizes",{row:t,column:0,size:i})):e._table.setColumnWidth(n,i),e._table.refreshSelectArea()}(h,t,n,i)})}h.attachEvent("onDataParse",function(e){if(e.sizes){for(var t=0;t<e.sizes.length;t++){var n=e.sizes[t];if(1*n[0]!=0){var i=h.getRow(n[0]);i&&(i.$height=1*n[2])}else{var o=h.getColumn(n[1]);o&&(o.width=1*n[2])}}e.sizes.length&&h.refresh(!0)}}),h.attachEvent("onDataSerialize",function(e){for(var t=[],n=h.$$("cells"),i=n.getState().order,o=n.data.order,r=n.config.columnWidth,
a=n.config.rowHeight,l=1;l<i.length;l++){var s=n.getColumnConfig(i[l]).width;s&&s!=r&&t.push([0,l,s])}for(var c=0;c<o.length;c++){var u=n.getItem(o[c]).$height;u&&u!=a&&t.push([1*o[c],0,u])}e.sizes=t}),h.attachEvent("onAction",function(e,t){"before-grid-change"==e&&function h(e,t,n,i){var o,r=n.sizes,a=[];if(t){for(o=r.length-1;0<=o;o--){var l=S(r[o],3),s=l[0],c=l[1],u=l[2];s&&"row"==e&&s>=i.row||c&&"column"==e&&c>=i.column?(s="row"==e?1*s+t:s,c="column"==e?1*c+t:c,("row"==e&&0<s||"column"==e&&0<c)&&a.push([s,c,u])):a.push(r[o])}n.sizes=a}}(t.name,t.inc,t.data,t.start)}),h.attachEvent("onBeforeStyleChange",function(e,t,n,i){!i||"wrap"!==i.props.wrap||n&&n.props.wrap==i.props.wrap||delete h._table.getItem(e).$height}),h.attachEvent("onStyleChange",Io),h.attachEvent("onCellChange",Io)}});var Fo=Object.freeze({init:function cl(r){var a=r._table,e=0;a.attachEvent("onAreaDrag",function(){return e=new Date}),a.attachEvent("onBeforeAreaRemove",function(){if(r.$handleSelection&&new Date-e<500)return!1}),a.attachEvent("onBeforeAreaAdd",function(e){if("rowId"==e.start.column)return!1;var t=a.getEditor();if(!t||t.row==e.start.row&&t.column==e.start.column&&t.row==e.start.row&&t.column==e.start.column||a.editStop(),!r.$handleSelection)return!0;var n=$[e.start.column]+e.start.row,i=$[e.end.column]+e.end.row,o=r.$handleSelection(e.start,e.end,n,i);return!1!==o&&(r.$handleSelection=null,a.removeSelectArea()),o}),webix.event(a.$view,"mousedown",function(e){if(r.$handleSelection)return webix.html.preventEvent(e)})}});function Oo(e,t,n){var i=n.getItem(e)[t];return i!==undefined&&""!==i}var Mo={"int":function(o,r){return function(e,t){var n=parseFloat(e[r])||-Infinity,i=parseFloat(t[r])||-Infinity;return(i<n?1:n==i?0:-1)*o}},str:function(o,r){return function(e,t){var n=(e[r]||"").toString().toLowerCase(),i=(t[r]||"").toString().toLowerCase();return(i<n?1:n==i?0:-1)*o}}};function Do(e,t,n,i){if(t=t||e._table.getSelectArea()){var o=t=A(t,e);t.start.row===t.end.row&&(o=function v(e,t){for(var n=t.config.rowCount,i=t.config.columnCount,o=t._table,r=e.start.row,a=e.end.row,l=e.start.column,s=e.end.column,c=r-1;0<c&&Oo(c,e.start.column,o);c--)r=c;for(var u=a+1;u<n&&Oo(u,e.end.column,o);u++)a=u;for(var h=l-1;0<h&&Oo(e.start.row,h,o);h--)l=h;for(var d=s+1;d<i&&Oo(e.end.row,d,o);d++)s=d;var f={start:{row:r,column:l},end:{row:a,column:s}};return r==e.start.row&&a==e.end.row&&l==e.start.column&&s==e.end.column||t._table.addSelectArea(f.start,f.end),f}(t,e));var r=e.getRow(t.start.row)[t.start.column];i=i||(isNaN(parseFloat(r))?"str":"int"),n=n&&"asc"!==n?-1:1,m.set(function(){return function w(e,t,n,i,o){for(var r=e.start.column;r<=e.end.column;r++){for(var a=[],l=e.start.row;l<=e.end.row;l++)if(o.isRowVisible(l)){var s=o.getRow(l),c=s[r],u=o.getStyle(l,r),h=s["$"+r],d=o.conditions.get(l,r);d&&ao("remove",l,r,d,null,o),a.push({value:c,style:u,math:h,row:l,conditions:d})}a.sort(Mo[n](i,"value"));for(var f=e.start.row;f<=e.end.row;f++)if(o.isRowVisible(f)){var v=a.shift();if(o.setStyle(f,r,v.style||null),v.conditions&&ao("update",f,r,null,v.conditions,o),v.math){var m={id:"move",row:f-v.row,column:0},g=_n(v.math,m);o.setCellValue(f,r,g)}else o.setCellValue(f,r,v.value)}}o.refresh()}(o,t.start.column,i,n,e)})}}var No=Object.freeze({init:function ul(t){t.attachEvent("onCommand",function(e){"sort-asc"!==e.id&&"sort-desc"!==e.id||Do(t,null,e.id.replace("sort-",""))})},sortRange:Do});function Uo(e){var t=!(1<arguments.length&&arguments[1]!==undefined)||arguments[1],n=e.$$("cells"),i=-1!=n.$view.className.indexOf("webix_borderless");"toggle"===t&&(t=!i),!i&&t?(webix.html.addCss(n.$view,"webix_borderless",!0),e.callEvent("onCommand",[{id:"toolbar-update",name:"hide-gridlines",value:!0}]),e.callEvent("onAction",["gridlines-hide",{newValue:!0,value:!1}])):i&&!t&&(webix.html.removeCss(n.$view,"webix_borderless"),e.callEvent("onCommand",[{id:"toolbar-update",name:"hide-gridlines",value:!1}]),e.callEvent("onAction",["gridlines-hide",{newValue:!1,value:!0}]))}var To=Object.freeze({init:function hl(o){o.attachEvent("onUndo",function(e,t,n,i){
"gridlines-hide"==e&&Uo(o,i)})},hideGridlines:Uo,serialize:function dl(e,t){t.table.gridlines=-1!=e.$$("cells").$view.className.indexOf("webix_borderless")?0:1},load:function fl(e,t){var n=!1;webix.isUndefined(t.table)||webix.isUndefined(t.table.gridlines)||(n=!t.table.gridlines),Uo(e,n)}});function jo(e){var t=!(1<arguments.length&&arguments[1]!==undefined)||arguments[1],n=e.$$("cells");"toggle"===t&&(t=n.config.header),n.config.header&&t?(n.config.header=!1,n.isColumnVisible("rowId")&&n.hideColumn("rowId",{},!0,!0),n.refreshColumns(),e.callEvent("onCommand",[{id:"toolbar-update",name:"hide-headers",value:!0}]),e.callEvent("onAction",["header-hide",{newValue:!0,value:!1}])):n.config.header||t||(n.config.header=!0,n.isColumnVisible("rowId")||n.showColumn("rowId",{},!0),n.refreshColumns(),e.callEvent("onCommand",[{id:"toolbar-update",name:"hide-headers",value:!1}]),e.callEvent("onAction",["header-hide",{newValue:!1,value:!0}]))}var Bo=Object.freeze({init:function vl(o){o.attachEvent("onUndo",function(e,t,n,i){"header-hide"==e&&jo(o,i)})},hideHeaders:jo,serialize:function ml(e,t){t.table.headers=e.$$("cells").config.header?1:0},load:function gl(e,t){var n=!1;t.table&&!webix.isUndefined(t.table.headers)&&(n=!t.table.headers),jo(e,n)}});function Ho(e,t,n,i){Wo(e,t,n)&&(i||e.callEvent("onAction",["comment",{row:t,column:n,newValue:null,value:e.comments._pull[t][n]}]),delete e.comments._pull[t][n]),e.$$("cells").updateItem(t,n)}function Po(e,t){m.set(function(){F(t,e,function(e,t){Wo(e,t.row,t.column)&&Ho(e,t.row,t.column)}),e.$$("cells").refresh()})}function Lo(e,t,n,i,o){var r=Wo(e,t,n)?e.comments._pull[t][n]:null,a=e.$$("cells");"string"==typeof i&&(i=i.trim()?i:""),e.comments._pull[t]=e.comments._pull[t]?e.comments._pull[t]:{},e.comments._pull[t][n]=i,a.addCellCss(t,n,"ssheet_commented_cell"),o||e.callEvent("onAction",["comment",{row:t,column:n,newValue:i,value:r}])}function Wo(e,t,n){return e.comments._pull[t]&&e.comments._pull[t][n]?e.comments._pull[t][n]:""}var Ko=Object.freeze({init:function wl(r){r.comments={get:function(e,t){return Wo(r,e,t)},add:function(e,t,n){Lo(r,e,t,n)},remove:function(e,t){Ho(r,e,t)},_activeComment:{},_pull:{}},r.attachEvent("onReset",function(){return function t(e){e.comments._pull={}}(r)}),r.attachEvent("onUndo",function(e,t,n,i){"comment"===e&&function o(e,t,n,i){Wo(e,t,n)&&Ho(e,t,n,!0),i&&Lo(e,t,n,i,!0)}(r,t,n,i)}),r.attachEvent("onAction",function(e,t){"before-grid-change"==e&&function c(e,t,n,i){var o=n.comments,r=o.length;if(t)for(;r--;){var a=S(o[r],2),l=a[0],s=a[1];(l&&"row"==e&&l>=i.row||s&&"column"==e&&s>=i.column)&&("row"==e?l<i.row-t?o.splice(r,1):o[r][0]=1*l+t:"column"==e&&(s<i.column-t?o.splice(r,1):o[r][1]=1*s+t))}}(t.name,t.inc,t.data,t.start)})},removeCommentsRange:Po,serialize:function pl(e,t){var n,i,o=[];for(n in e.comments._pull)for(i in e.comments._pull[n])e.comments._pull[n][i]&&o.push([n,i,e.comments._pull[n][i]]);t.comments=o},load:function bl(e,t){var n,i=t.comments;if(i)for(n=0;n<i.length;n++)Lo(e,i[n][0],i[n][1],i[n][2],!0)}});var Yo=Object.freeze({init:function xl(n){var i=[To,Bo,jn,wo,hi,Ro,Ko];n.attachEvent("onDataSerialize",function(e){for(var t=0;t<i.length;t++)i[t].serialize&&i[t].serialize(n,e)}),n.attachEvent("onDataParse",function(e){for(var t=0;t<i.length;t++)i[t].load&&i[t].load(n,e)})}}),qo=[".xls",".xlt",".xla",".xlsx",".xlsm",".xltx",".xltm",".xlam",".xlsb"];var Go=Object.freeze({init:function _l(t){var i;window.XMLHttpRequest&&(new XMLHttpRequest).upload&&((i=webix.ui({view:"uploader",apiOnly:!0,accept:qo.join()})).attachEvent("onBeforeFileAdd",webix.bind(function(e){return-1<qo.indexOf("."+e.type.toLowerCase())&&(t.reset(),t.parse(e.file,"excel")),!1},this)),t._destroy_with_me.push(i)),t.attachEvent("onCommand",function(e){"excel-import"===e.id&&function n(e,t){t?webix.delay(function(){return t.fileDialog()}):e.alert(webix.i18n.spreadsheet.labels["import-not-support"])}(t,i)})}});function Zo(e,t){var n=e._activeSheet,i=function o(a,l,s){var c=webix.html.create("div",{"class":"webix_ssheet_print"});l.xCorrection=1,l.header=l.header||!1,
l.trim=!!webix.isUndefined(l.trim)||l.trim,l.sheetnames=!!webix.isUndefined(l.sheetnames)||l.sheetnames;var u=a.$index;return a._sheets.forEach(function(e,t){if(a.$index=u+"_"+t,"all"===l.data||"all"!==l.data&&e.name===s){a.showSheet(e.name);var n="wss_"+a.$index;if(l.sheetnames){var i=webix.html.create("div",{"class":"webix_view webix_ssheet_sheetname"},e.name+":");c.appendChild(i)}var o=a._table.$customPrint(l,!0);if(o.firstChild&&(o.firstChild.className+=" "+n),c.appendChild(o),"all"==l.data&&t+1<a._sheets.length){var r=webix.html.create("DIV",{"class":"webix_print_pagebreak"});c.appendChild(r)}}}),a.$index=u,c}(e,t,n);webix.html.insertBefore(i,t.docFooter,document.body),window.print(),function r(n,e){webix.html.remove(e),n._sheets.forEach(function(e,t){webix.html.removeStyle(".wss_"+n.$index+"_"+t)})}(e,i),e.showSheet(n)}var Xo=Object.freeze({print:Zo,init:function yl(t){t.$customPrint=function(e){Zo(t,e)}}});function Qo(e){return 3===(e=e.substring(1)).length&&(e+=e),e}function Jo(a,l){a.compactStyles();var s=[],c={},u=function n(){var e=U;return{font:{sz:.75*e["font-size"].replace("px",""),name:e["font-family"].replace(/'|,.*$/g,"")},alignment:{horizontal:e["text-align"],vertical:"center",wrapText:"nowrap"!=e["white-space"]}}}();l.docHeader&&(s=s.concat([{0:tr(l.docHeader.css)},{}])),l.header&&s.push({});var e=s.length,t=a.$$("cells"),h=t.config.columns,d=e;return t.eachRow(function(e){for(var t=l.xCorrection;t<h.length;t++){var n=a.getStyle(e,h[t].id),i=t-l.xCorrection;s[d]=s[d]||{};var o=void 0;if(n)if(c[n.id])o=c[n.id];else{for(var r in o=er(n.text),u)webix.extend(o[r],u[r]);c[n.id]=o}else o=u;s[d][i]=webix.copy(o),s[d][i].type=Ut(a,e,h[t].id)}d++}),l.docFooter&&(s=s.concat([{},{0:tr(l.docFooter.css)}])),s}function er(e){for(var t=e.split(";"),n={font:{},alignment:{},border:{}},i=0;i<t.length;i++)if(t[i])switch(j[i]){case"color":n.font.color={rgb:Qo(t[i])};break;case"background":var o=Qo(t[i]);o&&"ffffff"!==o.toLowerCase()&&(n.fill={fgColor:{rgb:o}});break;case"text-align":n.alignment.horizontal=t[i];break;case"font-family":n.font.name=t[i].replace(/'|,.*$/g,"");break;case"font-size":n.font.sz=.75*t[i].replace("px","");break;case"font-style":n.font.italic="italic"==t[i];break;case"text-decoration":n.font.underline="underline"==t[i];break;case"font-weight":n.font.bold="bold"==t[i];break;case"vertical-align":n.alignment.vertical="middle"==t[i]?"center":t[i];break;case"wrap":n.alignment.wrapText="wrap"==t[i];break;case"borders":break;case"format":n.format=ve(t[i],!0)||"";break;case"border-right":n.border.right={color:{rgb:Qo(t[i])},style:"thin"};break;case"border-bottom":n.border.bottom={color:{rgb:Qo(t[i])},style:"thin"};break;case"border-left":n.border.left={color:{rgb:Qo(t[i])},style:"thin"};break;case"border-top":n.border.top={color:{rgb:Qo(t[i])},style:"thin"}}return n}function tr(e){if(!e)return{};for(var t=[],n=0;n<j.length;n++)t.push(e[j[n]]||"");return er(t.join(";"))}var nr=Object.freeze({init:function Cl(t){t.$exportView=function(e){return webix.extend(e,{header:!1,rawValues:!0,spans:!0,styles:!0,math:!0,xCorrection:1,ignore:{rowId:!0}}),"excel"==e.export_mode?function u(e,t){!0===t.sheets?t.sheets=e._sheets.map(function(e){return e.name}):t.sheets&&t.sheets.length?"string"==typeof t.sheets&&(t.sheets=[t.sheets]):t.sheets=[e._activeSheet],t.dataOnly=!0;for(var n=[],i=e._activeSheet,o=0;o<t.sheets.length;o++){var r=t.sheets[o],a=r.id||r;e.showSheet(a),t.xCorrection=e.$$("cells").config.header?1:0;var l=r.options?webix.extend(r.options,t):webix.copy(t);l.name||(l.name=a),(n=n.concat(webix.toExcel(e._table,l)))[o].ranges=[];for(var s=e.ranges.getRanges(),c=0;c<s.length;c++)n[o].ranges.push({Sheet:o,Name:s[c].name,Ref:l.name+"!"+s[c].range.replace(/(\w)/gi,function(e){return"$"+e})});l.styles&&(n[o].styles=Jo(e,l))}return e.showSheet(i),delete t.dataOnly,n}(t,e):t._table}}});function ir(e,t,n){for(var i=webix.copy(e.data),o=[],r=0;r<i.length;r++)for(var a=0;a<i[0].length;a++){var l=r+1,s=a+1;o.push([l,s,i[r][a]])}if(e.data=o,e.spans)for(var c=0;c<e.spans.length;c++)e.spans[c][0]++,e.spans[c][1]++;if(t)for(
var u=0;u<t.length;u++)t[u].Sheet===n&&(e.ranges=e.ranges||[],e.ranges.push([t[u].Name.toUpperCase(),t[u].Ref.substring(t[u].Ref.indexOf("!")+1).replace(/\$/g,"")]));if(e.styles&&function d(e){for(var t={},n=[],i=1,o=0;o<e.styles.length;o++){for(var r=[],a=e.styles[o][2],l=void 0,s=0;s<j.length;s++)switch(j[s]){case"color":r[s]=a.font&&a.font.color&&or(a.font.color.rgb)||"";break;case"background":r[s]=a.fill&&a.fill.fgColor&&or(a.fill.fgColor.rgb)||"";break;case"text-align":r[s]=(a.alignment?a.alignment.horizontal:"")||U["text-align"];break;case"font-family":r[s]=a.font&&a.font.name?rr(a.font.name):"";break;case"font-size":r[s]=a.font&&a.font.sz&&a.font.sz/.75+"px"||"";break;case"font-style":r[s]=a.font&&a.font.italic?"italic":"";break;case"text-decoration":r[s]=a.font&&a.font.underline?"underline":"";break;case"font-weight":r[s]=a.font&&a.font.bold?"bold":"";break;case"vertical-align":var c=a.alignment&&a.alignment.vertical||"";r[s]=("center"==c?"middle":c)||U["vertical-align"];break;case"wrap":r[s]=a.alignment&&a.alignment.wrapText?"wrap":"nowrap";break;case"borders":case"format":r[s]="";break;case"border-right":r[s]=a.border&&a.border.right&&or(a.border.right.color.rgb)||"";break;case"border-bottom":r[s]=a.border&&a.border.bottom&&or(a.border.bottom.color.rgb)||"";break;case"border-left":r[s]=a.border&&a.border.left&&or(a.border.left.color.rgb)||"";break;case"border-top":r[s]=a.border&&a.border.top&&or(a.border.top.color.rgb)||""}r=r.join(";"),l=t[r]||"wss"+i;for(var u=0;u<e.data.length;u++)if(e.data[u][0]===e.styles[o][0]+1&&e.data[u][1]===e.styles[o][1]+1){e.data[u][3]=l;break}t[r]||(n.push([l,r]),t[r]=l,i++)}e.styles=n}(e),e.types&&function f(n){n.types.forEach(function(e){for(var t=0;t<n.data.length;t++)if(n.data[t][0]===e[0]+1&&n.data[t][1]===e[1]+1){n.data[t][4]=e[2];break}})}(e),e.sizes)for(var h=0;h<e.sizes.length;h++)"column"==e.sizes[h][0]?e.sizes[h]=[0,e.sizes[h][1]+1,e.sizes[h][2]]:e.sizes[h]=[e.sizes[h][1]+1,0,e.sizes[h][2]];return e}function or(e){return 8===(e=e||"000000").length&&(e=e.substring(2)),"#"+e}function rr(e){for(var t=U["font-family"],n=0;n<Je.length;n++)if(Je[n].value==e){t=Je[n].id;break}return t}var ar=Object.freeze({init:function $l(r){r._parseExcel=function(e,t){var n=e.options||{};n.math=r.config.math;for(var i={sheets:[]},o=0;o<e.names.length;o++)n.name=e.names[o],i.sheets.push({name:n.name,content:ir(t.getSheet(e,n),e.ranges,o)});return i},r._parseCsv=function(e,t){e=t.getRecords(e);for(var n=webix.copy(e),i=[],o=0;o<n.length;o++)for(var r=n[o].split(t.cell),a=0;a<r.length;a++){var l=o+1,s=a+1;i.push([l,s,r[a]])}return{data:i}}}});var lr,sr=Object.freeze({init:function Sl(t){t.attachEvent("onCommand",function(e){switch(e.id){case"clear-value":t.clearRange(null,{values:!0});break;case"clear-style":t.clearRange(null,{styles:!0});break;case"clear-conditional-formats":t.clearRange(null,{conditions:!0});break;case"clear-dropdown-editors":t.clearRange(null,{editors:!0});break;case"clear-all":m.set(function(){t.clearRange(null,null)});break;case"clear-comments":t.clearRange(null,{comments:!0})}})}});function cr(i){clearTimeout(lr),lr=webix.delay(function(){var e=i.views;for(var t in e._pull){var n=e._types[e._pull[t].type].track;n&&ur(i,e.get(t),n,e._pull[t])}})}function ur(e,t,n,i){n(t,pr(e,i,!0))}function hr(e,t){for(var n in e.views._pull){var i=webix.$$(n),o=e.views._pull[n];t&&(o.x=fr(e,i,o.x,"x"),o.y=fr(e,i,o.y,"y")),dr(e,i,o)}}function dr(e,t,n){var i=e._table.getScrollState();t.show({x:n.x,y:n.y-i.y})}function fr(e,t,n,i){if(n||0===n){var o="x"==i?function r(e){var t=0;return e._table.eachColumn(function(e){"rowId"!==e&&(t+=this.getColumnConfig(e).width)}),t}(e)-t.$width:function a(e){var t=0;return e._table.data.each(function(e){t+=e.$height||webix.skin.$active.rowHeight}),t}(e)-t.$height;n=Math.min(Math.max(n,0),o)}else n=e._table.getScrollState()[i]+50;return n}var vr=[["#f55b50","#ff6d3f","#ffa521"],["#ffc927","#ffee54","#d3e153"],["#9acb61","#63b967","#21a497"],["#21c5da","#3ea4f5","#5868bf"],["#7b53c0","#a943ba","#ec3b77"]],mr=0;function gr(e){return"pie"==e||"donut"==e}
function wr(e){return"string"==typeof e&&-1===e.indexOf("data:image")}function pr(e,t,n){var i=t.data,o=wr(i)?V(i,e):null;if(o){var r=$n(e,o[4]);i=r?r.getRangeCols.apply(r,o):[]}var a=t.config;return webix.isArray(i)&&n&&a&&a.legend&&!gr(a.type)&&i.shift(),i}var br=Object.freeze({init:function kl(f){var u=f._table.$view.querySelector(".webix_ss_center");f.views={register:function(e,t,n){this._types[e]||(this._types[e]={render:t,track:n})},move:function(e,t,n){var i=webix.$$(e),o=webix.copy(this._pull[e]);this._pull[e].x=fr(f,i,t,"x"),this._pull[e].y=fr(f,i,n,"y"),f.callEvent("onAction",["move-view",{row:e,newValue:webix.copy(this._pull[e]),value:o}]),dr(f,i,this._pull[e])},add:function(e,t,n,i,o,r){var a=this;o=o||{},"chart"!=n||o.type||(o.type="line");var l=o.height||300,s=o.width||500,c=webix.ui({id:r||"$ssheet-ui"+webix.uid(),view:"ssheet-ui",container:u,master:f._table.config.id,width:s,height:l});return c.attachEvent("onViewMoveEnd",function(e,t){var n=f._table.getScrollState();a.move(r,e,t+n.y)}),c.attachEvent("onViewResize",function(){var e=a._pull[r],t=webix.copy(e);e.config.height=c.$height,e.config.width=c.$width,f.callEvent("onAction",["resize-view",{row:r,newValue:webix.copy(e),value:t}])}),c.attachEvent("onViewEdit",function(){var e=a._pull[r],t=a._commands[e.type]||e.type;f.callEvent("onCommand",[{id:t,viewid:r,config:e}])}),c.attachEvent("onViewRemove",function(){return a.remove(r)}),r=c.config.id,e=fr(f,c,e,"x"),t=fr(f,c,t,"y"),this._pull[r]={x:e,y:t,type:n,data:i,config:o},this.update(r,i,o,!0),dr(f,c,this._pull[r]),r},remove:function(e){var t=this._pull[e];webix.$$(e).close(),delete this._pull[e],f.callEvent("onAction",["add-view",{row:e,newValue:null,value:t}])},get:function(e){return webix.$$(e).getBody()},update:function(e,t,n,i){var o=i?null:webix.copy(this._pull[e]),r=this._pull[e],a=this._types[r.type];t=t||n.data||[],delete(n=n||{}).data;var l=webix.$$(e),s=n.height||300,c=n.width||500;l.$width!=c&&l.$height!=s&&(l.define({width:c,height:s}),l.resize()),r.data=t,r.config=n;var u=this.get(e);if(t=pr(f,r),a.render){var h=webix.copy(r);h.config.width=h.config.height=0,function d(e){var t=function(e){return"datatable"==e.name||"treetable"==e.name},n=e.queryView(t,"self");(n=n?[n]:e.queryView(t,"all")).forEach(function(e){return e.disable()})}(u=a.render(u,h,t))}a.track&&ur(f,u,a.track,r),f.callEvent("onAction",["add-view",{row:e,newValue:webix.copy(this._pull[e]),value:o}])},_commands:{image:"add-image-top",chart:"add-chart"},_types:{},_pull:{}},f._table.attachEvent("onResize",function(){return hr(f)}),f._table.attachEvent("onScrollY",function(){return hr(f)}),f._table.attachEvent("onScrollX",function(){return hr(f)}),f.attachEvent("onReset",function(){return function e(t){Ui(function(){for(var e in t.views._pull)t.views.remove(e)},t)}(f)}),f.attachEvent("onUndo",function(e,t,n,i){var o="move-view"===e;(o||"resize-view"==e||"add-view"===e)&&function c(e,t,n,i){if(i){var o=i.x,r=i.y,a=i.type,l=i.data,s=i.config;e.views._pull[n]?t?e.views.move(n,o,r):e.views.update(n,l,s):e.views.add(o,r,a,l,s,n)}else e.views.remove(n)}(f,o,t,i)}),f.attachEvent("onAction",function(e,t){"header-hide"==e?hr(f):"before-grid-change"==e&&function l(e,t,n,i,o){for(var r=i.views,a=0;a<r.length;a++)wr(r[a][3])&&(r[a][3]=D(r[a][3],t,n,o))}(0,t.name,t.inc,t.data,t.start)}),f.attachEvent("onColumnOperation",function(){return hr(f,!0)}),f.attachEvent("onRowOperation",function(){return hr(f,!0)}),f.attachEvent("onDataParse",function(e){return function i(t,n){webix.delay(function(){var e=n.views;e&&Ui(function(){e.forEach(function(e){return t.views.add.apply(t.views,e)})},t)})}(f,e)}),f.attachEvent("onDataSerialize",function(e,t){return function a(e,t,n){for(var i in t.views=[],e.views._pull){var o=e.views._pull[i],r=[o.x,o.y,o.type,o.data,o.config];n&&n.viewIds&&r.push(i),t.views.push(r)}}(f,e,t)}),f.attachEvent("onCellChange",function(){return cr(f)}),f.attachEvent("onMathRefresh",function(){return cr(f)}),f.attachEvent("onDestruct",function(){for(var e in f.views._pull)webix.$$(e).destructor()}),f.views.register("chart",function(
e,t,n){var i=function d(e,t){var n,i,o=webix.copy(e.config),r=o.type,a={align:"center",valign:"bottom",layout:"x"};if(gr(r))!function h(e,t,n){for(var i in e[0]){t.legend?(n.template="#".concat(i,"#"),t.value="#".concat(i.replace("0","1"),"#")):t.value="#".concat(i,"#");break}t.legend&&(t.legend=n),t.pieInnerText&&(t.pieInnerText=t.value)}(t,o,a);else{a.values=[];var l=[];for(var s in t[0])if("data0"!=s||!o.xAxis){var c=(void 0,n=vr[mr],i=Math.floor(Math.random()*n.length),++mr==vr.length&&(mr=0),n[i]),u={value:"#".concat(s,"#")};"radar"==r||"line"==r||"spline"==r?(u.line={color:c},u.item={borderColor:c}):u.color=c,"area"!=r&&"splineArea"!=r||(u.alpha=.7),l.push(u),a.values.push({text:t[0][s],color:c})}o.xAxis&&(o.xAxis={template:"#data0#"}),o.legend&&(o.legend=a),o.series=l}return webix.extend({view:"chart",yAxis:{}},o||{},!0)}(t,n);return webix.ui(i,e)},function(e,t){e.clearAll(),e.parse(t)}),f.views.register("image",function(e,t,n){var i=webix.extend({css:"webix_ssheet_bgimage",template:'<img src="'.concat(n,'"/>')},t.config||{},!0);return webix.ui(i,e)})}});function xr(e,t,n){if(t)webix.isArray(t)||(t=[t,t]);else{var i=e.$$("cells").getSelectArea();i&&(t=[i.start[n],i.end[n]])}return t}function _r(e,t,n,i){var o={},r={};(i=xr(e,i,n))&&(o[n]=i[0],r[n]=i[1],e.callEvent("onCommand",[{id:t,group:n},o,r]))}function yr(t,n,i,o){(i=xr(t,i,n))&&m.set(function(){for(var e=i[0];e<=i[1];e++)"auto"!=o?t.$$("cells")["row"==n?"setRowHeight":"setColumnWidth"](e,o):("row"==n?yt:_t)(e,t)})}var Cr=Object.freeze({insertRow:function El(e){_r(this,"add","row",e)},deleteRow:function Vl(e){_r(this,"del","row",e)},setRowHeight:function zl(e,t){yr(this,"row",e,t)},insertColumn:function Rl(e){_r(this,"add","column",e)},deleteColumn:function Il(e){_r(this,"del","column",e)},setColumnWidth:function Al(e,t){yr(this,"column",e,t)}});var $r=Object.freeze({lockCell:function Fl(e,t,n){Un(this,e,t,n)},isCellLocked:function Ol(e,t){return Tn(this,e,t)}});function Sr(e,t,n){var i=e.$$("cells");if("row"==t)for(var o=e.config.columnCount,r=1;r<=o;r++){var a=i.getSpan(n,r);if(a)n<1*a[0]+a[3]-1&&kr(e,a,a[2],n-a[1]+1)}else for(var l=i.data.order[0],s=i.data.order.length,c=l;c<=s;c++){var u=i.getSpan(c,n);if(u)n<1*u[1]+u[2]-1&&kr(e,u,n-u[0]+1,u[3])}}function kr(e,t,n,i){e.splitCell(t[0],t[1]),e.combineCells({cell:{row:t[0],column:t[1]},x:n,y:i})}var Er=Object.freeze({freezeColumns:function Ml(e){var t=this.$$("cells");if(webix.isUndefined(e)){var n=this.getSelectedId(!0);e=n.length?n[0].column:0}var i="rowId"==t.config.columns[0].id?1:0,o=i;e&&e!=this._frozenColumns&&(o=t.getColumnIndex(1*e+1)),o<i&&(o=i),m.set(function(){Sr(this,"column",e),t.unselect(),t.define("leftSplit",o),t.refreshColumns(),this.callEvent("onAction",["freeze-column",{value:this._frozenColumns||0,newValue:e}]),this._frozenColumns=i<o?e:0},this)},freezeRows:function Dl(e){var t=this.$$("cells");if(webix.isUndefined(e)){var n=this.getSelectedId(!0);e=n.length?n[0].row:0}var i=0;e&&e!=this._frozenRows&&(i=t.getIndexById(1*e+1)),i<0&&(i=0),m.set(function(){Sr(this,"row",e),t.unselect(),t.define("topSplit",i),t.refreshColumns(),this.callEvent("onAction",["freeze-row",{value:this._frozenRows,newValue:e}]),this._frozenRows=i?e:0},this)}});var Vr=Object.freeze({hideGridlines:function Nl(e){Uo(this,e)}});var zr=Object.freeze({hideHeaders:function Ul(e){jo(this,e)}});var Rr=Object.freeze({hideColumn:function Tl(e,t){Co(this,e,t,"column")},isColumnVisible:function jl(e){return yo(this,e)},hideRow:function Bl(e,t){Co(this,e,t,"row")},isRowVisible:function Hl(e){return $o(this,e)}});var Ir=Object.freeze({addImage:function Pl(e,t,n){this.setCellValue(e,t,function i(e){return'=IMAGE("'.concat(e,'")')}(n)),this.refresh()},addSparkline:function Ll(e,t,n){var i=function o(e){var t="=SPARKLINE(".concat(e.range,',"').concat(e.type,'"');return"bar"===e.type?t+=',"'.concat(e.color,'","').concat(e.negativeColor,'"'):e.color&&(t+=',"'.concat(e.color,'"')),t+")"}(n);this.setCellValue(e,t,i),this.refresh()},getSheetData:function Wl(e){return $n(this,e)},recalculate:function Kl(){this.callEvent("onMathRefresh",[]
)},registerMathMethod:function Yl(e,t){if(function o(e,t){Mt[e]=t}(e=e.toUpperCase(),t),this.$$("liveEditor")){var n=this.$$("liveEditor").config.suggest,i=webix.$$(n).getList();i.exists(e)||i.add({id:e,value:e})}}});var Ar=Object.freeze({combineCells:function ql(e){var t=this;if(!e){var n=this.getSelectedId(!0);1<n.length&&(e=Ln(n))}m.set(function(){e&&Hn(t,e.cell,e.x,e.y)}),this.refresh()},splitCell:function Gl(e,t){e&&t?Pn(this,{row:e,column:t}):m.set(function(){this.eachSelectedCell(function(e){Pn(this,e)})},this),this.refresh()},addStyle:function Zl(e,t){return K(this,e,t)},getStyle:function Xl(e,t){return W(this,{row:e,column:t})},setStyle:function Ql(e,t,n){return Z(this,{row:e,column:t},n)},setRangeStyle:function Jl(e,t){m.set(function(){X(this,e,t)},this)},clearRange:function es(e,t){(e||(e=this._table.getSelectArea()))&&(t||(t={styles:!0,values:!0,editors:!0,conditions:!0,comments:!0}),t.styles&&ie(this,e),t.conditions&&oo(this,e),t.values&&this.setRangeValue(e,null),t.editors&&(co(this,e),Jn(this,e)),t.comments&&Po(this,e),this.refresh())},compactStyles:function ts(){return oe(this)},serialize:function ns(e){var t={};return this.callEvent("onDataSerialize",[t,e]),e&&e.sheets?function n(e,t){return rn(e,e._activeSheet).content=t,e._sheets}(this,t):t},showSheet:function is(e){on(this,e)},getActiveSheet:function os(){return this._activeSheet},addSheet:function rs(e){Xt(this,e)},clearSheet:function as(){this.reset()},renameSheet:function ls(e,t){en(this,e,t)},editSheet:function ss(e){Qt(this,e)},removeSheet:function cs(e){Jt(this,e)},undo:function us(){Oi(this)},redo:function hs(){Mi(this)},sortRange:function ds(e,t){Do(this,e,t)}});var Fr=Object.freeze({refresh:function fs(e){e?this._table.refreshColumns():this._table.refresh()},eachSelectedCell:function vs(e){for(var t=this.getSelectedId(!0),n=0;n<t.length;n++)e.call(this,t[n])},getSelectedRange:function ms(e){var t=this._table.getSelectArea();return t?(e&&(e=this.getActiveSheet()),I(t.start.row,t.start.column,t.end.row,t.end.column,e)):""},getSelectedId:function gs(e){var t=this._table.getSelectArea();if(!e)return t&&t.start.row?t.start:null;var n=[];if(t)for(var i=t.start,o=t.end,r=i.row;r<=o.row;r++)for(var a=i.column;a<=o.column;a++)n.push({row:r,column:a});return n},getCellValue:function ws(e,t){var n=this.getRow(e),i=n["$"+t]||n[t];return void 0===i?"":i},setCellValue:function ps(e,t,n){var i=this;if(this.getCellValue(e,t)!==n){var o=this.getRow(e),r=o["$"+t]||o[t];m.set(function(){i.callEvent("onBeforeValueChange",[e,t,n,r])&&(o[t]=n,delete o["$"+t],i.callEvent("onCellChange",[e,t,n]),i.saveCell(e,t))})}},setRangeValue:function bs(i,o){m.set(function(){for(var e=A(i,this),t=e.start.row;t<=e.end.row;t++)for(var n=e.start.column;n<=e.end.column;n++)this.setCellValue(t,n,o)},this)},getRow:function xs(e){return this._table.getItem(e)},getColumn:function _s(e){return this._table.getColumnConfig(e)},reset:function ys(e){var t={data:[]};e&&e.sheets&&(t=nn(t)),this.parse(t)},_resetTable:function Cs(){Et(this)}});var Or=Object.freeze({saveCell:function $s(e,t){var n=this.getStyle(e,t);p(this,"data",{row:e,column:t,value:this.getCellValue(e,t),style:n?n.id:""})}});var Mr=Object.freeze({getRow:function Ss(e){return this._table.getItem(e)},setCellEditor:function ks(e,t,n){var i=this._table._ssEditors[e]=this._table._ssEditors[e]||{},o=i[t]||this.getRow(e)[t];(i[t]=n)&&n.editor?(this.getCellFilter(e,t)&&this.setCellFilter(e,t,null),this._table.addCellCss(e,t,"ss_editor")):(delete this._table._ssEditors[e][t],this._table.removeCellCss(e,t,"ss_editor")),this.callEvent("onAction",["dropdown",{row:e,column:t,value:o,newValue:n}])},getCellEditor:function Es(e,t){return(this._table._ssEditors[e]||{})[t]||null}});var Dr=Object.freeze({setCellFilter:function Vs(e,t,n){var i=this._table._ssFilters[e]=this._table._ssFilters[e]||{},o=i[t]||this.getRow(e)[t];if(n&&(webix.isArray(n)||"string"==typeof n)&&(n={options:n}),(i[t]=n)&&"object"==g(n)){if(this.getCellEditor(e,t)&&this.setCellEditor(e,t,null),this._table.addCellCss(e,t,"ss_filter"),n.value&&ti(n.value
)&&this._table.addCellCss(e,t,"ss_filter_active"),webix.extend(n,{row:e,column:t},!0),n.options=ei(this,e,t,n.options),!n.handler&&n.value&&n.value.condition){var r=n.mode||oi(this,e,t);n.handler=si(r,n.value)}}else delete this._table._ssFilters[e][t],this._table.removeCellCss(e,t,"ss_filter"),this._table.removeCellCss(e,t,"ss_filter_active");this.callEvent("onAction",["filter",{row:e,column:t,value:o,newValue:n}])},getCellFilter:function zs(e,t){return(this._table._ssFilters[e]||{})[t]||null},removeFilters:function Rs(){for(var t=this,e=Qn(this._table),n=0;n<e.length;n++){var i=e[n],o=i.row,r=i.column;this.setCellFilter(o,r,null)}this._table._ssFilters={},this._table.data.filter(function(e){return t.isRowVisible(e.id)})},filterSpreadSheet:function Is(){this._table.data.silent(function(){var a=this,l=Qn(this._table);this._table.data.filter(function(e){return a.isRowVisible(e.id)});for(var e=function(e){var t=l[e],n=t.row,i=t.column,o=t.handler,r=!1;a._table.data.filter(function(e){return!!(r||1*e.id<=1*n)||(e[i]||""===e[i]?"function"!=typeof o||o(String(e[i])):r=!0)},1,!0)},t=0;t<l.length;t++)e(t)},this),this._table.callEvent("onAfterFilter",[]),this._table.refresh()}});var Nr=Object.freeze({confirm:function As(e){return webix.confirm({css:"webix_ssheet_confirm",text:e.text,ok:webix.i18n.spreadsheet.labels.ok,cancel:webix.i18n.spreadsheet.labels.cancel})},alert:function Fs(e){return webix.alert({css:"webix_ssheet_alert",text:e.text,ok:webix.i18n.spreadsheet.labels.ok})}});var Ur=Object.freeze({resetUndo:function Os(){Di(this)},groupUndo:function Ms(e){m.set(e,this)},ignoreUndo:function Ds(e){Ui(e,this)}});var Tr=Object.freeze({setFormat:function Ns(e,t,n){ge(this,e,t,n)},changeDecimals:function Us(e,t,n){ke(this,n,e,t)}});var jr=[{id:"file",submenu:[{id:"sheet",submenu:[{id:"new-sheet"},{id:"copy-sheet"},{id:"remove-sheet"}]},{id:"excel-import"},{id:"excel-export"}]},{id:"edit",submenu:[{id:"add-range"},{id:"add-dropdown"},{id:"add-link"},{id:"lock-cell"},{id:"conditional-format"},{id:"clear",submenu:[{id:"clear-value"},{id:"clear-style"},{id:"clear-conditional-formats"},{id:"clear-dropdown-editors"},{id:"clear-comments"},{$template:"Separator"},{id:"clear-all"}]}]},{id:"insert",submenu:[{id:"image",submenu:[{id:"add-image-cell"},{id:"add-image-top"}]},{id:"graph",submenu:[{id:"add-sparkline"},{id:"add-chart"}]},{id:"add-comment"}]},{id:"data",submenu:[{id:"sort",submenu:[{id:"sort-asc"},{id:"sort-desc"}]},{id:"create-filter"}]},{id:"view",submenu:[{id:"columns",submenu:[{id:"insert-column"},{id:"delete-column"},{id:"show-column"},{id:"hide-column"},{id:"resize-column"}]},{id:"rows",submenu:[{id:"insert-row"},{id:"delete-row"},{id:"show-row"},{id:"hide-row"},{id:"resize-row"}]},{$template:"Separator"},{id:"freeze-columns"},{id:"freeze-rows"},{id:"hide-gridlines"},{id:"hide-headers"}]}],Br={getMenuData:function(e,t){var n;for(n=0;n<e.length;n++)"string"==typeof e[n]&&(e[n]={id:e[n]}),"resize-column"!=e[n].id&&"resize-row"!=e[n].id||t.resizeCell?(e[n].value||(e[n].value=webix.i18n.spreadsheet.menus[e[n].id]),e[n].submenu&&(e[n].submenu=this.getMenuData(e[n].submenu,t))):(e.splice(n,1),n--);return e}};function Hr(t){t.attachEvent("onComponentInit",function(){return function e(t){t.$$("menu")&&t.$$("menu").attachEvent("onMenuItemClick",function(e){return function n(e,t){e.callEvent("onMenuItemClick",[t])&&(Pr[t]?Pr[t](e):e.callEvent("onCommand",[{id:t}]))}(t,e)})}(t)});var e=t.config,n={view:"menu",borderless:!0,css:"webix_ssheet_menu",id:"menu",autowidth:!0,type:{height:40},data:Br.getMenuData(webix.isArray(e.menu)?e.menu:webix.copy(jr),t.config)};return t.callEvent("onViewInit",["menu",n]),n}var Pr={undo:function(e){e.undo()},redo:function(e){e.redo()},"insert-column":function(e){e.insertColumn()},"delete-column":function(e){e.deleteColumn()},"show-column":function(e){e.hideColumn(null,!1)},"hide-column":function(e){e.hideColumn(null,!0)},"resize-column":function(e){e.callEvent("onCommand",[{id:"resize",group:"column",value:webix.i18n.spreadsheet.menus["resize-column"]}])},"insert-row":function(e){e.insertRow()},
"delete-row":function(e){e.deleteRow()},"show-row":function(e){e.hideRow(null,!1)},"hide-row":function(e){e.hideRow(null,!0)},"resize-row":function(e){e.callEvent("onCommand",[{id:"resize",group:"row",value:webix.i18n.spreadsheet.menus["resize-row"]}])},"freeze-columns":function(e){e.freezeColumns()},"hide-gridlines":function(e){e.hideGridlines("toggle")},"hide-headers":function(e){e.hideHeaders("toggle")}};function Lr(e){return webix.i18n.spreadsheet.labels[e]||e}var Wr={name:"spreadsheet",_base_index:{count:1},defaults:{spans:!0,rowCount:50,math:!0,columnCount:20,resizeCell:!0,sheetTabWidth:90,conditionStyle:function Ts(){return[{name:Lr("normal"),css:"webix_ssheet_condition_regular"},{name:Lr("neutral"),css:"webix_ssheet_condition_neutral"},{name:Lr("bad"),css:"webix_ssheet_condition_bad"},{name:Lr("good"),css:"webix_ssheet_condition_good"}]}()},$init:function(){var e=this;this.$index=this._base_index.count++,this.$view.className+=" webix_ssheet",this.$ready.unshift(this._sub_init),this.$ready.unshift(he),this.$ready.push(this._set_handlers),this._destroy_with_me=[],this.attachEvent("onDestruct",function(){e._destroy_with_me.forEach(function(e){return e.destructor()})})},$skin:function(){!function t(){var e="material"==webix.skin.$name||"mini"==webix.skin.$name;N.width=webix.skin.$active.inputHeight+2,N.titleHeight="mini"==webix.skin.$name?18:20,U["font-family"]=e?"'Roboto', sans-serif":"'PT Sans', Tahoma"}()},_sub_init:function(){var e=this.config,t=[];!e.readonly&&e.menu&&t.push(Hr(this)),e.readonly||!1===e.toolbar||t.push(ht(this)),e.subbar&&t.push(e.subbar),!e.readonly&&e.liveEditor&&t.push(Pt(this));var n={editable:!e.readonly,clipboard:e.clipboard,liveEditor:e.liveEditor,clipboardDecimalDelimiter:e.clipboardDecimalDelimiter};e.rowHeight&&(n.rowHeight=e.rowHeight),e.columnWidth&&(n.columnWidth=e.columnWidth),t.push(St(this,n));var i=!0===e.bottombar?Gt(this):e.bottombar;i&&t.push(i),qt(this),e.readonly||function o(r){r.attachEvent("onComponentInit",function(){return r.$$("context").attachTo(r._table.$view)});var e=webix.i18n.spreadsheet.menus,a={data:[{id:"clear",value:e.clear,submenu:nt()},{id:"lock-cell",value:e["lock-cell"]},{$template:"Separator"},{id:"sort",value:e.sort,submenu:[{id:"sort-asc",value:e["sort-asc"]},{id:"sort-desc",value:e["sort-desc"]}]},{id:"create-filter",value:e["create-filter"]},{$template:"Separator"},{id:"add-range",value:e["add-range"]},{id:"add-link",value:e["add-link"]},{id:"add-comment",value:e["add-comment"]}],column:et(r),row:tt(r)},t={view:"contextmenu",id:"context",padding:0,submenuConfig:{padding:0,on:{onBeforeShow:function(){this.sizeToContent()}}},data:[],on:{onMenuItemClick:function(e){r.callEvent("onCommand",[this.getMenuItem(e)])},onBeforeShow:function(e){var t,n;if(webix.callEvent("onClick",[]),!(n=r._table.locate(e)))return!1;if(t="",n.header&&"rowId"!==n.column?(gt(n.column,r)||mt(n.column,r),t="column"):n.row&&(t="rowId"===n.column?(pt(n.row,r)||ft(n.row,r),"row"):(wt(n.row,n.column,r)||r._table.addSelectArea(n,n),"data")),t){var i=function o(e,t,n){var i={area:t,data:n};return e.callEvent("onContextMenuConfig",[i])?i.data:null}(r,t,a[t]);if(i)return this.clearAll(),this.parse(i),this.sizeToContent(),webix.html.preventEvent(e),!0}return!1}}};r.callEvent("onViewInit",["context",t]);var n=r.ui(t);r._destroy_with_me.push(n)}(this),function a(e){for(var r={},t=e.config.readonly?[Ei]:Ai,n=0;n<t.length;n++)r[t[n].action]=new t[n].DialogBox(e);e.attachEvent("onCommand",function(e){var t=r[e.id];t&&t.open(e)}),e.attachEvent("onAction",function(e,t){if("add-view"==e&&!t.newValue)for(var n=["add-chart","add-image-top"],i=0;i<n.length;i++){var o=r[n[i]];if(o&&o.viewid==t.row){o.close();break}}})}(this),this.rows_setter(t)},_set_handlers:function(){var e=this;this._table||(this._table=this.$$("cells"),function i(e){for(var t=[Li,lo,re,bo,je,Fo,No,Bi,$t,Ro,jn,Zn,Ao,je,xo,qi,wo,Yo,jt,Go,hi,Xo,nr,ar,sr,Ko,b,Bo,To,br],n=0;n<t.length;n++)t[n].init&&t[n].init(e)}(this),Et(this,this.config.columnCount,this.config.rowCount),Vt(this),this._table.attachEvent("onAfterAreaAdd",function(){
return e.callEvent("onAfterSelect",[e.getSelectedId(!0)])}),this.callEvent("onComponentInit",[]))},$onLoad:function(e,t){this._set_handlers(),e.excel?e=this._parseExcel(e,t):e.data||"string"!=typeof e||!t.cell&&!t.row||(e=this._parseCsv(e,t)),function n(e,t){!(t=webix.isArray(t)?{sheets:t}:t).excel&&t.sheets||!e._activeSheet?tn(e,t):ln(e,rn(e,e._activeSheet).content=t)}(this,e)}};!function js(e){for(var t=[Cr,$r,Er,Vr,zr,Rr,Ir,Ar,Fr,Or,Mr,Dr,Nr,Ur,Tr],n=0;n<t.length;n++)webix.extend(e,t[n])}(Wr),webix.protoUI(Wr,webix.AtomDataLoader,webix.IdSpace,webix.ui.layout)});
//# sourceMappingURL=spreadsheet.min.js.map
