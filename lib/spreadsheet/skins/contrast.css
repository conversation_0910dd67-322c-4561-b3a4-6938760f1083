@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  src: local('Roboto'), local('Roboto-Regular'), url(https://fonts.gstatic.com/s/roboto/v20/KFOmCnqEu92Fr1Me5Q.ttf) format('truetype');
}
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 500;
  src: local('Roboto Medium'), local('Roboto-Medium'), url(https://fonts.gstatic.com/s/roboto/v20/KFOlCnqEu92Fr1MmEU9vAw.ttf) format('truetype');
}
.webix_el_button.webix_ssheet_button button {
  font-weight: 400;
}
.webix_ssheet_colorboard .webix_color_selector {
  height: 22px !important;
}
.webix_ssheet_colorboard .webix_color_row > div {
  height: 22px !important;
}
.webix_ssheet_toolbar .wxi-menu-down:before,
.webix_ssheet_dialog .wxi-menu-down:before,
.webix_ssheet_color .wxi-menu-down:before,
.webix_ssheet_bottom_toolbar .wxi-menu-down:before {
  font-size: 14px;
  content: "\F001";
  margin-left: 3px;
}
.webix_ssheet_button_icon {
  -webkit-filter: brightness(0) invert(1);
  filter: brightness(0) invert(1);
}
.webix_ssheet_form .webix_el_text input:focus,
.webix_ssheet_dialog .webix_el_text input:focus,
.webix_ssheet_form .webix_el_combo input:focus,
.webix_ssheet_dialog .webix_el_combo input:focus {
  border-color: #cd00cc;
}
.webix_ssheet_dialog_table .webix_icon {
  color: #e2e2a7;
}
.lockStyle {
  bottom: 4px;
  right: 2px;
  font-size: 14px;
}
.viewMenu {
  font-size: 20px;
  border-radius: 3px;
  padding: 4px 2px;
  color: #e2e2a7;
}
@font-face {
  font-family: "Spreadsheet Icons";
  src: url("./../fonts/ssheet-webfont.eot");
  src: url("./../fonts/ssheet-webfont.eot?#iefix") format("embedded-opentype"), url("./../fonts/ssheet-webfont.woff") format("woff"), url("./../fonts/ssheet-webfont.woff2") format("woff2"), url("./../fonts/ssheet-webfont.ttf") format("truetype"), url("./../fonts/ssheet-webfont.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}
.webix_ssheet_icon {
  color: #fff;
  width: 24px;
  display: inline-block;
  vertical-align: middle;
  font-family: "Spreadsheet Icons";
  font-size: 20px;
  font-weight: normal;
  font-style: normal;
  text-rendering: auto;
  text-transform: none;
  line-height: inherit;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.ssi-add-comment:before {
  content: '\f001';
}
.ssi-add-dropdown:before {
  content: '\f002';
}
.ssi-add-image:before {
  content: '\f003';
}
.ssi-add-link:before {
  content: '\f004';
}
.ssi-add-range:before {
  content: '\f005';
}
.ssi-add-sparkline:before {
  content: '\f006';
}
.ssi-background:before {
  content: '\f007';
}
.ssi-borders-all:before {
  content: '\f008';
}
.ssi-borders-bottom:before {
  content: '\f009';
}
.ssi-borders-left:before {
  content: '\f00a';
}
.ssi-borders-no:before {
  content: '\f00b';
}
.ssi-borders-outer:before {
  content: '\f00c';
}
.ssi-borders-right:before {
  content: '\f00d';
}
.ssi-borders-top-bottom:before {
  content: '\f00e';
}
.ssi-borders-top:before {
  content: '\f00f';
}
.ssi-bottom:before {
  content: '\f010';
}
.ssi-center:before {
  content: '\f011';
}
.ssi-clear-styles:before {
  content: '\f012';
}
.ssi-close:before {
  content: '\f013';
}
.ssi-color:before {
  content: '\f014';
}
.ssi-column:before {
  content: '\f015';
}
.ssi-conditional-format:before {
  content: '\f016';
}
.ssi-create-filter:before {
  content: '\f017';
}
.ssi-decrease-decimals:before {
  content: '\f018';
}
.ssi-excel-export:before {
  content: '\f019';
}
.ssi-excel-import:before {
  content: '\f01a';
}
.ssi-format:before {
  content: '\f01b';
}
.ssi-freeze-columns:before {
  content: '\f01c';
}
.ssi-freeze-rows:before {
  content: '\f01d';
}
.ssi-hide-gridlines:before {
  content: '\f01e';
}
.ssi-hide-headers:before {
  content: '\f01f';
}
.ssi-increase-decimals:before {
  content: '\f020';
}
.ssi-left:before {
  content: '\f021';
}
.ssi-lock-cell-fill:before {
  content: '\f022';
}
.ssi-lock-cell:before {
  content: '\f023';
}
.ssi-middle:before {
  content: '\f024';
}
.ssi-print:before {
  content: '\f025';
}
.ssi-redo:before {
  content: '\f026';
}
.ssi-right:before {
  content: '\f027';
}
.ssi-row:before {
  content: '\f028';
}
.ssi-sheet:before {
  content: '\f029';
}
.ssi-sort-asc:before {
  content: '\f02a';
}
.ssi-sort-desc:before {
  content: '\f02b';
}
.ssi-span:before {
  content: '\f02c';
}
.ssi-top:before {
  content: '\f02d';
}
.ssi-undo:before {
  content: '\f02e';
}
.ssi-wrap:before {
  content: '\f02f';
}
.webix_ssheet_cp_color {
  height: 4px;
  width: 20px;
  background: #e0e3e5;
}
.webix_ssheet_color .webix_icon {
  display: block;
  font-size: 13px;
  line-height: 20px;
}
.webix_ssheet_color .webix_input_icon.wxi-menu-down {
  background-color: transparent;
  color: #ffffff;
}
.webix_ssheet_colorboard .webix_color_selector {
  border: 1px solid #444;
  height: 24px !important;
  box-shadow: inset 0px 0px 1px 1px #fff;
}
.webix_ssheet_colorboard .webix_color_row > div {
  height: 24px !important;
  padding: 1px;
  box-sizing: border-box;
}
.webix_ssheet_colorboard .webix_color_row:nth-child(1),
.webix_ssheet_colorboard .webix_color_row:nth-child(2) {
  margin-bottom: 5px;
}
.webix_toolbar.webix_ssheet_toolbar {
  background: #575757;
  border-top-color: #6e6e6e;
}
.webix_ssheet_toolbar .webix_button,
.webix_ssheet_toolbar .webix_inp_static {
  background: #575757 !important;
  color: #fff;
}
.webix_ssheet_toolbar .webix_button:hover,
.webix_ssheet_toolbar .webix_inp_static:hover {
  background: #4a4a4a !important;
}
.webix_ssheet .webix_ssheet_toolbar .webix_button,
.webix_ssheet .webix_ssheet_toolbar .webix_img_btn {
  color: #fff;
  font-size: 13px;
  border-color: #6e6e6e;
  border-radius: 3px;
}
.webix_ssheet .webix_ssheet_toolbar .webix_el_label {
  color: #fff;
}
.webix_ssheet .webix_ssheet_toolbar .webix_icon_btn {
  color: #ffffff;
}
.webix_ssheet .webix_ssheet_toolbar .webix_button:hover,
.webix_ssheet .webix_ssheet_toolbar .webix_button:focus,
.webix_ssheet .webix_ssheet_toolbar .webix_pressed .webix_button {
  border-color: #6e6e6e;
  color: #fff;
}
.webix_ssheet .webix_ssheet_toolbar .webix_icon {
  color: #ffffff;
}
.webix_ssheet_toolbar .webix_selected {
  background-color: #4a4a4a !important;
}
.webix_ssheet_toolbar .webix_segment_0,
.webix_ssheet_toolbar .webix_segment_1,
.webix_ssheet_toolbar .webix_segment_N {
  border-color: #6e6e6e !important;
}
.webix_ssheet_toolbar .webix_segment_0 {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.webix_ssheet_toolbar .webix_segment_N {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
.webix_ssheet .webix_el_toggle .webix_el_box.webix_pressed .webix_button {
  background-color: #4a4a4a;
}
.webix_ssheet_subbar_title {
  font-size: 13px;
  color: #fff;
  text-align: center;
  background: transparent;
}
.webix_ssheet_subbar_title .webix_template {
  padding: 3px 5px;
}
.webix_toolbar.webix_ssheet_toolbar .webix_inp_static,
.webix_ssheet_color .webix_inp_static {
  background: #575757;
  border-color: #6e6e6e !important;
  border-radius: 3px;
  text-overflow: initial;
}
.webix_toolbar.webix_ssheet_toolbar .webix_button {
  border: 1px solid #6e6e6e;
}
.webix_toolbar.webix_ssheet_toolbar .webix_el_box:hover .webix_inp_static,
.webix_toolbar.webix_ssheet_toolbar .webix_inp_static:focus,
.webix_ssheet_color .webix_el_box:hover .webix_inp_static,
.webix_ssheet .webix_ssheet_toolbar .webix_img_btn:hover,
.webix_ssheet .webix_ssheet_toolbar .webix_img_btn:focus {
  border-color: #6e6e6e !important;
  background-color: rgba(0, 0, 0, 0.05);
}
.webix_ssheet_toolbar .wxi-menu-down {
  font-size: 15px;
  margin-top: 2px;
  color: #ffffff !important;
  background-color: transparent !important;
}
.webix_ssheet_color .webix_inp_static {
  line-height: 0px !important;
}
.webix_ssheet_color .webix_inp_static .webix_icon {
  line-height: 22px;
}
.webix_ssheet_bold .webix_button {
  font-weight: 500;
}
.webix_ssheet_italic .webix_button {
  font-style: italic;
}
.webix_ssheet_underline .webix_button {
  text-decoration: underline;
}
.webix_ssheet .webix_ssheet_toolbar .webix_ssheet_bold .webix_button,
.webix_ssheet .webix_ssheet_toolbar .webix_ssheet_italic .webix_button,
.webix_ssheet .webix_ssheet_toolbar .webix_ssheet_underline .webix_button {
  font-size: 15px;
}
.webix_ssheet_align .webix_icon {
  height: 9px;
  line-height: 9px;
  overflow: hidden;
}
.webix_ssheet_align .webix_segment_0 .webix_icon {
  vertical-align: top;
}
.webix_ssheet_align .webix_segment_N .webix_icon {
  vertical-align: bottom;
}
.webix_ssheet_toolbar_spacer {
  background: #6e6e6e;
}
.webix_ssheet_redo .webix_icon {
  transform: scaleX(-1);
  /* Standard */
  filter: FlipH;
  /* IE 6/7/8 */
}
.webix_ssheet_wrap .webix_icon {
  transform: scale(-1);
  /* Standard */
  filter: Flip;
  /* IE 6/7/8 */
}
.webix_ssheet_button_icon {
  width: 18px;
  margin-right: 4px;
}
.webix_ssheet_button .webix_ssheet_button_icon {
  margin-right: 4px;
}
.webix_el_richselect .webix_ssheet_button_icon,
.webix_ssheet_button_menu .webix_ssheet_button_icon {
  margin-right: 0;
  height: 21px;
}
.webix_el_richselect .webix_ssheet_button_icon {
  margin-top: -10px;
}
.webix_el_richselect .webix_ssheet_color_button_icon {
  display: block;
  width: 18px;
  height: 10px;
  margin-top: 12px;
}
.webix_ssheet_borders_list .webix_dataview_item {
  border: none;
}
.webix_ssheet_dataview .webix_selected {
  background-color: #4a4a4a !important;
}
.webix_ssheet_dataview .webix_dataview_item {
  border: 0;
}
.webix_ssheet_right {
  float: right;
  color: #ffffff;
}
.webix_ssheet_suggest .webix_list .webix_selected {
  background-color: #4a4a4a;
  color: #ffffff;
}
.webix_ssheet_suggest .webix_list_item {
  border: none;
}
.webix_ssheet_suggest .webix_list_item:hover,
.webix_ssheet_dataview .webix_dataview_item:hover,
.webix_ssheet_dataview .webix_dataview_item:active {
  background-color: #545454;
}
.webix_ssheet .webix_ssheet_toolbar .webix_button .webix_img_btn_text {
  color: #fff;
  font-size: 13px;
}
.ssheet_button_icon_top .webix_ssheet_button_icon {
  background-position: center center;
  background-size: 22px 22px;
  background-repeat: no-repeat;
  height: 22px;
  margin: 8px 2px 0 0;
}
.webix_toolbar.webix_ssheet_toolbar .ssheet_button_icon_top_arrow {
  margin-top: 0;
  position: relative;
  bottom: 4px;
}
.webix_ssheet_button button {
  padding-left: 6px;
  text-align: left;
}
.webix_ssheet_button_measure {
  font-size: 13px;
  white-space: nowrap;
  padding: 0 5px 0 34px;
}
.webix_ssheet_dataview .webix_dataview_item {
  padding: 8px;
  line-height: 20px;
}
.ssheet_button_icon_top_text {
  font-weight: normal;
}
.webix_ssheet_colorpicker .webix_input_icon {
  width: 20px;
  height: 20px;
}
.webix_ssheet_dialog .webix_win_head {
  border-bottom-color: transparent;
}
.webix_ssheet_dialog .webix_win_head button {
  border: none;
  background: transparent;
}
.webix_ssheet_dialog .webix_win_head .webix_el_label > div {
  padding-left: 8px;
  font-size: 18px;
}
.cancel {
  border-color: transparent;
  background: transparent;
  color: #ffffff;
}
.cancelHover {
  background: #191919;
  border-color: #191919;
}
.webix_ssheet_form .webix_win_head > .webix_view > .webix_template,
.webix_ssheet_dialog .webix_win_head > .webix_view > .webix_template {
  box-shadow: none;
  text-align: left;
  padding: 0 0 0 19px;
}
.webix_ssheet_form .webix_el_combo input,
.webix_ssheet_dialog .webix_el_combo input,
.webix_ssheet_form .webix_el_text input,
.webix_ssheet_dialog .webix_el_text input,
.webix_ssheet_form .webix_inp_static,
.webix_ssheet_dialog .webix_inp_static,
.webix_ssheet_form .webix_inp_static:focus,
.webix_ssheet_dialog .webix_inp_static:focus {
  border-width: 0 0 1px 0;
  box-shadow: none;
  border-radius: 0;
  background-color: transparent;
}
.webix_ssheet_form span.webix_input_icon,
.webix_ssheet_dialog span.webix_input_icon {
  background: transparent;
  color: #ffffff;
}
.webix_ssheet_form .webix_button,
.webix_ssheet_dialog .webix_button {
  border-radius: 3px;
}
.webix_ssheet_form .ssheet_cancel_button button,
.webix_ssheet_dialog .ssheet_cancel_button button {
  border-color: transparent;
  background: transparent;
  color: #ffffff;
}
.webix_ssheet_form .ssheet_cancel_button button:hover,
.webix_ssheet_dialog .ssheet_cancel_button button:hover,
.webix_ssheet_form .ssheet_cancel_button button:focus,
.webix_ssheet_dialog .ssheet_cancel_button button:focus {
  background: #191919;
  border-color: #191919;
}
.webix_ssheet_form .webix_selected,
.webix_ssheet_dialog .webix_selected {
  color: #ffffff;
  background: #4a4a4a;
}
.webix_ssheet_dialog_table .webix_ss_header td {
  border-right-color: #191919;
}
.webix_ssheet_dialog_table .webix_ss_vscroll_header {
  border-left-color: #191919;
}
.webix_ssheet_dialog_table .webix_hcell {
  text-align: left;
}
.webix_ssheet_dialog_table .webix_cell {
  border-right-color: transparent;
}
.webix_ssheet_dialog_table div.webix_row_select {
  color: #ffffff;
  background: #4a4a4a;
}
.webix_ssheet_hide_icon .webix_icon {
  height: 23px;
}
.webix_ssheet_cformats .webix_inp_static,
.webix_ssheet_cformats input {
  padding-left: 12px;
}
.webix_ssheet_cformat {
  height: 100%;
  color: #666666;
  padding-left: 12px;
  background: #fff;
}
.webix_ssheet_cformat_list .webix_list_item {
  border-bottom: 0;
  padding: 0;
}
.webix_ssheet_cformat_list .webix_list_item.webix_selected {
  padding: 0;
  background: transparent;
  border-bottom: 0;
}
.webix_ssheet_cformats .webix_ssheet_cformat_select .webix_inp_static {
  padding-left: 0;
}
.webix_ssheet_cformats .webix_ssheet_cformat_select .webix_inp_static .webix_placeholder {
  padding-left: 12px;
}
.webix_ssheet_cformats .webix_ssheet_cformat_select .webix_ssheet_conditionFormat {
  border: 0;
}
.webix_ssheet_cformat_clear .webix_icon {
  font-size: 15px;
}
.webix_ssheet_format_type .webix_list_item {
  border: none;
}
.webix_ssheet_format_negative .webix_template,
.webix_ssheet_format_date .webix_template {
  padding: 0 0 0 2px;
}
.webix_ssheet_format_negative .webix_list_item,
.webix_ssheet_format_date .webix_list_item {
  border: none;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.webix_ssheet_format_type .webix_list_item:hover,
.webix_ssheet_format_negative .webix_list_item:hover,
.webix_ssheet_format_date .webix_list_item:hover {
  background: #545454;
}
.webix_ssheet_format_preview {
  text-align: right;
  font-size: 20px;
}
.webix_ssheet_format_preview div {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.webix_ssheet_dialog_table .webix_icon {
  cursor: pointer;
  vertical-align: middle;
  font-size: 15px;
}
.webix_ssheet_preview .webix_template {
  padding: 0;
  text-align: center;
  line-height: 90px;
  font-size: 20px;
  font-weight: bold;
  color: #bbb;
}
.webix_ssheet_confirm .webix_popup_text,
.webix_ssheet_alert .webix_popup_text {
  min-height: 40px;
}
.webix_ssheet_confirm .webix_popup_button div,
.webix_ssheet_alert .webix_popup_button div {
  border-radius: 3px;
}
.webix_ssheet_confirm .webix_popup_controls,
.webix_ssheet_alert .webix_popup_controls {
  padding-bottom: 20px;
}
.webix_ssheet_confirm .webix_popup_button {
  border: none;
}
.webix_ssheet_confirm .webix_popup_button:first-child {
  border-color: transparent;
  background: transparent;
  color: #ffffff;
}
.webix_ssheet_confirm .webix_popup_button:first-child:hover {
  background: #191919;
  border-color: #191919;
}
.ss_sep_line {
  height: 1px;
  border-top: 1px solid #6e6e6e;
}
.webix_window.ssheet_filter .webix_list_item div {
  display: inline-block;
}
.webix_ssheet_menu {
  background: #191919;
}
.webix_ssheet_menu .webix_list_item {
  line-height: 36px;
  background: #191919;
  color: #ffffff;
  border: none;
  border-bottom: 1px solid #6e6e6e;
}
.webix_ssheet_menu .webix_list_item:first-child {
  border-radius: 0;
}
.webix_ssheet_menu .webix_list_item:last-child {
  border-radius: 0;
}
.webix_ssheet_menu .webix_list_item:hover,
.webix_ssheet_menu .webix_list_item:active,
.webix_ssheet_menu .webix_list_item:focus {
  background: #545454;
  box-shadow: 0 0 0 3px #191919 inset;
}
.webix_ssheet_bottom_toolbar.webix_layout_toolbar {
  background: #4a4a4a;
}
.webix_ssheet_bottom_toolbar.webix_layout_toolbar .webix_button {
  color: #fff;
  border-color: transparent;
  border-radius: 3px;
  background: transparent;
}
.webix_ssheet_bottom_toolbar.webix_layout_toolbar .webix_button:hover,
.webix_ssheet_bottom_toolbar.webix_layout_toolbar .webix_button:focus,
.webix_ssheet_bottom_toolbar.webix_layout_toolbar .webix_button:active {
  background: #404040;
  color: #fff;
}
.webix_ssheet_icon_add_sheet:before {
  content: "+";
  font-size: 22px;
}
.ssheet_list_sheets {
  display: table;
  background: #4a4a4a;
}
.ssheet_list_sheets .webix_input_icon {
  font-size: 15px;
  height: 29px;
  padding-top: 6px;
  text-align: center;
}
.ssheet_list_sheets .webix_list_item {
  font-size: 14px;
  border-left: none;
  border-color: #6e6e6e;
  border-top: 1px solid #6e6e6e;
  border-bottom: 1px solid #6e6e6e;
  background: #4a4a4a;
  color: #fff;
  padding-right: 3px;
}
.ssheet_list_sheets .webix_list_item:first-child {
  border-left: 1px solid #6e6e6e;
}
.ssheet_list_sheets .webix_list_item.webix_selected {
  border-top-color: #191919;
  background: #191919;
  color: #fff;
  border-right-color: #6e6e6e;
  border-bottom-color: #616161;
}
.ssheet_list_sheets .webix_list_item:hover {
  background: #404040;
}
.ssheet_list_sheets .webix_list_item.webix_selected:hover {
  background: #0f0f0f;
}
.ssheet_list_sheets .webix_list_item div:first-child {
  float: left;
  line-height: 29px;
  width: calc(100% - 20px);
  overflow: hidden;
  text-overflow: ellipsis;
}
.webix_drag_zone .ssheet_order_sheets {
  background: #191919;
  color: #fff;
  box-shadow: none;
}
.webix_sheet_hidden {
  visibility: hidden;
}
.webix_ssheet_bottom_toolbar .webix_el_button.webix_disabled_view .webix_el_htmlbutton {
  background: #4a4a4a;
  border-color: #4a4a4a;
  color: #fff;
  width: 30px;
  height: 28px;
  padding: 0;
  border: 0;
}
.webix_ssheet_bottom_toolbar .webix_el_button.webix_disabled_view .webix_el_htmlbutton:hover,
.webix_ssheet_bottom_toolbar .webix_el_button.webix_disabled_view .webix_el_htmlbutton:focus,
.webix_ssheet_bottom_toolbar .webix_el_button.webix_disabled_view .webix_el_htmlbutton:active {
  background: #404040;
}
.webix_ssheet_bottom_toolbar .webix_el_button.webix_disabled_view .webix_icon {
  color: #ffffff !important;
}
.webix_ssheet_cimage {
  max-width: 100%;
  max-height: 100%;
  width: auto;
}
.webix_ssheet_condition_neutral {
  background: #ffeb9c !important;
  color: #ad6524 !important;
}
.webix_ssheet_condition_bad {
  background: #ffc7ce !important;
  color: #9c0006 !important;
}
.webix_ssheet_condition_good {
  background: #c6efce !important;
  color: #006100 !important;
}
.webix_ssheet_condition_normal {
  background: white !important;
  color: black !important;
}
.webix_column .webix_lock {
  position: relative;
}
.webix_lock:before {
  display: block;
  font-family: "Spreadsheet Icons";
  content: '\f022';
  width: 11px;
  height: 11px;
  position: absolute;
  color: orange;
  font-weight: normal;
  font-style: normal;
  bottom: 4px;
  right: 2px;
  font-size: 14px;
}
.webix_ssheet_format_green {
  color: green !important;
}
.webix_ssheet_format_red {
  color: red !important;
}
.webix_ssheet_format_blue {
  color: blue !important;
}
.webix_ssheet_format_orange {
  color: darkorange !important;
}
.webix_ssheet_format_black {
  color: black !important;
}
.webix_ssheet_format_magenta {
  color: magenta !important;
}
.webix_ssheet_format_violet {
  color: darkviolet !important;
}
/* print */
.webix_ssheet_print {
  display: none;
}
@media print {
  .webix_ssheet_print {
    display: block !important;
    visibility: visible !important;
  }
  .webix_ssheet_print,
  .webix_ssheet_print * {
    visibility: visible !important;
  }
  .webix_ssheet_sheetname {
    margin-left: 3px;
    margin-bottom: 10px;
    font-weight: 200;
  }
}
.ssheet_commented_cell {
  position: relative;
}
.ssheet_commented_sign:before {
  content: "";
  position: absolute;
  right: 0;
  top: 0;
  border-top: 8px solid #ffffff;
  border-left: 8px solid transparent;
}
.ssheet_comment_view div {
  white-space: pre-wrap;
}
.webix_measure_size.webix_ssheet_formula {
  letter-spacing: normal;
}
.webix_ssheet_highlight_background_1 {
  background: rgba(85, 205, 151, 0.4) !important;
}
.webix_ssheet_highlight_color_1 {
  color: #55CD97;
}
.webix_ssheet_highlight_background_2 {
  background: rgba(255, 92, 76, 0.4) !important;
}
.webix_ssheet_highlight_color_2 {
  color: #FF5C4C;
}
.webix_ssheet_highlight_background_3 {
  background: rgba(148, 161, 179, 0.4) !important;
}
.webix_ssheet_highlight_color_3 {
  color: #94A1B3;
}
.webix_ssheet_highlight_background_4 {
  background: rgba(255, 165, 0, 0.4) !important;
}
.webix_ssheet_highlight_color_4 {
  color: orange;
}
.webix_ssheet_highlight_background_5 {
  background: rgba(123, 223, 242, 0.4) !important;
}
.webix_ssheet_highlight_color_5 {
  color: #7BDFF2;
}
.webix_ssheet_highlight_background_6 {
  background: rgba(242, 181, 212, 0.4) !important;
}
.webix_ssheet_highlight_color_6 {
  color: #F2B5D4;
}
.webix_ssheet_highlight_background_7 {
  background: rgba(144, 97, 191, 0.4) !important;
}
.webix_ssheet_highlight_color_7 {
  color: #9061BF;
}
.webix_ssheet_ui:focus {
  border-color: #cd00cc;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1) !important;
}
.webix_ssheet_view_menu {
  position: absolute;
  right: 2px;
  top: 2px;
  z-index: 2;
  cursor: pointer;
  background-color: rgba(57, 57, 57, 0.8);
  width: 24px;
  font-size: 20px;
  border-radius: 3px;
  padding: 4px 2px;
  color: #e2e2a7;
}
.webix_ssheet_bgimage .webix_template {
  padding: 0;
}
.webix_ssheet_bgimage .webix_template img {
  height: 100%;
  width: 100%;
}
.webix_ssheet_ui .webix_dtable .webix_cell {
  padding: 0 12px;
}
.webix_ssheet_ui .webix_dtable .webix_disabled {
  cursor: initial;
  background-color: transparent;
}
.sheet_column_0 div {
  text-align: center;
  background: #191919;
  color: #ffffff;
  border-color: #4d4d4d;
  position: relative;
}
.sheet_column_0 .webix_ssheet_hide_row:after {
  content: '';
  width: 100%;
  height: 2px;
  background: #555555;
  position: absolute;
  bottom: 0;
  left: 0;
}
.webix_ssheet_table .webix_cell {
  color: #ffffff;
  display: flex;
  align-items: center;
  line-height: normal !important;
  padding: 2px 0px;
}
.webix_ssheet_table .webix_cell div:first-child {
  margin: 0 12px;
  text-overflow: ellipsis;
  overflow: hidden;
}
.webix_ssheet_table .sheet_column_0 .webix_cell {
  padding: 0 12px;
}
.webix_hcell.webix_highlight,
.sheet_column_0 div.webix_highlight {
  background-color: #800080;
}
.webix_ssheet_table .webix_hcell {
  text-align: center;
}
.webix_borderless .webix_column:not(.sheet_column_0) .webix_cell,
.webix_borderless .webix_span_layer .webix_cell {
  border-color: transparent;
}
.webix_borderless .webix_last_topcell {
  border-bottom: 1px solid #d0d0d0;
}
.webix_ssheet_table .webix_dd_drag_column,
.webix_ssheet_table .webix_ss_header,
.webix_ssheet_table .webix_ss_header TD,
.webix_ssheet_table .webix_ss_vscroll_header {
  user-select: none;
  background: #191919;
  color: #ffffff;
}
.webix_ssheet_table .webix_ss_header td {
  border-right-color: #4d4d4d;
  border-bottom-color: #4d4d4d;
}
.webix_ssheet_table .webix_hcell {
  border: 2px solid transparent;
  padding: 0;
  font-weight: 400;
}
.webix_ssheet_table .webix_ssheet_hide_column {
  border-right-color: #555555;
}
.webix_ssheet_table .webix_ssheet_hide_row {
  border-bottom-color: #555555;
}
.webix_ssheet_table div.webix_ss_vscroll_header {
  border-color: #4d4d4d;
}
.webix_ssheet_table .webix_ss_header tr:last-child td {
  border-bottom: 1px solid #4d4d4d;
}
.webix_ssheet_suggest .webix_ssheet_options .webix_selected {
  background-color: transparent;
  color: #ffffff;
  border-bottom: none;
}
.webix_cell.ss_filter,
.webix_cell.ss_editor {
  position: relative;
}
.webix_cell.ss_filter div:first-child,
.webix_cell.ss_editor div:first-child {
  margin-right: 28px;
}
.webix_cell.webix_dtable_span.ss_filter,
.webix_cell.webix_dtable_span.ss_editor {
  position: absolute;
}
.ssheet_filter_sign,
.webix_cell.ss_editor:after {
  display: inline-block;
  font-family: "Webix Material Icons";
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 20px;
  height: 20px;
  width: 20px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translate(0, 0);
  position: absolute;
  right: 6px;
  top: 50%;
  margin-top: -10px;
}
.ssheet_filter_sign {
  cursor: pointer;
  color: #ffffff;
}
.ssheet_filter_sign:hover {
  color: #ffffff;
}
.ssheet_filter_sign:after {
  content: "\F045";
}
.webix_cell.ss_editor:after {
  content: "\F001";
  color: #ffffff;
}
.webix_cell.ss_filter_active .ssheet_filter_sign {
  color: #cd00cc;
}
.webix_ssheet_empty {
  font-style: italic;
}
.webix_ssheet_counter .webix_inp_counter_next,
.webix_ssheet_counter .webix_inp_counter_prev {
  background: none;
  border: 0px;
  border-radius: 5px;
  color: #cd00cc;
}
.webix_ssheet_counter .webix_inp_counter_next:hover,
.webix_ssheet_counter .webix_inp_counter_prev:hover {
  background: #545454;
}
.webix_ssheet_counter .webix_inp_counter_value {
  border-top-color: transparent;
  margin: 0 3px 0 3px;
  background-color: transparent;
}
.webix_el_counter input[type=text]:focus {
  border-color: transparent;
  box-shadow: none;
  border-bottom-color: #cd00cc;
}
a.docs {
  color: #cd00cc;
}
.webix_ssheet_table .webix_hs_left .webix_first .webix_hcell:before {
  cursor: pointer;
  content: "";
  display: block;
  border-top: 20px solid transparent;
  margin-left: 10px;
  border-right: 20px solid #ffffff;
}
