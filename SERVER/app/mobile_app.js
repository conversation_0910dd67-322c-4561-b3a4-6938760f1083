const sql = require('mssql');
const moment = require('moment');
const async = require("async");
const CryptoJS = require("crypto-js");
const path = require("path");
let bearer_token = "";
let origin_url = "";

let connection;

function REST_ROUTER(router, cn) {

    connection = cn;

    let self = this;
    self.handleRoutes(router);

}


REST_ROUTER.prototype.handleRoutes = function (router) {

    //LOGIN
    router.get("/get_app_pin", function (req, res) {
        origin_url = req.headers.referer;
        if (origin_url == "https://sapphire.mfs.sa.gov.au/" || origin_url == "http://localhost:63343/" || origin_url == "https://sapphire-uat.mfs.sa.gov.au/") {
            getUserPINcode(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_employee_info", function (req, res) {
        origin_url = req.headers.referer;
        if (origin_url == "https://sapphire.mfs.sa.gov.au/" || origin_url == "http://localhost:63343/" || origin_url == "https://sapphire-uat.mfs.sa.gov.au/") {
            getEmployeeValues(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_latest_version", function (req, res) {
        getLatestVersion(req, res);
    });

    //SCHEDULE
    router.get("/mobile_roster", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url == "https://sapphire.mfs.sa.gov.au/" || origin_url == "http://localhost:63343/" || origin_url == "https://sapphire-uat.mfs.sa.gov.au/")) {
            mobile_roster_load(req, res);
        } else {
            res.status(401).send();
        }
    });


};


function getLatestVersion(req, res) {

    const request = new sql.Request(connection);

    request.query("SELECT curr_app_version FROM global_settings", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function getEmployeeValues(req, res) {

    let pay_id = req.query.pay_id;
    const request = new sql.Request(connection);

    request.input('pay_id', sql.Int, pay_id);

    request.query("SELECT * FROM employees WHERE pay_id = @pay_id", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send({
                    data: result.recordset,
                    api_key: api_register[0].api_key
                });
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function getUserPINcode(req, res) {

    let pay_id = req.query.pay_id;

    const ps = new sql.PreparedStatement(connection);

    ps.input('pay_id', sql.Int);

    ps.prepare("SELECT pin_code FROM employees WHERE pay_id = @pay_id", (err, result) => {

        if (!err) {
            // Execute statement
            ps.execute({pay_id: pay_id}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    let pinCode = result.recordset[0].pin_code;

                    if (pinCode === null || pinCode == "" || pinCode == 0) {
                        res.status(200).send("invalid");
                    } else if (pinCode === undefined) {
                        res.status(200).send("not_exist");
                    } else {
                        let decryptedPIN = "";

                        try {
                            decryptStringNode(result.recordset[0].pin_code, function (result) {
                                decryptedPIN = result;
                            });
                        } catch (e){
                            console.log(moment().format("DD-MM-YY HH:mm") + " | Decrypt PIN (Mobile)- " + e + " - for Pay ID " + pay_id);
                            decryptedPIN = "";
                        }

                        result.recordset[0].pin_code = decryptedPIN;
                        res.status(200).send({
                            user_pin: decryptedPIN,
                            api_key: api_register[0].api_key
                        });
                    }

                } else {
                    ps.unprepare(err => {
                    });

                    console.log("Mobile-- " + moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send([]);
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log("Mobile-- " + moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }

    });
}


function mobile_roster_load(req, res) {

    let roster = req.query.roster_name; // 'Metro'
    let shift = req.query.shift_name;   // 'A Shift'
    let start_date = req.query.start_date;   // '26-11-2019' or Moment()
    let time_period = req.query.time_period;
    let date_string;
    let sql_query = "";
    let shift_periods;
    let columns = [];
    let periods = [];

    async.series([

        function (callback) {

            date_string = moment(start_date, "DD-MM-YYYY").format("YYYYMMDD");

            if (time_period == "8 Day") {
                sql_query = "SELECT TOP 8 shift_id, roster, shift, seq_date, date_string, seq_number, start_time, end_time, day_off, icon, colour, roster_id FROM roster_shift_days INNER JOIN shifts ON shift_id = shifts.id WHERE roster = @roster AND shift = @shift AND date_string >= @date_string ORDER BY date_string";
            } else {
                sql_query = "SELECT TOP 4 shift_id, roster, shift, seq_date, date_string, seq_number, start_time, end_time, day_off, icon, colour, roster_id FROM roster_shift_days INNER JOIN shifts ON shift_id = shifts.id WHERE roster = @roster AND shift = @shift AND date_string >= @date_string ORDER BY date_string";
            }

            callback();
        },
        function (callback) {

            // Find the current shift day and shift days details

            const ps = new sql.PreparedStatement(connection);

            // Inputs
            ps.input('roster', sql.VarChar(40));
            ps.input('shift', sql.VarChar(40));
            ps.input('date_string', sql.VarChar(8));


            // Prepare statement
            ps.prepare(sql_query, err => {

                if (!err) {
                    // Execute statement
                    ps.execute({roster: roster, shift: shift, date_string: date_string}, (err, result) => {

                        if (!err) {
                            // release the connection after queries are executed
                            ps.unprepare(err => {
                            });

                            if (result.recordset.length > 0) {

                                shift_periods = result.recordset;
                                callback();

                            } else {
                                shift_periods = undefined;
                                callback();
                            }
                        }
                    });
                }
            });
        },
        function (callback) {

            if (shift_periods != undefined) {

                if (roster !== "Port Pirie") {
                    columns.push({id: "names", header: "", width: 160, css: "column_staff_names"});
                } else {
                    columns.push({id: "names", header: "", width: 160, css: "column_staff_names_pp"});
                }

                columns.push({id: "pay_id", header: "", width: 5, css: "column_staff_names"});

                columns.push({id: "rank", header: "", width: 5, css: "column_staff_names"});

                columns.push({id: "skills", header: "", width: 5, css: "skill_codes"});

                columns.push({id: "covid", header: "", width: 5, css: "column_staff_names"});

                // Keep pushing periods for the duration
                for (let day = 0; day < shift_periods.length; day++) {

                    let text = moment(shift_periods[day].date_string, "YYYYMMDD").format("ddd</br>DD/MM");

                    // Add Column id, header label & Date
                    columns.push({
                        id: (day + 1).toString(),
                        header: {
                            text: text,
                            css: "schedule_header",
                            date: shift_periods[day].date_string
                        },
                        fillspace: 1
                    });

                    // Push period
                    periods.push(shift_periods[day]);

                }

                columns.push({id: "right_border", header: {text: "", css: "right_border"}, maxWidth: 15});

                callback();
            } else {
                callback();
            }
        }

    ], function () {
        res.status(200).send({
            shift_days: periods,
            columns: columns
        });
    });

}

function decryptStringNode(string, callback) {

        let salt = CryptoJS.enc.Hex.parse(string.slice(0, 32));
        let iv = CryptoJS.enc.Hex.parse(string.slice(32, 64))
        let encrypted = string.substring(64);

        let key = CryptoJS.PBKDF2("7b8765d5-457b-4ec0-8583-69b3a36d5989", salt, {
            keySize: 256 / 32,
            iterations: 100
        });

        let decrypted = CryptoJS.AES.decrypt(encrypted, key, {
            iv: iv,
            padding: CryptoJS.pad.Pkcs7,
            mode: CryptoJS.mode.CBC
        });

        callback(decrypted.toString(CryptoJS.enc.Utf8));

}

module.exports = REST_ROUTER;