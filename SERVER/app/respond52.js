const moment = require('moment');
const sql = require('mssql');
const async = require('async');
const CronJob = require('cron').CronJob;
const admin = require('firebase-admin');
const superagent = require('superagent');

let bearer_token = "";
let origin_url = "";
let live_site = false;
let connection;


let serviceAccount = require("../respond52-05a827de7643.json");
const path = require("path");

admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: "https://respond52.firebaseio.com"
});

// Day Shift Changeover
let job1 = new CronJob('0 6 * * *', function () {
    assignCrews();
}, null, true, 'Australia/Adelaide');
job1.start();

let job2 = new CronJob('0 7 * * *', function () {
    assignCrews();
}, null, true, 'Australia/Adelaide');
job2.start();

let job3 = new CronJob('0 8 * * *', function () {
    assignCrews();
}, null, true, 'Australia/Adelaide');
job3.start();

let job4 = new CronJob('0 9 * * *', function () {
    assignCrews();
    assignRegionalCommand();
}, null, true, 'Australia/Adelaide');
job4.start();


// Night Shift Changeover
let job5 = new CronJob('0 17 * * *', function () {
    assignCrews();
    assignRegionalCommand();
}, null, true, 'Australia/Adelaide');
job5.start();

let job6 = new CronJob('0 18 * * *', function () {
    assignCrews();
}, null, true, 'Australia/Adelaide');
job6.start();

let job7 = new CronJob('0 19 * * *', function () {
    assignCrews();
}, null, true, 'Australia/Adelaide');
job7.start();


// Update metro stats every 5th minute
let job8 = new CronJob('*/5 * * * *', function () {
    updateMetroStats()
}, null, true, 'Australia/Adelaide');
job8.start();


function REST_ROUTER(router, cn) {

    connection = cn;

    let self = this;
    self.handleRoutes(router);

    if (connection.config.database === "sapphire_live") {
        live_site = true;
    } else {
        live_site = false;
    }

    /*setTimeout(function () {
        assignCrews();
        updateMetroStats();
    }, 2000);*/
}

REST_ROUTER.prototype.handleRoutes = function (router) {

    router.get("/", function (req, res) {
        res.status(200).send("Sapphire Respond52 API");
    });

    router.get("/deployments", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            r52Deployments(req, res);
        } else {
            res.status(401).send();
        }

    });

    router.get("/get_locations_list", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getLocationsList(req, res);
        } else {
            res.status(401).send();
        }
    });

    //REPORTS
    router.get("/incident_report", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getIncidentReportData(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/incident_type_summary_report", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getIncidentTypeReportData(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/incident_location_summary_report", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getIncidentLocationReportData(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/incident_info_search_report", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getIncidentInfoSearchData(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/airs_din_data", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[3].api_key && origin_url === "samfs_airs") {
            getAIRSDINData(req, res);
        } else {
            res.status(401).send();
        }

    });

    router.get("/get_r52_stations", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sapphireGetStations(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_r52_shifts", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sapphireGetShifts(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/r52_update_station_shift", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sapphireChangeStationShift(req, res);
        } else {
            res.status(401).send();
        }
    });

};


function sapphireGetStations(req,res){

    const ps = new sql.PreparedStatement(connection);

    // Prepare statement
    ps.prepare("SELECT name, station_id FROM [locations] WHERE station_id > 20 ORDER BY name", err => {

        if (!err) {
            // Execute statement
            ps.execute({

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        res.status(200).send(result.recordset);
                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}

function sapphireChangeStationShift(req, res) {

    let user_id = req.query.user_id;
    let station_id = req.query.station_id;
    let shift = req.query.shift;

    if (user_id.length > 5) {

        let users = [];

        admin.firestore().collection("users").where("user_id", "==", user_id)
            .get()
            .then(function (querySnapshot) {

                querySnapshot.docs.map(function (doc) {
                    users.push({id: doc.id});
                });

                if (users.length > 0) {
                    let doc_id = users[0].id;

                    let docUser = admin.firestore().collection("users").doc(doc_id);
                    docUser.set({
                        station_id: station_id,
                        operating_station_id: station_id,
                        mobile_view_station_id: station_id,
                        shift: shift,
                        modified_date: moment().toDate()
                    }, {merge: true}).then(function () {
                        res.send('OK');
                    });


                } else {
                    res.send('Error: User Not Found');
                }
            });

    } else {
        res.send('Missing user_id');
    }
}

function sapphireGetShifts(req, res) {

    let station_id = req.query.station_id;
    if (station_id.length > 0) {
        let shifts = [];
        admin.firestore().collection("stations").where("station_id", "==", station_id)
            .get()
            .then(function (querySnapshot) {
                querySnapshot.docs.map(function (doc) {
                    let data = doc.data();
                    if (data.hasOwnProperty('shifts')) {
                        shifts = data.shifts;
                    } else {
                        shifts = [];
                    }
                });
                if (shifts.length > 0) {
                    res.send(shifts);
                } else {
                    res.send('Error: No Shifts Found');
                }
            });
    } else {
        res.send('Missing user_id');
    }
}


function getAIRSDINData(req, res) {

    let din_no = req.query.din_no;
    let din_date = req.query.din_date;
    let uid = "";

    const ps = new sql.PreparedStatement(connection);

    if (din_no.length === 1) {
        uid = din_date + "000" + din_no;
    } else if (din_no.length === 2) {
        uid = din_date + "00" + din_no;
    } else if (din_no.length === 3) {
        uid = din_date + "0" + din_no;
    }

    // Inputs
    ps.input('uid', sql.BigInt);

    // Prepare statement
    ps.prepare("SELECT r52.user_id, r52.position, emp.surname, emp.first_name FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE uid = @uid ORDER BY position", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                uid: uid

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}

function r52Deployments(req, res) {

    let station_id = req.query.station_id;
    let users = [];
    let vehicles = [];

    if (station_id) {

        async.series([
            function (callback) {

                // Get all logged in station crew deployments
                admin.firestore().collection("users").where("operating_station_id", "==", station_id).where("logged_in", "==", true).where("active", "==", true)
                    .get()
                    .then(function (querySnapshot) {

                        querySnapshot.forEach(function (doc) {

                            let data = doc.data();

                            if (data.deployed.station === true) {
                                let user = {
                                    first_name: data.given_name,
                                    surname: data.family_name,
                                    rank: data.rank,
                                    user_id: data.user_id,
                                    background_css: data.background_css
                                };

                                users.push(user);
                            }

                        });

                        callback();
                    });

            },
            function (callback) {

                // Get all logged in station crew deployments
                admin.firestore().collection("vehicles").where("station_id", "==", station_id).where("active", "==", true)
                    .get()
                    .then(function (querySnapshot) {

                        querySnapshot.forEach(function (doc) {

                            let data = doc.data();

                            let vehicle = {
                                callsign: data.callsign,
                                position1: {
                                    first_name: data.positions[1].user.given_name,
                                    surname: data.positions[1].user.family_name,
                                    rank: data.positions[1].user.rank,
                                    user_id: data.positions[1].user.user_id,
                                    background_css: data.positions[1].user.background_css
                                },
                                position2: {
                                    first_name: data.positions[2].user.given_name,
                                    surname: data.positions[2].user.family_name,
                                    rank: data.positions[2].user.rank,
                                    user_id: data.positions[2].user.user_id,
                                    background_css: data.positions[2].user.background_css
                                },
                                position3: {
                                    first_name: data.positions[3].user.given_name,
                                    surname: data.positions[3].user.family_name,
                                    rank: data.positions[3].user.rank,
                                    user_id: data.positions[3].user.user_id,
                                    background_css: data.positions[3].user.background_css
                                },
                                position4: {
                                    first_name: data.positions[4].user.given_name,
                                    surname: data.positions[4].user.family_name,
                                    rank: data.positions[4].user.rank,
                                    user_id: data.positions[4].user.user_id,
                                    background_css: data.positions[4].user.background_css
                                },
                                position5: {
                                    first_name: data.positions[5].user.given_name,
                                    surname: data.positions[5].user.family_name,
                                    rank: data.positions[5].user.rank,
                                    user_id: data.positions[5].user.user_id,
                                    background_css: data.positions[5].user.background_css
                                }
                            };

                            if (station_id == 38) {
                                vehicle.position6 = {
                                    first_name: data.positions[6].user.given_name,
                                    surname: data.positions[6].user.family_name,
                                    rank: data.positions[6].user.rank,
                                    user_id: data.positions[6].user.user_id,
                                    background_css: data.positions[6].user.background_css
                                };

                                vehicle.position7 = {
                                    first_name: data.positions[7].user.given_name,
                                    surname: data.positions[7].user.family_name,
                                    rank: data.positions[7].user.rank,
                                    user_id: data.positions[7].user.user_id,
                                    background_css: data.positions[7].user.background_css
                                }
                            }

                            vehicles.push(vehicle);

                        });

                        callback();
                    });


            }], function () {

            res.status(200).send({
                "vehicles": vehicles,
                "station": users
            });

        });

    }

}


function getLocationsList(req, res) {

    const request = new sql.Request(connection);

    request.query("SELECT station_id, name FROM locations WHERE station_id > 20 AND station_id < 50 AND station_id <> 38", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function updateMetroStats() {

    if (live_site === true && __dirname.includes("mfsroster/live")) {

        let idToken = '766adab1-d62f-45ed-8ee6-5252efc50328';

        let exportedIds = [];

        superagent
            .get('https://samfs.respond52.com/mobile/stats/metro_stats')
            .query({idToken: idToken})
            .end((err, res) => {

                if (err) {
                    console.log(moment().format("DD/MM/YYYY HH:mm") + ' - Metro Stats Error: ' + JSON.stringify(err));
                } else {

                    //console.log(moment().format("DD/MM/YYYY HH:mm") + ' - Updating Metro Stats');

                    if (res.body.length > 0) {
                        let rows = res.body;

                        async.eachSeries(rows, function (row, callback) {

                            const ps = new sql.PreparedStatement(connection);

                            // Inputs
                            ps.input('uid', sql.BigInt);
                            ps.input('user_id', sql.Int);
                            ps.input('station_id', sql.VarChar(10));
                            ps.input('position', sql.Int);
                            ps.input('station', sql.Int);
                            ps.input('vehicle', sql.Int);
                            ps.input('callsign', sql.VarChar(15));
                            ps.input('din', sql.VarChar(10));
                            ps.input('incident_time', sql.DateTime2, new Date().toISOString());
                            ps.input('incident_type', sql.VarChar(50));
                            ps.input('latitude', sql.Decimal(9, 6));
                            ps.input('longitude', sql.Decimal(9, 6));

                            // Prepare statement
                            ps.prepare("If Not Exists(SELECT * FROM respond52_stats WHERE uid = @uid AND user_id = @user_id) Begin INSERT INTO respond52_stats (uid, user_id, station_id, position, station, vehicle, callsign, din, incident_time, incident_type, latitude, longitude) VALUES(@uid, @user_id, @station_id, @position, @station, @vehicle, @callsign, @din, @incident_time, @incident_type, @latitude, @longitude) End", err => {

                                if (!err) {

                                    // Execute statement
                                    ps.execute({
                                        uid: row.uid,
                                        user_id: row.user_id,
                                        station_id: row.station_id,
                                        position: row.position,
                                        station: row.station,
                                        vehicle: row.vehicle,
                                        callsign: row.callsign,
                                        din: row.din,
                                        incident_time: row.incident_time,
                                        incident_type: row.incident_type,
                                        latitude: row.latitude,
                                        longitude: row.longitude
                                    }, (err, result) => {

                                        if (!err) {

                                            // Store the IDs for use in the exported update sql statement.
                                            exportedIds.push(row.id);

                                            // release the connection after queries are executed
                                            ps.unprepare(err => {
                                                callback();
                                            });

                                        } else {

                                            console.log(moment().format("DD/MM/YYYY HH:mm") + ' - Metro Stats Error: ' + JSON.stringify(err));

                                            ps.unprepare(err => {
                                                callback();
                                            });
                                        }

                                    });

                                } else {

                                    ps.unprepare(err => {
                                        callback();
                                    });
                                }
                            });

                        }, function () {

                            if (exportedIds.length > 0) {
                                let IDs = exportedIds.join();

                                superagent
                                    .post('https://samfs.respond52.com/mobile/stats/mark_metro_stats_exported')
                                    .send({idToken: idToken, exported: IDs})
                                    .set('accept', 'json')
                                    .end((err, res) => {
                                        //console.log(moment().format("DD/MM/YYYY HH:mm") + ' - Metro stats exported');
                                    });

                            }
                        });

                    }
                }

            });
    }
}


function assignRegionalCommand() {

    if (live_site === true && __dirname.includes("mfsroster/live")) {

        getRegionalCommand(function (operationalCrews) {

            for (let x = 0; x < operationalCrews.length; x++) {

                if (operationalCrews[x].perm_station_id === '38') // Learning & Development & Regional Operations
                {
                    if (operationalCrews[x].perm_shift === 'Regional Command') {
                        operationalCrews[x].perm_station_id = '200';  // Regional Command
                    }
                }
            }


            // Get Respond52 Users
            admin.firestore().collection("users").get()
                .then(function (snapshot) {

                    let users = [];

                    snapshot.forEach(function (doc) {

                        let user = doc.data();
                        user.docId = doc.id;

                        users.push(user);

                    });

                    return users;


                })
                .then(function (users) {

                    if (users) {

                        let data;

                        // Sapphire Crew
                        async.eachSeries(operationalCrews, function (operationalCrew, callback) {

                            // Search Respond52 firestore user document
                            async.eachSeries(users, function (user, callback) {

                                if (operationalCrew.user_id === user.user_id) {


                                    let updateDoc = false;

                                    let background_css = 'theme_white';


                                    if (user.rank !== operationalCrew.rank) {
                                        //console.log('rank:' + user.user_id + ' ' + user.rank + ' ' + operationalCrew.rank);
                                        updateDoc = true;
                                    } else if (user.shift !== operationalCrew.perm_shift) {
                                        //console.log('shift:' + user.user_id + ' ' + user.shift + ' ' + operationalCrew.shift);
                                        updateDoc = true;
                                    } else if (user.station_id !== operationalCrew.perm_station_id) {
                                        //console.log('station_id:' + user.user_id + ' ' + user.station_id + ' ' + operationalCrew.station_id);
                                        updateDoc = true;
                                    } else if (user.employment !== 'Full-Time') {
                                        updateDoc = true;
                                    }


                                    if (user.rank === 'ACFO' || user.rank === 'DCO' || user.rank === 'CO') {
                                        background_css = 'theme_acfo';
                                    } else if (user.rank === 'CMD') {
                                        background_css = 'theme_commander';
                                    } else if (user.rank === 'SO') {
                                        background_css = 'theme_yellow';
                                    } else if (user.rank === 'SFF' || user.rank === 'ESFF') {
                                        background_css = 'theme_dark_blue';
                                    }


                                    if (user.background_css !== background_css) {
                                        //console.log('background_css:' + user.user_id + ' ' + user.background_css + ' ' + background_css);
                                        updateDoc = true;
                                    }

                                    if (updateDoc === true) {
                                        data = {
                                            rank: operationalCrew.rank,
                                            shift: operationalCrew.perm_shift,
                                            station_id: operationalCrew.perm_station_id,
                                            employment: 'Full-Time',
                                            background_css: background_css,
                                            modified_date: moment().toDate()
                                        };
                                    }


                                    if (updateDoc === true) {


                                        //console.log(user.user_id + ' ' + user.rank + ' ' + user.given_name + ' ' + user.family_name + ' ' + user.station_id + ' ' + user.shift);

                                        admin.firestore().collection("users").doc(user.docId).set(data, {merge: true}).then(function () {
                                            callback({updated: true});
                                        });


                                    } else {
                                        callback({updated: false});
                                    }

                                } else {
                                    callback();
                                }

                            }, function () {
                                callback();
                            });


                        }, function () {
                            //console.log('done');
                        });

                    }

                })
                .catch(function (error) {
                    console.log(JSON.stringify(error));
                });


        });


    }

}


function assignCrews() {

    if (live_site === true && __dirname.includes("mfsroster/live")) {


        // Get Sapphire Users
        getCrewAssignedLocations(function (operationalCrews) {


            for (let x = 0; x < operationalCrews.length; x++) {
                if (operationalCrews[x].perm_station_id === '29') // MCO Central
                {
                    operationalCrews[x].perm_station_id = '20'; // Adelaide

                } else if (operationalCrews[x].perm_station_id === '39') // MCO Central
                {
                    operationalCrews[x].perm_station_id = '32'; // Salisbury

                } else if (operationalCrews[x].perm_station_id === '49') // MCO South
                {
                    operationalCrews[x].perm_station_id = '43'; // Noarlunga

                } else if (operationalCrews[x].perm_station_id === '100') // OTR
                {
                    operationalCrews[x].perm_station_id = '20'; // Adelaide

                } else if (operationalCrews[x].perm_station_id === '19') // Relievers
                {
                    operationalCrews[x].perm_station_id = '20'; // Adelaide
                } else if (operationalCrews[x].perm_station_id === '27') // Marine
                {
                    operationalCrews[x].perm_station_id = '25'; // Port Adelaide
                } else if (operationalCrews[x].perm_station_id === '38') // Learning & Development & Regional Operations
                {
                    if (operationalCrews[x].perm_shift === 'Regional Operations') {

                        operationalCrews[x].perm_station_id = '200';  // Regional Command
                    } else if (operationalCrews[x].perm_shift === 'Learning & Development') {
                        operationalCrews[x].perm_station_id = '20';  // Learning & Development
                    }
                }


                if (operationalCrews[x].moved_station_id !== null) {
                    if (operationalCrews[x].moved_station_id === '29') // MCO Central
                    {
                        operationalCrews[x].moved_station_id = '20'; // Adelaide

                    } else if (operationalCrews[x].moved_station_id === '39') // MCO Central
                    {
                        operationalCrews[x].moved_station_id = '32'; // Salisbury

                    } else if (operationalCrews[x].moved_station_id === '49') // MCO South
                    {
                        operationalCrews[x].moved_station_id = '43'; // Noarlunga

                    } else if (operationalCrews[x].moved_station_id === '100') // OTR
                    {
                        operationalCrews[x].moved_station_id = '20'; // Adelaide

                    } else if (operationalCrews[x].moved_station_id === '19') // Relievers
                    {
                        operationalCrews[x].moved_station_id = '20'; // Adelaide
                    } else if (operationalCrews[x].moved_station_id === '27') // Relievers
                    {
                        operationalCrews[x].moved_station_id = '25'; // Port Adelaide
                    }
                }

                if (operationalCrews[x].perm_station === 'Recruit Training') {
                    operationalCrews[x].perm_station_id = '38';        // Angle Park Training Center
                    operationalCrews[x].perm_shift = "Recruit Training";
                }
            }


            // Get Respond52 Users
            admin.firestore().collection("users").get()
                .then(function (snapshot) {

                    let users = [];

                    snapshot.forEach(function (doc) {

                        let user = doc.data();
                        user.docId = doc.id;

                        users.push(user);

                    });

                    return users;


                })
                .then(function (users) {

                    if (users) {

                        let data;

                        // Sapphire Crew
                        async.eachSeries(operationalCrews, function (operationalCrew, callback) {

                            // Search Respond52 firestore user document
                            async.eachSeries(users, function (user, callback) {

                                if (operationalCrew.user_id === user.user_id) {


                                    let updateDoc = false;

                                    let background_css = 'theme_white';


                                    if (user.rank !== operationalCrew.rank) {
                                        //console.log('rank:' + user.user_id + ' ' + user.rank + ' ' + operationalCrew.rank);
                                        updateDoc = true;
                                    } else if (user.shift !== operationalCrew.perm_shift) {
                                        //console.log('shift:' + user.user_id + ' ' + user.shift + ' ' + operationalCrew.shift);
                                        updateDoc = true;
                                    } else if (user.station_id !== operationalCrew.perm_station_id) {
                                        //console.log('station_id:' + user.user_id + ' ' + user.station_id + ' ' + operationalCrew.station_id);
                                        updateDoc = true;
                                    } else if (user.employment !== 'Full-Time') {
                                        updateDoc = true;
                                    }


                                    switch (operationalCrew.perm_shift) {

                                        case 'A Shift':
                                            background_css = 'theme_black';
                                            break;
                                        case 'A shift Part Time 1':
                                            background_css = 'theme_black';
                                            break;
                                        case 'B Shift':
                                            background_css = 'theme_green';
                                            break;
                                        case 'C Shift':
                                            background_css = 'theme_red';
                                            break;
                                        case 'C shift Part Time 1':
                                            background_css = 'theme_red';
                                            break;
                                        case 'C shift Part Time 2':
                                            background_css = 'theme_red';
                                            break;
                                        case 'CommCen E1':
                                            background_css = 'theme_dark_blue';
                                            break;
                                        case 'CommCen E2':
                                            background_css = 'theme_dark_blue';
                                            break;
                                        case 'D Shift':
                                            background_css = 'theme_blue';
                                            break;
                                        case 'OTR':
                                            background_css = 'theme_purple';
                                            break;
                                        case 'Recruit Training':
                                            background_css = 'theme_pink';
                                            break;
                                        default:
                                            background_css = 'theme_white';
                                    }


                                    if (operationalCrew.perm_roster === 'Day Work') {
                                        background_css = 'theme_orange';
                                    }

                                    if (user.rank === 'ACFO' || user.rank === 'DCO' || user.rank === 'CO') {
                                        background_css = 'theme_acfo';
                                    }


                                    if (user.background_css !== background_css) {
                                        //console.log('background_css:' + user.user_id + ' ' + user.background_css + ' ' + background_css);
                                        updateDoc = true;
                                    }

                                    if (updateDoc === true) {
                                        data = {
                                            rank: operationalCrew.rank,
                                            shift: operationalCrew.perm_shift,
                                            station_id: operationalCrew.perm_station_id,
                                            employment: 'Full-Time',
                                            background_css: background_css,
                                            modified_date: moment().toDate()
                                        };
                                    }


                                    if (updateDoc === true) {


                                        //console.log(user.user_id + ' ' + user.rank + ' ' + user.given_name + ' ' + user.family_name + ' ' + user.station_id + ' ' + user.shift);

                                        admin.firestore().collection("users").doc(user.docId).set(data, {merge: true}).then(function () {
                                            callback({updated: true});
                                        });


                                    } else {
                                        callback({updated: false});
                                    }

                                } else {
                                    callback();
                                }

                            }, function () {
                                callback();
                            });


                        }, function () {
                            //console.log('done');
                        });

                    }

                })
                .catch(function (error) {
                    console.log(JSON.stringify(error));
                });


        });

    }
}

function assignDayWorkers() {

    // Get Sapphire Day Workers
    getDayWorkAssignedLocations(function (operationalCrews) {


        for (let x = 0; x < operationalCrews.length; x++) {
            if (operationalCrews[x].perm_station_id === '38') // Learning & Development & Regional Operations
            {
                if (operationalCrews[x].perm_shift === 'Regional Operations') {
                    operationalCrews[x].perm_station_id = '200';  // Regional Command
                } else if (operationalCrews[x].perm_shift === 'Learning & Development') {
                    operationalCrews[x].perm_station_id = '20';  // Learning & Development
                }


            } else if (operationalCrews[x].perm_station_id === '00') // Learning & Development & Regional Operations
            {
                operationalCrews[x].perm_station_id = '20';  // Adelaide

            }
        }


        // Get Respond52 Users
        admin.firestore().collection("users").get()
            .then(function (snapshot) {

                let users = [];

                snapshot.forEach(function (doc) {

                    let user = doc.data();
                    user.docId = doc.id;

                    users.push(user);

                });

                return users;


            })
            .then(function (users) {

                if (users) {

                    let data;

                    // Sapphire Crew
                    async.eachSeries(operationalCrews, function (operationalCrew, callback) {

                        // Search Respond52 firestore user document
                        async.eachSeries(users, function (user, callback) {

                            if (operationalCrew.user_id.toString() === user.user_id) {

                                let updateDoc = false;

                                let background_css = 'theme_white';


                                if (user.rank !== operationalCrew.rank) {
                                    //console.log('rank:' + user.user_id + ' ' + user.rank + ' ' + operationalCrew.rank);
                                    updateDoc = true;
                                } else if (user.shift !== operationalCrew.perm_shift) {
                                    //console.log('shift:' + user.user_id + ' ' + user.shift + ' ' + operationalCrew.shift);
                                    updateDoc = true;
                                } else if (user.station_id !== operationalCrew.perm_station_id) {
                                    //console.log('station_id:' + user.user_id + ' ' + user.station_id + ' ' + operationalCrew.station_id);
                                    updateDoc = true;
                                } else if (user.operating_station_id !== operationalCrew.perm_station_id) {
                                    updateDoc = true;
                                }


                                if (user.background_css !== background_css) {
                                    //console.log('background_css:' + user.user_id + ' ' + user.background_css + ' ' + background_css);
                                    updateDoc = true;
                                }

                                if (updateDoc === true) {
                                    data = {
                                        rank: operationalCrew.rank,
                                        shift: operationalCrew.perm_shift,
                                        station_id: operationalCrew.perm_station_id,
                                        employment: 'Full-Time',
                                        background_css: background_css,
                                        modified_date: moment().toDate()
                                    };

                                    if (user.logged_in === false) {
                                        console.log(JSON.stringify(data));

                                        admin.firestore().collection("users").doc(user.docId).set(data, {merge: true}).then(function () {
                                            callback({updated: true});
                                        });

                                    } else {
                                        callback({updated: false});
                                    }
                                } else {
                                    callback();
                                }


                            } else {
                                callback();
                            }

                        }, function () {
                            callback();
                        });


                    }, function () {
                        console.log('done');
                    });

                }

            })
            .catch(function (error) {

            });


    });
}

function importCrew() {


    getDayWorkEmployees(function (users) {

        let permissions_cmdr = {
            allow_remote_logoff: true,
            allow_remote_logon: true,
            change_crew_positions: true,
            remote_logon_self: true,
            messaging_create_groups: true,
            messaging_create_groups_stations: true,
            messaging_send_custom_list: true,
            messaging_send_grn: true,
            messaging_send_messages: true,
            messaging_send_sms: true,
            messaging_send_tag_select: true,
            messaging_send_to_station: true,
            messaging_view_all_messages: true,
            rosters: true,
            edit_low_crew_alerts: true
        };

        let permissions_so = {
            allow_remote_logoff: true,
            allow_remote_logon: true,
            change_crew_positions: true,
            remote_logon_self: true,
            messaging_create_groups: true,
            messaging_create_groups_stations: false,
            messaging_send_custom_list: true,
            messaging_send_grn: true,
            messaging_send_messages: true,
            messaging_send_sms: true,
            messaging_send_tag_select: true,
            messaging_send_to_station: true,
            messaging_view_all_messages: false,
            rosters: true,
            edit_low_crew_alerts: true
        };

        let permissions_sff = {
            allow_remote_logoff: true,
            allow_remote_logon: true,
            change_crew_positions: true,
            remote_logon_self: true,
            messaging_create_groups: false,
            messaging_create_groups_stations: false,
            messaging_send_custom_list: true,
            messaging_send_grn: true,
            messaging_send_messages: true,
            messaging_send_sms: true,
            messaging_send_tag_select: true,
            messaging_send_to_station: true,
            messaging_view_all_messages: false,
            rosters: true,
            edit_low_crew_alerts: true
        };

        let permissions_ff = {
            allow_remote_logoff: false,
            allow_remote_logon: false,
            change_crew_positions: false,
            remote_logon_self: true,
            messaging_create_groups: false,
            messaging_create_groups_stations: false,
            messaging_send_custom_list: false,
            messaging_send_grn: false,
            messaging_send_messages: false,
            messaging_send_sms: false,
            messaging_send_tag_select: false,
            messaging_send_to_station: false,
            messaging_view_all_messages: false,
            rosters: false,
            edit_low_crew_alerts: false
        };


        async.eachSeries(users, function (user, callback) {

            let create_doc = true;


            user.email = user.user_id + '@samfs.respond52.com';

            if (user.email.length > 10) {

                async.series([

                    function (callback) {

                        // Create login
                        admin.auth().createUser({
                            email: user.email,
                            password: user.user_id
                        })
                            .then(function (userRecord) {
                                callback();
                            })
                            .catch(function (error) {
                                create_doc = false;
                                console.log('Error creating new auth user:', error.message);
                                callback();
                            });

                    },
                    function (callback) {

                        // Create auth lookup

                        if (create_doc === true) {

                            let auth = {
                                email: user.email,
                                enabled: true,
                                organisation: "SAMFS",
                                user_id: user.user_id,
                            };

                            admin.firestore().collection("logins").add(auth).then(function () {
                                callback()
                            }).catch(function (error) {
                                console.log('Error creating new login:' + auth, error);
                            });

                        } else {
                            callback();
                        }

                    },
                    function (callback) {

                        // Create user document

                        if (create_doc === true) {

                            let doc_id = admin.firestore().collection("users").doc().id;


                            user.logon_position = user.rank_index;


                            let permissions = {};

                            if (user.rank === 'CMD' || user.rank === 'ACFO' || user.rank === 'DCO' || user.rank === 'CO') {
                                permissions = permissions_cmdr;
                                user.rank_level = 4;
                            } else if (user.rank === 'SO' || user.rank === 'RSO') {
                                permissions = permissions_so;
                                user.rank_level = 3;
                            } else if (user.rank === 'SFF' || user.rank === 'RFS' || user.rank === 'ESFF') {
                                permissions = permissions_sff;
                                user.rank_level = 2;
                            } else {
                                permissions = permissions_ff;
                                user.rank_level = 1;
                            }

                            user.background_css = 'theme_white';


                            console.log('Creating: ' + user.user_id + ' ' + user.given_name + ' ' + user.family_name);


                            let doc = {
                                active: true,
                                available: false,
                                background_css: user.background_css,
                                default_travel_time: "0",
                                delayed: 0,
                                deployable: true,
                                deployed: {
                                    position: 0,
                                    station: false,
                                    vehicle: ""
                                },
                                email: user.email,
                                employment: 'Full-Time',
                                family_name: user.family_name,
                                geofence: true,
                                given_name: user.given_name,
                                id: doc_id,
                                inactive_date: admin.firestore.Timestamp.fromDate(new Date()),
                                incident_subscriptions: [],
                                last_login_time: admin.firestore.Timestamp.fromDate(new Date()),
                                logged_in: false,
                                logon_position: user.logon_position,
                                message_groups: [],
                                mobile_availability_view: "graphical",
                                mobile_view_station_id: user.station_id,
                                modified_date: admin.firestore.Timestamp.fromDate(new Date()),
                                nfc_id: '',
                                operating_station_id: user.station_id,
                                operation_type: "added",
                                organisation: "SAMFS",
                                permissions: permissions,
                                phone: '',
                                pager_id: "",
                                play_voice: true,
                                push_token: "",
                                rank: user.rank,
                                rank_level: user.rank_level,
                                reason: "Unknown",
                                respond_time: admin.firestore.Timestamp.fromDate(new Date()),
                                responding: false,
                                session_id: "",
                                shift: user.shift,
                                station_id: user.station_id,
                                status: "Unknown",
                                tags: [],
                                user_id: user.user_id,
                                member_id: user.user_id,
                                low_crew_alert: false
                            };

                            admin.firestore().collection("users").doc(doc_id).set(doc).then(function () {
                                console.log(user.given_name + ', ' + user.family_name);
                                callback();
                            });

                        } else {
                            callback();
                        }

                    }

                ], function () {
                    callback();
                });

            } else {
                callback();
            }


        }, function () {
            console.log('done');
        });
    });
}


function getDayWorkEmployees(callback) {

    const ps = new sql.PreparedStatement(connection);

    // Get Day work employees
    ps.prepare("SELECT * FROM (SELECT e.pay_id AS user_id, e.first_name AS given_name, e.surname AS family_name, (SELECT TOP 1 rank FROM roster_arrangements WHERE pay_id = e.pay_id and date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') as rank, (SELECT TOP 1 rank_index FROM roster_arrangements WHERE pay_id = e.pay_id and date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') as rank_index, (SELECT TOP 1 roster FROM roster_arrangements WHERE pay_id = e.pay_id and date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') as perm_roster from employees e) AS results WHERE perm_roster = 'Day Work' AND rank != 'NON'", err => {

        if (!err) {
            // Execute statement
            ps.execute({}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        // Fix station_id's
                        let users = result.recordset;

                        for (let x = 0; x < users.length; x++) {

                            users[x].station_id = "";
                            users[x].shift = "";
                            users[x].user_id = users[x].user_id.toString();

                        }


                        callback(users);
                    } else {
                        callback([]);
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

                    callback([]);
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);
            callback([]);
        }
    });

}


function getOperationalEmployees(callback) {

    const ps = new sql.PreparedStatement(connection);

    // Get operational employees who will ride on vehicles
    ps.prepare("SELECT pay_id AS user_id, first_name AS given_name, surname AS family_name, rank, rank_index, home_station_id AS station_id from employees WHERE home_station!= '3rd Floor' AND home_station != '4th Floor' AND home_station != '' AND home_station != 'Angle Park Training Centre' AND home_station_id > 0 AND retained = 0", err => {

        if (!err) {
            // Execute statement
            ps.execute({}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        // Fix station_id's
                        let users = result.recordset;

                        for (let x = 0; x < users.length; x++) {
                            if (users[x].station_id === 4) // Comms
                            {
                                users[x].station_id = '20';  // Adelaide
                                users[x].shift = "";
                            } else if (users[x].station_id === 29) // MCO Central
                            {
                                users[x].station_id = '20'; // Adelaide
                                users[x].shift = "";
                            } else if (users[x].station_id === 39) // MCO Central
                            {
                                users[x].station_id = '32'; // Salisbury
                                users[x].shift = "";
                            } else if (users[x].station_id === 49) // MCO South
                            {
                                users[x].station_id = '43'; // Noarlunga
                                users[x].shift = "";
                            } else if (users[x].station_id === 100) // OTR
                            {
                                users[x].station_id = '20'; // Adelaide
                                users[x].shift = "OTR";
                            } else if (users[x].station_id === 19) // Relievers
                            {
                                users[x].station_id = '20'; // Adelaide
                                users[x].shift = "";
                            } else {
                                users[x].station_id = users[x].station_id.toString();
                                users[x].shift = "";
                            }

                            users[x].user_id = users[x].user_id.toString();

                        }


                        callback(users);
                    } else {
                        callback([]);
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

                    callback([]);
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);
            callback([]);
        }
    });

}


function getRegionalCommand(callback) {

    const ps = new sql.PreparedStatement(connection);

    // Get crew and their currently assigned location

    ps.prepare("SELECT * FROM (SELECT DISTINCT CAST(ra.pay_id AS varchar) as 'user_id', ra.roster as perm_roster, ra.shift as perm_shift, ra.location as perm_station, ra.rank, ra.shift_type, (SELECT TOP 1 colour FROM shifts WHERE shift_name = ra.shift) as perm_shift_colour, (SELECT station_id FROM locations WHERE name = ra.location) AS perm_station_id, (SELECT TOP 1 roster FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_shift, (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_station, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) AS moved_station_id, (SELECT TOP 1 colour FROM shifts WHERE shift_name = (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) as moved_shift_colour, (SELECT TOP 1 booking_type FROM bookings WHERE pay_id = ra.pay_id and date_string = ra.date_string AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' ORDER BY created_date DESC) as booking_type FROM roster_arrangements ra WHERE ra.date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND ra.type = 'Permanent') as results WHERE perm_roster = 'Regional Operations' AND perm_shift = 'Regional Command'", err => {

        if (!err) {
            // Execute statement
            ps.execute({}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        callback(result.recordset);
                    } else {
                        callback([]);
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

                    callback([]);
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

            callback([]);
        }
    });
}


function getCrewAssignedLocations(callback) {

    const ps = new sql.PreparedStatement(connection);

    // Get crew and their currently assigned location


    //SELECT * FROM (SELECT DISTINCT CAST(ra.pay_id AS varchar) as 'user_id', ra.rank, ra.roster as perm_roster, ra.shift as perm_shift, (SELECT TOP 1 colour FROM shifts WHERE shift_name = ra.shift) as perm_shift_colour,  ra.location as perm_station, (SELECT station_id FROM locations WHERE name = ra.location) AS perm_station_id, (SELECT TOP 1 roster FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_shift, (SELECT TOP 1 colour FROM shifts WHERE shift_name = (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) as moved_shift_colour, (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_station, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) AS moved_station_id FROM roster_arrangements ra WHERE ra.date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND ra.type = 'Permanent' AND ra.shift != 'Regional' AND ra.rank != 'NON') as results", err => {

    ps.prepare("SELECT * FROM (SELECT DISTINCT CAST(ra.pay_id AS varchar) as 'user_id', ra.roster as perm_roster, ra.shift as perm_shift, ra.location as perm_station, ra.rank, ra.shift_type, (SELECT TOP 1 colour FROM shifts WHERE shift_name = ra.shift) as perm_shift_colour, (SELECT station_id FROM locations WHERE name = ra.location) AS perm_station_id, (SELECT TOP 1 roster FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_shift, (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_station, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) AS moved_station_id, (SELECT TOP 1 colour FROM shifts WHERE shift_name = (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) as moved_shift_colour, (SELECT TOP 1 booking_type FROM bookings WHERE pay_id = ra.pay_id and date_string = ra.date_string AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' ORDER BY created_date DESC) as booking_type FROM roster_arrangements ra WHERE ra.date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND ra.type = 'Permanent') as results WHERE perm_roster <> 'Regional Operations' AND rank <> 'NON'", err => {

        if (!err) {
            // Execute statement
            ps.execute({}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        callback(result.recordset);
                    } else {
                        callback([]);
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

                    callback([]);
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

            callback([]);
        }
    });

}

function getDayWorkAssignedLocations(callback) {

    const ps = new sql.PreparedStatement(connection);

    // Get crew and their currently assigned location

    //SELECT * FROM (SELECT DISTINCT ra.pay_id, ra.roster as perm_roster, ra.shift as perm_shift, ra.location as perm_station, (SELECT station_id FROM locations WHERE name = ra.location) AS perm_station_id, (SELECT TOP 1 roster FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_shift, (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_station, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) AS moved_station_id FROM roster_arrangements ra WHERE ra.date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND ra.type = 'Permanent') as results
    //"SELECT CAST(pay_id AS varchar) AS user_id, r.rank, r.roster, r.shift, r.location, station_id, (SELECT TOP 1 colour FROM shifts WHERE shift_name = r.shift) as shift_colour FROM roster_arrangements r JOIN locations ON r.location = locations.name where date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND shift != 'Regional' AND shift != 'Regional Operations' AND r.rank != 'NON'

    ps.prepare("SELECT * FROM (SELECT e.pay_id AS user_id, e.first_name AS given_name, e.surname AS family_name, (SELECT TOP 1 rank FROM roster_arrangements WHERE pay_id = e.pay_id and date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') as rank, (SELECT TOP 1 roster FROM roster_arrangements WHERE pay_id = e.pay_id and date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') as perm_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE pay_id = e.pay_id and date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') as perm_shift, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE pay_id = e.pay_id and date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent')) AS perm_station_id from employees e) AS results WHERE perm_roster = 'Day Work'", err => {

        if (!err) {
            // Execute statement
            ps.execute({}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        callback(result.recordset);
                    } else {
                        callback([]);
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

                    callback([]);
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

            callback([]);
        }
    });

}

function getCrewAssignedLocation(pay_id, callback) {


    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('pay_id', sql.Int);


    ps.prepare("SELECT * FROM (SELECT DISTINCT CAST(ra.pay_id AS varchar) as 'user_id', ra.rank, ra.roster as perm_roster, ra.shift as perm_shift, (SELECT TOP 1 colour FROM shifts WHERE shift_name = ra.shift) as perm_shift_colour,  ra.location as perm_station, (SELECT station_id FROM locations WHERE name = ra.location) AS perm_station_id, (SELECT TOP 1 roster FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_shift, (SELECT TOP 1 colour FROM shifts WHERE shift_name = (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) as moved_shift_colour, (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_station, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) AS moved_station_id FROM roster_arrangements ra WHERE ra.pay_id = @pay_id AND ra.date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND ra.type = 'Permanent' AND ra.shift != 'Regional' AND ra.rank != 'NON') as results", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                pay_id: pay_id
            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        callback(result.recordset);
                    } else {
                        callback([]);
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

                    callback([]);
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + err);

            callback([]);
        }
    });

}


function getIncidentInfoSearchData(req, res) {

    let din = req.query.din;
    let from_date = moment(moment(req.query.incident_time).format("YYYY-MM-DD 00:00:01")).toDate();
    let to_date = moment(moment(req.query.incident_time).format("YYYY-MM-DD 23:59:59")).toDate();
    let sqlQuery = "";

    const ps = new sql.PreparedStatement(connection);

    if (din.length == 1) {
        din = "000" + din;
    } else if (din.length == 2) {
        din = "00" + din;
    } else if (din.length == 3) {
        din = "0" + din;
    }

    // Inputs
    ps.input('din', sql.VarChar(10));
    ps.input('from_date', sql.DateTime);
    ps.input('to_date', sql.DateTime);

    sqlQuery = "SELECT r52.*, emp.surname, emp.first_name, emp.middle_name FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND din = @din AND vehicle = 1 ORDER BY incident_time, callsign, position, surname";

    // Prepare statement
    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                din: din,
                from_date: from_date,
                to_date: to_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.incident_time = moment.tz(record.incident_time, process.env.timezone).format("DD/MM/YYYY H:mm");
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function getIncidentReportData(req, res) {

    let pay_id = req.query.pay_id;
    let roster = req.query.roster;
    let shift = req.query.shift;
    let location = req.query.location;
    let from_date = moment(moment(req.query.from_date).format("YYYY-MM-DD 00:00:01")).toDate();
    let to_date = moment(moment(req.query.to_date).format("YYYY-MM-DD 23:59:59")).toDate();
    let sqlQuery = "";

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('pay_id', sql.Int);
    ps.input('roster', sql.VarChar(40));
    ps.input('shift', sql.VarChar(40));
    ps.input('location', sql.VarChar(40));
    ps.input('from_date', sql.DateTime);
    ps.input('to_date', sql.DateTime);

    if (pay_id == 1) {
        if (roster == "-- All Rosters --") {
            sqlQuery = "SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%'ORDER BY r52.incident_time, emp.surname, emp.first_name";
        } else {
            if (shift == "-- All Shifts --") {
                if (location == "-- All Stations --") {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster ORDER BY incident_time, surname, first_name";
                } else {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster AND location = @location ORDER BY incident_time, surname, first_name";
                }
            } else {
                if (location == "-- All Stations --") {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster AND shift = @shift ORDER BY incident_time, surname, first_name";
                } else {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster AND shift = @shift AND location = @location ORDER BY incident_time, surname, first_name";
                }
            }
        }
    } else {
        if (roster == "-- All Rosters --") {
            sqlQuery = "SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%' AND r52.user_id = @pay_id ORDER BY r52.incident_time, emp.surname, emp.first_name";
        } else {
            if (shift == "-- All Shifts --") {
                if (location == "-- All Stations --") {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster AND user_id = @pay_id ORDER BY incident_time, surname, first_name";
                } else {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster AND location = @location AND user_id = @pay_id ORDER BY incident_time, surname, first_name";
                }
            } else {
                if (location == "-- All Stations --") {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster AND shift = @shift AND user_id = @pay_id ORDER BY incident_time, surname, first_name";
                } else {
                    sqlQuery = "SELECT * FROM (SELECT r52.*, emp.surname, emp.first_name, emp.middle_name, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(r52.incident_time AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = r52.user_id ORDER BY roster_date DESC) AS location FROM respond52_stats r52 JOIN employees emp ON r52.user_id = emp.pay_id WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%') as results WHERE roster = @roster AND shift = @shift AND location = @location AND user_id = @pay_id ORDER BY incident_time, surname, first_name";
                }
            }
        }
    }

    // Prepare statement
    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                pay_id: pay_id,
                roster: roster,
                shift: shift,
                location: location,
                from_date: from_date,
                to_date: to_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.incident_time = moment.tz(record.incident_time, process.env.timezone).format("DD/MM/YYYY H:mm");
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function getIncidentLocationReportData(req, res) {

    let station_id = req.query.station_id;
    let from_date = moment(moment(req.query.from_date).format("YYYY-MM-DD 00:00:01")).toDate();
    let to_date = moment(moment(req.query.to_date).format("YYYY-MM-DD 23:59:59")).toDate();
    let sqlQuery = "";

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('station_id', sql.VarChar(10));
    ps.input('from_date', sql.DateTime);
    ps.input('to_date', sql.DateTime);

    if (station_id == "1") {
        sqlQuery = "SELECT incident_type, station_id,\n" +
            "(SELECT COUNT(*) FROM (SELECT COUNT(din) AS ttl FROM respond52_stats WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type = r52.incident_type AND station_id = r52.station_id GROUP BY din, incident_time, incident_type) as results) AS qty\n" +
            "FROM respond52_stats r52 WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%' GROUP BY incident_type, station_id ORDER BY incident_type, station_id";
    } else {
        sqlQuery = "SELECT incident_type, station_id,\n" +
            "(SELECT COUNT(*) FROM (SELECT COUNT(din) AS ttl FROM respond52_stats WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type = r52.incident_type AND station_id = r52.station_id GROUP BY din, incident_time, incident_type) as results) AS qty\n" +
            "FROM respond52_stats r52 WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%' AND station_id = @station_id GROUP BY incident_type, station_id ORDER BY incident_type";
    }


    // Prepare statement
    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                station_id: station_id,
                from_date: from_date,
                to_date: to_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        res.status(200).send(result.recordset);
                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function getIncidentTypeReportData(req, res) {

    let pay_id = req.query.pay_id;
    let from_date = moment(moment(req.query.from_date).format("YYYY-MM-DD 00:00:01")).toDate();
    let to_date = moment(moment(req.query.to_date).format("YYYY-MM-DD 23:59:59")).toDate();
    let sqlQuery = "";

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('pay_id', sql.Int);
    ps.input('from_date', sql.DateTime);
    ps.input('to_date', sql.DateTime);

    if (pay_id == 1) {
        sqlQuery = "SELECT incident_type,\n" +
            "(SELECT COUNT(*) FROM (SELECT COUNT(din) AS ttl FROM respond52_stats WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type = r52.incident_type GROUP BY din, incident_time, incident_type) as results) AS qty\n" +
            "FROM respond52_stats r52 WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%' GROUP BY incident_type ORDER BY incident_type";
    } else {
        sqlQuery = "SELECT incident_type,\n" +
            "(SELECT COUNT(*) FROM (SELECT COUNT(din) AS ttl FROM respond52_stats WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type = r52.incident_type AND user_id = @pay_id GROUP BY din, incident_time, incident_type) as results) AS qty\n" +
            "FROM respond52_stats r52 WHERE incident_time BETWEEN @from_date AND @to_date AND incident_type NOT LIKE '%TEST%' GROUP BY incident_type ORDER BY incident_type";
    }


    // Prepare statement
    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                pay_id: pay_id,
                from_date: from_date,
                to_date: to_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        res.status(200).send(result.recordset);
                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}

module.exports = REST_ROUTER;
