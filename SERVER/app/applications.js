let moment = require('moment');
const sql = require('mssql');
let nodemailer = require('nodemailer');
let path = require('path');
let live_site = false;
let bearer_token = "";
let origin_url = "";

let connection;

let smtp_settings = nodemailer.createTransport({
    host: "mail.smtp2go.com",
    port: 2525,
    secure: false,
    auth: {
        user: "sapphire",
        pass: "YzFoanJ4Zmg0c2Yw"
    }
});

function REST_ROUTER(router, cn) {

    connection = cn;

    let self = this;
    self.handleRoutes(router);

    if (connection.config.database == "sapphire_live") {
        live_site = true;
    } else {
        live_site = false;
    }
}


REST_ROUTER.prototype.handleRoutes = function (router) {

    //APPLICATIONS - standbys

    router.get("/get_employee_info", function (req, res) {
        getEmployeeInfo(req, res);
    });

    router.post("/save_sb_request", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            saveSBRequest(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_req_info", function (req, res) {
        getMessageInfo(req, res);
    });

    router.get("/get_rrl_req_info", function (req, res) {
        getRRLMessageInfo(req, res);
    });

    router.put("/save_response", function (req, res) {
        saveResponse(req, res);
    });

    router.put("/save_rrl_response", function (req, res) {
        saveRRLResponse(req, res);
    });

    router.get("/get_sb_requests", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getSBRequests(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_notify_list", function (req, res) {
        getNofityEmployeeList(req, res);
    });

    router.post("/send_response_email", function (req, res) {
        sendResponseEmail(req, res);
    });

    router.post("/send_rrl_response_email", function (req, res) {
        sendRRLResponseEmail(req, res);
    });

    router.post("/save_rrl_request", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            saveRRLRequest(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_rrl_requests", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getRRLRequests(req, res);
        } else {
            res.status(401).send();
        }
    });


};


function getEmployeeInfo(req, res) {

    let pay_id = req.query.pay_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('pay_id', sql.Int);

    // Prepare statement
    ps.prepare("SELECT employees.*, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank FROM employees WHERE employees.pay_id = @pay_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({pay_id: pay_id}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send(
                        result.recordset
                    );
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function getNofityEmployeeList(req, res) {

    const ps = new sql.PreparedStatement(connection);

    ps.prepare("SELECT * FROM (SELECT pay_id, surname, first_name, middle_name, notifications_email, (SELECT TOP 1 rank FROM roster_arrangements WHERE pay_id = employees.pay_id AND date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd')) AS ra_rank FROM employees) as results WHERE ra_rank IN ('SO','SFF','ESFF','CMD','SCOP','ACFO','CO','DCO','RSO') ORDER BY surname", err => {

        if (!err) {
            // Execute statement
            ps.execute({

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });


}


function getSBRequests(req, res) {

    let request_date_from = req.query.request_from_date;
    let request_date_to = req.query.request_to_date;
    let from_date = moment(request_date_from).format("YYYY-MM-DD 00:01");
    let to_date = moment(request_date_to).format("YYYY-MM-DD 23:59");
    let show_all = req.query.show_all;
    let pay_id = req.query.pay_id;
    let sqlQuery = "";

    const ps = new sql.PreparedStatement(connection);

    //INPUTS
    ps.input('from_date', sql.DateTime);
    ps.input('to_date', sql.DateTime);
    ps.input('pay_id', sql.Int);

    if (show_all == 1){
        sqlQuery = "SELECT standby_requests.*, em1.surname, em1.first_name, em2.surname, em2.first_name FROM standby_requests JOIN employees em1 ON standby_requests.requested_by = em1.pay_id JOIN employees em2 ON standby_requests.substitute = em2.pay_id WHERE request_date BETWEEN @from_date AND @to_date ORDER BY request_date";
    } else {
        sqlQuery = "SELECT standby_requests.*, em1.surname, em1.first_name, em2.surname, em2.first_name FROM standby_requests JOIN employees em1 ON standby_requests.requested_by = em1.pay_id JOIN employees em2 ON standby_requests.substitute = em2.pay_id WHERE request_date BETWEEN @from_date AND @to_date AND (standby_requests.substitute = @pay_id OR standby_requests.requested_by = @pay_id) ORDER BY request_date";
    }

    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                from_date: from_date,
                to_date: to_date,
                pay_id: pay_id
            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.request_date = moment.tz(record.request_date, process.env.timezone).format("DD/MM/YYYY HH:mm");
                            if (record.response_date !== null) {
                                record.response_date = moment.tz(record.response_date, process.env.timezone).format("DD/MM/YYYY HH:mm");
                            }
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function getRRLRequests(req, res) {

    let request_date_from = req.query.request_from_date;
    let request_date_to = req.query.request_to_date;
    let from_date = moment(request_date_from).format("YYYY-MM-DD 00:01");
    let to_date = moment(request_date_to).format("YYYY-MM-DD 23:59");
    let show_all = req.query.show_all;
    let pay_id = req.query.pay_id;
    let sqlQuery = "";

    const ps = new sql.PreparedStatement(connection);

    //INPUTS
    ps.input('from_date', sql.DateTime);
    ps.input('to_date', sql.DateTime);
    ps.input('pay_id', sql.Int);

    if (show_all == 1){
        sqlQuery = "SELECT rrl_requests.*, em1.surname, em1.first_name, em2.surname, em2.first_name FROM rrl_requests JOIN employees em1 ON rrl_requests.requested_by = em1.pay_id JOIN employees em2 ON rrl_requests.substitute = em2.pay_id WHERE request_date BETWEEN @from_date AND @to_date ORDER BY request_date";
    } else {
        sqlQuery = "SELECT rrl_requests.*, em1.surname, em1.first_name, em2.surname, em2.first_name FROM rrl_requests JOIN employees em1 ON rrl_requests.requested_by = em1.pay_id JOIN employees em2 ON rrl_requests.substitute = em2.pay_id WHERE request_date BETWEEN @from_date AND @to_date AND (rrl_requests.substitute = @pay_id OR rrl_requests.requested_by = @pay_id) ORDER BY request_date";
    }

    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                from_date: from_date,
                to_date: to_date,
                pay_id: pay_id
            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.request_date = moment.tz(record.request_date, process.env.timezone).format("DD/MM/YYYY HH:mm");
                            record.rrl_start_date = moment.tz(record.rrl_start_date, process.env.timezone).format("DD/MM/YYYY");
                            record.rrl_return_date = moment.tz(record.rrl_return_date, process.env.timezone).format("DD/MM/YYYY");
                            record.swap_start_date = moment.tz(record.swap_start_date, process.env.timezone).format("DD/MM/YYYY");
                            record.swap_return_date = moment.tz(record.swap_return_date, process.env.timezone).format("DD/MM/YYYY");
                            if (record.response_date !== null) {
                                record.response_date = moment.tz(record.response_date, process.env.timezone).format("DD/MM/YYYY HH:mm");
                            }
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function saveResponse(req, res) {

    let request_id = req.body.request_id;
    let response = req.body.response;
    let response_date = moment().toDate();
    let so_pay_id = req.body.so_pay_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('request_id', sql.VarChar(10));
    ps.input('response', sql.VarChar(10));
    ps.input('response_date', sql.DateTime);
    ps.input('so_pay_id', sql.Int);

    // Prepare statement
    ps.prepare("UPDATE standby_requests SET response = @response, response_date = @response_date, so_pay_id = @so_pay_id WHERE request_id = @request_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                request_id: request_id,
                response: response,
                response_date: response_date,
                so_pay_id: so_pay_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: "OK"
                    });
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function saveRRLResponse(req, res) {

    let request_id = req.body.request_id;
    let response = req.body.response;
    let response_date = moment().toDate();

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('request_id', sql.VarChar(10));
    ps.input('response', sql.VarChar(10));
    ps.input('response_date', sql.DateTime);

    // Prepare statement
    ps.prepare("UPDATE rrl_requests SET response = @response, response_date = @response_date WHERE request_id = @request_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                request_id: request_id,
                response: response,
                response_date: response_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: "OK"
                    });
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function getMessageInfo(req, res) {

    let request_id = req.query.request_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('request_id', sql.VarChar(10));

    ps.prepare("SELECT standby_requests.*, emp1.surname, emp1.first_name, emp1.notifications_email, emp2.surname, emp2.first_name, emp2.notifications_email FROM standby_requests JOIN employees emp1 ON standby_requests.requested_by = emp1.pay_id JOIN employees emp2 ON standby_requests.substitute = emp2.pay_id WHERE request_id = @request_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                request_id: request_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.request_date = moment.tz(record.request_date, process.env.timezone).format("dddd, DD/MM/YYYY HH:mm");
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function getRRLMessageInfo(req, res) {

    let request_id = req.query.request_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('request_id', sql.VarChar(10));

    ps.prepare("SELECT rrl_requests.*, emp1.surname, emp1.first_name, emp1.notifications_email, emp2.surname, emp2.first_name, emp2.notifications_email FROM rrl_requests JOIN employees emp1 ON rrl_requests.requested_by = emp1.pay_id JOIN employees emp2 ON rrl_requests.substitute = emp2.pay_id WHERE request_id = @request_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                request_id: request_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.request_date = moment.tz(record.request_date, process.env.timezone).format("dddd, DD/MM/YYYY HH:mm");
                            record.rrl_start_date = moment.tz(record.rrl_start_date, process.env.timezone).format("ddd, DD/MM/YYYY");
                            record.rrl_return_date = moment.tz(record.rrl_return_date, process.env.timezone).format("ddd, DD/MM/YYYY");
                            record.swap_start_date = moment.tz(record.swap_start_date, process.env.timezone).format("ddd, DD/MM/YYYY");
                            record.swap_return_date = moment.tz(record.swap_return_date, process.env.timezone).format("ddd, DD/MM/YYYY");
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function saveSBRequest(req, res) {

    let request_id = req.body.request_id;
    let request_date = moment().toDate();
    let requested_by = req.body.requested_by;
    let roster = req.body.roster;
    let shift = req.body.shift;
    let location = req.body.location;
    let substitute = req.body.substitute;
    let shift_date = req.body.shift_date;
    let shift_start = req.body.shift_start;
    let shift_end = req.body.shift_end;
    let response = "Pending";
    let request_comments = req.body.request_comments;
    let substitute_roster = req.body.substitute_roster;
    let substitute_shift = req.body.substitute_shift;
    let substitute_location = req.body.substitute_location;
    let so_pay_id = req.body.so_pay_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('request_id', sql.VarChar(10));
    ps.input('request_date', sql.DateTime);
    ps.input('requested_by', sql.Int);
    ps.input('roster', sql.VarChar(40));
    ps.input('shift', sql.VarChar(40));
    ps.input('location', sql.VarChar(40));
    ps.input('substitute', sql.Int);
    ps.input('shift_date', sql.Numeric(8, 0));
    ps.input('shift_start', sql.VarChar(5));
    ps.input('shift_end', sql.VarChar(5));
    ps.input('response', sql.VarChar(10));
    ps.input('request_comments', sql.VarChar(500));
    ps.input('substitute_roster', sql.VarChar(40));
    ps.input('substitute_shift', sql.VarChar(40));
    ps.input('substitute_location', sql.VarChar(40));
    ps.input('so_pay_id', sql.Int);

    // Prepare statement
    ps.prepare("INSERT INTO standby_requests(request_id, request_date, requested_by, roster, shift, location, substitute, shift_date, shift_start, shift_end, response, request_comments, substitute_roster, substitute_shift, substitute_location, so_pay_id) VALUES(@request_id, @request_date, @requested_by, @roster, @shift, @location, @substitute, @shift_date, @shift_start, @shift_end, @response, @request_comments, @substitute_roster, @substitute_shift, @substitute_location, @so_pay_id)", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                request_id: request_id,
                request_date: request_date,
                requested_by: requested_by,
                roster: roster,
                shift: shift,
                location: location,
                substitute: substitute,
                shift_date: shift_date,
                shift_start: shift_start,
                shift_end: shift_end,
                response: response,
                request_comments: request_comments,
                substitute_roster: substitute_roster,
                substitute_shift: substitute_shift,
                substitute_location: substitute_location,
                so_pay_id: so_pay_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: "OK"
                    });

                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function saveRRLRequest(req, res) {

    let request_id = req.body.request_id;
    let request_date = moment().toDate();
    let requested_by = parseInt(req.body.requested_by);
    let roster = req.body.roster;
    let shift = req.body.shift;
    let location = req.body.location;
    let swap_roster = req.body.swap_roster;
    let swap_shift = req.body.swap_shift;
    let swap_location = req.body.swap_location;
    let substitute = parseInt(req.body.substitute);
    let rrl_start_date = moment(req.body.rrl_start_date).toDate();
    let rrl_return_date = moment(req.body.rrl_return_date).toDate();
    let swap_start_date = moment(req.body.swap_start_date).toDate();
    let swap_return_date = moment(req.body.swap_return_date).toDate();
    let response = "Pending";
    let request_comments = req.body.request_comments;
    let requester_group = req.body.requester_group;
    let substitute_group = req.body.substitute_group;
    let requester_rank = req.body.requester_rank;
    let substitute_rank = req.body.substitute_rank;


    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('request_id', sql.VarChar(10));
    ps.input('request_date', sql.DateTime);
    ps.input('requested_by', sql.Int);
    ps.input('roster', sql.VarChar(40));
    ps.input('shift', sql.VarChar(40));
    ps.input('location', sql.VarChar(40));
    ps.input('swap_roster', sql.VarChar(40));
    ps.input('swap_shift', sql.VarChar(40));
    ps.input('swap_location', sql.VarChar(40));
    ps.input('substitute', sql.Int);
    ps.input('rrl_start_date', sql.DateTime);
    ps.input('rrl_return_date', sql.DateTime);
    ps.input('swap_start_date', sql.DateTime);
    ps.input('swap_return_date', sql.DateTime);
    ps.input('response', sql.VarChar(10));
    ps.input('request_comments', sql.VarChar(500));
    ps.input('requester_group', sql.VarChar(10));
    ps.input('substitute_group', sql.VarChar(10));
    ps.input('requester_rank', sql.VarChar(10));
    ps.input('substitute_rank', sql.VarChar(10));

    // Prepare statement
    ps.prepare("INSERT INTO rrl_requests(request_id, request_date, requested_by, roster, shift, location, swap_roster, swap_shift, swap_location, substitute, rrl_start_date, rrl_return_date, swap_start_date, swap_return_date, response, request_comments, requester_group, substitute_group, request_rank, substitute_rank) VALUES(@request_id, @request_date, @requested_by, @roster, @shift, @location, @swap_roster, @swap_shift, @swap_location, @substitute, @rrl_start_date, @rrl_return_date, @swap_start_date, @swap_return_date, @response, @request_comments, @requester_group, @substitute_group, @requester_rank, @substitute_rank)", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                request_id: request_id,
                request_date: request_date,
                requested_by: requested_by,
                roster: roster,
                shift: shift,
                location: location,
                swap_roster: swap_roster,
                swap_shift: swap_shift,
                swap_location: swap_location,
                substitute: substitute,
                rrl_start_date: rrl_start_date,
                rrl_return_date: rrl_return_date,
                swap_start_date: swap_start_date,
                swap_return_date: swap_return_date,
                response: response,
                request_comments: request_comments,
                requester_group: requester_group,
                substitute_group: substitute_group,
                requester_rank: requester_rank,
                substitute_rank: substitute_rank

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: "OK"
                    });

                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function sendRRLResponseEmail(req, res) {

    let from_address = req.body.from_address;
    let to_address = req.body.to_address;
    let subject = req.body.subject;
    let message = req.body.message;
    let extra_message = req.body.extra_message;
    let recipient = req.body.recipient;
    let signature = req.body.signature;
    let signature2 = req.body.signature2;

    let body_html = "<!DOCTYPE html>\n" +
        "<html>\n" +
        "<head>\n" +
        "<title></title>\n" +
        "<meta http-equiv='content-type' content='text/html; charset=utf-8' />\n" +
        "</head>\n" +
        "<body>\n" +
        "<table><tr><h1><th style='width: 65px'><img src='https://i.ibb.co/bF7pkW1/email-logo.gif' alt='sapphire'></th><th valign='center'> SAPPHIRE: Roster Management</th></h1></tr></table>\n" +
        "<h3>Dear " + recipient + ",</h3>\n" +
        "<h3>" + message + "</h3>\n" +
        "<h3>" + extra_message + "</h3>\n" +
        "<h3>" + signature + "</h3>\n" +
        "<h3>" + signature2 + "</h3>\n" +
        "</body>\n" +
        "</html>";

    let mailOptions = {
        from: "SAPPHIRE<<EMAIL>>", //from_address (SAFECOM have stated to use this ESO address as it's authenticated)
        replyTo: from_address,
        to: to_address,
        subject: subject,
        html: body_html
    };

    smtp_settings.sendMail(mailOptions, function (error, info) {
        if (error) {
            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + error + "(login.js - sendEmail)");
            res.status(400).send(
                {response: error}
            );
        } else {
            res.status(200).send(
                {response: info.response}
            );
        }
    });

}


function sendResponseEmail(req, res) {

    let from_address = req.body.from_address;
    let to_address = req.body.to_address;
    let subject = req.body.subject;
    let message = req.body.message;
    let extra_message = req.body.extra_message;
    let recipient = req.body.recipient;
    let signature = req.body.signature;
    let signature2 = req.body.signature2;

    let body_html = "<!DOCTYPE html>\n" +
        "<html>\n" +
        "<head>\n" +
        "<title></title>\n" +
        "<meta http-equiv='content-type' content='text/html; charset=utf-8' />\n" +
        "</head>\n" +
        "<body>\n" +
        "<table><tr><h1><th style='width: 65px'><img src='https://i.ibb.co/bF7pkW1/email-logo.gif' alt='sapphire'></th><th valign='center'> SAPPHIRE: Roster Management</th></h1></tr></table>\n" +
        "<h3>Dear " + recipient + ",</h3>\n" +
        "<h3>" + message + "</h3>\n" +
        "<h3>" + extra_message + "</h3>\n" +
        "<h3>" + signature + "</h3>\n" +
        "<h3>" + signature2 + "</h3>\n" +
        "</body>\n" +
        "</html>";

    let mailOptions = {
        from: "SAPPHIRE<<EMAIL>>", //from_address (SAFECOM have stated to use this ESO address as it's authenticated)
        replyTo: from_address,
        to: to_address,
        subject: subject,
        html: body_html
    };

    smtp_settings.sendMail(mailOptions, function (error, info) {
        if (error) {
            console.log(moment().format("DD-MM-YY HH:mm") + ' ' + error + "(login.js - sendEmail)");
            res.status(400).send(
                {response: error}
            );
        } else {
            res.status(200).send(
                {response: info.response}
            );
        }
    });

}

module.exports = REST_ROUTER;