const sql = require('mssql');
const moment = require('moment');
let path = require('path');
let bearer_token = "";

let connection;

function REST_ROUTER(router, cn) {

    connection = cn;

    let self = this;
    self.handleRoutes(router);

}


REST_ROUTER.prototype.handleRoutes = function (router) {

    //GET EMPLOYEE ROSTER
    router.get("/get_employee_roster", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }

        if (bearer_token === api_register[1].api_key) {
            getEmployeeRoster(req, res);
        } else {
            res.status(401).send();
        }
    });


    router.get("/get_all_employee_rosters", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }

        if (bearer_token === api_register[1].api_key) {
            getAllEmployeeRosters(req, res);
        } else {
            res.status(401).send();
        }
    });

};


function getEmployeeRoster(req, res) {

    const request = new sql.Request(connection);

    request.query("SELECT * FROM (SELECT DISTINCT ra.pay_id, ra.roster as perm_roster, ra.shift as perm_shift, ra.location as perm_station, (SELECT DISTINCT station_id FROM locations WHERE name = ra.location) AS perm_station_id, (SELECT TOP 1 roster FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_shift, (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_station, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) AS moved_station_id FROM roster_arrangements ra WHERE ra.date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND ra.type = 'Permanent') as results WHERE moved_roster IS NOT NULL", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")" + " - live site");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function getAllEmployeeRosters(req, res) {

    const request = new sql.Request(connection);

    request.query("SELECT DISTINCT ra.pay_id, ra.roster as perm_roster, ra.shift as perm_shift, ra.location as perm_station, (SELECT DISTINCT station_id FROM locations WHERE name = ra.location) AS perm_station_id, (SELECT TOP 1 roster FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_roster, (SELECT TOP 1 shift FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_shift, (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC) as moved_station, (SELECT station_id FROM locations WHERE name = (SELECT TOP 1 location FROM roster_arrangements WHERE date_string = ra.date_string AND pay_id = ra.pay_id AND type = 'Temporary' ORDER BY created_date DESC)) AS moved_station_id FROM roster_arrangements ra WHERE ra.date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND ra.type = 'Permanent'", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")" + " - live site");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}

module.exports = REST_ROUTER;