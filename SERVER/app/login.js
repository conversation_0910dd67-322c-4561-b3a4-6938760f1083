const msal = require('@azure/msal-node');
let nodemailer = require('nodemailer');
const sql = require('mssql');
const moment = require('moment');
let path = require('path');
let live_site = false;
let msal_config = {};
let site_url = "";
let redirectUrl = "";
let cca;
let bearer_token = "";
let origin_url = "";

let connection;

function REST_ROUTER(router, cn) {

    connection = cn;

    let self = this;
    self.handleRoutes(router);

    if (__dirname === "/home/<USER>/uat/app"){
        site_url = "https://sapphire-uat.mfs.sa.gov.au";
        redirectUrl = "https://sapphire-uat.mfs.sa.gov.au/sapphire";
        msal_config = {
            auth: {
                clientId: 'fa3499cb-c892-4f74-aa4f-9fb964242a10',
                authority: 'https://login.microsoftonline.com/4abb7af4-36d5-4cd4-b6ab-10d21f35a591/',
                clientSecret: '****************************************',
                postLogoutRedirectUri: site_url
            },
            cache: {
                cacheLocation: "sessionStorage",
                storeAuthStateInCookie: false
            }
        };
    } else if (__dirname === "/home/<USER>/live/app"){
        site_url = "https://sapphire.mfs.sa.gov.au";
        redirectUrl = "https://sapphire.mfs.sa.gov.au/sapphire";
        msal_config = {
            auth: {
                clientId: 'bccfb9b5-b200-4817-ae3d-42b745208ad2',
                authority: 'https://login.microsoftonline.com/4abb7af4-36d5-4cd4-b6ab-10d21f35a591/',
                clientSecret: '**********************************',
                postLogoutRedirectUri: site_url
            },
            cache: {
                cacheLocation: "sessionStorage",
                storeAuthStateInCookie: false
            }
        };
    } else {
        site_url = "http://localhost:6001";
        redirectUrl = "http://localhost:6001";
        msal_config = {
            auth: {
                clientId: 'bccfb9b5-b200-4817-ae3d-42b745208ad2',
                authority: 'https://login.microsoftonline.com/4abb7af4-36d5-4cd4-b6ab-10d21f35a591/',
                clientSecret: '**********************************',
                postLogoutRedirectUri: site_url
            },
            cache: {
                cacheLocation: "sessionStorage",
                storeAuthStateInCookie: false
            }
        }
    }

    cca = new msal.ConfidentialClientApplication(msal_config);
}

let smtp_settings = nodemailer.createTransport({
    host: "mail.smtp2go.com",
    port: 2525,
    secure: false,
    auth: {
        user: "sapphire",
        pass: "YzFoanJ4Zmg0c2Yw"
    }
});


REST_ROUTER.prototype.handleRoutes = function (router) {


    //OAUTH ROUTES
    router.get('/auth', (req, res) => {
        const authCodeUrlParameters = {
            scopes: ["user.read"],
            redirectUri: redirectUrl + "/login/callback",
            postLogoutRedirectUri: site_url,
            responseMode: "query"
        };

        // get url to sign user in and consent to scopes needed for application
        cca.getAuthCodeUrl(authCodeUrlParameters).then((response) => {
            res.redirect(response);
        }).catch((error) => {
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + error + " | login.js - /auth");
            res.status(500).send("Auth error");
        });
    });

    router.get('/callback', (req, res) => {

        let redirectURi = "";

        const tokenRequest = {
            code: req.query.code,
            scopes: ["user.read"],
            redirectUri: redirectUrl + "/login/callback",
            postLogoutRedirectUri: site_url
        };

        cca.acquireTokenByCode(tokenRequest).then((response) => {

            res.cookie('access_token', response.accessToken, {
                httpOnly: true,
                secure: false,
                sameSite: 'lax'
            });

            if (live_site === true) {
                if (__dirname === "/home/<USER>/live/app") {
                    redirectURi = "https://sapphire.mfs.sa.gov.au";
                } else {
                    redirectURi = "http://localhost:63343/NEWSapphire";
                }
            } else {
                if (__dirname === "/home/<USER>/uat/app") {
                    redirectURi = "https://sapphire-uat.mfs.sa.gov.au";
                } else {
                    redirectURi = "http://localhost:63343/NEWSapphire";
                }
            }

            console.log(redirectURi);

            res.redirect(redirectURi);

        }).catch((error) => {
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + error + " | login.js - /callback");
        });
    });

    router.get('/logout', (req, res) => {
        // Clear cookie
        res.clearCookie('access_token', {
            httpOnly: true,
            secure: false, // Set to true in production with HTTPS
            sameSite: 'lax'
        });

        let logoutUrl = "";

        if (live_site === true) {
            if (__dirname === "/home/<USER>/live/app") {
                logoutUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri=${encodeURIComponent('https://sapphire.mfs.sa.gov.au/index.html')}`;
            } else {
                logoutUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri=${encodeURIComponent('http://localhost:63343/NEWSapphire/index.html')}`;
            }
        } else {
            if (__dirname === "/home/<USER>/uat/app") {
                logoutUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri=${encodeURIComponent('https://sapphire-uat.mfs.sa.gov.au/index.html')}`;
            } else {
                logoutUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/logout?post_logout_redirect_uri=${encodeURIComponent('http://localhost:63343/NEWSapphire/index.html')}`;
            }
        }

        res.redirect(logoutUrl);
    });


    // SEND EMAILS
    router.post('/send_email', function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sendEmail(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.post('/send_attachment_email', function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sendEmailwithAttachment(req, res);
        } else {
            res.status(401).send();
        }
    });

    //DEFAULT ROSTER VIEW
    router.get("/current_roster", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getCurrentRoster(req, res);
        } else {
            res.status(401).send();
        }
    });


    //SET CURRENT HOME STATION
    router.put('/update_home_station', function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            updateCurrentHomeStation(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get('/get_user_pl', async function (req, res) {
        try {
            const bearer_token = req.headers.authorization?.split(" ")[1];
            const origin_url = req.headers.referer;

            if (
                bearer_token === api_register[0].api_key &&
                (
                    origin_url.includes("https://sapphire.mfs.sa.gov.au/") ||
                    origin_url === "http://localhost:63343/" ||
                    origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/")
                )
            ) {
                await get_user_pl(req, res);
            } else {
                res.status(401).send();
            }
        } catch (err) {
            console.error("Error in /get_user_pl route:", err);
            res.status(500).send({ error: "Internal server error" });
        }
    });


    router.get('/me', async function (req, res) {

        origin_url = req.headers.referer;
        const accessToken = req.cookies.access_token;

        if (origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/")) {

            if (!accessToken) {
                res.status(401).send();
            } else {
                try {
                    const graphRes = await fetch('https://graph.microsoft.com/v1.0/me/?$select=mail,userPrincipalName,id,EmployeeId', {
                        headers: {
                            Authorization: `Bearer ${accessToken}`
                        }
                    });

                    if (!graphRes.ok) {
                        // Check if it's an InvalidAuthenticationToken error
                        if (graphRes.status === 401) {
                            try {
                                const errorData = await graphRes.json();
                                if (errorData.error && errorData.error.code === 'InvalidAuthenticationToken') {
                                    // Clear the invalid token
                                    res.clearCookie('access_token', {
                                        httpOnly: true,
                                        secure: false,
                                        sameSite: 'lax'
                                    });
                                    return res.status(401).json({ error: 'InvalidAuthenticationToken', message: 'Token expired or invalid' });
                                }
                            } catch (parseError) {
                                console.log(moment().format("DD-MM-YY HH:mm") + " | Error parsing Graph API error response: " + parseError + " | (" + path.basename(__filename) + " / /me)");
                            }
                        }
                        return res.status(500).send();
                    }

                    const userData = await graphRes.json();

                    try {
                        await updateUserToken(userData.employeeId, accessToken);

                        const response = await getUserData(userData.employeeId);

                        if (!response || response.length === 0) {
                            return res.status(500).send();
                        }

                        const response2 = await getUserViews(response[0].permission_level, userData.employeeId);

                        if (!response2 || response2.length === 0) {
                            return res.status(500).send();
                        }

                        res.json({
                            views: response2,
                            data: response[0],
                            api_key: api_register[0].api_key
                        });

                    } catch (err) {
                        console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | " + userData.employeeId + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
                        res.status(500).send();
                    }

                } catch (err) {
                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | No Pay ID | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
                    res.status(500).json({error: 'Internal server error'});
                }
            }

        } else {
            res.status(401).send();
        }
    });

    router.get('/get_user_id', async function (req, res) {
        try {
            const bearer_token = req.headers.authorization?.split(" ")[1];
            const origin_url = req.headers.referer;

            if (
                bearer_token === api_register[0].api_key &&
                (
                    origin_url.includes("https://sapphire.mfs.sa.gov.au/") ||
                    origin_url === "http://localhost:63343/" ||
                    origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/")
                )
            ) {
                await get_user_pay_id(req, res);
            } else {
                res.status(401).send();
            }
        } catch (err) {
            console.error("Error in /get_user_pl route:", err);
            res.status(500).send({ error: "Internal server error" });
        }
    });


};


async function get_user_pay_id(req, res){

    const userToken = req.cookies.access_token;

    try {
        const graphRes = await fetch('https://graph.microsoft.com/v1.0/me/?$select=mail,userPrincipalName,id,EmployeeId', {
            headers: {
                Authorization: `Bearer ${userToken}`
            }
        });

        if (!graphRes.ok) {
            // Check if it's an InvalidAuthenticationToken error
            if (graphRes.status === 401) {
                try {
                    const errorData = await graphRes.json();
                    if (errorData.error && errorData.error.code === 'InvalidAuthenticationToken') {
                        return res.status(401).json({ error: 'InvalidAuthenticationToken', message: 'Token expired or invalid' });
                    }
                } catch (parseError) {
                    console.log(moment().format("DD-MM-YY HH:mm") + " | Error parsing Graph API error response: " + parseError + " | (" + path.basename(__filename) + " / get_user_pay_id)");
                }
            }
            return res.status(401).send(0);
        }

        const userData = await graphRes.json();

        res.status(200).send(userData.employeeId);

    } catch (err) {
        console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | No Pay ID | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
        res.status(500).json({error: 'Internal server error'});
    }
}

async function getUserViews(user_permission_level, user_logged_in) {

    let report_menu_data = [];
    let admin_menu_data = [];
    let leave_menu_data = [];
    let respond52_menu_data = [];

    setReportPermissionViews();
    setAdminPermissionViews();
    setLeavePermissionViews();
    setRespond52PermissionViews();

    return {
        "leave_views": leave_menu_data,
        "admin_views": admin_menu_data,
        "r52_views": respond52_menu_data,
        "report_views": report_menu_data
    }

    function setReportPermissionViews() {
        if (user_permission_level === 1) {
            report_menu_data = [
                {
                    id: "reporting_leave_taken_breakdown",
                    icon: "fas fa-chart-bar",
                    value: "Actual Leave Taken Report",
                },
                {
                    id: "reporting_bookings",
                    icon: "fas fa-chart-bar",
                    value: "Creation Report",
                },
                {
                    id: "reporting_deletion",
                    icon: "fas fa-chart-bar",
                    value: "Deletion Report",
                },
                {
                    id: "reporting_overtime",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Report",
                },
                {
                    id: "reporting_overtime_summary",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Summary Report",
                },
                {
                    id: "reporting_staff_overtime_summary",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Staff Summary Report",
                },
                {
                    id: "reporting_reason_overtime_summary",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Reason Summary Report",
                },
                {
                    id: "reporting_travel",
                    icon: "fas fa-chart-bar",
                    value: "Travel Report",
                },
                {
                    id: "reporting_sickness",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Report",
                },
                {
                    id: "reporting_sickness_summary",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Summary Report",
                },
                {
                    id: "reporting_sickness_certificate",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Certificate Report",
                },
                {
                    id: "reporting_supplementary",
                    icon: "fas fa-chart-bar",
                    value: "Supplementary Report",
                },
                {
                    id: "reporting_staff_movement",
                    icon: "fas fa-chart-bar",
                    value: "Staff Movement Report",
                },
                {
                    id: "reporting_skill_codes",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Report",
                },
                {
                    id: "reporting_skill_codes_availability",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Availability Report",
                },
                {
                    id: "reporting_roster_arrangements",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Report",
                },
                {
                    id: "reporting_ra_summary",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Summary Report",
                },
                {
                    id: "reporting_ras_by_rank",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements By Rank Report",
                },
                {
                    id: "reporting_shift_summary",
                    icon: "fas fa-chart-bar",
                    value: "Shift Summary Report",
                },
                {id: "reporting_rrl", icon: "fas fa-chart-bar", value: "RRL Report"},
                {
                    id: "reporting_booking_errors",
                    icon: "fas fa-chart-bar",
                    value: "Booking Errors Report",
                },
                {
                    id: "reporting_covid_vax_status",
                    icon: "fas fa-chart-bar",
                    value: "COVID Vaccination Status Report",
                },
                {
                    id: "reporting_covid_vax_summary",
                    icon: "fas fa-chart-bar",
                    value: "COVID Vaccination Summary Report",
                },
                {
                    id: "reporting_covid_status_summary",
                    icon: "fas fa-chart-bar",
                    value: "COVID Status Summary Report",
                },
                {
                    id: "reporting_station_preference",
                    icon: "fas fa-chart-bar",
                    value: "Station Preference Report",
                },
                {
                    id: "reporting_overtime_fatigue",
                    icon: "fas fa-chart-bar",
                    value: "Overtime & Fatigue Report",
                },
                {
                    id: "reporting_overtime_distance_fatigue",
                    icon: "fas fa-chart-bar",
                    value: "Overtime & Fatigue By Distance Report",
                },
            ];
        } else if (user_permission_level === 2) {
            report_menu_data = [
                {
                    id: "reporting_leave_taken_breakdown",
                    icon: "fas fa-chart-bar",
                    value: "Actual Leave Taken Report",
                },
                {
                    id: "reporting_bookings",
                    icon: "fas fa-chart-bar",
                    value: "Creation Report",
                },
                {
                    id: "reporting_deletion",
                    icon: "fas fa-chart-bar",
                    value: "Deletion Report",
                },
                {
                    id: "reporting_overtime_summary",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Summary Report",
                },
                {
                    id: "reporting_staff_overtime_summary",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Staff Summary Report",
                },
                {
                    id: "reporting_reason_overtime_summary",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Reason Summary Report",
                },
                {
                    id: "reporting_sickness",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Report",
                },
                {
                    id: "reporting_sickness_summary",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Summary Report",
                },
                {
                    id: "reporting_skill_codes",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Report",
                },
                {
                    id: "reporting_skill_codes_availability",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Availability Report",
                },
                {
                    id: "reporting_roster_arrangements",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Report",
                },
                {
                    id: "reporting_ra_summary",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Summary Report",
                },
                {
                    id: "reporting_ras_by_rank",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements By Rank Report",
                },
                {
                    id: "reporting_shift_summary",
                    icon: "fas fa-chart-bar",
                    value: "Shift Summary Report",
                },
                {id: "reporting_rrl", icon: "fas fa-chart-bar", value: "RRL Report"},
                {
                    id: "reporting_station_preference",
                    icon: "fas fa-chart-bar",
                    value: "Station Preference Report",
                },
                {
                    id: "reporting_overtime_fatigue",
                    icon: "fas fa-chart-bar",
                    value: "Overtime & Fatigue Report",
                },
                {
                    id: "reporting_overtime_distance_fatigue",
                    icon: "fas fa-chart-bar",
                    value: "Overtime & Fatigue By Distance Report",
                },
            ];
        } else if (user_permission_level === 3) {
            if (
                user_logged_in === 2302001 ||
                user_logged_in === 2302300 ||
                user_logged_in === 2302442 ||
                user_logged_in === 2302037
            ) {
                report_menu_data = [
                    {
                        id: "reporting_overtime",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Report",
                    },
                    {
                        id: "reporting_overtime_summary",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Summary Report",
                    },
                    {
                        id: "reporting_staff_overtime_summary",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Staff Summary Report",
                    },
                    {
                        id: "reporting_reason_overtime_summary",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Reason Summary Report",
                    },
                    {
                        id: "reporting_sickness_summary",
                        icon: "fas fa-chart-bar",
                        value: "Sickness Summary Report",
                    },
                    {
                        id: "reporting_supplementary",
                        icon: "fas fa-chart-bar",
                        value: "Supplementary Report",
                    },
                    {
                        id: "reporting_skill_codes",
                        icon: "fas fa-chart-bar",
                        value: "Skill Codes Report",
                    },
                    {
                        id: "reporting_skill_codes_availability",
                        icon: "fas fa-chart-bar",
                        value: "Skill Codes Availability Report",
                    },
                    {
                        id: "reporting_roster_arrangements",
                        icon: "fas fa-chart-bar",
                        value: "Roster Arrangements Report",
                    },
                    {
                        id: "reporting_ra_summary",
                        icon: "fas fa-chart-bar",
                        value: "Roster Arrangements Summary Report",
                    },
                    {
                        id: "reporting_ras_by_rank",
                        icon: "fas fa-chart-bar",
                        value: "Roster Arrangements By Rank Report",
                    },
                    {
                        id: "reporting_shift_summary",
                        icon: "fas fa-chart-bar",
                        value: "Shift Summary Report",
                    },
                    {id: "reporting_rrl", icon: "fas fa-chart-bar", value: "RRL Report"},
                    {
                        id: "reporting_covid_vax_status",
                        icon: "fas fa-chart-bar",
                        value: "COVID Vaccination Status Report",
                    },
                    {
                        id: "reporting_covid_status_summary",
                        icon: "fas fa-chart-bar",
                        value: "COVID Status Summary Report",
                    },
                    {
                        id: "reporting_overtime_fatigue",
                        icon: "fas fa-chart-bar",
                        value: "Overtime & Fatigue Report",
                    },
                    {
                        id: "reporting_overtime_distance_fatigue",
                        icon: "fas fa-chart-bar",
                        value: "Overtime & Fatigue By Distance Report",
                    },
                ];
            } else {
                report_menu_data = [
                    {
                        id: "reporting_overtime",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Report",
                    },
                    {
                        id: "reporting_overtime_summary",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Summary Report",
                    },
                    {
                        id: "reporting_staff_overtime_summary",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Staff Summary Report",
                    },
                    {
                        id: "reporting_reason_overtime_summary",
                        icon: "fas fa-chart-bar",
                        value: "Overtime Reason Summary Report",
                    },
                    {
                        id: "reporting_sickness_summary",
                        icon: "fas fa-chart-bar",
                        value: "Sickness Summary Report",
                    },
                    {
                        id: "reporting_supplementary",
                        icon: "fas fa-chart-bar",
                        value: "Supplementary Report",
                    },
                    {
                        id: "reporting_skill_codes",
                        icon: "fas fa-chart-bar",
                        value: "Skill Codes Report",
                    },
                    {
                        id: "reporting_skill_codes_availability",
                        icon: "fas fa-chart-bar",
                        value: "Skill Codes Availability Report",
                    },
                    {
                        id: "reporting_roster_arrangements",
                        icon: "fas fa-chart-bar",
                        value: "Roster Arrangements Report",
                    },
                    {
                        id: "reporting_ra_summary",
                        icon: "fas fa-chart-bar",
                        value: "Roster Arrangements Summary Report",
                    },
                    {
                        id: "reporting_ras_by_rank",
                        icon: "fas fa-chart-bar",
                        value: "Roster Arrangements By Rank Report",
                    },
                    {
                        id: "reporting_shift_summary",
                        icon: "fas fa-chart-bar",
                        value: "Shift Summary Report",
                    },
                    {id: "reporting_rrl", icon: "fas fa-chart-bar", value: "RRL Report"},
                    {
                        id: "reporting_overtime_fatigue",
                        icon: "fas fa-chart-bar",
                        value: "Overtime & Fatigue Report",
                    },
                    {
                        id: "reporting_overtime_distance_fatigue",
                        icon: "fas fa-chart-bar",
                        value: "Overtime & Fatigue By Distance Report",
                    },
                ];
            }
        } else if (user_permission_level === 4) {
            report_menu_data = [
                {
                    id: "reporting_overtime",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Report",
                },
                {
                    id: "reporting_overtime_summary",
                    icon: "fas fa-chart-bar",
                    value: "Overtime Summary Report",
                },
                {
                    id: "reporting_sickness_summary",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Summary Report",
                },
                {
                    id: "reporting_supplementary",
                    icon: "fas fa-chart-bar",
                    value: "Supplementary Report",
                },
                {
                    id: "reporting_skill_codes",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Report",
                },
                {
                    id: "reporting_skill_codes_availability",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Availability Report",
                },
                {
                    id: "reporting_roster_arrangements",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Report",
                },
                {
                    id: "reporting_ra_summary",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Summary Report",
                },
                {
                    id: "reporting_overtime_fatigue",
                    icon: "fas fa-chart-bar",
                    value: "Overtime & Fatigue Report",
                },
                {
                    id: "reporting_overtime_distance_fatigue",
                    icon: "fas fa-chart-bar",
                    value: "Overtime & Fatigue By Distance Report",
                },
            ];
        } else if (user_permission_level === 5) {
            report_menu_data = [
                {
                    id: "reporting_sickness_summary",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Summary Report",
                },
                {
                    id: "reporting_skill_codes",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Report",
                },
                {
                    id: "reporting_skill_codes_availability",
                    icon: "fas fa-chart-bar",
                    value: "Skill Codes Availability Report",
                },
                {
                    id: "reporting_roster_arrangements",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Report",
                },
                {
                    id: "reporting_ra_summary",
                    icon: "fas fa-chart-bar",
                    value: "Roster Arrangements Summary Report",
                },
            ];

        } else if (user_permission_level === 6) {
            report_menu_data = [
                {
                    id: "reporting_sickness_summary",
                    icon: "fas fa-chart-bar",
                    value: "Sickness Summary Report",
                },
            ];
        }

    }

    function setAdminPermissionViews() {
        if (user_permission_level === 1) {
            admin_menu_data = [
                {id: "admin_search", icon: "fas fa-search", value: "Search"},
                {id: "locations", icon: "fas fa-building", value: "Locations"},
                {id: "location_groups", icon: "fas fa-city", value: "Location Groups"},
                {id: "shifts", icon: "fas fa-user-clock", value: "Shifts"},
                {id: "shift_groups", icon: "fas fa-clock", value: "Shift Groups"},
                {id: "rosters", icon: "fas fa-calendar-day", value: "Rosters"},
                {
                    id: "roster_arrangement",
                    icon: "fas fa-calendar-alt",
                    value: "Roster Arrangement",
                },
                {id: "leave_types", icon: "fas fa-business-time", value: "Leave Types"},
                {
                    id: "shift_types",
                    icon: "fas fa-bezier-curve",
                    value: "Relieving Codes",
                },
                {id: "skill_codes", icon: "fas fa-address-card", value: "Skill Codes"},
                {id: "activity_types", icon: "fas fa-tasks", value: "Overtime Types"},
                {id: "covid_vaccines", icon: "fas fa-syringe", value: "COVID Vaccines"},
                {
                    id: "tools",
                    icon: "fas fa-tools",
                    value: "Tools",
                    data: [
                        {id: "tools_login_message", value: "Login Message"},
                        {id: "tools_public_holidays", value: "Public Holidays"},
                        {id: "tools_admin_settings", value: "Admin Settings"},
                    ],
                },
            ];

        } else if (user_permission_level === 2) {
            admin_menu_data = [
                {id: "admin_search", icon: "fas fa-search", value: "Search"},
                {
                    id: "roster_arrangement",
                    icon: "fas fa-calendar-alt",
                    value: "Roster Arrangement",
                },
            ];

        } else if (user_permission_level === 3) {
            if (
                user_logged_in === 2302001 ||
                user_logged_in === 2302300 ||
                user_logged_in === 2302442 ||
                user_logged_in === 2302037
            ) {
                admin_menu_data = [
                    {id: "admin_search", icon: "fas fa-search", value: "Search"},
                    {
                        id: "covid_vaccines",
                        icon: "fas fa-syringe",
                        value: "COVID Vaccines",
                    },
                ];
            } else {
                admin_menu_data = [
                    {id: "admin_search", icon: "fas fa-search", value: "Search"},
                ];
            }

        } else if (user_permission_level === 4 || user_permission_level === 5) {
            if (
                user_logged_in === 2302001 ||
                user_logged_in === 2302300 ||
                user_logged_in === 2302442 ||
                user_logged_in === 2302037
            ) {
                admin_menu_data = [
                    {id: "admin_search", icon: "fas fa-search", value: "Search"},
                    {
                        id: "covid_vaccines",
                        icon: "fas fa-syringe",
                        value: "COVID Vaccines",
                    },
                ];
            } else {
                admin_menu_data = [
                    {id: "admin_search", icon: "fas fa-search", value: "Search"},
                ];
            }

        } else if (user_permission_level === 6) {
            admin_menu_data = [];

        }

    }

    function setLeavePermissionViews() {
        if (user_permission_level === 1 || user_permission_level === 2) {
            leave_menu_data = [
                {
                    id: "bookings",
                    icon: "fas fa-clipboard-list",
                    value: "Bookings",
                    data: [
                        {id: "booking_requests", value: "Requests"},
                        {id: "booking_edit", value: "Search"},
                        {id: "booking_leave_counts", value: "Leave Counts"},
                        {id: "booking_log", value: "Log"},
                    ],
                },
                {
                    id: "leave",
                    icon: "fas fa-exchange-alt",
                    value: "Leave (RRL)",
                    data: [
                        {id: "leave_swap", value: "Leave Swap"},
                        {id: "leave_swap_list", value: "Leave Swap List"},
                        {id: "leave_groups", value: "Leave Groups"},
                        {id: "leave_log", value: "Log"},
                    ],
                },
                {
                    id: "staff_movement",
                    icon: "fas fa-route",
                    value: "Staff Movement",
                    data: [
                        {id: "acting_up_log", value: "Acting Up Log"},
                        {id: "standby_log", value: "Standby Log"},
                        {id: "staff_movement_log", value: "Staff Movement Log"},
                    ],
                },
                {
                    id: "overtime",
                    icon: "fas fa-clock",
                    value: "Overtime",
                    data: [{id: "overtime_log", value: "Log"}],
                },
                {
                    id: "travel",
                    icon: "fas fa-road",
                    value: "Travel",
                    data: [
                        {id: "travel_create", value: "Create"},
                        {id: "travel_requests", value: "Claims"},
                    ],
                },
                {
                    id: "sickness",
                    icon: "fas fa-file-medical",
                    value: "Sickness",
                    data: [
                        {id: "sick_certificates", value: "Sick Certificates"},
                        {id: "sick_bookings_log", value: "Sick Bookings Log"},
                    ],
                },
                {
                    id: "soil_toil_balances",
                    icon: "fas fa-balance-scale",
                    value: "Leave Balances",
                },
                {
                    id: "day_work",
                    icon: "fas fa-sun",
                    value: "Day Work",
                    data: [{id: "day_work_requests", value: "Leave Requests"}],
                },
            ];
        } else if (user_permission_level === 3) {
            leave_menu_data = [
                {
                    id: "bookings",
                    icon: "fas fa-clipboard-list",
                    value: "Bookings",
                    data: [
                        {id: "booking_requests", value: "Requests"},
                        {id: "booking_edit", value: "Search"},
                        {id: "booking_leave_counts", value: "Leave Counts"},
                        {id: "booking_log", value: "Log"},
                    ],
                },
                {
                    id: "leave",
                    icon: "fas fa-exchange-alt",
                    value: "Leave (RRL)",
                    data: [{id: "leave_groups", value: "Leave Groups"}],
                },
                {
                    id: "staff_movement",
                    icon: "fas fa-route",
                    value: "Staff Movement",
                    data: [
                        {id: "acting_up_log", value: "Acting Up Log"},
                        {id: "standby_log", value: "Standby Log"},
                        {id: "staff_movement_log", value: "Staff Movement Log"},
                    ],
                },
                {
                    id: "overtime",
                    icon: "fas fa-clock",
                    value: "Overtime",
                    data: [{id: "overtime_log", value: "Log"}],
                },
                {
                    id: "travel",
                    icon: "fas fa-road",
                    value: "Travel",
                    data: [
                        {id: "travel_create", value: "Create"},
                        {id: "travel_requests", value: "Claims"},
                    ],
                },
                {
                    id: "sickness",
                    icon: "fas fa-file-medical",
                    value: "Sickness",
                    data: [{id: "sick_certificates", value: "Sick Certificates"}],
                },
                {
                    id: "soil_toil_balances",
                    icon: "fas fa-balance-scale",
                    value: "Leave Balances",
                },
                {
                    id: "day_work",
                    icon: "fas fa-sun",
                    value: "Day Work",
                    data: [{id: "day_work_requests", value: "Leave Requests"}],
                },
            ];
        } else if (user_permission_level === 4) {
            leave_menu_data = [
                {
                    id: "bookings",
                    icon: "fas fa-clipboard-list",
                    value: "Bookings",
                    data: [{id: "booking_log", value: "Log"}],
                },
                {
                    id: "leave",
                    icon: "fas fa-exchange-alt",
                    value: "Leave (RRL)",
                    data: [{id: "leave_groups", value: "Leave Groups"}],
                },
                {
                    id: "staff_movement",
                    icon: "fas fa-route",
                    value: "Staff Movement",
                    data: [
                        {id: "acting_up_log", value: "Acting Up Log"},
                        {id: "standby_log", value: "Standby Log"},
                        {id: "staff_movement_log", value: "Staff Movement Log"},
                    ],
                },
                {
                    id: "overtime",
                    icon: "fas fa-clock",
                    value: "Overtime",
                    data: [{id: "overtime_log", value: "Log"}],
                },
                {
                    id: "travel",
                    icon: "fas fa-road",
                    value: "Travel",
                    data: [
                        {id: "travel_create", value: "Create"},
                        {id: "travel_requests", value: "Claims"},
                    ],
                },
                {
                    id: "sickness",
                    icon: "fas fa-file-medical",
                    value: "Sickness",
                    data: [{id: "sick_certificates", value: "Sick Certificates"}],
                },
                {
                    id: "soil_toil_balances",
                    icon: "fas fa-balance-scale",
                    value: "Leave Balances",
                },
                {
                    id: "day_work",
                    icon: "fas fa-sun",
                    value: "Day Work",
                    data: [{id: "day_work_requests", value: "Leave Requests"}],
                },
            ];
        } else if (user_permission_level === 5) {
            leave_menu_data = [
                {
                    id: "bookings",
                    icon: "fas fa-clipboard-list",
                    value: "Bookings",
                    data: [{id: "booking_log", value: "Log"}],
                },
                {
                    id: "leave",
                    icon: "fas fa-exchange-alt",
                    value: "Leave (RRL)",
                    data: [{id: "leave_groups", value: "Leave Groups"}],
                },
                {
                    id: "staff_movement",
                    icon: "fas fa-route",
                    value: "Staff Movement",
                    data: [
                        {id: "acting_up_log", value: "Acting Up Log"},
                        {id: "standby_log", value: "Standby Log"},
                        {id: "staff_movement_log", value: "Staff Movement Log"},
                    ],
                },
                {
                    id: "overtime",
                    icon: "fas fa-clock",
                    value: "Overtime",
                    data: [{id: "overtime_log", value: "Log"}],
                },
                {
                    id: "sickness",
                    icon: "fas fa-file-medical",
                    value: "Sickness",
                    data: [{id: "sick_certificates", value: "Sick Certificates"}],
                },
                {
                    id: "soil_toil_balances",
                    icon: "fas fa-balance-scale",
                    value: "Leave Balances",
                },
            ];
        } else if (user_permission_level === 6) {
            leave_menu_data = [
                {
                    id: "bookings",
                    icon: "fas fa-clipboard-list",
                    value: "Bookings",
                    data: [{id: "booking_log", value: "Log"}],
                },
                {
                    id: "leave",
                    icon: "fas fa-exchange-alt",
                    value: "Leave (RRL)",
                    data: [{id: "leave_groups", value: "Leave Groups"}],
                },
                {
                    id: "soil_toil_balances",
                    icon: "fas fa-balance-scale",
                    value: "Leave Balances",
                },
            ];
        }
    }


    function setRespond52PermissionViews() {
        respond52_menu_data = [
            {
                id: "respond52_incident",
                icon: "fas fa-digital-tachograph",
                value: "Individual Incident Report",
            },
            {
                id: "respond52_incident_type_summary",
                icon: "fas fa-chart-bar",
                value: "Incident Type Summary Report",
            },
            {
                id: "respond52_incident_location_summary",
                icon: "fas fa-warehouse",
                value: "Incident Location Summary Report",
            },
            {
                id: "respond52_riding_position_location",
                icon: "fas fa-chair",
                value: "Riding Position Location Report",
            },
            {
                id: "respond52_incident_info_search",
                icon: "fas fa-search",
                value: "Incident Info Search Report",
            },
        ];
    }

}


async function getUserData(employeeId) {
    const request = new sql.Request(connection);

    request.input('pay_id', employeeId);

    return new Promise((resolve, reject) => {
        request.query("SELECT TOP 1 * FROM employees WHERE pay_id = @pay_id ORDER BY id DESC", (err, result) => {
            if (err) {
                console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getUserData)");
                return resolve([]); // Return empty array on error
            }

            if (result.recordset) {
                resolve(result.recordset);
            } else {
                resolve([]);
            }
        });
    });
}



async function get_user_pl(req, res) {

    let user_token = req.cookies.access_token;

    let pay_id = await getUserPayIDFromMS(user_token);

    // Check if token was invalid
    if (pay_id === -1) {
        // Clear the invalid token
        res.clearCookie('access_token', {
            httpOnly: true,
            secure: false,
            sameSite: 'lax'
        });
        return res.status(401).json({ error: 'InvalidAuthenticationToken', message: 'Token expired or invalid' });
    }

    const request = new sql.Request(connection);

    request.input('pay_id', sql.Int, pay_id);

    request.query("SELECT TOP 1 permission_level FROM employees WHERE pay_id = @pay_id", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send([]);
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}

function updateCurrentHomeStation(req, res) {

    let pay_id = req.body.pay_id;
    let home_station = req.body.home_station;
    let home_station_id = req.body.home_station_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('pay_id', sql.Int);
    ps.input('home_station', sql.VarChar(50));
    ps.input('home_station_id', sql.Int);

    // Prepare statement
    ps.prepare("UPDATE employees SET home_station_id = @home_station_id, home_station = @home_station WHERE pay_id = @pay_id", err => {

        if (!err) {

            // Execute statement
            ps.execute({
                pay_id: pay_id,
                home_station: home_station,
                home_station_id: home_station_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });


                    res.status(200).send(
                        {response: 'OK'}
                    );
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


async function updateUserToken(pay_id, access_token) {

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('pay_id', sql.Int);
    ps.input('access_token', sql.VarChar(8000));

    // Prepare statement
    ps.prepare("UPDATE employees SET session_token = @access_token WHERE pay_id = @pay_id", err => {

        if (!err) {

            // Execute statement
            ps.execute({
                pay_id: pay_id,
                access_token: access_token

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / updateUserToken)");

                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / updateUserToken)");
        }
    });

}

async function getUserPayIDFromMS(userToken) {
    try {
        const graphRes = await fetch('https://graph.microsoft.com/v1.0/me/?$select=mail,userPrincipalName,id,EmployeeId', {
            headers: {
                Authorization: `Bearer ${userToken}`
            }
        });

        if (!graphRes.ok) {
            // Check if it's an InvalidAuthenticationToken error
            if (graphRes.status === 401) {
                try {
                    const errorData = await graphRes.json();
                    if (errorData.error && errorData.error.code === 'InvalidAuthenticationToken') {
                        console.log(moment().format("DD-MM-YY HH:mm") + " | InvalidAuthenticationToken error in getUserPayIDFromMS | (" + path.basename(__filename) + " / getUserPayIDFromMS)");
                        return -1; // Return -1 to indicate invalid token
                    }
                } catch (parseError) {
                    console.log(moment().format("DD-MM-YY HH:mm") + " | Error parsing Graph API error response: " + parseError + " | (" + path.basename(__filename) + " / getUserPayIDFromMS)");
                }
            }
            return 0;
        }

        const userData = await graphRes.json();

        return userData.employeeId || 0;

    } catch (err) {
        console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | No Pay ID | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
        res.status(500).json({error: 'Internal server error'});
    }
}


function sendEmail(req, res) {

    if (live_site === true) {

        let from_address = req.body.from_address;
        let to_address = req.body.to_address;
        let subject = req.body.subject;
        let message = req.body.message;
        let extra_message = req.body.extra_message;
        let recipient = req.body.recipient;
        let signature = req.body.signature;
        let signature2 = req.body.signature2;

        let body_html = "<!DOCTYPE html>\n" +
            "<html>\n" +
            "<head>\n" +
            "<title></title>\n" +
            "<meta http-equiv='content-type' content='text/html; charset=utf-8' />\n" +
            "</head>\n" +
            "<body>\n" +
            "<table><tr><h1><th style='width: 65px'><img src='https://i.ibb.co/bF7pkW1/email-logo.gif' alt='sapphire'></th><th valign='center'> SAPPHIRE: Roster Management</th></h1></tr></table>\n" +
            "<h3>Dear " + recipient + ",</h3>\n" +
            "<h3>" + message + "</h3>\n" +
            "<h3>" + extra_message + "</h3>\n" +
            "<h3>" + signature + "</h3>\n" +
            "<h3>" + signature2 + "</h3>\n" +
            "</body>\n" +
            "</html>";

        let mailOptions = {
            from: "SAPPHIRE<<EMAIL>>", //from_address (SAFECOM have stated to use this ESO address as it's authenticated)
            replyTo: from_address,
            to: to_address,
            subject: subject,
            html: body_html
        };

        if (to_address) {
            smtp_settings.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + error + " | From: " + from_address + " | To: " + to_address + " (login.js - sendEmail)");
                    res.status(400).send(
                        {response: error}
                    );
                } else {
                    res.status(200).send(
                        {response: info.response}
                    );
                }
            });
        } else {
            res.status(400).send(
                {response: "error"}
            );
        }
    } else {
        res.status(400).send(
            {response: "error"}
        );
    }
}


function sendEmailwithAttachment(req, res) {

    if (live_site === true) {

        let from_address = req.body.from_address;
        let to_address = req.body.to_address;
        let subject = req.body.subject;
        let file_name = req.body.file_name;
        let sender_name = "Regards, " + "</br>" + req.body.sender_name;

        let message = "Please find attached the file " + file_name;

        let body_html = "<!DOCTYPE html>\n" +
            "<html>\n" +
            "<head>\n" +
            "<title></title>\n" +
            "<meta http-equiv='content-type' content='text/html; charset=utf-8' />\n" +
            "</head>\n" +
            "<body>\n" +
            "<table><tr><h1><th style='width: 65px'><img src='https://i.ibb.co/bF7pkW1/email-logo.gif' alt='sapphire'></th><th valign='center'> SAPPHIRE: Roster Management</th></h1></tr></table>\n" +
            "<h3>" + message + "</h3>\n" +
            "<h3>" + sender_name + "</h3>\n" +
            "</body>\n" +
            "</html>";

        let attachmentString = req.body.attachment;
        let toBase64 = attachmentString.replace("data:application/xlsx;base64,", "");

        let mailOptions = {
            from: "SAPPHIRE<<EMAIL>>", //from_address (SAFECOM have stated to use this ESO address as it's authenticated)
            replyTo: from_address,
            to: to_address,
            subject: subject,
            html: body_html,
            attachments: [
                {
                    filename: file_name,
                    content: Buffer.from(toBase64, 'base64')
                }
            ]
        };

        if (to_address) {
            smtp_settings.sendMail(mailOptions, function (error, info) {
                if (error) {
                    console.log(moment().format("DD-MM-YY HH:mm") + ' ' + error + " | From: " + from_address + " | To: " + to_address + " (login.js - sendEmailwithAttachment)");
                    res.status(400).send(
                        {response: error}
                    );
                } else {
                    res.status(200).send(
                        {response: info.response}
                    );
                }
            });
        } else {
            res.status(400).send(
                {response: "error"}
            );
        }
    } else {
        res.status(400).send(
            {response: "error"}
        );
    }

}


function getCurrentRoster(req, res) {

    let pay_id = req.query.pay_id;

    const request = new sql.Request(connection);

    request.input('pay_id', sql.Int, pay_id);

    request.query("SELECT TOP 1 roster, shift, location, rank, (SELECT station_id FROM locations WHERE locations.name = roster_arrangements.location) as curr_home_station, (SELECT location FROM roster_arrangements WHERE pay_id = @pay_id AND date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') AS perm_home_station, (SELECT station_id FROM roster_arrangements JOIN locations ON roster_arrangements.location = locations.name WHERE pay_id = @pay_id AND date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') AND type = 'Permanent') AS perm_home_station_id FROM roster_arrangements WHERE pay_id = @pay_id AND date_string = FORMAT(SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time', 'yyyyMMdd') ORDER BY created_date DESC", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


module.exports = REST_ROUTER;
