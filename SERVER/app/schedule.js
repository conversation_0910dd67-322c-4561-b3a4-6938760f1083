const moment = require('moment');
const async = require('async');
const sql = require('mssql');
let path = require('path');
let bearer_token = "";
let origin_url = "";

let connection;

function REST_ROUTER(router, cn) {

    connection = cn;

    let self = this;
    self.handleRoutes(router);
}

REST_ROUTER.prototype.handleRoutes = function (router) {

    // ROSTERS
    router.get("/roster", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            roster_load(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/check_roster_started_yet", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            check_roster_started_yet(req, res);
        } else {
            res.status(401).send();
        }
    });

    // SHIFTS
    router.get("/shifts", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            shifts_load(req, res);
        } else {
            res.status(401).send();
        }
    });

    // ROSTER CREW
    router.get("/roster_crew", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            roster_crew_load(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_day_leave_counts", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getDayLeaveCounts(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/roster_grand_totals_header", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            all_stations_totals_header(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/roster_grand_totals", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            all_stations_totals(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/roster_total_bookings", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            all_stations_bookings(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_locations", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            get_locations_list(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/load_availability_report", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getAvailabilityData(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.get("/get_all_public_holidays", function (req, res) {
        getAllPublicHolidays(req, res);
    });

    router.get("/get_actual_skill_codes", function (req, res) {
        if (req.headers.authorization) {
            bearer_token = req.headers.authorization.split(" ")[1];
        }
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getActualSkillCodes(req, res);
        } else {
            res.status(401).send();
        }
    });

};


function check_roster_started_yet(req, res) {

    let roster_name = req.query.roster_name;
    let shift_name = req.query.shift_name;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('roster_name', sql.VarChar(40));
    ps.input('shift_name', sql.VarChar(40));

    // Prepare statement
    ps.prepare("SELECT seq_start_date FROM shifts WHERE roster_name = @roster_name AND shift_name = @shift_name", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                roster_name: roster_name,
                shift_name: shift_name

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        res.status(200).send(result.recordset);
                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getActualSkillCodes)");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getActualSkillCodes)");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}

function getActualSkillCodes(req, res) {

    let roster = req.query.roster_name;
    let shift = req.query.shift_name;
    let location = req.query.location_name;
    let date_string = req.query.selDate;
    let selRank = req.query.selRank;
    let rank = "";

    let sqlQuery = "";

    if (selRank === "SO") {
        rank = "'SO','COFF'";
    } else if (selRank === "SFF / FF") {
        rank = "'FF','SFF','SCOP','COP','MOP','EFF','ESFF'";
    }

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('roster', sql.VarChar(40));
    ps.input('shift', sql.VarChar(40));
    ps.input('location', sql.VarChar(40));
    ps.input('date_string', sql.Numeric(8, 0));

    if (selRank == "SO") {
        sqlQuery = "SELECT\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R9O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_R9O,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R9O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_R9O_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'A5O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_A5O,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'A5O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_A5O_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'H6O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_H6O,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'H6O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_H6O_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'C3O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_C3O,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'C3O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_C3O_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R4O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_R4O,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R4O' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_R4O_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'GPO' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_GPO,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'GPO' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_GPO_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'DRO' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_DRO,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'DRO' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_DRO_bk";
    } else if (selRank == "SFF / FF") {
        sqlQuery = "SELECT\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R9' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_R9,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R9' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_R9_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'A5' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_A5,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'A5' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_A5_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'H6' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_H6,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'H6' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_H6_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'C3' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_C3,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'C3' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_C3_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R4' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_R4,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'R4' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_R4_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'GP' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_GP,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'GP' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_GP_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'ICV' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_ICV,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'ICV' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_ICV_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN (" + rank + ") AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'HL' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_HL,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN (" + rank + ") AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code = 'HL' and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_HL_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN ('FF','EFF') AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code IN ('R9','A5','H6','C3','R4','GP','ICV','HL') and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_FF,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN ('FF','EFF') AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code IN ('R9','A5','H6','C3','R4','GP','ICV','HL') and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_FF_bk,\n" +
            "(SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = @location AND date_string =  @date_string AND rank IN ('SFF','ESFF','SCOP','COP','MOP') AND pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code IN ('R9','A5','H6','C3','R4','GP','ICV','HL') and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time')) AS act_SF,\n" +
            "(SELECT COUNT(DISTINCT bk.pay_id) FROM bookings bk JOIN roster_arrangements ra ON bk.pay_id = ra.pay_id AND bk.date_string = ra.date_string WHERE bk.date_string = @date_string AND bk.roster = @roster AND bk.shift = @shift AND bk.location = @location AND ra.rank IN ('SFF','ESFF','SCOP','COP','MOP') AND bk.pay_id IN (SELECT DISTINCT pay_id FROM employee_skills WHERE code IN ('R9','A5','H6','C3','R4','GP','ICV','HL') and expire_date >= SYSDATETIMEOFFSET() AT TIME ZONE 'Cen. Australia Standard Time') AND deleted = 0 AND day_deleted IS NULL AND booking_type IN ('sick_leave','leave_request') AND (bk_period = ra.shift_type OR bk_period = 'both') AND status = 'Approved') AS act_SF_bk";
    }

    // Prepare statement
    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                roster: roster,
                shift: shift,
                location: location,
                date_string: date_string

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {
                        res.status(200).send(result.recordset);
                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getActualSkillCodes)");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getActualSkillCodes)");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function roster_crew_load(req, res) {

    let roster = req.query.roster_name;
    let shift = req.query.shift_name;
    let location = req.query.location;
    let startOfPeriod = req.query.start_date;
    let endOfPeriod = req.query.end_date;
    let overtime_filter = req.query.overtime_filter;
    let mobile_device = req.query.mobile_device;

    roster_crew_get_data(roster, shift, location, startOfPeriod, endOfPeriod, overtime_filter, mobile_device, function (data) {

        res.status(200).send(data);

    });

}


function roster_crew_get_data(roster, shift, location, startOfPeriod, endOfPeriod, overtime_filter, mobile_device, callback) {

    if (startOfPeriod == null || startOfPeriod == undefined || startOfPeriod == "") {
        console.log(moment().format("DD-MM-YY HH:mm") + " | startOfPeriod: " + startOfPeriod + " | Mobile App: " + mobile_device + " (" + path.basename(__filename) + " / roster_crew_get_data)");
        callback([]);
    } else {
        // Format date for query
        let start_date = moment(startOfPeriod, "DD-MM-YYYY").toDate();
        let end_date = moment(endOfPeriod, "DD-MM-YYYY").toDate();


        let start_date_string = moment(startOfPeriod, "DD-MM-YYYY").format("YYYYMMDD");
        let end_date_string = moment(endOfPeriod, "DD-MM-YYYY").format("YYYYMMDD");

        const ps1 = new sql.Request(connection);
        const ps2 = new sql.Request(connection);
        const ps3 = new sql.Request(connection);


        // Inputs
        ps1.input('roster', sql.VarChar(40), roster);
        ps1.input('shift', sql.VarChar(40), shift);
        ps1.input('location', sql.VarChar(40), location);
        ps1.input('start_date_string', sql.Numeric(8, 0), start_date_string);
        ps1.input('end_date_string', sql.Numeric(8, 0), end_date_string);

        let crews = [];
        let bookings = [];
        let avl_totals = [];
        let date_lookup = [];
        let firstName = "";

        async.series([
                function (callback) {

                    // Execute stored procedure
                    ps1.execute('spGetRosterCrew', (err, result) => {

                        if (!err) {

                            for (let x = 0; x < result.recordset.length; x++) {

                                // Check to see if crew member already exists
                                let found = false;
                                let covidCode = "";

                                for (let crewIndex = 0; crewIndex < crews.length; crewIndex++) {
                                    if (crews[crewIndex].pay_id === result.recordset[x].pay_id) {

                                        crews[crewIndex].roster_arrangement.push({
                                            roster_arrangement_id: result.recordset[x].roster_arrangement_id,
                                            roster_date: result.recordset[x].roster_date,
                                            date_string: result.recordset[x].date_string,
                                            shift_type: result.recordset[x].shift_type,
                                            acting_up: result.recordset[x].acting_up
                                        });

                                        found = true;
                                        break;
                                    }
                                }

                                if (found === false) {

                                    let curr_date = moment().startOf('day');
                                    let first_dose_date = "";
                                    let second_dose_date = "";
                                    let booster_date = "";
                                    let verified_date = "";
                                    let exempt_expiry_date = "";

                                    if (result.recordset[x].first_dose_date != null) {
                                        first_dose_date = moment(result.recordset[x].first_dose_date);
                                    }

                                    if (result.recordset[x].second_dose_date != null) {
                                        second_dose_date = moment(result.recordset[x].second_dose_date);
                                    }

                                    if (result.recordset[x].booster_date != null) {
                                        booster_date = moment(result.recordset[x].booster_date);
                                    }

                                    if (result.recordset[x].verified_date != null) {
                                        verified_date = moment(moment(result.recordset[x].verified_date)).endOf('day');
                                    }

                                    if (result.recordset[x].exemption_date != null) {
                                        exempt_expiry_date = moment(moment(result.recordset[x].exemption_date)).endOf('day');
                                    }

                                    if (result.recordset[x].status == "Medically Exempt" && exempt_expiry_date.isSameOrAfter(curr_date) === true) {
                                        covidCode = "<div style='background-color: yellow; color: black; margin-left: -4px; width: 120%; height: 100%'>ME (" + moment(result.recordset[x].exemption_date).format('DD/MM/YY') + ")</div>";
                                    } else {
                                        if (result.recordset[x].digital_cert_received == true || result.recordset[x].vax_form_received == true) {
                                            if (first_dose_date == "" && second_dose_date == "") {
                                                covidCode = "<div style='background-color: red; color: white; margin-left: -4px; width: 120%; height: 100%'>NC</div>";
                                            } else if (first_dose_date != "" && second_dose_date == "") {
                                                covidCode = "<div style='background-color: red; color: white; margin-left: -4px; width: 120%; height: 100%'>NC2</div>";
                                            } else if (first_dose_date != "" && second_dose_date != "" && booster_date == "") {
                                                if (second_dose_date < curr_date && second_dose_date < verified_date && booster_date == "") {
                                                    if (curr_date.diff(second_dose_date, 'days', false) >= 183) {
                                                        covidCode = "<div style='background-color: red; color: white; margin-left: -4px; width: 120%; height: 100%'>NCB</div>";
                                                    } else {
                                                        covidCode = "<div style='background-color: green; color: white; margin-left: -4px; width: 120%; height: 100%'>C12</div>";
                                                    }
                                                } else {
                                                    covidCode = "<div style='background-color: darkorange; color: white; margin-left: -4px; width: 120%; height: 100%'>C12 (" + moment(result.recordset[x].second_dose_date).format('DD/MM/YY') + ")</div>";
                                                }
                                            } else if (first_dose_date != "" && second_dose_date != "" && booster_date != "") {
                                                if (booster_date <= curr_date && booster_date < verified_date) {
                                                    covidCode = "<div style='background-color: green; color: white; margin-left: -4px; width: 120%; height: 100%'>C12B</div>";
                                                } else {
                                                    covidCode = "<div style='background-color: darkorange; color: white; margin-left: -4px; width: 120%; height: 100%'>C12B (" + moment(result.recordset[x].booster_date).format('DD/MM/YY') + ")</div>";
                                                }
                                            }
                                        } else {
                                            covidCode = "<div style='background-color: red; color: white; margin-left: -4px; width: 120%; height: 100%'>NR</div>";
                                        }
                                    }

                                    crews.push({
                                        pay_id: result.recordset[x].pay_id,
                                        first_name: result.recordset[x].first_name,
                                        middle_name: result.recordset[x].middle_name,
                                        surname: result.recordset[x].surname,
                                        rank: result.recordset[x].rank,
                                        rank_class: result.recordset[x].rank_class,
                                        skills: result.recordset[x].skills,
                                        covid: covidCode,
                                        home_station: result.recordset[x].home_station,
                                        home_station_id: result.recordset[x].home_station_id,
                                        notifications_email: result.recordset[x].notifications_email,
                                        work_email_address: result.recordset[x].work_email_address,
                                        personal_phone: result.recordset[x].personal_mobile_no,
                                        hide_phone: result.recordset[x].hide_phone,
                                        home_suburb: result.recordset[x].suburb,
                                        position_number: result.recordset[x].position_number,
                                        roster_arrangement: [
                                            {
                                                roster_arrangement_id: result.recordset[x].roster_arrangement_id,
                                                roster_date: result.recordset[x].roster_date,
                                                date_string: result.recordset[x].date_string,
                                                shift_type: result.recordset[x].shift_type,
												acting_up: result.recordset[x].acting_up
                                            }
                                        ]
                                    });
                                }
                            }

                            callback();

                        } else {

                            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | " + roster + " | " + shift + " | " + location + " | " + start_date_string + " | " + end_date_string + " | " + overtime_filter + " | (" + path.basename(__filename) + " / roster_crew_get_data)");
                            callback();
                        }
                    });
                },
                function (callback) {
                    // Add columns for each date in the range

                    endOfPeriod = moment(endOfPeriod, "DD-MM-YYYY");
                    startOfPeriod = moment(startOfPeriod, "DD-MM-YYYY");

                    let duration = moment.duration(endOfPeriod.diff(startOfPeriod)).asDays();

                    if (duration === 0) {
                        duration = 1;
                    }

                    // Populate date_lookup array - Used to lookup date from day position
                    let dayMoment = startOfPeriod.clone();

                    let dayString = dayMoment.format('YYYYMMDD');

                    for (let day = 0; day <= duration; day++) {
                        date_lookup[day] = dayString;
                        dayString = dayMoment.add(1, 'days').format('YYYYMMDD');
                    }

                    let row = 2; // Start at row 2 - Row 0 is the header, Row 1 is the icons.

                    // Add data to names column
                    for (let x = 0; x < crews.length; x++) {
                        crews[x].id = crews[x].pay_id;

                        if (crews[x].home_station == location) {
                            firstName = crews[x].surname + ', ' + crews[x].first_name.slice(0, 2).toUpperCase();
                        } else {
                            firstName = crews[x].surname + ', ' + crews[x].first_name.slice(0, 2).toUpperCase() + " (" + crews[x].home_station_id + ")";
                        }

                        crews[x].names = firstName;

                        // Add data to date columns - Initialise to correct length based on date range
                        for (let day = 0; day <= duration; day++) {
                            crews[x][(day + 1).toString()] = "";
                            crews[x][(day + 1).toString() + "-code"] = "";
                        }

                        row++;

                    }

                    callback();

                },
                function (callback) {

                    // Get leave data for the roster
                    let sqlSP = "";

                    start_date = startOfPeriod.format("YYYYMMDD");
                    end_date = endOfPeriod.format("YYYYMMDD");

                    ps2.input('start_date', sql.Numeric(8, 0), start_date);
                    ps2.input('end_date', sql.Numeric(8, 0), end_date);

                    if (overtime_filter == 1) { //removed AND roster = @roster AND shift = @shift AND location = @location to show bookings everywhere
                        sqlSP = 'spGetRosterBookingsAll'
                    } else {
                        sqlSP = 'spGetRosterBookingsNoOT'
                    }

                    // Execute stored procedure
                    ps2.execute(sqlSP, (err, result) => {
                        if (!err) {
                            bookings = result.recordset;
                            callback();
                        } else {
                            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | " + roster + " | " + shift + " | " + location + " | " + start_date_string + " | " + end_date_string + " | " + overtime_filter + " | (" + path.basename(__filename) + " / roster_crew_get_data_2)");
                            callback();
                        }
                    });
                },
                function (callback) {

                    for (let crewIndex = 0; crewIndex < crews.length; crewIndex++) {
                        for (let bookingsIndex = 0; bookingsIndex < bookings.length; bookingsIndex++) {
                            for (let dayIndex = 0; dayIndex < date_lookup.length; dayIndex++) {

                                if (bookings[bookingsIndex].date_string == date_lookup[dayIndex]) {

                                    if (crews[crewIndex].pay_id === bookings[bookingsIndex].pay_id) {

                                        let bookingCode = "";
                                        let currentCode = "";

                                        //push booking code to a temp index for later reference
                                        if (bookings[bookingsIndex].booking_type != "standby_link") {
                                            crews[crewIndex][(dayIndex + 1) + "-code"] = bookings[bookingsIndex].leave_type_code;
                                        }

                                        //add brackets if booking is pending and not yet approved
                                        if (bookings[bookingsIndex].status == "Pending") {
                                            bookingCode = "(" + bookings[bookingsIndex].leave_type_code + ")";
                                        } else {
                                            bookingCode = bookings[bookingsIndex].leave_type_code;
                                        }

                                        if (roster !== "Metro" && roster !== "Comms" && roster !== "Port Pirie" && roster !== "Mt Gambier" && roster !== "OTR" && roster !== "Long Term Leave") {
                                            if (bookings[bookingsIndex].status == "Pending" && bookings[bookingsIndex].booking_type == "leave_request") {
                                                if (bookings[bookingsIndex].approved_denied_by == null && bookings[bookingsIndex].wfr_approved_denied_by == null) {
                                                    bookingCode = "((" + bookings[bookingsIndex].leave_type_code + "))";
                                                } else {
                                                    bookingCode = "(" + bookings[bookingsIndex].leave_type_code + ")";
                                                }
                                            } else {
                                                if (bookings[bookingsIndex].leave_type_code != "PH") {
                                                    bookingCode = bookings[bookingsIndex].leave_type_code;
                                                } else {
                                                    bookingCode = "<span style='color: orangered !important; font-weight: bold !important'>PH</span>";
                                                }
                                            }
                                        }

                                        //new SICM and FAML status change
                                        if ((bookings[bookingsIndex].leave_type_code == "SICM" || bookings[bookingsIndex].leave_type_code == "FAML") && (bookings[bookingsIndex].sick_certificate == null && bookings[bookingsIndex].statutory_declaration == null)) {
                                            bookingCode = "(" + bookings[bookingsIndex].leave_type_code + ")";
                                        }

                                        if (bookings[bookingsIndex].roster == "Port Pirie") {

                                            currentCode = crews[crewIndex][dayIndex + 1];
                                            if (bookings[bookingsIndex].bk_period == "day") {
                                                bookingCode = bookingCode + "-D"
                                            } else if (bookings[bookingsIndex].bk_period == "night") {
                                                bookingCode = bookingCode + "-N"
                                            }


                                            if (currentCode.includes("-N")) {
                                                crews[crewIndex][dayIndex + 1] = bookingCode + "</br>" + currentCode
                                            } else {
                                                crews[crewIndex][dayIndex + 1] = currentCode + "</br>" + bookingCode
                                            }

                                            //strip out the 2nd line feed if found
                                            let pp_string = crews[crewIndex][dayIndex + 1];
                                            if (pp_string.match(/br>/g).length == 2) {
                                                crews[crewIndex][dayIndex + 1] = crews[crewIndex][dayIndex + 1].replace('</br>', '');
                                            }

                                            if (bookings[bookingsIndex].roster == roster && bookings[bookingsIndex].shift == shift && bookings[bookingsIndex].location == location) {

                                                if (bookings[bookingsIndex].booking_type == "act-up") {
                                                    if (crews[crewIndex][dayIndex + 1].slice(-1) == "R") {
                                                        if (crews[crewIndex][(dayIndex + 1) + "-code"] == "AU") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //bullet only
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span><span><u>" + crews[crewIndex][dayIndex + 1] + "</u></span>"; //bullet + code
                                                        }
                                                    } else if (crews[crewIndex][dayIndex + 1] == "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span>") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //red bullet + bullet
                                                    } else if (crews[crewIndex][dayIndex + 1] == "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='float: left'>-D</span></br>AU-D") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><div style='float: left'>-D</div><span style='font-size: 36px !important; float: left !important;'>&#8226</span><div style='float: left'>-D</div>"; //red bullet + bullet
                                                    } else if (crews[crewIndex][dayIndex + 1] == "AU-N</br><span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='float: left'>-N</span>") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><div style='float: left'>-N</div><span style='font-size: 36px !important; float: left !important;'>&#8226</span><div style='float: left'>-N</div>"; //red bullet + bullet
                                                    } else if (crews[crewIndex][dayIndex + 1] == "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span></br>AU") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //red bullet + bullet
                                                    } else {
                                                        if (bookings[bookingsIndex].bk_period == "day") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span><span style='float: left'>-D</span>"; //bullet + D
                                                        } else if (bookings[bookingsIndex].bk_period == "night") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span><span style='float: left'>-N</span>"; //bullet + code
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //bullet only
                                                        }
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "RC") {
                                                    if (crews[crewIndex][(dayIndex + 1) + "-code"] == "AU") {
                                                        if (bookings[bookingsIndex].bk_period == "day") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 36px !important; float: left !important;'>&#8226</span><span style='float: left'>-D</span>"; //red bullet + bullet
                                                        } else if (bookings[bookingsIndex].bk_period == "night") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 36px !important; float: left !important;'>&#8226</span><span style='float: left'>-N</span>"; //red bullet + bullet
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //red bullet + bullet
                                                        }
                                                    } else {
                                                        if (bookings[bookingsIndex].bk_period == "day") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='float: left'>-D</span>"; //red bullet
                                                        } else if (bookings[bookingsIndex].bk_period == "night") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='float: left'>-N</span>"; //red bullet
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span>"; //red bullet
                                                        }
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "VSBW") {
                                                    if (crews[crewIndex][(dayIndex + 1) + "-code"] == "") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: white !important;'>&#8226</span>"; //white bullet
                                                    }
                                                } else if (bookings[bookingsIndex].booking_type == "standby_link") {
                                                    crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                }
                                            } else {
                                                if (bookings[bookingsIndex].leave_type_code == "SB") {
                                                    if (bookings[bookingsIndex].bk_period == "day") {
                                                        if (bookings[bookingsIndex].hours < 10) {
                                                            crews[crewIndex][dayIndex + 1] = "*";
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                        }
                                                    } else if (bookings[bookingsIndex].bk_period == "night") {
                                                        if (bookings[bookingsIndex].hours < 14) {
                                                            crews[crewIndex][dayIndex + 1] = "*";
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                        }
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                    }
                                                } else {
                                                    crews[crewIndex][dayIndex + 1] = bookingCode;
                                                }
                                            }

                                        } else {
                                            //underline if another booking exists on same cell
                                            if (crews[crewIndex][dayIndex + 1] != "") {
                                                bookingCode = "<u>" + bookingCode + "</u>";
                                            }

                                            if (bookings[bookingsIndex].roster == roster && bookings[bookingsIndex].shift == shift && bookings[bookingsIndex].location == location) {

                                                if (bookings[bookingsIndex].booking_type == "act-up") {
                                                    if (crews[crewIndex][dayIndex + 1].slice(-1) == "R") {
                                                        if (crews[crewIndex][(dayIndex + 1) + "-code"] == "AU") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //bullet only
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span><span><u>" + crews[crewIndex][dayIndex + 1] + "</u></span>"; //bullet + code
                                                        }
                                                    } else if (crews[crewIndex][dayIndex + 1] == "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span>") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //red bullet + bullet
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //bullet only
                                                    }
                                                } else if (bookings[bookingsIndex].booking_type == "staff_movement") {

                                                    if (crews[crewIndex][dayIndex + 1] == "RC") {
                                                        //do nothing
                                                    } else {
                                                        if (crews[crewIndex][dayIndex + 1] == "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>") {
                                                            crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span><span>" + bookingCode + "</span>"; //bullet + code
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = bookingCode;
                                                        }
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "RC") {
                                                    if (crews[crewIndex][(dayIndex + 1) + "-code"] == "AU") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //red bullet + bullet
                                                    } else if (crews[crewIndex][dayIndex + 1] == "OL") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span><span style='font-size: 18px !important; float: left !important;'>_</span>"; //red bullet + underline
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span>"; //red bullet
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "DRILL") {
                                                    if (crews[crewIndex][(dayIndex + 1) + "-code"] == "") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: blue !important;'>&#8226</span>"; //blue bullet
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "REL") {
                                                    if (crews[crewIndex][(dayIndex + 1) + "-code"] == "") {

                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: forestgreen !important;'>&#8226</span>"; //green bullet
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "VSBW") {
                                                    if (crews[crewIndex][(dayIndex + 1) + "-code"] == "") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: white !important;'>&#8226</span>"; //white bullet
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "SB") {
                                                    if (bookings[bookingsIndex].bk_period == "day") {
                                                        if (bookings[bookingsIndex].hours < 10) {
                                                            crews[crewIndex][dayIndex + 1] = "SB-P";
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = bookingCode;
                                                        }
                                                    } else if (bookings[bookingsIndex].bk_period == "night") {
                                                        if (bookings[bookingsIndex].hours < 14) {
                                                            crews[crewIndex][dayIndex + 1] = "SB-P";
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = bookingCode;
                                                        }
                                                    }
                                                } else {
                                                    //always show the SM if another booking is present on same shift
                                                    if (crews[crewIndex][dayIndex + 1].slice(-1) == "R") {
                                                        crews[crewIndex][dayIndex + 1] = "<u>" + crews[crewIndex][dayIndex + 1] + "</u>";
                                                    } else if (crews[crewIndex][dayIndex + 1] == "SIC" || crews[crewIndex][dayIndex + 1] == "SICM" || crews[crewIndex][dayIndex + 1] == "WRSL" || crews[crewIndex][dayIndex + 1] == "FAML" || crews[crewIndex][dayIndex + 1] == "BERE" || crews[crewIndex][dayIndex + 1] == "SLUP" || crews[crewIndex][dayIndex + 1] == "SLUW") {
                                                        if (crews[crewIndex][(dayIndex + 1) + "-code"] != "") {
                                                            crews[crewIndex][dayIndex + 1] = "<u>" + crews[crewIndex][dayIndex + 1] + "</u>";
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = bookingCode;
                                                        }
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = bookingCode;
                                                    }
                                                }

                                            } else {

                                                if (bookings[bookingsIndex].booking_type == "act-up") {
                                                    if (crews[crewIndex][dayIndex + 1].slice(-1) == "R") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span><span><u>" + crews[crewIndex][dayIndex + 1] + "</u></span>"; //bullet + code
                                                    } else if (crews[crewIndex][dayIndex + 1] == "<u>SB</u>") {
                                                        //do nothing
                                                    } else if (crews[crewIndex][dayIndex + 1] == "RC") {
                                                        crews[crewIndex][dayIndex + 1] = "<u>RC</u>";
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //bullet only
                                                    }
                                                } else if (bookings[bookingsIndex].booking_type == "staff_movement") {
                                                    if (bookings[bookingsIndex].home_station_on_date == bookings[bookingsIndex].moved_location && bookings[bookingsIndex].moved_location == location) {
                                                        if (crews[crewIndex][dayIndex + 1] == "<span style='font-size: 36px !important; float: left !important; color: red !important;'>&#8226</span>") {
                                                            //do nothing
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = crews[crewIndex][dayIndex + 1] = "__";
                                                        }
                                                    } else {
                                                        if (crews[crewIndex][dayIndex + 1] == "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>") {
                                                            //do nothing
                                                            //crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important;'>&#8226</span>"; //bullet only
                                                        } else {
                                                            if (crews[crewIndex][dayIndex + 1] == "SB") {
                                                                if (location == "Adelaide Relievers") {
                                                                    crews[crewIndex][dayIndex + 1] = bookingCode;
                                                                } else {
                                                                    crews[crewIndex][dayIndex + 1] = "<u>SB</u>";
                                                                }
                                                            }
                                                        }
                                                        if (crews[crewIndex][dayIndex + 1] != "" && crews[crewIndex][dayIndex + 1] != crews[crewIndex][(dayIndex + 1) + "-code"]) {
                                                            if (crews[crewIndex][dayIndex + 1].slice(-1) == "R" && crews[crewIndex][(dayIndex + 1) + "-code"].slice(-1) == "R") {
                                                                crews[crewIndex][dayIndex + 1] = "<u>" + bookingCode + "</u>";
                                                            }
                                                        }
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "SB") {
                                                    if (crews[crewIndex][dayIndex + 1].slice(-1) == "R") {
                                                        crews[crewIndex][dayIndex + 1] = bookingCode;
                                                    } else if (crews[crewIndex][dayIndex + 1] == "SB") {

                                                    } else {
                                                        if (bookings[bookingsIndex].bk_period == "day") {
                                                            if (bookings[bookingsIndex].hours < 10) {
                                                                crews[crewIndex][dayIndex + 1] = "*";
                                                            } else {
                                                                crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                            }
                                                        } else if (bookings[bookingsIndex].bk_period == "night") {
                                                            if (bookings[bookingsIndex].hours < 14) {
                                                                crews[crewIndex][dayIndex + 1] = "*";
                                                            } else {
                                                                crews[crewIndex][dayIndex + 1] = "<span style='font-size: 1px !important'></span>"; //show nothing
                                                            }
                                                        }
                                                    }
                                                } else if (bookings[bookingsIndex].leave_type_code == "RC") {
                                                    if (crews[crewIndex][(dayIndex + 1) + "-code"] == "REL") {
                                                        crews[crewIndex][dayIndex + 1] = "<span style='font-size: 36px !important; float: left !important; color: forestgreen !important;'>&#8226</span>"; //green bullet
                                                    } else if (crews[crewIndex][dayIndex + 1] == "SB") { //added on 17/04/2022 for the RC with SB only showing __
                                                        crews[crewIndex][dayIndex + 1] = "<u>SB</u>"; //added on 17/04/2022 for the RC with SB only showing __
                                                    } else {
                                                        crews[crewIndex][dayIndex + 1] = bookingCode;
                                                    }
                                                } else {
                                                    //always show the SM if another booking is present on same shift
                                                    if (crews[crewIndex][dayIndex + 1].slice(-1) == "R") {
                                                        crews[crewIndex][dayIndex + 1] = "<u>" + crews[crewIndex][dayIndex + 1] + "</u>";
                                                    } else if (crews[crewIndex][dayIndex + 1] == "SIC" || crews[crewIndex][dayIndex + 1] == "SICM" || crews[crewIndex][dayIndex + 1] == "WRSL" || crews[crewIndex][dayIndex + 1] == "FAML" || crews[crewIndex][dayIndex + 1] == "BERE" || crews[crewIndex][dayIndex + 1] == "SLUP" || crews[crewIndex][dayIndex + 1] == "SLUW") {
                                                        if (crews[crewIndex][(dayIndex + 1) + "-code"] != "") {
                                                            crews[crewIndex][dayIndex + 1] = "<u>" + crews[crewIndex][dayIndex + 1] + "</u>";
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = bookingCode;
                                                        }
                                                    } else {
                                                        if (bookings[bookingsIndex].leave_type_code == "ARL" || bookings[bookingsIndex].leave_type_code == "ANN") {
                                                            //do nothing
                                                        } else {
                                                            crews[crewIndex][dayIndex + 1] = bookingCode;
                                                        }
                                                    }
                                                }
                                            }

                                            //check if booking is not during shift times
                                            for (let r = 0; r < crews[crewIndex].roster_arrangement.length; r++) {
                                                if (crews[crewIndex].roster_arrangement[r].date_string == bookings[bookingsIndex].date_string) {
                                                    if ((bookings[bookingsIndex].bk_period == "day" && crews[crewIndex].roster_arrangement[r].shift_type == "night") || (bookings[bookingsIndex].bk_period == "night" && crews[crewIndex].roster_arrangement[r].shift_type == "day")) {
                                                        if (crews[crewIndex][dayIndex + 1] == crews[crewIndex][(dayIndex + 1) + "-code"]) {
                                                            if (crews[crewIndex][dayIndex + 1] == "SB" && crews[crewIndex][(dayIndex + 1) + "-code"] == "SB") {
                                                                crews[crewIndex][dayIndex + 1] = "<u>" + bookingCode + "</u>";
                                                            } else {
                                                                crews[crewIndex][dayIndex + 1] = "__";
                                                            }
                                                        } else {
                                                            if (crews[crewIndex][dayIndex + 1] != "<u>SB</u>") {
                                                                crews[crewIndex][dayIndex + 1] = "__";
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                        }
                                    }
                                }

                            }
                        }
                    }
                    callback();
                },
                function (callback) {

                    // Get totals data for the roster
                    start_date = startOfPeriod.format("YYYYMMDD");
                    end_date = endOfPeriod.format("YYYYMMDD");

                    let sqlSP = "";

                    ps3.input('roster', sql.VarChar(40), roster);
                    ps3.input('shift', sql.VarChar(40), shift);
                    ps3.input('location', sql.VarChar(40), location);
                    ps3.input('start_date', sql.Numeric(8, 0), start_date);
                    ps3.input('end_date', sql.Numeric(8, 0), end_date);

                    if (roster === "Port Pirie") {
                        sqlSP = "spPortPirieStationTotals";
                    } else {
                        sqlSP = "spStationTotals";
                    }

                    // Execute stored procedure
                    ps3.execute(sqlSP, (err, result) => {

                        if (!err) {
                            avl_totals = result.recordset;
                            callback();
                        } else {
                            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | " + roster + " | " + shift + " | " + location + " | " + start_date_string + " | " + end_date_string + " | " + overtime_filter + " | (" + path.basename(__filename) + " / roster_crew_get_data_3)");
                            callback();
                        }

                    });

                },
                function (callback) {

                    let amount = 0;
                    let amount_day = 0;
                    let amount_night = 0;
                    let total_value = {};
                    let total_value_day = {};
                    let total_value_night = {};
                    let dayNo = "";
                    let value = "";
                    let value_day = "";
                    let value_night = "";

                    for (let dayIndex = 1; dayIndex < date_lookup.length + 1; dayIndex++) {
                        if (avl_totals[dayIndex - 1]) {
                            if (avl_totals[dayIndex - 1].date_string == date_lookup[dayIndex - 1]) {
                                if (avl_totals[dayIndex - 1].shift_type != "off" && avl_totals[dayIndex - 1].icon != "ban") {
                                    dayNo = dayIndex.toString();
                                    if (roster !== "Port Pirie") {
                                        if (avl_totals[dayIndex - 1].shift_type == null && (avl_totals[dayIndex - 1].icon == "sun" || avl_totals[dayIndex - 1].icon == "moon")) {
                                            amount = 0;
                                        } else {
                                            if (avl_totals[dayIndex - 1].shift_type == "day") {
                                                amount = avl_totals[dayIndex - 1].total_ras - avl_totals[dayIndex - 1].day_period_bks - avl_totals[dayIndex - 1].both_period_bks + avl_totals[dayIndex - 1].sm_back_to_hs_day + avl_totals[dayIndex - 1].OL_RC_same_shift_day;
                                            } else if (avl_totals[dayIndex - 1].shift_type == "night") {
                                                amount = avl_totals[dayIndex - 1].total_ras - avl_totals[dayIndex - 1].night_period_bks - avl_totals[dayIndex - 1].both_period_bks + avl_totals[dayIndex - 1].sm_back_to_hs_night + avl_totals[dayIndex - 1].OL_RC_same_shift_night;
                                            } else {
                                                amount = 0;
                                            }
                                        }

                                    } else {
                                        amount_day = (avl_totals[dayIndex - 1].perm_ras - avl_totals[dayIndex - 1].day_bks) - avl_totals[dayIndex - 1].both_bks + avl_totals[dayIndex - 1].temp_ras_day + avl_totals[dayIndex - 1].temp_ras_both;
                                        amount_night = (avl_totals[dayIndex - 1].perm_ras - avl_totals[dayIndex - 1].night_bks) - avl_totals[dayIndex - 1].both_bks + avl_totals[dayIndex - 1].temp_ras_night + avl_totals[dayIndex - 1].temp_ras_both;
                                    }

                                    if (roster !== "Port Pirie") {
                                        value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                        total_value[dayNo] = value
                                    } else {
                                        value_day = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount_day.toString() + "</span>"; //total_day
                                        value_night = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount_night.toString() + "</span>"; //total night
                                        total_value_day[dayNo] = value_day;
                                        total_value_night[dayNo] = value_night;
                                    }
                                }
                            }
                        }
                    }

                    if (roster !== "Port Pirie") {

                        total_value.pay_id = "";
                        total_value.first_name = "";
                        total_value.middle_name = "";
                        total_value.surname = "";
                        total_value.rank = "";
                        total_value.skills = null;
                        total_value.covid = "";
                        total_value.roster_arrangement = [];
                        total_value.id = "";
                        if (mobile_device === undefined) {
                            total_value.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>AVAILABLE FOR SHIFT</span>"; //totals
                        } else {
                            total_value.names = "<span style='font-size: 11px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>AVAILABLE FOR SHIFT</span>"; //totals
                        }
                        crews.push(total_value);

                    } else {

                        total_value_day.pay_id = "";
                        total_value_day.first_name = "";
                        total_value_day.middle_name = "";
                        total_value_day.surname = "";
                        total_value_day.rank = "";
                        total_value_day.skills = null;
                        total_value_day.covid = "";
                        total_value_day.roster_arrangement = [];
                        total_value_day.id = "";
                        if (mobile_device === undefined) {
                            total_value_day.names = "<span style='font-size: 12px !important; line-height: 30px !important; font-weight: 500 !important; float: right !important; color: black !important;'>AVAILABLE FOR DAY SHIFT</span>"; //totals
                        } else {
                            total_value_day.names = "<span style='font-size: 10px !important; line-height: 34px !important; font-weight: 500 !important; float: right !important; color: black !important;'>AVAILABLE FOR DAY SHIFT</span>"; //totals
                        }
                        crews.push(total_value_day);

                        total_value_night.pay_id = "";
                        total_value_night.first_name = "";
                        total_value_night.middle_name = "";
                        total_value_night.surname = "";
                        total_value_night.rank = "";
                        total_value_night.skills = null;
                        total_value_night.covid = "";
                        total_value_night.roster_arrangement = [];
                        total_value_night.id = "";
                        if (mobile_device === undefined) {
                            total_value_night.names = "<span style='font-size: 12px !important; line-height: 30px !important; font-weight: 500 !important; float: right !important; color: black !important;'>AVAILABLE FOR NIGHT SHIFT</span>"; //totals
                        } else {
                            total_value_night.names = "<span style='font-size: 10px !important; line-height: 34px !important; font-weight: 500 !important; float: right !important; color: black !important;'>AVAILABLE FOR NIGHT SHIFT</span>"; //totals
                        }
                        crews.push(total_value_night);

                    }

                    callback();

                }
            ],
            function () {

                callback({
                    crews: crews
                });

            }
        );
    }

}


function all_stations_totals(req, res) {

    // Format date for query
    let startOfPeriod = req.query.start_date;
    let endOfPeriod = req.query.end_date;
    let start_date = moment(startOfPeriod, "DD-MM-YYYY").format("YYYYMMDD");
    let end_date = moment(endOfPeriod, "DD-MM-YYYY").format("YYYYMMDD");
    let roster = req.query.roster_name;
    let shift = req.query.shift_name;
    let locations = req.query.locations;

    const request = new sql.Request(connection);

    locations = locations.replace("[", "(");
    locations = locations.replace("]", ")");
    locations = locations.replace("'", "''");
    locations = locations.replace(/"/g, "'");

    request.input('start_date', start_date);
    request.input('end_date', end_date);
    request.input('roster', roster);
    request.input('shift', shift);
    request.input('locations', locations);

    let sqlQuery = "SELECT r.date_string, r.icon, (SELECT DISTINCT COUNT(pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location IN " + locations + " AND date_string = r.date_string) as total_ras, (SELECT DISTINCT shift_type FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location IN " + locations + " AND date_string = r.date_string AND type = 'Permanent') as shift_type, (SELECT DISTINCT COUNT(pay_id) FROM bookings WHERE roster = @roster AND shift = @shift AND location IN " + locations + " AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('RC','AU','DC','RO','IA','T/E','REL','DRILL','VSBW') AND date_string = r.date_string AND bk_period = 'day') as day_period_bks, (SELECT DISTINCT COUNT(pay_id) FROM bookings WHERE roster = @roster AND shift = @shift AND location IN " + locations + " AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('RC','AU','DC','RO','IA','T/E','REL','DRILL','VSBW') AND date_string = r.date_string AND bk_period = 'night') as night_period_bks, (SELECT DISTINCT COUNT(pay_id) FROM bookings WHERE roster = @roster AND shift = @shift AND location IN " + locations + " AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('RC','AU','DC','RO','IA','T/E','REL','DRILL','VSBW') AND date_string = r.date_string AND bk_period = 'both') as both_period_bks, (SELECT COUNT(pay_id) FROM bookings WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND date_string = r.date_string AND bk_period = 'day' AND moved_location = (SELECT ra.location FROM roster_arrangements ra WHERE ra.type = 'Permanent' AND ra.date_string = r.date_string AND ra.pay_id = bookings.pay_id)) AS sm_back_to_hs_day, (SELECT COUNT(pay_id) FROM bookings WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND date_string = r.date_string AND bk_period = 'night' AND moved_location = (SELECT ra.location FROM roster_arrangements ra WHERE ra.type = 'Permanent' AND ra.date_string = r.date_string AND ra.pay_id = bookings.pay_id)) AS sm_back_to_hs_night, (SELECT DISTINCT count(*) FROM bookings b1 JOIN bookings b2 ON b1.pay_id = b2.pay_id AND b1.date_string = b2.date_string AND b1.roster = b2.roster AND b1.shift = b2.shift AND b1.location = b2.location WHERE b1.roster = @roster AND b1.shift = @shift AND b1.location IN " + locations + " AND b1.date_string = r.date_string AND b1.deleted = 0 AND b1.day_deleted IS NULL AND b2.deleted = 0 AND b2.day_deleted IS NULL AND b1.leave_type_code = 'OL' AND b2.leave_type_code = 'RC' AND b1.bk_period = b2.bk_period AND b1.bk_period = 'day') as OL_RC_same_shift_day, (SELECT DISTINCT count(*) FROM bookings b1 JOIN bookings b2 ON b1.pay_id = b2.pay_id AND b1.date_string = b2.date_string AND b1.roster = b2.roster AND b1.shift = b2.shift AND b1.location = b2.location WHERE b1.roster = @roster AND b1.shift = @shift AND b1.location IN " + locations + " AND b1.date_string = r.date_string AND b1.deleted = 0 AND b1.day_deleted IS NULL AND b2.deleted = 0 AND b2.day_deleted IS NULL AND b1.leave_type_code = 'OL' AND b2.leave_type_code = 'RC' AND b1.bk_period = b2.bk_period AND b1.bk_period = 'night') as OL_RC_same_shift_night FROM roster_shift_days r WHERE roster = @roster AND shift = @shift AND date_string BETWEEN @start_date AND @end_date ORDER BY date_string";

    request.query(sqlQuery, (err, result) => {

        if (!err) {
            if (result.recordset) {

                let grand_totals = [];
                let dates_lookup = [];

                let amount = 0;
                let total_value = {};
                let dayNo = "";
                let value = "";

                grand_totals = result.recordset;

                endOfPeriod = moment(endOfPeriod, "DD-MM-YYYY");
                startOfPeriod = moment(startOfPeriod, "DD-MM-YYYY");

                let duration = moment.duration(endOfPeriod.diff(startOfPeriod)).asDays();

                if (duration === 0) {
                    duration = 1;
                }

                // Populate date_lookup array - Used to lookup date from day position
                let dayMoment = startOfPeriod.clone();

                let dayString = dayMoment.format('YYYYMMDD');

                for (let day = 0; day <= duration; day++) {
                    dates_lookup[day] = dayString;
                    dayString = dayMoment.add(1, 'days').format('YYYYMMDD');
                }

                for (let dayIndex = 1; dayIndex < dates_lookup.length + 1; dayIndex++) {
                    if (grand_totals[dayIndex - 1]) {
                        if (grand_totals[dayIndex - 1].date_string == dates_lookup[dayIndex - 1]) {
                            if (grand_totals[dayIndex - 1].shift_type != "off") {
                                dayNo = dayIndex.toString();

                                if (grand_totals[dayIndex - 1].shift_type == null && (grand_totals[dayIndex - 1].icon == "sun" || grand_totals[dayIndex - 1].icon == "moon")) {
                                    amount = 0;
                                } else {
                                    if (grand_totals[dayIndex - 1].shift_type == "day") {
                                        amount = grand_totals[dayIndex - 1].total_ras - grand_totals[dayIndex - 1].day_period_bks - grand_totals[dayIndex - 1].both_period_bks + grand_totals[dayIndex - 1].OL_RC_same_shift_day; //+ grand_totals[dayIndex - 1].sm_back_to_hs_day;
                                    } else if (grand_totals[dayIndex - 1].shift_type == "night") {
                                        amount = grand_totals[dayIndex - 1].total_ras - grand_totals[dayIndex - 1].night_period_bks - grand_totals[dayIndex - 1].both_period_bks + grand_totals[dayIndex - 1].OL_RC_same_shift_night; //+ grand_totals[dayIndex - 1].sm_back_to_hs_night;
                                    } else {
                                        amount = 0;
                                    }
                                }

                                value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                total_value[dayNo] = value
                            }
                        }
                    }
                }


                total_value.pay_id = "";
                total_value.first_name = "";
                total_value.middle_name = "";
                total_value.surname = "";
                total_value.rank = "";
                total_value.skills = null;
                total_value.covid = "";
                total_value.roster_arrangement = [];
                total_value.id = "";
                total_value.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>TOTAL AVAILABLE FOR SHIFT</span>"; //totals

                res.status(200).send(total_value);

            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / all_stations_totals)");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}

function all_stations_bookings(req, res) {

    // Format date for query
    let startOfPeriod = req.query.start_date;
    let start_date = moment(startOfPeriod, "DD-MM-YYYY").format("YYYYMMDD");
    let roster = req.query.roster_name;
    let shift = req.query.shift_name;
    let locations = req.query.locations;
    let no_of_days = req.query.no_of_days;
    let days = 0;

    switch (no_of_days) {
        case 'Day':
            days = 1;
            break;

        case '8 Days':
            days = 8;
            break;

        case '16 Days':
            days = 16;
            break;

        case '32 Days':
            days = 32;
            break;

    }

    const request = new sql.Request(connection);

    locations = locations.replace("[", "(");
    locations = locations.replace("]", ")");
    locations = locations.replace("'", "''");
    locations = locations.replace(/"/g, "'");

    request.input('start_date', start_date);
    request.input('roster', roster);
    request.input('shift', shift);
    request.input('locations', locations);
    request.input('no_of_days', no_of_days);

    let sqlQuery = "SELECT TOP " + days + " bookings.date_string,\n" +
        "sum(case when leave_type_code = 'AU' then 1 else 0 end) as 'total_actups',\n" +
        "sum(case when leave_type_code = 'RC' then 1 else 0 end) as 'total_recalls',\n" +
        "sum(case when leave_type_code IN ('BERE','FAML','SIC','SICM','SLUP','SLUW','CVPL','CVUL','WRSL') then 1 else 0 end) as 'total_sick', \n" +
        "sum(case when leave_type_code = 'RRL' then 1 else 0 end) as 'total_rrl',\n" +
        "sum(case when leave_type_code IN ('ARL','LSL','TOIL','PHOL','VSBT','RET','XLSS','ULSS','XARL','XLSL','XPHL','XRET','XTOI','ULSL','UPHL','URET','AWOL','ADOP','MATH','MATP','PPLS','PUR4','PURA','LSLS','SOIL','LSLH') then 1 else 0 end) as 'total_leave',\n" +
        "sum(case when leave_type_code IN ('J','E','LD','SPEC','OTHE','UGAD','WC','LWOP','OL','NWD','RW','EXCH') then 1 else 0 end) as 'total_other'\n" +
        "FROM bookings inner join roster_shift_days on bookings.date_string = roster_shift_days.date_string WHERE bookings.roster = @roster AND roster_shift_days.roster = @roster AND bookings.shift = @shift AND roster_shift_days.shift = @shift AND bookings.location IN " + locations + " AND bookings.deleted = 0 AND bookings.day_deleted IS NULL AND bookings.status = 'Approved' AND bookings.date_string >= @start_date AND day_off = 0 GROUP BY bookings.date_string ORDER BY bookings.date_string";

    request.query(sqlQuery, (err, result) => {

        if (!err) {
            if (result.recordset) {

                let grand_totals = [];
                let dates_lookup = [];

                let amount = 0;
                let total_actups = {};
                let total_recalls = {};
                let total_sick = {};
                let total_rrl = {};
                let total_leave = {};
                let total_other = {};
                let dayNo = "";
                let value = "";
                let totalsArray = [];

                grand_totals = result.recordset;

                startOfPeriod = moment(startOfPeriod, "DD-MM-YYYY");

                // Populate date_lookup array - Used to lookup date from day position
                let dayMoment = startOfPeriod.clone();

                let dayString = dayMoment.format('YYYYMMDD');

                for (let day = 0; day <= days; day++) {
                    dates_lookup[day] = dayString;
                    dayString = dayMoment.add(1, 'days').format('YYYYMMDD');
                }

                //do actups
                for (let totalIndex = 0; totalIndex < grand_totals.length; totalIndex++) {
                    if (grand_totals[totalIndex].total_actups) {
                        for (let dayIndex = 1; dayIndex < dates_lookup.length + 1; dayIndex++) {
                            if (grand_totals[totalIndex].date_string == dates_lookup[dayIndex - 1]) {
                                dayNo = dayIndex.toString();
                                amount = grand_totals[totalIndex].total_actups;
                                value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                total_actups[dayNo] = value;
                                break
                            }
                        }
                    }
                }

                total_actups.pay_id = "";
                total_actups.first_name = "";
                total_actups.middle_name = "";
                total_actups.surname = "";
                total_actups.rank = "";
                total_actups.skills = null;
                total_actups.covid = "";
                total_actups.roster_arrangement = [];
                total_actups.id = "";
                total_actups.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>TOTAL ACT-UPS</span>"; //totals

                totalsArray.push(total_actups);

                //do recalls
                for (let totalIndex = 0; totalIndex < grand_totals.length; totalIndex++) {
                    if (grand_totals[totalIndex].total_recalls) {
                        for (let dayIndex = 1; dayIndex < dates_lookup.length + 1; dayIndex++) {
                            if (grand_totals[totalIndex].date_string == dates_lookup[dayIndex - 1]) {
                                dayNo = dayIndex.toString();
                                amount = grand_totals[totalIndex].total_recalls;
                                value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                total_recalls[dayNo] = value;
                                break
                            }
                        }
                    }
                }

                total_recalls.pay_id = "";
                total_recalls.first_name = "";
                total_recalls.middle_name = "";
                total_recalls.surname = "";
                total_recalls.rank = "";
                total_recalls.skills = null;
                total_recalls.covid = "";
                total_recalls.roster_arrangement = [];
                total_recalls.id = "";
                total_recalls.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>TOTAL RECALLS</span>"; //totals

                totalsArray.push(total_recalls);

                //do sickness
                for (let totalIndex = 0; totalIndex < grand_totals.length; totalIndex++) {
                    if (grand_totals[totalIndex].total_sick) {
                        for (let dayIndex = 1; dayIndex < dates_lookup.length + 1; dayIndex++) {
                            if (grand_totals[totalIndex].date_string == dates_lookup[dayIndex - 1]) {
                                dayNo = dayIndex.toString();
                                amount = grand_totals[totalIndex].total_sick;
                                value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                total_sick[dayNo] = value;
                                break
                            }
                        }
                    }
                }

                total_sick.pay_id = "";
                total_sick.first_name = "";
                total_sick.middle_name = "";
                total_sick.surname = "";
                total_sick.rank = "";
                total_sick.skills = null;
                total_sick.covid = "";
                total_sick.roster_arrangement = [];
                total_sick.id = "";
                total_sick.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>TOTAL SICK</span>"; //totals

                totalsArray.push(total_sick);

                //do rrl
                for (let totalIndex = 0; totalIndex < grand_totals.length; totalIndex++) {
                    if (grand_totals[totalIndex].total_rrl) {
                        for (let dayIndex = 1; dayIndex < dates_lookup.length + 1; dayIndex++) {
                            if (grand_totals[totalIndex].date_string == dates_lookup[dayIndex - 1]) {
                                dayNo = dayIndex.toString();
                                amount = grand_totals[totalIndex].total_rrl;
                                value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                total_rrl[dayNo] = value;
                                break
                            }
                        }
                    }
                }

                total_rrl.pay_id = "";
                total_rrl.first_name = "";
                total_rrl.middle_name = "";
                total_rrl.surname = "";
                total_rrl.rank = "";
                total_rrl.skills = null;
                total_rrl.covid = "";
                total_rrl.roster_arrangement = [];
                total_rrl.id = "";
                total_rrl.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>TOTAL RRL</span>"; //totals

                totalsArray.push(total_rrl);

                //do leave
                for (let totalIndex = 0; totalIndex < grand_totals.length; totalIndex++) {
                    if (grand_totals[totalIndex].total_leave) {
                        for (let dayIndex = 1; dayIndex < dates_lookup.length + 1; dayIndex++) {
                            if (grand_totals[totalIndex].date_string == dates_lookup[dayIndex - 1]) {
                                dayNo = dayIndex.toString();
                                amount = grand_totals[totalIndex].total_leave;
                                value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                total_leave[dayNo] = value;
                                break
                            }
                        }
                    }
                }

                total_leave.pay_id = "";
                total_leave.first_name = "";
                total_leave.middle_name = "";
                total_leave.surname = "";
                total_leave.rank = "";
                total_leave.skills = null;
                total_leave.covid = "";
                total_leave.roster_arrangement = [];
                total_leave.id = "";
                total_leave.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>TOTAL LEAVE</span>"; //totals

                totalsArray.push(total_leave);

                //do other
                for (let totalIndex = 0; totalIndex < grand_totals.length; totalIndex++) {
                    if (grand_totals[totalIndex].total_other) {
                        for (let dayIndex = 1; dayIndex < dates_lookup.length + 1; dayIndex++) {
                            if (grand_totals[totalIndex].date_string == dates_lookup[dayIndex - 1]) {
                                dayNo = dayIndex.toString();
                                amount = grand_totals[totalIndex].total_other;
                                value = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; color: black !important;'>" + amount.toString() + "</span>"; //total
                                total_other[dayNo] = value;
                                break
                            }
                        }
                    }
                }

                total_other.pay_id = "";
                total_other.first_name = "";
                total_other.middle_name = "";
                total_other.surname = "";
                total_other.rank = "";
                total_other.skills = null;
                total_other.covid = "";
                total_other.roster_arrangement = [];
                total_other.id = "";
                total_other.names = "<span style='font-size: 12px !important; line-height: 28px !important; font-weight: 500 !important; float: right !important; color: black !important;'>TOTAL OTHER</span>"; //totals

                totalsArray.push(total_other);

                //now send the array to client to parse to datatable
                res.status(200).send(totalsArray);

            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / all_stations_bookings)");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function shifts_load(req, res) {

    let roster_name = req.query.roster_name;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('roster_name', sql.VarChar(100));

    // Prepare statement
    ps.prepare("SELECT shifts,locations FROM rosters WHERE roster_name = @roster_name ORDER BY shifts", err => {

        if (!err) {
            // Execute statement
            ps.execute({roster_name: roster_name}, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: 'OK',
                        data: result.recordset
                    });
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / shifts_load)");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / shifts_load)");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function roster_load(req, res) {

    let roster = req.query.roster_name; // 'Metro'
    let shift = req.query.shift_name;   // 'A Shift'
    let time_period = req.query.time_period; // 'Month'
    let start_date = req.query.start_date;   // '26-11-2019' or Moment()

    let date_string;
    let days = 16;
    let sql_query = "";
    let shift_periods;
    let columns = [];
    let periods = [];

    async.series([

        function (callback) {

            date_string = moment(start_date, "DD-MM-YYYY").format("YYYYMMDD");

            switch (time_period) {
                case 'Day':
                    days = 1;
                    break;

                case '8 Days':
                    days = 8;
                    break;

                case '16 Days':
                    days = 16;
                    break;

                case '32 Days':
                    days = 32;
                    break;

            }

            sql_query = "SELECT TOP " + days + " shift_id, roster, shift, seq_date, date_string, seq_number, start_time, end_time, day_off, icon, colour, roster_id FROM roster_shift_days INNER JOIN shifts ON shift_id = shifts.id WHERE roster = @roster AND shift = @shift AND date_string >= @date_string ORDER BY date_string";

            callback();
        },
        function (callback) {

            // Find the current shift day and shift days details

            const ps = new sql.PreparedStatement(connection);

            // Inputs
            ps.input('roster', sql.VarChar(40));
            ps.input('shift', sql.VarChar(40));
            ps.input('date_string', sql.VarChar(8));


            // Prepare statement
            ps.prepare(sql_query, err => {

                if (!err) {
                    // Execute statement
                    ps.execute({roster: roster, shift: shift, date_string: date_string}, (err, result) => {

                        if (!err) {
                            // release the connection after queries are executed
                            ps.unprepare(err => {
                            });

                            if (result.recordset.length > 0) {

                                shift_periods = result.recordset;
                                callback();

                            } else {
                                shift_periods = undefined;
                                callback();
                            }
                        }
                    });
                }
            });
        },
        function (callback) {

            if (shift_periods != undefined) {

                columns.push({id: "names", header: "", minWidth: 190, adjust: true, css: "column_staff_names"});

                columns.push({id: "pay_id", header: "", width: 70, css: "column_staff_names"});

                columns.push({id: "rank", header: "", width: 50, css: "column_staff_names"});

                columns.push({id: "skills", header: "", width: 310, css: "skill_codes"});

                columns.push({id: "covid", header: "", width: 100, css: "column_staff_names"});

                // Keep pushing periods for the duration
                for (let day = 0; day < shift_periods.length; day++) {

                    let text = moment(shift_periods[day].date_string, "YYYYMMDD").format("ddd</br>DD/MM");

                    // Add Column id, header label & Date
                    columns.push({
                        id: (day + 1).toString(),
                        header: {
                            text: text,
                            css: "schedule_header",
                            date: shift_periods[day].date_string
                        },
                        fillspace: 1
                    });

                    // Push period
                    periods.push(shift_periods[day]);

                }

                columns.push({id: "right_border", header: {text: "", css: "right_border"}, maxWidth: 15});

                callback();
            } else {
                callback();
            }
        }

    ], function () {
        res.status(200).send({
            shift_days: periods,
            columns: columns
        });
    });

}


function all_stations_totals_header(req, res) {

    let roster = req.query.roster_name; // 'Metro'
    let shift = req.query.shift_name;   // 'A Shift'
    let time_period = req.query.time_period; // 'Month'
    let start_date = req.query.start_date;   // '26-11-2019' or Moment()

    let date_string;
    let days = 16;
    let sql_query = "";
    let shift_periods;
    let columns = [];
    let periods = [];

    async.series([

        function (callback) {

            date_string = moment(start_date, "DD-MM-YYYY").format("YYYYMMDD");

            switch (time_period) {
                case 'Day':
                    days = 1;
                    break;

                case '8 Days':
                    days = 8;
                    break;

                case '16 Days':
                    days = 16;
                    break;

                case '32 Days':
                    days = 32;
                    break;

            }

            sql_query = "SELECT TOP " + days + " shift_id, roster, shift, seq_date, date_string, seq_number, start_time, end_time, day_off, icon, colour, roster_id FROM roster_shift_days INNER JOIN shifts ON shift_id = shifts.id WHERE roster = @roster AND shift_name = @shift AND date_string >= @date_string ORDER BY date_string ASC";

            callback();
        },
        function (callback) {

            // Find the current shift day and shift days details

            const ps = new sql.PreparedStatement(connection);

            // Inputs
            ps.input('roster', sql.VarChar(40));
            ps.input('shift', sql.VarChar(40));
            ps.input('date_string', sql.VarChar(8));


            // Prepare statement
            ps.prepare(sql_query, err => {

                if (!err) {
                    // Execute statement
                    ps.execute({roster: roster, shift: shift, date_string: date_string}, (err, result) => {

                        if (!err) {
                            // release the connection after queries are executed
                            ps.unprepare(err => {
                            });

                            if (result.recordset.length > 0) {

                                shift_periods = result.recordset;
                                callback();

                            } else {
                                shift_periods = undefined;
                            }
                        }
                    });
                }
            });
        },
        function (callback) {

            if (shift_periods) {

                columns.push({id: "names", header: "", width: 190, css: "column_staff_names"});

                columns.push({id: "pay_id", header: "", width: 70, css: "column_staff_names"});

                columns.push({id: "rank", header: "", width: 50, css: "column_staff_names"});

                columns.push({id: "skills", header: "", width: 310, css: "skill_codes"});

                columns.push({id: "covid", header: "", width: 100, css: "column_staff_names"});

                // Keep pushing periods for the duration
                for (let day = 0; day < shift_periods.length; day++) {

                    let text = moment(shift_periods[day].date_string, "YYYYMMDD").format("ddd DD");

                    // Add Column id, header label & Date
                    columns.push({
                        id: (day + 1).toString(),
                        header: {
                            text: text,
                            css: "schedule_header",
                            date: shift_periods[day].date_string
                        },
                        fillspace: 1
                    });

                    // Push period
                    periods.push(shift_periods[day]);

                }

                columns.push({id: "right_border", header: {text: "", css: "right_border"}, maxWidth: 15});

                callback();
            } else {
                callback();
            }
        },
        function (callback) {

            callback();

        }

    ], function () {
        res.status(200).send({
            shift_days: periods,
            columns: columns
        });
    });

}


function getDayLeaveCounts(req, res) {

    let date_string = parseInt(req.query.date_string);
    let rank = req.query.rank;
    let roster = req.query.roster;
    let shift = req.query.shift;
    let no_of_days = req.query.no_of_days;
    let sqlQuery = "";

    const request = new sql.Request(connection);

    // Inputs
    request.input('date_string', date_string);
    request.input('rank', rank);
    request.input('roster', roster);
    request.input('shift', shift);

    if (roster == "Port Pirie") {
        if (no_of_days > 30) {
            sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL') AND bk_period = 'day') AS ARL_pending_day, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL') AND bk_period = 'night') AS ARL_pending_night, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND bookings.roster = @roster AND deleted = 0 AND day_deleted IS NULL AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL') AND bk_period = 'day') AS ARL_booked_day, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND bookings.roster = @roster AND deleted = 0 AND day_deleted IS NULL AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL') AND bk_period = 'night') AS ARL_booked_night, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_booked FROM bookings WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND bookings.roster = @roster AND bookings.shift = @shift";
        } else {
            sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH') AND bk_period = 'day') AS ALL_pending_day, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH') AND bk_period = 'night') AS ALL_pending_night, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH') AND bk_period = 'day') AS ALL_booked_day, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH') AND bk_period = 'night') AS ALL_booked_night, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH') AND bk_period = 'both') AS ALL_pending_both, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH') AND bk_period = 'both') AS ALL_booked_both FROM bookings";
        }
    } else if (roster == "OTR") {
        if (no_of_days > 30) {
            if (rank == "SO") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            } else if (rank == "FF" || rank == "SFF" || rank == "ESFF" || rank == "EFF") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            }
        } else {
            if (rank == "SO") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            } else if (rank == "FF" || rank == "SFF" || rank == "ESFF" || rank == "EFF") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            }
        }
    } else if (roster == "Metro") {
        if (no_of_days > 30) {
            if (rank == "SO" || rank == "CMD") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('SO','CMD') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL')) AS ARL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND bookings.roster = @roster AND deleted = 0 AND day_deleted IS NULL AND bookings.shift = @shift AND rank IN ('SO','CMD') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL')) AS ARL_booked, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('SO','CMD') AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('SO','CMD') AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_booked FROM bookings WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND bookings.roster = @roster AND bookings.shift = @shift";
            } else if (rank == "FF" || rank == "SFF" || rank == "MOFF" || rank == "ESFF" || rank == "EFF") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','MOFF','MOP','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL')) AS ARL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND bookings.roster = @roster AND deleted = 0 AND day_deleted IS NULL AND bookings.shift = @shift AND rank IN ('FF','SFF','MOFF','MOP','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL')) AS ARL_booked, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','MOFF','MOP','EFF','ESFF') AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','MOFF','MOP','EFF','ESFF') AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_booked FROM bookings WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND bookings.roster = @roster AND bookings.shift = @shift";
            }
        } else {
            if (rank == "SO" || rank == "CMD") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('SO','CMD') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('SO','CMD') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            } else if (rank == "FF" || rank == "SFF" || rank == "MOFF" || rank == "ESFF" || rank == "EFF") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','MOFF','MOP','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','MOFF','MOP','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            }
        }
    } else if (roster == "Mt Gambier") {
        if (no_of_days > 30) {
            if (rank == "SO") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            } else if (rank == "FF" || rank == "SFF" || rank == "ESFF" || rank == "EFF") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            }
        } else {
            if (rank == "SO") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank = 'SO' AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            } else if (rank == "FF" || rank == "SFF" || rank == "ESFF" || rank == "EFF") {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND rank IN ('FF','SFF','EFF','ESFF') AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            }
        }
    } else if (roster == "Comms") {
        if (shift == "A Shift" || shift == "B Shift" || shift == "C Shift" || shift == "D Shift") {
            if (no_of_days > 30) {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL')) AS ARL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND bookings.roster = @roster AND deleted = 0 AND day_deleted IS NULL AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL')) AS ARL_booked, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('LSL','LSLS','ULSL','LSLH')) AS LSL_booked FROM bookings WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND bookings.roster = @roster AND bookings.shift = @shift";
            } else {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            }
        } else if (shift == "CommCen E1" || shift == "CommCen E2") {
            if (no_of_days > 30) {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            } else {
                sqlQuery = "SELECT COUNT(*) AS total, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Pending' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_pending, (SELECT COUNT(*) FROM bookings INNER JOIN roster_arrangements ON bookings.pay_id = roster_arrangements.pay_id AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift and bookings.date_string = roster_arrangements.date_string WHERE bookings.date_string = @date_string AND bookings.linked_booking_id IS NULL AND status = 'Approved' AND deleted = 0 AND day_deleted IS NULL AND bookings.roster = @roster AND bookings.shift = @shift AND leave_type_code IN ('ARL','PHOL','RET','TOIL','SOIL','LSL','LSLS','ULSL','LSLH')) AS ALL_booked FROM bookings";
            }
        }
    }

    request.query(sqlQuery, (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send([]);
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getDayLeaveCounts)");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function get_locations_list(req, res) {

    let group_name = req.query.group_name;

    const request = new sql.Request(connection);

    // Inputs
    request.input('group_name', group_name);

    request.query("SELECT locations FROM location_groups WHERE group_name = @group_name", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / get_locations_list)");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


function getAvailabilityData(req, res) {

    let roster = req.query.roster;
    let shift = req.query.shift;
    let location = req.query.location;
    let date_string = parseInt(req.query.date_string);
    let otr_shift_type = req.query.otr_shift_type;
    let sel_shift_type = req.query.sel_shift_type;
    let locationArray = [];
    let locationList = "";
    let group_name = "";
    let arrayLength = 0;
    let x = 0;
    let sqlQuery = "";
    let otr_results = [];

    if (roster == "Metro") {
        if (location == "") {
            location = 'All Stations';
        } else if (location != 'All Stations' && location != 'Central Command' && location != 'Northern Command' && location != 'Southern Command') {
            location = 'All Stations';
        }
    } else {
        if (location == "") {
            location = roster;
        }
    }

    async.series([

        function (callback) {

            if (location === 'All Stations' || location === 'Central Command' || location === 'Northern Command' || location === 'Southern Command') {

                const get_request = new sql.Request(connection);

                get_request.input('location', location);
                get_request.query("SELECT locations FROM location_groups WHERE group_name = @location", (err, result) => {

                    if (!err) {
                        locationList = result.recordset[0].locations;
                        locationArray = locationList.split(",");
                        callback();
                    } else {
                        // Log error
                        console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getAvailabilityData)");
                    }

                });
            } else {
                locationArray = [location];
                callback();
            }

        },
        function (callback) {

            locationList = "(";

            arrayLength = locationArray.length;

            locationArray.forEach(function (value) {
                x += 1;

                value = value.replace("'", "''");

                if (x == arrayLength) {
                    locationList = locationList + "'" + value + "'";
                } else {
                    locationList = locationList + "'" + value + "',";
                }
            });

            locationList = locationList + ")";

            group_name = locationList;
            callback();

        },
        function (callback) {
            if (roster == 'Metro' && otr_shift_type == 'sun' && sel_shift_type.includes('fa-sun')) {
                getOTRAvailabilityData(date_string, function (results) {
                    otr_results = results;
                    callback();
                });
            } else {
                callback();
            }
        },
        function (callback) {

            const request = new sql.Request(connection);

            // Inputs
            request.input('group_name', group_name);
            request.input('roster', roster);
            request.input('shift', shift);
            request.input('date_string', date_string);

            sqlQuery = "SELECT name, min_SO, min_SF, min_CMD, min_FF, min_MOF\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank = 'CMD') as total_cmd_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'CMD' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_cmd_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'CMD' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_cmd_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank = 'CMD' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_cmd_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank = 'CMD') AS cmd_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank IN ('SO','COFF')) as total_so_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SO','COFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_so_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SO','COFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_so_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank IN ('SO','COFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_so_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank IN ('SO','COFF')) AS so_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank IN ('SFF','ESFF','SCOP','COP','MOP')) as total_sff_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SFF','SCOP','COP','MOP','ESFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_sff_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SFF','SCOP','COP','MOP','ESFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_sff_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank IN ('SFF','SCOP','COP','MOP','ESFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_sff_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank IN ('SFF','SCOP','COP','MOP','ESFF')) AS sff_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank IN ('FF','EFF')) as total_ff_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('FF','EFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_ff_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('FF','EFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_ff_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank IN ('FF','EFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_ff_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank IN ('FF','EFF')) AS ff_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank = 'MOFF') as total_moff_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'MOFF' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_moff_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'MOFF' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_moff_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank = 'MOFF' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_moff_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank = 'MOFF') AS moff_sm_back_to_hs\n" +
                "FROM locations WHERE name IN " + group_name + " AND roster = @roster";

            request.query(sqlQuery, (err, result) => {

                if (!err) {
                    if (result.recordset) {
                        if (otr_results.length > 0) {
                            res.status(200).send({
                                metro: result.recordset,
                                otr: otr_results
                            })
                        } else {
                            res.status(200).send({
                                metro: result.recordset,
                                otr: []
                            })
                        }
                    } else {
                        res.status(200).send();
                    }
                } else {
                    // Log error
                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getAvailabilityData)");

                    // Error
                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }
            });

            callback();

        }], function () {

    });
}

function getOTRAvailabilityData(date_string, response) {

    let roster = "OTR";
    let shift = "OTR";
    let location = "('OTR Central','OTR North','OTR South')";
    let group_name = "";
    let sqlQuery = "";

    async.series([

        function (callback) {

            const request = new sql.Request(connection);

            group_name = location;

            // Inputs
            request.input('group_name', group_name);
            request.input('roster', roster);
            request.input('shift', shift);
            request.input('date_string', date_string);

            sqlQuery = "SELECT name, min_SO, min_SF, min_CMD, min_FF, min_MOF\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank = 'CMD') as total_cmd_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'CMD' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_cmd_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'CMD' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_cmd_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank = 'CMD' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_cmd_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank = 'CMD') AS cmd_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank IN ('SO','COFF')) as total_so_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SO','COFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_so_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SO','COFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_so_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank IN ('SO','COFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_so_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank IN ('SO','COFF')) AS so_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank IN ('SFF','ESFF','SCOP','COP','MOP')) as total_sff_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SFF','SCOP','COP','MOP','ESFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_sff_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('SFF','SCOP','COP','MOP','ESFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_sff_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank IN ('SFF','SCOP','COP','MOP','ESFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_sff_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank IN ('SFF','SCOP','COP','MOP','ESFF')) AS sff_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank IN ('FF','EFF')) as total_ff_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('FF','EFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_ff_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank IN ('FF','EFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_ff_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank IN ('FF','EFF') AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_ff_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank IN ('FF','EFF')) AS ff_sm_back_to_hs\n" +
                ", (SELECT COUNT(DISTINCT pay_id) FROM roster_arrangements WHERE roster = @roster AND shift = @shift AND location = locations.name AND date_string = @date_string AND rank = 'MOFF') as total_moff_ras\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'MOFF' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type <> 'standby_link') as total_moff_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND rank = 'MOFF' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','ULSL','XLSL') AND bk_period = 'both') as total_moff_both_bks\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements ON bookings.date_string = roster_arrangements.date_string AND bookings.roster = roster_arrangements.roster AND bookings.shift = roster_arrangements.shift AND bookings.location = roster_arrangements.location AND bookings.pay_id = roster_arrangements.pay_id WHERE bookings.roster = @roster AND bookings.shift = @shift AND bookings.location = locations.name AND bookings.date_string = @date_string AND actup_rank = 'MOFF' AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code NOT IN ('DC','RO','IA','T/E','REL','DRILL','RC','VSBW') AND bk_period = shift_type AND booking_type NOT IN ('standby_link','staff_movement','overtime')) as total_moff_actups\n" +
                ", (SELECT COUNT(DISTINCT bookings.pay_id) FROM bookings JOIN roster_arrangements r ON bookings.date_string = r.date_string AND bookings.pay_id = r.pay_id WHERE booking_type = 'staff_movement' AND RIGHT(leave_type_code,1) = 'R' AND deleted = 0 AND day_deleted IS NULL AND moved_roster = @roster AND moved_shift = @shift AND moved_location = r.location AND bookings.date_string = r.date_string AND bk_period = r.shift_type AND moved_location = locations.name AND r.type = 'Permanent' AND r.date_string = @date_string AND rank = 'MOFF') AS moff_sm_back_to_hs\n" +
                "FROM locations WHERE name IN " + group_name + " AND roster = @roster";

            request.query(sqlQuery, (err, result) => {

                if (!err) {
                    if (result.recordset) {
                        response(result.recordset);
                    } else {
                        response([])
                    }
                } else {
                    // Log error
                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getOTRAvailabilityData)");
                    // Error
                    response([]);
                }
            });

            callback();

        }], function () {

    });
}

function getAllPublicHolidays(req, res) {

    const request = new sql.Request(connection);

    request.query("SELECT * FROM public_holidays WHERE state = 'SA'", (err, result) => {

        if (!err) {
            if (result.recordset) {
                res.status(200).send(result.recordset);
            } else {
                res.status(200).send();
            }
        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / getAllPublicHolidays)");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });
}


module.exports = REST_ROUTER;