let moment = require('moment');
const sql = require('mssql');
const superagent = require('superagent');
let path = require('path');
let bearer_token = "";
let origin_url = "";
let live_site = true;

let connection;

function REST_ROUTER(router, cn) {

    connection = cn;

    let self = this;
    self.handleRoutes(router);

    if (connection.config.database == "sapphire_live") {
        live_site = true;
    } else {
        live_site = false;
    }

}


let sms_auth_keys = {
    api_key: "Acunx6t6bO8iH7GJVGPUyeezdFhdALB5gRuxaMf1", //old_api_key = "sav2gcw2newtxrb4zfy535dc"
    auth_key: "bWZzYXBpOjI2aWZXc2ghenI0ejZzcjU=", //old_auth_key = "bS50cmlnd2VsbDo4YkFIRFNBRnNTelp2VWE1"
    template: "Sapphire",
    url: "https://api.au.whispir.com/messages"
};

REST_ROUTER.prototype.handleRoutes = function (router) {

    //MESSAGING
    router.get("/get_employees", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getEmployees(req, res);
        } else {
            res.status(401).send();
        }
    });


    router.post("/save_message", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            saveMessage(req, res);
        } else {
            res.status(401).send();
        }
    });


    router.get("/get_messages", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getMessages(req, res);
        } else {
            res.status(401).send();
        }
    });


    router.post("/send_messages", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sendSMSMessages(req, res);
        } else {
            res.status(401).send();
        }
    });


    router.post("/send_response_message", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sendSMSMessageToResponders(req, res);
        } else {
            res.status(401).send();
        }
    });

    router.post("/send_C19_messages", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            sendC19Messages(req, res);
        } else {
            res.status(401).send();
        }
    });


    router.get("/get_msg_info", function (req, res) {
        getMessageInfo(req, res);
    });


    router.get("/check_response", function (req, res) {
        checkResponse(req, res);
    });


    router.post("/save_response", function (req, res) {
        saveResponse(req, res);
    });


    router.put("/update_response", function (req, res) {
        updateResponse(req, res);
    });


    router.get("/get_responses", function (req, res) {
        bearer_token = req.headers.authorization.split(" ")[1];
        origin_url = req.headers.referer;

        if (bearer_token === api_register[0].api_key && (origin_url.includes("https://sapphire.mfs.sa.gov.au/") || origin_url === "http://localhost:63343/" || origin_url.includes("https://sapphire-uat.mfs.sa.gov.au/"))) {
            getResponses(req, res);
        } else {
            res.status(401).send();
        }
    });


};


function getMessageInfo(req, res) {

    let message_id = req.query.message_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('message_id', sql.VarChar(10));

    ps.prepare("SELECT sender_id, sent_date, body, surname, first_name FROM messages JOIN employees ON messages.sender_id = employees.pay_id WHERE message_id = @message_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                message_id: message_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.sent_date = moment.tz(record.sent_date, process.env.timezone).format("dddd, DD/MM/YYYY HH:mm");
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function getEmployees(req, res) {

    let msg_date = req.query.msg_date;
    let msg_rosters = req.query.msg_rosters;
    let msg_shifts = req.query.msg_shifts;
    let msg_locations = req.query.msg_locations;
    let msg_ranks = req.query.msg_ranks;
    let msg_skill_1 = req.query.msg_skill_1;
    let msg_skill_2 = req.query.msg_skill_2;
    let include_leave = req.query.include_leave;
    let overtimeFatigueMode = req.query.overtimeFatigueMode;
    let overtimeFatigueSM = [];
    let sqlString = "";

    const request = new sql.Request(connection);

    msg_rosters = "'" + msg_rosters;
    msg_rosters = msg_rosters.replace(/,/g, "','");
    msg_rosters = msg_rosters + "'";

    msg_shifts = "'" + msg_shifts;
    msg_shifts = msg_shifts.replace(/,/g, "','");
    msg_shifts = msg_shifts + "'";

    msg_locations = msg_locations.replace("'", "''");
    msg_locations = "'" + msg_locations;
    msg_locations = msg_locations.replace(/,/g, "','");
    msg_locations = msg_locations + "'";

    msg_ranks = "'" + msg_ranks;
    msg_ranks = msg_ranks.replace(/,/g, "','");
    msg_ranks = msg_ranks + "'";

    msg_skill_1 = "'" + msg_skill_1 + "'";
    msg_skill_2 = "'" + msg_skill_2 + "'";

    // Inputs
    request.input('msg_date', sql.DateTime, msg_date);
    request.input('msg_rosters', msg_rosters);
    request.input('msg_shifts', msg_shifts);
    request.input('msg_locations', msg_locations);
    request.input('msg_ranks', msg_ranks);
    request.input('msg_skill_1', msg_skill_1);
    request.input('msg_skill_2', msg_skill_2);

    if (msg_shifts == "''" && msg_locations == "''") {
        if (msg_skill_1 == "''") {
            if (include_leave == 1) {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") ORDER by surname";
            } else {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT count(*) from bookings WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','XLSL')) as on_leave FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND on_leave = 0 ORDER by surname";
            }
        } else {
            if (msg_skill_2 == "''") {
                if (include_leave == 1) {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 ORDER by surname";
                } else {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT count(*) from bookings WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','XLSL')) as on_leave FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND on_leave = 0 ORDER by surname";
                }
            } else {
                if (include_leave == 1) {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND CHARINDEX(''" + msg_skill_2 + "'',all_skill_codes) > 0 ORDER by surname";
                } else {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT count(*) from bookings WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','XLSL')) as on_leave FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND CHARINDEX(''" + msg_skill_2 + "'',all_skill_codes) > 0 AND on_leave = 0 ORDER by surname";
                }
            }
        }
    } else {
        if (msg_skill_1 == "''") {
            if (include_leave == 1) {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") ORDER by surname";
            } else {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT count(*) from bookings WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','XLSL')) as on_leave FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND on_leave = 0 ORDER by surname";
            }
        } else {
            if (msg_skill_2 == "''") {
                if (include_leave == 1) {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 ORDER by surname";
                } else {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT count(*) from bookings WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','XLSL')) as on_leave FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND on_leave = 0 ORDER by surname";
                }
            } else {
                if (include_leave == 1) {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND CHARINDEX(''" + msg_skill_2 + "'',all_skill_codes) > 0 ORDER by surname";
                } else {
                    sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT count(*) from bookings WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND leave_type_code IN ('RRL','LSL','XLSL')) as on_leave FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND CHARINDEX(''" + msg_skill_2 + "'',all_skill_codes) > 0 AND on_leave = 0 ORDER by surname";
                }
            }
        }
    }

    request.query(sqlString, (err, result) => {

        if (!err) {

            if (overtimeFatigueMode == "true") {
                getRecentSMs(msg_date, msg_rosters, msg_shifts, msg_locations, msg_ranks, msg_skill_1, msg_skill_2, function (results) {
                    overtimeFatigueSM = results;
                    if (overtimeFatigueSM.length > 0) {
                        if (result.recordset) {
                            res.status(200).send(result.recordset.concat(overtimeFatigueSM));
                        } else {
                            res.status(200).send();
                        }
                    } else {
                        if (result.recordset) {
                            res.status(200).send(result.recordset);
                        } else {
                            res.status(200).send();
                        }
                    }
                })
            } else {
                if (result.recordset) {
                    res.status(200).send(result.recordset);
                } else {
                    res.status(200).send();
                }
            }

        } else {
            // Log error
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            // Error
            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function getRecentSMs(filter_date, msg_rosters, msg_shifts, msg_locations, msg_ranks, msg_skill_1, msg_skill_2, callback) {

    let date_1 = moment(filter_date, "YYYY-MM-DD HH:mm").add(23, 'hours');
    let msg_end_date = date_1.toDate();
    let date_2 = date_1.subtract(8, 'days');
    let msg_date = date_2.toDate();
    let sqlString = "";

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('msg_date', sql.DateTime);
    ps.input('msg_end_date', sql.DateTime);

    if (msg_shifts == "''" && msg_locations == "''") {
        if (msg_skill_1 == "''") {
            sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as roster, (SELECT TOP 1 shift from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as shift, (SELECT TOP 1 location from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT TOP 1 leave_type_code FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code, (SELECT TOP 1 date_string FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code_date FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND sm_code IS NOT NULL ORDER by surname";
        } else {
            if (msg_skill_2 == "''") {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as roster, (SELECT TOP 1 shift from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as shift, (SELECT TOP 1 location from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT TOP 1 leave_type_code FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code, (SELECT TOP 1 date_string FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code_date FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND sm_code IS NOT NULL ORDER by surname";
            } else {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as roster, (SELECT TOP 1 shift from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as shift, (SELECT TOP 1 location from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT TOP 1 leave_type_code FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code, (SELECT TOP 1 date_string FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code_date FROM employees) as results where roster IN (" + msg_rosters + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND CHARINDEX(''" + msg_skill_2 + "'',all_skill_codes) > 0 AND sm_code IS NOT NULL ORDER by surname";
            }
        }
    } else {
        if (msg_skill_1 == "''") {
            sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as roster, (SELECT TOP 1 shift from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as shift, (SELECT TOP 1 location from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT TOP 1 leave_type_code FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code, (SELECT TOP 1 date_string FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code_date FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND sm_code IS NOT NULL ORDER by surname";
        } else {
            if (msg_skill_2 == "''") {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as roster, (SELECT TOP 1 shift from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as shift, (SELECT TOP 1 location from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT TOP 1 leave_type_code FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code, (SELECT TOP 1 date_string FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code_date FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND sm_code IS NOT NULL ORDER by surname";
            } else {
                sqlString = "SELECT * FROM (SELECT pay_id, first_name, middle_name, surname, personal_mobile_no, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as ra_rank, (SELECT TOP 1 roster from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as roster, (SELECT TOP 1 shift from roster_arrangements WHERE roster_date BETWEEN @msg_date AND @msg_end_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = FORMAT(@msg_date, 'yyyyMMdd') AND roster_arrangements.pay_id = employees.pay_id AND type = 'Temporary' ORDER BY roster_date DESC) as location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > @msg_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT TOP 1 leave_type_code FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code, (SELECT TOP 1 date_string FROM bookings WHERE start_date BETWEEN @msg_date AND @msg_end_date AND bookings.pay_id = employees.pay_id AND deleted = 0 AND day_deleted IS NULL AND status = 'Approved' AND booking_type = 'staff_movement' ORDER BY start_date DESC) as sm_code_date FROM employees) as results where roster IN (" + msg_rosters + ") AND shift IN (" + msg_shifts + ") AND location IN (" + msg_locations + ") AND ra_rank IN (" + msg_ranks + ") AND CHARINDEX(''" + msg_skill_1 + "'',all_skill_codes) > 0 AND CHARINDEX(''" + msg_skill_2 + "'',all_skill_codes) > 0 AND sm_code IS NOT NULL ORDER by surname";
            }
        }
    }

    // Prepare statement
    ps.prepare(sqlString, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                msg_date: msg_date,
                msg_end_date: msg_end_date,
                msg_rosters: msg_rosters,
                msg_shifts: msg_shifts,
                msg_locations: msg_locations,
                msg_ranks: msg_ranks

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    callback(result.recordset);

                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    callback([]);
                }
            });
        } else {
            ps.unprepare(err => {
            });
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
            callback([]);
        }
    });
}


function saveResponse(req, res) {

    let message_id = req.body.message_id;
    let responder_id = req.body.responder_id;
    let response = req.body.response;
    let response_date = moment().toDate();

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('message_id', sql.VarChar(10));
    ps.input('responder_id', sql.Int);
    ps.input('response', sql.VarChar(20));
    ps.input('response_date', sql.DateTime);


    // Prepare statement
    ps.prepare("INSERT INTO responses(message_id, responder_id, response, response_date) VALUES(@message_id, @responder_id, @response, @response_date)", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                message_id: message_id,
                responder_id: responder_id,
                response: response,
                response_date: response_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: "OK"
                    });
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function updateResponse(req, res) {

    let message_id = req.body.message_id;
    let responder_id = req.body.responder_id;
    let response = req.body.response;
    let response_date = moment().toDate();

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('message_id', sql.VarChar(10));
    ps.input('responder_id', sql.Int);
    ps.input('response', sql.VarChar(20));
    ps.input('response_date', sql.DateTime);


    // Prepare statement
    ps.prepare("UPDATE responses SET response = @response, response_date = @response_date WHERE message_id = @message_id AND responder_id = @responder_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                message_id: message_id,
                responder_id: responder_id,
                response: response,
                response_date: response_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: "OK"
                    });
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function saveMessage(req, res) {

    let message_id = req.body.msg_id;
    let msg_date = req.body.msg_date;
    let rosters = req.body.rosters;
    let shifts = req.body.shifts;
    let locations = req.body.locations;
    let ranks = req.body.ranks;
    let subject = req.body.subject;
    let body = req.body.body;
    let type = req.body.type;
    let no_of_recipients = req.body.no_of_recipients;
    let sent_date = moment().toDate();
    let sender_id = req.body.sender_id;
    let skill_code_1 = req.body.skill_code_1;
    let skill_code_2 = req.body.skill_code_2;
    let no_response = req.body.no_response;
    let shift_period = req.body.shift_period;

    if (no_response == 0) {
        no_response = false;
    } else {
        no_response = true;
    }

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('message_id', sql.VarChar(10));
    ps.input('msg_date', sql.Numeric(8, 0));
    ps.input('rosters', sql.VarChar(500));
    ps.input('shifts', sql.VarChar(500));
    ps.input('locations', sql.VarChar(500));
    ps.input('ranks', sql.VarChar(500));
    ps.input('subject', sql.VarChar(50));
    ps.input('body', sql.VarChar(sql.MAX));
    ps.input('type', sql.VarChar(10));
    ps.input('no_of_recipients', sql.Int);
    ps.input('sent_date', sql.DateTime);
    ps.input('sender_id', sql.Int);
    ps.input('skill_code_1', sql.VarChar(5));
    ps.input('skill_code_2', sql.VarChar(5));
    ps.input('no_response', sql.Bit);
    ps.input('shift_period', sql.VarChar(5));


    // Prepare statement
    ps.prepare("INSERT INTO messages(message_id, msg_date, rosters, shifts, locations, ranks, subject, body, type, no_of_recipients, sent_date, sender_id, skill_code_1, skill_code_2, no_response, shift_period) VALUES(@message_id, @msg_date, @rosters, @shifts, @locations, @ranks, @subject, @body, @type, @no_of_recipients, @sent_date, @sender_id, @skill_code_1, @skill_code_2, @no_response, @shift_period)", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                message_id: message_id,
                msg_date: msg_date,
                rosters: rosters,
                shifts: shifts,
                locations: locations,
                ranks: ranks,
                subject: subject,
                body: body,
                type: type,
                no_of_recipients: no_of_recipients,
                sent_date: sent_date,
                sender_id: sender_id,
                skill_code_1: skill_code_1,
                skill_code_2: skill_code_2,
                no_response: no_response,
                shift_period: shift_period

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    res.status(200).send({
                        response: "OK"
                    });
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function getMessages(req, res) {

    let search_date = req.query.sent_date;
    let start_date = moment(search_date).format("YYYY-MM-DD 00:01");
    let end_date = moment(search_date).format("YYYY-MM-DD 23:59");

    let adv_msg_filters = req.query.adv_msg_filters;
    let adv_msg_search_date = moment(req.query.adv_msg_search_date).format("DD/MM/YYYY");
    let adv_msg_search_time = req.query.adv_msg_search_time;
    let adv_msg_search_station = req.query.adv_msg_search_station;

    let sqlQuery = "";

    adv_msg_search_station = adv_msg_search_station.replace("'","''");

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('start_date', sql.DateTime);
    ps.input('end_date', sql.DateTime);


    if (adv_msg_filters == 1){
        if (adv_msg_search_time == "" && adv_msg_search_station == ""){
            sqlQuery = "SELECT * FROM messages WHERE body LIKE " + "'%" + adv_msg_search_date + "%'" + " ORDER BY sent_date DESC";
        } else if (adv_msg_search_time == "" && adv_msg_search_station != ""){
            sqlQuery = "SELECT * FROM messages WHERE body LIKE " + "'%" + adv_msg_search_date + "%'" + " AND body LIKE " + "'%" + adv_msg_search_station + "%'" + " ORDER BY sent_date DESC";
        } else if (adv_msg_search_time != "" && adv_msg_search_station == ""){
            sqlQuery = "SELECT * FROM messages WHERE body LIKE " + "'%" + adv_msg_search_date + "%'" + " AND body LIKE " + "'%" + adv_msg_search_time + "%'" + " ORDER BY sent_date DESC";
        } else if (adv_msg_search_time != "" && adv_msg_search_station != ""){
            sqlQuery = "SELECT * FROM messages WHERE body LIKE " + "'%" + adv_msg_search_date + "%'" + " AND body LIKE " + "'%" + adv_msg_search_time + "%'" + " AND body LIKE " + "'%" + adv_msg_search_station + "%'" + " ORDER BY sent_date DESC";
        }
    } else {
        sqlQuery = "SELECT * FROM messages WHERE sent_date BETWEEN @start_date AND @end_date ORDER BY sent_date DESC";
    }

    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                start_date: start_date,
                end_date: end_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.sent_date = moment.tz(record.sent_date, process.env.timezone).format("DD/MM/YYYY HH:mm");
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}

function getResponses(req, res) {

    let message_id = req.query.message_id;
    let msg_date = req.query.msg_date;
    let expiry_date = moment(msg_date, "YYYYMMDD").format("YYYY-MM-DD 23:59");
    let response = req.query.response;
    let fatigue = req.query.fatigue;
    let efy_date = "";
    let sqlQuery = "";

    const ps = new sql.PreparedStatement(connection);

    efy_date = moment(expiry_date).subtract(1, "years");
    efy_date = moment(efy_date).add(1, "minutes").format("YYYY-MM-DD HH:mm");

    /* if (moment().format("YYYY") == "2022" && moment().isBefore(moment("2022-07-01 00:00","YYYY-MM-DD HH:mm"))){
         efy_date = "2021-07-01 00:00";
     } else if (moment().format("YYYY") == "2022" && moment().isSameOrAfter(moment("2022-07-01 00:00","YYYY-MM-DD HH:mm"))){
         efy_date = "2022-07-01 00:00";
     } else if (moment().format("YYYY") == "2023" && moment().isBefore(moment("2023-07-01 00:00","YYYY-MM-DD HH:mm"))) {
         efy_date = "2022-07-01 00:00";
     } else if (moment().format("YYYY") == "2023" && moment().isSameOrAfter(moment("2023-07-01 00:00","YYYY-MM-DD HH:mm"))){
         efy_date = "2023-07-01 00:00";
     } else if (moment().format("YYYY") == "2024" && moment().isBefore(moment("2024-07-01 00:00","YYYY-MM-DD HH:mm"))) {
         efy_date = "2023-07-01 00:00";
     } else if (moment().format("YYYY") == "2024" && moment().isSameOrAfter(moment("2024-07-01 00:00","YYYY-MM-DD HH:mm"))){
         efy_date = "2024-07-01 00:00";
     } else if (moment().format("YYYY") == "2025" && moment().isBefore(moment("2025-07-01 00:00","YYYY-MM-DD HH:mm"))) {
         efy_date = "2024-07-01 00:00";
     } else if (moment().format("YYYY") == "2025" && moment().isSameOrAfter(moment("2025-07-01 00:00","YYYY-MM-DD HH:mm"))){
         efy_date = "2025-07-01 00:00";
     } else if (moment().format("YYYY") == "2026" && moment().isBefore(moment("2026-07-01 00:00","YYYY-MM-DD HH:mm"))) {
         efy_date = "2025-07-01 00:00";
     } else if (moment().format("YYYY") == "2026" && moment().isSameOrAfter(moment("2026-07-01 00:00","YYYY-MM-DD HH:mm"))){
         efy_date = "2026-07-01 00:00";
     } else if (moment().format("YYYY") == "2027" && moment().isBefore(moment("2027-07-01 00:00","YYYY-MM-DD HH:mm"))) {
         efy_date = "2026-07-01 00:00";
     } else if (moment().format("YYYY") == "2027" && moment().isSameOrAfter(moment("2027-07-01 00:00","YYYY-MM-DD HH:mm"))){
         efy_date = "2027-07-01 00:00";
     }*/

    // Inputs
    ps.input('message_id', sql.VarChar(10));
    ps.input('msg_date', sql.Numeric(8, 0));
    ps.input('expiry_date', sql.DateTime);
    ps.input('efy_date', sql.DateTime);

    if (response == 1) {
        if (fatigue == 1) {
            sqlQuery = "SELECT responses.*, surname, first_name, middle_name, personal_mobile_no, notifications_email, overtime_hrs_balance, overtime_hrs_date, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank FROM roster_arrangements WHERE pay_id = employees.pay_id AND date_string = @msg_date AND type = 'Permanent') AS rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > = @expiry_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT SUM(hours) FROM bookings WHERE deleted = 0 AND day_deleted IS NULL AND leave_type_code = 'RC' AND start_date BETWEEN @efy_date AND @expiry_date AND pay_id = employees.pay_id) AS overtime_hours FROM responses JOIN employees ON responses.responder_id = employees.pay_id WHERE message_id = @message_id AND response = 'Yes' ORDER BY overtime_hours";
        } else {
            sqlQuery = "SELECT responses.*, surname, first_name, middle_name, personal_mobile_no, notifications_email, overtime_hrs_balance, overtime_hrs_date, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank FROM roster_arrangements WHERE pay_id = employees.pay_id AND date_string = @msg_date AND type = 'Permanent') AS rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > = @expiry_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT SUM(hours) FROM bookings WHERE deleted = 0 AND day_deleted IS NULL AND leave_type_code = 'RC' AND start_date BETWEEN @efy_date AND @expiry_date AND pay_id = employees.pay_id) AS overtime_hours FROM responses JOIN employees ON responses.responder_id = employees.pay_id WHERE message_id = @message_id AND response = 'Yes' ORDER BY response_date";
        }
    } else {
        if (fatigue == 1) {
            sqlQuery = "SELECT responses.*, surname, first_name, middle_name, personal_mobile_no, notifications_email, overtime_hrs_balance, overtime_hrs_date, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank FROM roster_arrangements WHERE pay_id = employees.pay_id AND date_string = @msg_date AND type = 'Permanent') AS rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > = @expiry_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT SUM(hours) FROM bookings WHERE deleted = 0 AND day_deleted IS NULL AND leave_type_code = 'RC' AND start_date BETWEEN @efy_date AND @expiry_date AND pay_id = employees.pay_id) AS overtime_hours FROM responses JOIN employees ON responses.responder_id = employees.pay_id WHERE message_id = @message_id ORDER BY overtime_hours";
        } else {
            sqlQuery = "SELECT responses.*, surname, first_name, middle_name, personal_mobile_no, notifications_email, overtime_hrs_balance, overtime_hrs_date, (SELECT TOP 1 location from employee_hs_preferences WHERE pay_id = employees.pay_id AND pref_no = 1) as hs_1_pref, (SELECT TOP 1 rank FROM roster_arrangements WHERE pay_id = employees.pay_id AND date_string = @msg_date AND type = 'Permanent') AS rank, (SELECT TOP 1 roster from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS roster, (SELECT TOP 1 shift from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS shift, (SELECT TOP 1 location from roster_arrangements WHERE date_string = @msg_date AND roster_arrangements.pay_id = employees.pay_id AND type = 'Permanent') AS location, (STUFF((SELECT (',' + '''' + code + '''') FROM employee_skills WHERE employee_skills.pay_id = employees.pay_id AND expire_date > = @expiry_date FOR XML PATH('')), 1, 1, '')) AS all_skill_codes, (SELECT SUM(hours) FROM bookings WHERE deleted = 0 AND day_deleted IS NULL AND leave_type_code = 'RC' AND start_date BETWEEN @efy_date AND @expiry_date AND pay_id = employees.pay_id) AS overtime_hours FROM responses JOIN employees ON responses.responder_id = employees.pay_id WHERE message_id = @message_id ORDER BY response_date";
        }
    }

    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({
                message_id: message_id,
                msg_date: msg_date,
                expiry_date: expiry_date,
                efy_date: efy_date

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.response_date = moment.tz(record.response_date, process.env.timezone).format("DD/MM/YYYY HH:mm");
                            record.overtime_hrs_date = moment.tz(record.overtime_hrs_date, process.env.timezone);
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function checkResponse(req, res) {

    let message_id = req.query.message_id;
    let responder_id = req.query.responder_id;

    const ps = new sql.PreparedStatement(connection);

    // Inputs
    ps.input('message_id', sql.VarChar(10));
    ps.input('responder_id', sql.Int);

    ps.prepare("SELECT * FROM responses WHERE message_id = @message_id AND responder_id = @responder_id", err => {

        if (!err) {
            // Execute statement
            ps.execute({
                message_id: message_id,
                responder_id: responder_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    if (result.recordset) {

                        result.recordset.forEach(function (record) {
                            record.sent_date = moment.tz(record.sent_date, process.env.timezone).format("dddd, DD/MM/YYYY HH:mm");
                        });

                        res.status(200).send(result.recordset);

                    } else {
                        res.status(200).send();
                    }
                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    res.status(500).send({
                        response: 'ERROR',
                        error: JSON.stringify(err)
                    });
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            res.status(500).send({
                response: 'ERROR',
                error: JSON.stringify(err)
            });
        }
    });

}


function sendSMSMessageToResponders(req, res) {

    let subject = req.body.subject;
    let message = req.body.message;
    let recipients = req.body.recipients_array;
    let recipients_array = JSON.parse(recipients);
    let error_found = false;
    let send_error = 0;

    //put all phone numbers in a comma separated string format
    let phone_numbers_array = [];

    recipients_array.forEach((values) => {
        phone_numbers_array.push(values.mobile_no);
    });

    let phone_numbers_string = JSON.stringify(phone_numbers_array);
    phone_numbers_string = phone_numbers_string.replace("[", "");
    phone_numbers_string = phone_numbers_string.replace("]", "");
    phone_numbers_string = phone_numbers_string.replace(/"/g, "");

    const sms_object = {
        to: phone_numbers_string,
        messageTemplateName: sms_auth_keys.template,
        subject: subject,
        body: message
    };

    superagent
        .post(sms_auth_keys.url + '?apikey=' + sms_auth_keys.api_key)
        .send(sms_object)
        .set('Content-Type', 'application/vnd.whispir.message-v1+json')
        .set('accept', 'application/vnd.whispir.message-v1+json')
        .set('x-api-key', sms_auth_keys.api_key)
        .set('Authorization', 'Basic ' + sms_auth_keys.auth_key)
        .end((err, res) => {
            if (err.statusCode != 202) {
                error_found = true;
                send_error = err.statusCode;
            }
        });


    if (error_found === false) {
        res.status(202).send("ok")
    } else {
        console.log(moment().format("DD-MM-YY HH:mm") + " | " + send_error + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
        res.status(400).send("error")
    }

}


function sendSMSMessages(req, res) {


    if (live_site === true) {

        let msg_id = req.body.msg_id;
        let subject = req.body.subject;
        let message = req.body.message;
        let recipients = req.body.recipients_array;
        let recipients_array = JSON.parse(recipients);
        let no_response = req.body.no_response;
        let sms_text = "";
        let error_found = false;
        let send_error = 0;

        if (no_response == 0) {

            recipients_array.forEach((values, index) => {

                setTimeout(function () {

                    sms_text = message + "\n" + "\n" + "Respond via --> https://sapphire.mfs.sa.gov.au/response/" + "?p_id=" + values.pay_id + "&" + "m_id=" + msg_id;

                    const sms_object = {};

                    sms_object.to = values.mobile_no;
                    sms_object.messageTemplateName = sms_auth_keys.template;
                    sms_object.subject = subject;
                    sms_object.body = sms_text;

                    superagent
                        .post(sms_auth_keys.url + '?apikey=' + sms_auth_keys.api_key)
                        .send(sms_object)
                        .set('Content-Type', 'application/vnd.whispir.message-v1+json')
                        .set('accept', 'application/vnd.whispir.message-v1+json')
                        .set('x-api-key', sms_auth_keys.api_key)
                        .set('Authorization', 'Basic ' + sms_auth_keys.auth_key)
                        .end((err, res) => {
                            if (err.statusCode != 202) {
                                error_found = true;
                                send_error = err.statusCode;
                            }
                        });

                }, 400 * index)

            });

        } else {

            sms_text = message;

            //put all phone numbers in a comma separated string format
            let phone_numbers_array = [];

            recipients_array.forEach((values) => {
                phone_numbers_array.push(values.mobile_no);
            });

            let phone_numbers_string = JSON.stringify(phone_numbers_array);
            phone_numbers_string = phone_numbers_string.replace("[", "");
            phone_numbers_string = phone_numbers_string.replace("]", "");
            phone_numbers_string = phone_numbers_string.replace(/"/g, "");

            const sms_object = {
                to: phone_numbers_string,
                messageTemplateName: sms_auth_keys.template,
                subject: subject,
                body: sms_text
            };

            superagent
                .post(sms_auth_keys.url + '?apikey=' + sms_auth_keys.api_key)
                .send(sms_object)
                .set('Content-Type', 'application/vnd.whispir.message-v1+json')
                .set('accept', 'application/vnd.whispir.message-v1+json')
                .set('x-api-key', sms_auth_keys.api_key)
                .set('Authorization', 'Basic ' + sms_auth_keys.auth_key)
                .end((err, res) => {
                    if (err.statusCode != 202) {
                        error_found = true;
                        send_error = err.statusCode;
                    }
                });
        }


        if (error_found === false) {
            res.status(202).send("ok")
        } else {
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + send_error + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
            res.status(send_error).send(send_error)
        }
    } else {
        res.status(202).send("ok")
    }
}


function sendC19Messages(req, res) {

    if (live_site === true) {

        let subject = req.body.subject;
        let recipients = req.body.recipients_array;
        let recipients_array = JSON.parse(recipients);
        let sms_text = "";
        let error_found = false;
        let sms_error_found = false;
        let send_error = 0;
        let curr_date = moment().format("DD/MM/YYYY H:mm");
        let sms_filter = req.body.sms_filter;

        recipients_array.forEach((values, index) => {

            setTimeout(function () {

                if (sms_filter == "All Records") {
                    sms_text = "This SMS is to advise you that as of " + curr_date + "\n" + "Your COVID vaccination compliance status is;" + "\n" + "\n" + values.message;
                } else {
                    sms_text = values.message;
                }

                const sms_object = {};

                sms_object.to = values.phone_number;
                sms_object.messageTemplateName = sms_auth_keys.template;
                sms_object.subject = subject;
                sms_object.body = sms_text;

                if (sms_filter == "2nd Dose Expired" || sms_filter == "2nd Dose Final Notice" || sms_filter == "Booster Expired" || sms_filter == "Booster Final Notice" || sms_filter == "Booster Required" || sms_filter == "Booster Required in 7 days" || sms_filter == "Booster Expiring in 7 days" || sms_filter == "Exemption Expiring in 7 days") {
                    updateSMSsendStatus(values.pay_id, sms_filter, function (response) {
                        if (response === "error") {
                            sms_error_found = true;
                            console.log(moment().format("DD-MM-YY HH:mm") + " | " + sms_object.pay_id + " | " + "messaging.js / updateSMSsendStatus");
                        }
                    });
                }

                superagent
                    .post(sms_auth_keys.url + '?apikey=' + sms_auth_keys.api_key)
                    .send(sms_object)
                    .set('Content-Type', 'application/vnd.whispir.message-v1+json')
                    .set('accept', 'application/vnd.whispir.message-v1+json')
                    .set('x-api-key', sms_auth_keys.api_key)
                    .set('Authorization', 'Basic ' + sms_auth_keys.auth_key)
                    .end((err, res) => {
                        if (err.statusCode >= 400) {
                            error_found = true;
                            send_error = err.statusCode;
                            console.log(err);
                        }
                    });

            }, 400 * index)

        });

        if (error_found === false) {
            res.status(202).send("ok")
        } else {
            console.log(moment().format("DD-MM-YY HH:mm") + " | " + send_error + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");
            res.status(send_error).send(send_error)
        }
    } else {
        res.status(202).send("ok")
    }

}


function updateSMSsendStatus(pay_id, sms_filter, callback) {

    const ps = new sql.PreparedStatement(connection);
    let sqlQuery = "";

    // Inputs
    ps.input('pay_id', sql.Int);

    if (sms_filter == "2nd Dose Expired") {
        sqlQuery = "UPDATE covid_vaccines SET second_dose_expired_sms_sent = 1 WHERE pay_id = @pay_id";
    } else if (sms_filter == "2nd Dose Final Notice") {
        sqlQuery = "UPDATE covid_vaccines SET second_dose_final_sms_sent = 1 WHERE pay_id = @pay_id";
    } else if (sms_filter == "Booster Expired") {
        sqlQuery = "UPDATE covid_vaccines SET booster_dose_expired_sms_sent = 1 WHERE pay_id = @pay_id";
    } else if (sms_filter == "Booster Final Notice") {
        sqlQuery = "UPDATE covid_vaccines SET booster_dose_final_sms_sent = 1 WHERE pay_id = @pay_id";
    } else if (sms_filter == "Booster Required") {
        sqlQuery = "UPDATE covid_vaccines SET booster_required_sms_sent = 1 WHERE pay_id = @pay_id";
    } else if (sms_filter == "Booster Required in 7 days") {
        sqlQuery = "UPDATE covid_vaccines SET booster_required_warning_sms_sent = 1 WHERE pay_id = @pay_id";
    } else if (sms_filter == "Booster Expiring in 7 days") {
        sqlQuery = "UPDATE covid_vaccines SET booster_expiring_warning_sms_sent = 1 WHERE pay_id = @pay_id";
    } else if (sms_filter == "Exemption Expiring in 7 days") {
        sqlQuery = "UPDATE covid_vaccines SET exemption_expiring_warning_sms_sent = 1 WHERE pay_id = @pay_id";
    }

    // Prepare statement
    ps.prepare(sqlQuery, err => {

        if (!err) {
            // Execute statement
            ps.execute({

                pay_id: pay_id

            }, (err, result) => {

                if (!err) {
                    // release the connection after queries are executed
                    ps.unprepare(err => {
                    });

                    callback("ok");

                } else {
                    ps.unprepare(err => {
                    });

                    console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

                    callback("error");
                }

            });

        } else {
            ps.unprepare(err => {
            });

            console.log(moment().format("DD-MM-YY HH:mm") + " | " + err + " | (" + path.basename(__filename) + " / " + arguments.callee.name + ")");

            callback("error");
        }
    });

}

module.exports = REST_ROUTER;