const express = require('express');
const http = require('http');
const cors = require('cors');

const moment = require('moment');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const db = require('./connection_mssql');

let login = require('./app/login.js');
let admin = require('./app/admin.js');
let schedule = require('./app/schedule.js');
let bookings = require('./app/bookings.js');
let messaging = require('./app/messaging.js');
let respond52 = require('./app/respond52.js');
let emerald = require('./app/emerald.js');
let applications = require('./app/applications.js');
let mobile_app = require('./app/mobile_app.js');
let origin_url = '';
let svr_port = 0;

process.env.timezone = "Australia/Adelaide";

let app = express();

app.use(cookieParser());

console.log (__dirname);

if (__dirname === "/home/<USER>/uat") {
    origin_url = 'https://sapphire-uat.mfs.sa.gov.au/';
    svr_port = 6001;
} else if (__dirname === "C:\\Users\\<USER>\\WebstormProjects\\NEWSapphire\\SERVER") {
    origin_url = 'http://localhost:63343';
    svr_port = 6001;
} else {
    origin_url = 'https://sapphire.mfs.sa.gov.au/';
    svr_port = 7001;
}


const allowedOrigins = [
    'http://localhost:63343',
    'https://sapphire.mfs.sa.gov.au',
    'https://sapphire-uat.mfs.sa.gov.au'
];

const corsOptions = {
    origin: function (origin, callback) {
        if (!origin) return callback(null, true);

        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        } else {
            callback(new Error('CORS not allowed from this origin: ' + origin));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
};

app.use(cors(corsOptions));

function REST() {
    let self = this;

    self.configureExpress();
}

let server = http.createServer(app);

REST.prototype.configureExpress = function () {

    // parse application/json
    app.use(bodyParser.json({limit: "50mb"}));
    app.use(bodyParser.urlencoded({limit: "50mb", extended: true, parameterLimit: 50000}));

    let login_router = express.Router();
    app.use('/login', login_router);
    new login(login_router, db.pool);

    let admin_router = express.Router();
    app.use('/admin', admin_router);
    new admin(admin_router, db.pool);

    let schedule_router = express.Router();
    app.use('/schedule', schedule_router);
    new schedule(schedule_router, db.pool);

    let bookings_router = express.Router();
    app.use('/bookings', bookings_router);
    new bookings(bookings_router, db.pool);

    let messaging_router = express.Router();
    app.use('/messaging', messaging_router);
    new messaging(messaging_router, db.pool);

    let r52_router = express.Router();
    app.use('/respond52', r52_router);
    new respond52(r52_router, db.pool);

    let emerald_router = express.Router();
    app.use('/emerald', emerald_router);
    new emerald(emerald_router, db.pool);

    let applications_router = express.Router();
    app.use('/applications', applications_router);
    new applications(applications_router, db.pool);

    let mobile_app_router = express.Router();
    app.use('/mobile_app', mobile_app_router);
    new mobile_app(mobile_app_router, db.pool);

    server.listen(svr_port, function () {
        console.log('Server running on ' + svr_port);
    });

};

process.on('uncaughtException', function (err) {

    console.log(moment().format("YYYY-MM-DD HH:mm:ss") + ' ' + err);

    process.exit(1);

});

new REST();