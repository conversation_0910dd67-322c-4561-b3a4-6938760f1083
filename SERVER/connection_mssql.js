const mssql = require('mssql');



config ={
    user: 'mfsroster',
    password: 'bjWDtWMTvzVgb9E',
    server: 'mfsroster.database.windows.net',
    database: 'sapphire_test',
    requestTimeout: 120000,
    connectionTimeout: 120000,
    options: {
        encrypt: true,
        enableArithAbort: true,
        trustServerCertificate: false
    },
    pool: {
        max: 100,
        min: 0,
        idleTimeoutMillis: 120000
    }
};


global.api_register = [
    {
        api_id: 1,
        api_user: "sapphire",
        api_host_url: "https://sapphire.mfs.sa.gov.au",
        api_key: "7b8765d5-457b-4ec0-8583-69b3a36d5989"
    },
    {
        api_id: 2,
        api_user: "emerald",
        api_host_url: "https://emeraldportal.eso.sa.gov.au",
        api_key: "0555f39a-0033-4f1a-9645-989235e1aa21"
    },
    {
        api_id: 3,
        api_user: "respond52",
        api_host_url: "http://respond52.com",
        api_key: "aa58fea8-e41d-476f-b2bb-dad0e73db085"
    },
    {
        api_id: 4,
        api_user: "airs",
        api_host_url: "",
        api_key: "d22046d8-b1ab-4458-8034-a5464396f0e5"
    }
//spare API keys = e40327b5-6e6c-4457-b4e8-ba77c0d28fce + f4f79454-58fe-4e13-924a-9a7cf9161a08
];


const pool = new mssql.ConnectionPool(config);

pool.connect(err => {
    if(err)
    {
        console.log(err);
    }
});

module.exports.pool = pool;