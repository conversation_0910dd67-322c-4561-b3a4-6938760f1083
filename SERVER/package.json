{"name": "sapphire", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/Gartan-Replacement-Project/sapphire.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Gartan-Replacement-Project/sapphire/issues"}, "homepage": "https://github.com/Gartan-Replacement-Project/sapphire#readme", "dependencies": {"@authenio/samlify-node-xmllint": "^2.0.0", "@azure/msal-node": "^3.6.0", "@google-cloud/firestore": "^7.6.0", "@googlemaps/google-maps-services-js": "^3.1.16", "@googlemaps/routing": "^1.4.0", "@microsoft/microsoft-graph-client": "^2.1.0", "assert": "^2.0.0", "async": "^3.2.2", "body-parser": "^1.19.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^3.1.7", "dotenv": "^8.2.0", "emailjs": "^3.3.0", "errorhandler": "^1.5.1", "express": "^4.17.1", "express-session": "^1.17.1", "file-system-cache": "^1.0.5", "firebase-admin": "^12.1.0", "fs": "0.0.1-security", "google-auth-library": "^9.9.0", "google-distance-matrix": "^1.1.1", "helmet": "^4.1.1", "http": "0.0.1-security", "https": "^1.0.0", "isomorphic-fetch": "^3.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "morgan": "^1.10.0", "mssql": "^10.0.4", "multer": "^1.4.2", "nodemailer": "^6.7.2", "os": "^0.1.1", "passport": "^0.7.0", "passport-azure-ad": "^4.3.0", "passport-saml": "^3.2.4", "passport-saml-metadata": "^4.0.0", "password": "^0.1.1", "path": "^0.12.7", "samlify": "^2.7.6", "simple-oauth2": "^3.4.0", "superagent": "^6.1.0", "uglify-js": "^3.13.6", "url": "^0.11.0", "useragent": "^2.3.0", "util": "^0.12.3", "wstrust-client": "0.0.1", "xml2js": "^0.6.2"}}