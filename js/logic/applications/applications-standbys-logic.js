let applicationsStandbys = (function () {
  let dateDayType = "";
  let shift_period = "";
  let sub_roster = "";
  let sub_shift = "";
  let sub_location = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("applications-page").$$("sb_search_date_from").setValue(startOfMonth);
    $$("applications-page").$$("sb_search_date_to").setValue(endOfMonth);
    getEmployeeList();
  }
  function eventHandlers() {
    $$("applications-page")
      .$$("sb_show_all")
      .attachEvent("onChange", function (newValue) {
        loadSBRequests();
      });
    $$("applications-page")
      .$$("standbys_day_hours")
      .attachEvent("onItemClick", function (id) {
        let startDate = $$("applications-page")
          .$$("standbys_start_date")
          .getValue();
        let newValue = $$("applications-page")
          .$$("standbys_day_hours")
          .getValue();
        if (newValue === 1) {
          $$("applications-page").$$("standbys_start_time").setValue("08:00");
          $$("applications-page").$$("standbys_end_time").setValue("18:00");
          $$("applications-page").$$("standbys_part_shift").setValue(0);
          $$("applications-page").$$("standbys_night_hours").setValue(0);
          $$("applications-page").$$("standbys_full_shift").setValue(0);
          $$("applications-page").$$("standbys_start_time").disable();
          $$("applications-page").$$("standbys_end_time").disable();
          $$("applications-page").$$("standbys_end_date").setValue(startDate);
          $$("applications-page").$$("standbys_end_date").disable();
        } else {
          $$("applications-page").$$("standbys_night_hours").setValue(1);
        }
      });
    $$("applications-page")
      .$$("standbys_night_hours")
      .attachEvent("onItemClick", function (id) {
        let startDate = $$("applications-page")
          .$$("standbys_start_date")
          .getValue();
        let endDate = moment(startDate).add(1, "days");
        let newValue = $$("applications-page")
          .$$("standbys_night_hours")
          .getValue();
        if (newValue === 1) {
          $$("applications-page").$$("standbys_start_time").setValue("18:00");
          $$("applications-page").$$("standbys_end_time").setValue("08:00");
          $$("applications-page").$$("standbys_day_hours").setValue(0);
          $$("applications-page").$$("standbys_part_shift").setValue(0);
          $$("applications-page").$$("standbys_full_shift").setValue(0);
          $$("applications-page").$$("standbys_start_time").disable();
          $$("applications-page").$$("standbys_end_time").disable();
          $$("applications-page")
            .$$("standbys_end_date")
            .setValue(new Date(endDate));
          $$("applications-page").$$("standbys_end_date").disable();
        } else {
          $$("applications-page").$$("standbys_day_hours").setValue(1);
        }
      });
    $$("applications-page")
      .$$("standbys_part_shift")
      .attachEvent("onItemClick", function (id) {
        let startDate = $$("applications-page")
          .$$("standbys_start_date")
          .getValue();
        let endDate = moment(startDate).add(1, "days");
        let newValue = $$("applications-page")
          .$$("standbys_part_shift")
          .getValue();
        if (newValue === 1) {
          $$("applications-page").$$("standbys_start_time").setValue("08:00");
          $$("applications-page").$$("standbys_end_time").setValue("18:00");
          $$("applications-page").$$("standbys_day_hours").setValue(0);
          $$("applications-page").$$("standbys_night_hours").setValue(0);
          $$("applications-page").$$("standbys_full_shift").setValue(0);
          $$("applications-page").$$("standbys_start_time").enable();
          $$("applications-page").$$("standbys_end_time").enable();
          $$("applications-page").$$("standbys_start_date").setValue(startDate);
          $$("applications-page").$$("standbys_end_date").setValue(startDate);
          $$("applications-page").$$("standbys_end_date").enable();
        } else {
          $$("applications-page").$$("standbys_day_hours").setValue(1);
          $$("applications-page").$$("standbys_end_date").disable();
        }
      });
    $$("applications-page")
      .$$("standbys_full_shift")
      .attachEvent("onItemClick", function (id) {
        let startDate = $$("applications-page")
          .$$("standbys_start_date")
          .getValue();
        let endDate = moment(startDate).add(1, "days");
        let newValue = $$("applications-page")
          .$$("standbys_full_shift")
          .getValue();
        if (newValue === 1) {
          $$("applications-page").$$("standbys_start_time").setValue("08:00");
          $$("applications-page").$$("standbys_end_time").setValue("08:00");
          $$("applications-page").$$("standbys_day_hours").setValue(0);
          $$("applications-page").$$("standbys_night_hours").setValue(0);
          $$("applications-page").$$("standbys_part_shift").setValue(0);
          $$("applications-page").$$("standbys_start_date").setValue(startDate);
          $$("applications-page")
            .$$("standbys_end_date")
            .setValue(new Date(endDate));
          $$("applications-page").$$("standbys_end_date").disable();
          $$("applications-page").$$("standbys_start_time").disable();
          $$("applications-page").$$("standbys_end_time").disable();
        } else {
          $$("applications-page").$$("standbys_day_hours").setValue(1);
          $$("applications-page").$$("standbys_end_date").disable();
        }
      });
    $$("applications-page")
      .$$("standbys_start_date")
      .attachEvent("onChange", function (newv, oldv) {
        if (loadingApp === false) {
          let start_date = $$("applications-page")
            .$$("standbys_start_date")
            .getValue();
          let endDate = moment(start_date).add(1, "days");
          let dayValue = $$("applications-page")
            .$$("standbys_day_hours")
            .getValue();
          let nightValue = $$("applications-page")
            .$$("standbys_night_hours")
            .getValue();
          let partValue = $$("applications-page")
            .$$("standbys_part_shift")
            .getValue();
          let fullValue = $$("applications-page")
            .$$("standbys_full_shift")
            .getValue();
          let payId = $$("applications-page")
            .$$("standbys_employee")
            .getValue();
          if (newv !== oldv) {
            if (dayValue == 1) {
              $$("applications-page")
                .$$("standbys_end_date")
                .setValue(start_date);
            } else if (nightValue == 1) {
              $$("applications-page")
                .$$("standbys_end_date")
                .setValue(new Date(endDate));
            } else if (partValue == 1) {
              $$("applications-page")
                .$$("standbys_end_date")
                .setValue(start_date);
            } else if (fullValue == 1) {
              $$("applications-page")
                .$$("standbys_end_date")
                .setValue(new Date(endDate));
            } else {
              $$("applications-page")
                .$$("standbys_end_date")
                .setValue(start_date);
              $$("applications-page")
                .$$("standbys_start_time")
                .setValue("08:00");
              $$("applications-page").$$("standbys_end_time").setValue("18:00");
            }
          }
          let dateString = moment(start_date).format("YYYYMMDD");
          getRADetails(user_logged_in, dateString, true, function (values) {
            if (values.length > 0) {
              curr_user_roster = values[0].roster;
              curr_user_shift = values[0].shift;
              curr_user_location = values[0].location;
            } else {
              curr_user_roster = "";
              curr_user_shift = "";
              curr_user_location = "";
            }
          });
          displayShiftSequence();
          if (payId != "") {
            getSubstituteRAInfo(payId);
          }
        }
      });
    $$("applications-page")
      .$$("standbys_employee")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv != "") {
          getSubstituteRAInfo(newv);
        }
      });
    $$("applications-page")
      .$$("standbys_btn_send")
      .attachEvent("onItemClick", function (id) {
        if (
          user_logged_in === 3043323 ||
          user_logged_in === 2302205 ||
          live_site === true
        ) {
          let startDate = $$("applications-page")
            .$$("standbys_start_date")
            .getValue();
          let endDate = $$("applications-page")
            .$$("standbys_end_date")
            .getValue();
          let sb_date_string = moment(startDate).format("YYYYMMDD");
          let sb_end_date_string = moment(endDate).format("YYYYMMDD");
          let time_from = $$("applications-page")
            .$$("standbys_start_time")
            .getValue();
          let time_to = $$("applications-page")
            .$$("standbys_end_time")
            .getValue();
          let substituteId = $$("applications-page")
            .$$("standbys_employee")
            .getValue();
          let comments = $$("applications-page")
            .$$("standbys_comments")
            .getValue();
          let dateDiff = moment(endDate.slice(0, 10) + " " + time_to).diff(
            startDate.slice(0, 10) + " " + time_from,
            "h",
            true,
          );
          if (substituteId != "") {
            if (
              moment(endDate.slice(0, 10) + " " + time_to).isBefore(
                startDate.slice(0, 10) + " " + time_from,
              )
            ) {
              webix.alert("The 'End Date' can't be before the 'Start Date'!");
            } else {
              if (dateDiff > 24) {
                webix.alert({
                  text: "You can only request Standbys individually per shift. For multiple days simply send multiple requests, one for each shift.",
                  width: 500,
                });
              } else {
                if (
                  shift_period == "day" &&
                  $$("applications-page")
                    .$$("standbys_night_hours")
                    .getValue() == 1
                ) {
                  webix.alert({
                    text: "The selected date is a 'Day' shift but you have 'Night Shift' ticked!",
                    width: 500,
                  });
                } else if (
                  shift_period == "night" &&
                  $$("applications-page").$$("standbys_day_hours").getValue() ==
                    1
                ) {
                  webix.alert({
                    text: "The selected date is a 'Night' shift but you have 'Day Shift' ticked!",
                    width: 500,
                  });
                } else if (shift_period == "off") {
                  webix.alert(
                    "The selected date is a Non Work Day for your shift!",
                  );
                } else {
                  let officer = $$("applications-page")
                    .$$("standbys_approving_officer")
                    .getValue();
                  if (officer == "") {
                    webix.alert(
                      "You must select the 'Approving Officer' to notify if the Substitute accepts your request!",
                    );
                  } else {
                    let substituteName = $$("applications-page")
                      .$$("standbys_employee")
                      .getText();
                    getSubstituteRAs(
                      substituteId,
                      sb_date_string,
                      function (result) {
                        if (result.permission == "deny") {
                          webix.confirm({
                            title: "Confirm Standby Bookings!",
                            ok: "Yes",
                            cancel: "No",
                            width: 570,
                            text:
                              "The Substitute (" +
                              substituteName +
                              ") shouldn't do this Standby because it falls between their 2 consecutive '" +
                              result.subShift +
                              "' shifts which results in them working more than 24 hours!</br></br>Note: Do you want to process this Standby request anyway?",
                            callback: function (result) {
                              switch (result) {
                                case true:
                                  sendSBRequest(
                                    substituteId,
                                    sb_date_string,
                                    sb_end_date_string,
                                    time_from,
                                    time_to,
                                    comments,
                                    officer,
                                  );
                              }
                            },
                          });
                        } else {
                          sendSBRequest(
                            substituteId,
                            sb_date_string,
                            sb_end_date_string,
                            time_from,
                            time_to,
                            comments,
                            officer,
                          );
                        }
                      },
                    );
                  }
                }
              }
            }
          } else {
            webix.alert("You must select a Substitute first!");
          }
        } else {
          webix.alert("This function can't be used on the 'TEST SITE'");
        }
      });
    $$("applications-page")
      .$$("btnSBSearchLog")
      .attachEvent("onItemClick", function (id, e) {
        loadSBRequests();
      });
  }
  employees_subject.subscribe(function (data) {
    $$("applications-page").$$("standbys_employee").define("options", data);
    $$("applications-page").$$("standbys_employee").refresh();
  });
  function getSubstituteRAs(payId, dateString, callback) {
    let startDate = $$("applications-page")
      .$$("standbys_start_date")
      .getValue();
    let sbDateString = parseInt(dateString);
    let sub_shift_curr = "";
    let sub_shift_next = "";
    let subDayType = "";
    let bkFound = false;
    getRADetails(payId, sbDateString, false, function (RAInfo) {
      if (RAInfo.length > 0) {
        sub_shift_curr = RAInfo[0].shift_type;
      }
    });
    getRADetails(payId, sbDateString + 1, false, function (RAInfo) {
      if (RAInfo.length > 0) {
        sub_shift_next = RAInfo[0].shift_type;
      }
    });
    getDayDetails(
      payId,
      startDate,
      sub_roster,
      sub_shift,
      sub_location,
      function (value) {
        if (value.length > 0) {
          subDayType = value[0].shift_type;
        }
      },
    );
    getUserBookings(payId, sbDateString, function (bookingArray) {
      let no_of_bookings = bookingArray.length;
      for (let x = 0; x < no_of_bookings; x++) {
        if (
          bookingArray[x].booking_type == "sick_leave" ||
          bookingArray[x].booking_type == "leave_request"
        ) {
          bkFound = true;
        }
      }
    });
    getUserBookings(payId, sbDateString + 1, function (bookingArray) {
      let no_of_bookings = bookingArray.length;
      for (let x = 0; x < no_of_bookings; x++) {
        if (
          bookingArray[x].booking_type == "sick_leave" ||
          bookingArray[x].booking_type == "leave_request"
        ) {
          bkFound = true;
        }
      }
    });
    if (bkFound === false) {
      if (sub_shift_curr == "off") {
        callback({ subShift: subDayType, permission: "allow" });
      } else if (shift_period == "night" && sub_shift_curr == "day") {
        if (sub_shift_curr == sub_shift_next) {
          callback({ subShift: subDayType, permission: "deny" });
        } else {
          callback({ subShift: subDayType, permission: "allow" });
        }
      } else if (shift_period == "day" && sub_shift_curr == "night") {
        if (sub_shift_curr == sub_shift_next) {
          callback({ subShift: subDayType, permission: "allow" });
        } else {
          callback({ subShift: subDayType, permission: "deny" });
        }
      }
    } else {
      callback({ subShift: subDayType, permission: "allow" });
    }
  }
  function getDayDetails(payId, startDate, roster, shift, location, callback) {
    let dateStamp = moment(startDate, "YYYY-MM-DD").format("YYYYMMDD");
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/day_details",
        {
          pay_id: payId,
          date_string: dateStamp,
          roster: roster,
          shift: shift,
          location: location,
        },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let value = JSON.parse(results);
            callback(value);
          },
        },
      );
  }
  function getEmployeeList() {
    webix.ajax().get(
      server_url + "/applications/get_notify_list",
      {},
      {
        error: function (err) {},
        success: function (result) {
          let values = JSON.parse(result);
          let officers = [];
          values.forEach(function (value) {
            officers.push({
              id: value.pay_id,
              value:
                value.surname +
                ", " +
                value.first_name +
                " " +
                value.middle_name,
              email: value.notifications_email,
              first_name: value.first_name,
              surname: value.surname,
            });
          });
          $$("applications-page")
            .$$("standbys_approving_officer")
            .define("options", officers);
          $$("applications-page").$$("standbys_approving_officer").refresh();
        },
      },
    );
  }
  function displayShiftSequence() {
    let grid = $$("applications-page").$$("shift_sequence");
    let startDate = $$("applications-page")
      .$$("standbys_start_date")
      .getValue();
    let period = "8 Days";
    let start_date = moment(startDate).format("DD-MM-YYYY");
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/roster",
        {
          roster_name: curr_user_roster,
          shift_name: curr_user_shift,
          start_date: start_date,
          time_period: period,
        },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let data = JSON.parse(results);
              let nowDate = new Date();
              let curr_date = moment(nowDate).format("YYYYMMDD");
              let sel_date_col = 0;
              let count = 1;
              for (let y = 1; y < data.columns.length; y++) {
                if (data.columns[y].header.date == curr_date) {
                  data.columns[y].header.css = "curr_day";
                  sel_date_col = y;
                }
                if (
                  public_holiday_dates_array.some(
                    (public_holiday_dates_array) =>
                      public_holiday_dates_array.date_string ==
                      data.columns[y].header.date,
                  )
                ) {
                  data.columns[y].header.css = "ph_day";
                }
                data.columns[y].cssFormat = function (
                  value,
                  config,
                  row_id,
                  column_id,
                ) {
                  if (value != null) {
                    value = value.toString();
                    if (value.indexOf("fa-sun") > 0) {
                      if (column_id == 1) {
                        dateDayType = "work_day";
                        shift_period = "day";
                      }
                      if (y == sel_date_col) {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "#15317E !important",
                          "border-width": "1px 2px 1px 2px !important",
                        };
                      } else {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "dimgrey !important",
                          "border-width": "1px !important",
                        };
                      }
                    } else if (value.indexOf("fa-moon") > 0) {
                      if (column_id == 1) {
                        dateDayType = "work_day";
                        shift_period = "night";
                      }
                      if (y == sel_date_col) {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "#15317E !important",
                          "border-width": "1px 2px 1px 2px !important",
                        };
                      } else {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "dimgrey !important",
                          "border-width": "1px !important",
                        };
                      }
                    } else if (value.indexOf("fa-ban") > 0) {
                      if (column_id == 1) {
                        dateDayType = "off_day";
                        shift_period = "off";
                      }
                      if (y == sel_date_col) {
                        return {
                          "background-color": "#EAE9E9",
                          "border-color": "#15317E !important",
                          "border-width": "1px 2px 1px 2px !important",
                        };
                      } else {
                        return {
                          "background-color": "#EAE9E9",
                          "border-color": "dimgrey !important",
                          "border-width": "1px !important",
                        };
                      }
                    } else if (parseInt(column_id) >= 1) {
                      if (row_id !== 1) {
                        let found = false;
                        let columnIndex = 0;
                        if (found === true) {
                          if (
                            config.roster_arrangement[columnIndex]
                              .shift_type === "day"
                          ) {
                            if (y == sel_date_col) {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "#15317E !important",
                                "border-width": "1px 2px 1px 2px !important",
                              };
                            } else {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "dimgrey !important",
                                "border-width": "1px !important",
                              };
                            }
                          } else if (
                            config.roster_arrangement[columnIndex]
                              .shift_type === "night"
                          ) {
                            if (y == sel_date_col) {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "#15317E !important",
                                "border-width": "1px 2px 1px 2px !important",
                              };
                            } else {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "dimgrey !important",
                                "border-width": "1px !important",
                              };
                            }
                          } else if (
                            config.roster_arrangement[columnIndex]
                              .shift_type === "off"
                          ) {
                            if (y == sel_date_col) {
                              return {
                                "background-color": "#EAE9E9",
                                "border-color": "#15317E !important",
                                "border-width": "1px 2px 1px 2px !important",
                              };
                            } else {
                              return {
                                "background-color": "#EAE9E9",
                                "border-color": "dimgrey !important",
                                "border-width": "1px !important",
                              };
                            }
                          }
                        } else {
                          if (y == sel_date_col) {
                            return {
                              "background-color": "#FFFFFF !important",
                              "border-color": "#15317E !important",
                              "border-width": "1px 2px 1px 2px !important",
                              color: "#FFFFFF !important",
                            };
                          } else {
                            return {
                              "background-color": "#FFFFFF !important",
                              "border-color": "dimgrey !important",
                              "border-width": "1px !important",
                              color: "#FFFFFF !important",
                            };
                          }
                        }
                      }
                    }
                  }
                };
              }
              grid.config.columns = data.columns;
              grid.config.columns.splice(0, 4);
              grid.refreshColumns();
              let shift_data = data.shift_days;
              let iconRows = [];
              let iconCol = "";
              if (shift_data.length > 0) {
                for (let x = 1; x <= shift_data.length; x++) {
                  if (
                    shift_data[x - 1].roster == "Port Pirie" &&
                    shift_data[x - 1].icon == "sun"
                  ) {
                    iconCol = '{"id": 1,';
                    iconCol = iconCol + '"' + x + '":';
                    iconCol = iconCol + '"<span class=';
                    iconCol =
                      iconCol +
                      "'webix_icon fas fa-sun'></span></br><span class='webix_icon fas fa-moon'>";
                    iconCol = iconCol + '</span>"';
                    iconCol = iconCol + "}";
                  } else {
                    iconCol = '{"id": 1,';
                    iconCol = iconCol + '"' + x + '":';
                    iconCol = iconCol + '"<span class=';
                    iconCol =
                      iconCol +
                      "'webix_icon fas fa-" +
                      shift_data[x - 1].icon +
                      "'>";
                    iconCol = iconCol + '</span>"';
                    iconCol = iconCol + "}";
                  }
                  iconRows.push(JSON.parse(iconCol));
                }
                grid.parse(iconRows);
              }
            }
          },
        },
      );
  }
  function sendSBRequest(
    substituteId,
    sb_date_string,
    sb_end_date_string,
    sb_start,
    sb_end,
    request_comments,
    officer,
  ) {
    let rsq_id = formatUuid(getRandomValuesFunc());
    let requestId = rsq_id.slice(0, 8) + moment().format("ss");
    let substitute_name = "";
    let substitute_email = "";
    let msg_body = "";
    let sb_date = moment(sb_date_string, "YYYYMMDD").format("ddd, DD/MM/YYYY");
    let sb_end_date = moment(sb_end_date_string, "YYYYMMDD").format(
      "ddd, DD/MM/YYYY",
    );
    sb_start = sb_start.slice(0, 5);
    sb_end = sb_end.slice(0, 5);
    getEmployeeData(substituteId, function (result) {
      substitute_name = result[0].first_name;
      substitute_email = result[0].notifications_email;
    });
    if (sb_date_string == sb_end_date_string) {
      msg_body =
        "Are you able to do a Standby for me on " +
        sb_date +
        " from " +
        sb_start +
        " to " +
        sb_end +
        " at " +
        curr_user_roster +
        " | " +
        curr_user_shift +
        " | " +
        curr_user_location;
    } else {
      msg_body =
        "Are you able to do a Standby for me from " +
        sb_date +
        " " +
        sb_start +
        " to " +
        sb_end_date +
        " " +
        sb_end +
        " at " +
        curr_user_roster +
        " | " +
        curr_user_shift +
        " | " +
        curr_user_location;
    }
    saveSBRequest(
      requestId,
      substituteId,
      sb_date_string,
      sb_start,
      sb_end,
      request_comments,
      officer,
      function (response) {
        if (response == "ok") {
          if (live_site === true) {
            sendEmail(
              "SAPPHIRE<<EMAIL>>",
              substitute_email,
              "Standby Request",
              msg_body,
              "Comments: " +
                request_comments +
                " - To respond simply click on this link --\x3e https://sapphire.mfs.sa.gov.au/sb_requests/?m_id=" +
                requestId,
              substitute_name,
              "Regards, " + user_logged_in_name,
              "Note: DO NOT reply to this email. To respond you must click on the link above OR copy/paste the link into a web browser if the link is blocked by your Anti-Virus software.",
            );
          }
          $$("applications-page")
            .$$("standbys_start_date")
            .setValue(new Date());
          $$("applications-page").$$("standbys_day_hours").setValue(1);
          $$("applications-page").$$("standbys_night_hours").setValue(0);
          $$("applications-page").$$("standbys_part_shift").setValue(0);
          $$("applications-page").$$("standbys_full_shift").setValue(0);
          $$("applications-page").$$("standbys_employee").setValue("");
          $$("applications-page").$$("standbys_approving_officer").setValue("");
          $$("applications-page").$$("standbys_comments").setValue("");
          $$("applications-page").$$("standbys_btn_send").disable();
          loadSBRequests();
          webix.alert("Request has been sent!");
        } else {
          webix.alert("There was an error sending the request!");
        }
      },
    );
  }
  function saveSBRequest(
    requestId,
    substituteId,
    sb_date_string,
    sb_start,
    sb_end,
    request_comments,
    officer,
    callback,
  ) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .post(
        server_url + "/applications/save_sb_request",
        {
          request_id: requestId,
          requested_by: user_logged_in,
          roster: curr_user_roster,
          shift: curr_user_shift,
          location: curr_user_location,
          substitute: substituteId,
          shift_date: sb_date_string,
          shift_start: sb_start,
          shift_end: sb_end,
          request_comments: request_comments,
          substitute_roster: sub_roster,
          substitute_shift: sub_shift,
          substitute_location: sub_location,
          so_pay_id: officer,
        },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            callback("ok");
          },
        },
      );
  }
  function getSubstituteRAInfo(payId) {
    let startDate = $$("applications-page")
      .$$("standbys_start_date")
      .getValue();
    let date_string = moment(startDate, "YYYY-MM-DD HH:mm").format("YYYYMMDD");
    let conflictMsg = "";
    $$("applications-page").$$("standbys_btn_send").enable();
    if (payId > 0) {
      if (payId != user_logged_in) {
        if (dateDayType != "off_day") {
          getRADetails(payId, date_string, false, function (RAInfo) {
            if (RAInfo.length > 0) {
              sub_roster = RAInfo[0].roster;
              sub_shift = RAInfo[0].shift;
              sub_location = RAInfo[0].location;
              getDayDetails(
                payId,
                startDate,
                RAInfo[0].roster,
                RAInfo[0].shift,
                RAInfo[0].location,
                function (value) {
                  if (value.length > 0) {
                    if (RAInfo[0].roster != "Port Pirie") {
                      if (value[0].shift_type == "off") {
                        conflictMsg =
                          "Source location (" +
                          RAInfo[0].roster +
                          " - " +
                          RAInfo[0].shift +
                          " - " +
                          RAInfo[0].location +
                          ")</br>Destination location (" +
                          curr_user_roster +
                          " - " +
                          curr_user_shift +
                          " - " +
                          curr_user_location +
                          ")";
                      } else if (value[0].shift_type == "day") {
                        conflictMsg =
                          "Notice: the selected substitute is working a 'Day Shift' on the selected date at (" +
                          RAInfo[0].roster +
                          " - " +
                          RAInfo[0].shift +
                          " - " +
                          RAInfo[0].location +
                          ")";
                        $$("applications-page")
                          .$$("standbys_btn_send")
                          .enable();
                      } else if (value[0].shift_type == "night") {
                        conflictMsg =
                          "Notice: the selected substitute is working a 'Night Shift' on the selected date at (" +
                          RAInfo[0].roster +
                          " - " +
                          RAInfo[0].shift +
                          " - " +
                          RAInfo[0].location +
                          ")";
                        $$("applications-page")
                          .$$("standbys_btn_send")
                          .enable();
                      }
                    } else {
                      if (value[0].shift_type == "off") {
                        conflictMsg =
                          "Source location (" +
                          RAInfo[0].roster +
                          " - " +
                          RAInfo[0].shift +
                          " - " +
                          RAInfo[0].location +
                          ")</br>Destination location (" +
                          curr_user_roster +
                          " - " +
                          curr_user_shift +
                          " - " +
                          curr_user_location +
                          ")";
                      } else {
                        conflictMsg =
                          "Notice: the selected substitute is working a Shift on the selected date at (" +
                          RAInfo[0].roster +
                          " - " +
                          RAInfo[0].shift +
                          " - " +
                          RAInfo[0].location +
                          ")";
                        $$("applications-page")
                          .$$("standbys_btn_send")
                          .enable();
                      }
                    }
                  } else {
                    conflictMsg =
                      "Notice: there is no current roster arrangement for the selected substitute on the selected date!";
                    $$("applications-page").$$("standbys_btn_send").disable();
                  }
                },
              );
            } else {
              conflictMsg =
                "Notice: there is no current roster arrangement for the selected substitute!";
              $$("applications-page").$$("standbys_btn_send").disable();
            }
          });
        } else {
          conflictMsg =
            "Note: You are not on Shift on the selected date so you can't request a Standby!";
          $$("applications-page").$$("standbys_btn_send").disable();
        }
      } else {
        conflictMsg =
          "Notice: if have selected yourself as the substitute for a Standby!";
        $$("applications-page").$$("standbys_btn_send").disable();
      }
      $$("applications-page")
        .$$("standbys_sub_info")
        .define("template", conflictMsg);
      $$("applications-page").$$("standbys_sub_info").refresh();
    }
  }
  function loadSBRequests() {
    $$("loader-window").show();
    let grid = $$("applications-page").$$("grid_standby_requests");
    let request_from_date = $$("applications-page")
      .$$("sb_search_date_from")
      .getValue();
    let request_to_date = $$("applications-page")
      .$$("sb_search_date_to")
      .getValue();
    let show_all = $$("applications-page").$$("sb_show_all").getValue();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/applications/get_sb_requests",
        {
          request_from_date: request_from_date,
          request_to_date: request_to_date,
          show_all: show_all,
          pay_id: user_logged_in,
        },
        {
          error: function (err) {},
          success: function (results) {
            let requests = [];
            if (results) {
              let data = JSON.parse(results);
              let response = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].response === "Accepted") {
                    response =
                      "<div style='color: forestgreen; font-weight: bold'>Accepted</div>";
                  } else if (data[x].response === "Denied") {
                    response =
                      "<div style='color: red; font-weight: bold'>Denied</div>";
                  } else {
                    response =
                      "<div style='color: dimgrey; font-weight: bold'>Pending</div>";
                  }
                  requests.push({
                    request_id: data[x].request_id,
                    request_date: data[x].request_date,
                    requested_by:
                      data[x].surname[0] +
                      ", " +
                      data[x].first_name[0] +
                      " (" +
                      data[x].requested_by +
                      ")",
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    substitute:
                      data[x].surname[1] +
                      ", " +
                      data[x].first_name[1] +
                      " (" +
                      data[x].substitute +
                      ")",
                    requested_shift:
                      moment(data[x].shift_date, "YYYYMMDD").format(
                        "DD/MM/YYYY",
                      ) +
                      " " +
                      data[x].shift_start +
                      "~" +
                      data[x].shift_end,
                    response: response,
                    so_pay_id: data[x].so_pay_id,
                  });
                }
              }
            }
            grid.define("data", requests);
            grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
    displayShiftSequence: function () {
      displayShiftSequence();
    },
    loadSBRequests: function () {
      loadSBRequests();
    },
  };
})();
