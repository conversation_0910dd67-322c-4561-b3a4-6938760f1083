let applications = (function () {
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("applications-page")
      .$$("applications_tabs")
      .attachEvent("onItemClick", function (id, e) {
        let value = $$("applications-page").$$("applications_tabs").getValue();
        if (value) {
          if (value === "1") {
            $$("applications-page").$$("sb-requests-page").show();
          } else if (value === "2") {
            $$("applications-page").$$("rrl-requests-page").show();
          } else if (value === "3") {
            $$("applications-page").$$("sp90-requests-page").show();
          }
        }
      });
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
