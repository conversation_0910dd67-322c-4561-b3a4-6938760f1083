let applicationsRRLs = (function () {
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("applications-page").$$("rrl_search_date_from").setValue(startOfMonth);
    $$("applications-page").$$("rrl_search_date_to").setValue(endOfMonth);
  }
  function eventHandlers() {
    $$("applications-page")
      .$$("rrl_show_all")
      .attachEvent("onChange", function (newValue) {
        loadRRLRequests();
      });
    $$("applications-page")
      .$$("btnRRLSearchLog")
      .attachEvent("onItemClick", function (id, e) {
        loadRRLRequests();
      });
    $$("applications-page")
      .$$("swap_employee")
      .attachEvent("onChange", function (newv, oldv) {
        getRank(newv, "RRL_change_swap");
        getLeaveGroup(newv, "RRL_change_swap");
        getRRLBookings(newv, "swap", function (grid) {
          setRRLGrouping(grid);
        });
      });
    $$("applications-page")
      .$$("btn_send_rrl_request")
      .attachEvent("onItemClick", function (id, e) {
        let swapPayId = $$("applications-page").$$("swap_employee").getValue();
        let fromWeekCount = $$("applications-page")
          .$$("from_dates_grid")
          .getSelectedItem(true);
        let swapWeekCount = $$("applications-page")
          .$$("swap_dates_grid")
          .getSelectedItem(true);
        let properSwapSurname = "";
        if (fromWeekCount.length == 0 || swapWeekCount.length == 0) {
          webix.alert({
            text: "You must select an RRL week period for both Employees before you can request a leave swap!",
            width: 450,
          });
        } else {
          if (user_logged_in == swapPayId) {
            webix.alert({
              text: "The Substitute must be different to the Applicant!",
              width: 500,
            });
          } else {
            let checkFromStartDate = moment(
              fromWeekCount[0].start_date,
              "DD/MM/YYYY",
            );
            let checkSwapStartDate = moment(
              swapWeekCount[0].start_date,
              "DD/MM/YYYY",
            );
            let daysDiff = checkSwapStartDate.diff(
              checkFromStartDate,
              "days",
              true,
            );
            if (Math.abs(daysDiff) < 365) {
              let rsq_id = formatUuid(getRandomValuesFunc());
              let requestId = rsq_id.slice(0, 8) + moment().format("ss");
              let request_comments = $$("applications-page")
                .$$("swap_comments")
                .getValue();
              let requester_group = $$("applications-page")
                .$$("rrl_group_from")
                .getValue();
              let substitute_group = $$("applications-page")
                .$$("rrl_group_swap")
                .getValue();
              let requester_rank = $$("applications-page")
                .$$("curr_rank_from")
                .getValue();
              let substitute_rank = $$("applications-page")
                .$$("curr_rank_swap")
                .getValue();
              saveRRLRequest(
                requestId,
                swapPayId,
                fromWeekCount[0].roster,
                fromWeekCount[0].shift,
                fromWeekCount[0].location,
                swapWeekCount[0].roster,
                swapWeekCount[0].shift,
                swapWeekCount[0].location,
                fromWeekCount[0].start_date,
                fromWeekCount[0].return_date,
                swapWeekCount[0].start_date,
                swapWeekCount[0].return_date,
                request_comments,
                requester_group,
                substitute_group,
                requester_rank,
                substitute_rank,
                function (response) {
                  if (response == "ok") {
                    if (live_site === true) {
                      getEmployeeData(swapPayId, function (swap_values) {
                        properSwapSurname = toProperCase(
                          swap_values[0].surname,
                        );
                        sendEmail(
                          "SAPPHIRE<<EMAIL>>",
                          swap_values[0].notifications_email,
                          "RE: Leave Swap Request",
                          "The following Leave Swap was requested from " +
                            user_logged_in_name +
                            " (" +
                            user_logged_in +
                            ")",
                          "From your RRL tour period: " +
                            swapWeekCount[0].start_date +
                            " - " +
                            swapWeekCount[0].return_date,
                          swap_values[0].first_name + " " + properSwapSurname,
                          "To their RRL tour period: " +
                            fromWeekCount[0].start_date +
                            " - " +
                            fromWeekCount[0].return_date +
                            "<h4>" +
                            "Message: " +
                            request_comments +
                            "</h4>",
                          " - To respond simply click on this link --\x3e https://sapphire.mfs.sa.gov.au/rrl_requests/?m_id=" +
                            requestId +
                            "<h4>Note: DO NOT reply to this email. To respond you must click on the link above OR copy/paste the link into a web browser if the link is blocked by your Anti-Virus software.</h4>",
                        );
                      });
                    }
                    $$("applications-page").$$("swap_employee").setValue("");
                    $$("applications-page").$$("curr_rank_swap").setValue("");
                    $$("applications-page").$$("rrl_group_swap").setValue("");
                    $$("applications-page").$$("swap_comments").setValue("");
                    loadRRLRequests();
                    webix.alert({
                      text: "RRL swap request was sent!",
                      width: 400,
                    });
                  } else {
                    webix.alert({
                      text: "There was an error sending the request!",
                      width: 400,
                    });
                  }
                },
              );
            } else {
              webix.alert({
                text: "The date periods selected are more than 12 months apart!</br>This will need to be applied via SP26 directly to Workforce Rostering for ACFO approval.",
                width: 550,
              });
            }
          }
        }
      });
  }
  function saveRRLRequest(
    requestId,
    substituteId,
    roster,
    shift,
    location,
    swap_roster,
    swap_shift,
    swap_location,
    rrl_start,
    rrl_end,
    rrl_swap_start,
    rrl_swap_end,
    request_comments,
    requester_group,
    substitute_group,
    requester_rank,
    substitute_rank,
    callback,
  ) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .post(
        server_url + "/applications/save_rrl_request",
        {
          request_id: requestId,
          requested_by: user_logged_in,
          roster: roster,
          shift: shift,
          location: location,
          swap_roster: swap_roster,
          swap_shift: swap_shift,
          swap_location: swap_location,
          substitute: substituteId,
          rrl_start_date: moment(rrl_start, "DD/MM/YYYY").format(
            "YYYY-MM-DD 12:00",
          ),
          rrl_return_date: moment(rrl_end, "DD/MM/YYYY").format(
            "YYYY-MM-DD 12:00",
          ),
          swap_start_date: moment(rrl_swap_start, "DD/MM/YYYY").format(
            "YYYY-MM-DD 12:00",
          ),
          swap_return_date: moment(rrl_swap_end, "DD/MM/YYYY").format(
            "YYYY-MM-DD 12:00",
          ),
          request_comments: request_comments,
          requester_group: requester_group,
          substitute_group: substitute_group,
          requester_rank: requester_rank,
          substitute_rank: substitute_rank,
        },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            callback("ok");
          },
        },
      );
  }
  function loadRRLRequests() {
    $$("loader-window").show();
    let grid = $$("applications-page").$$("grid_rrl_requests");
    let request_from_date = $$("applications-page")
      .$$("rrl_search_date_from")
      .getValue();
    let request_to_date = $$("applications-page")
      .$$("rrl_search_date_to")
      .getValue();
    let show_all = $$("applications-page").$$("rrl_show_all").getValue();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/applications/get_rrl_requests",
        {
          request_from_date: request_from_date,
          request_to_date: request_to_date,
          show_all: show_all,
          pay_id: user_logged_in,
        },
        {
          error: function (err) {},
          success: function (results) {
            let requests = [];
            if (results) {
              let data = JSON.parse(results);
              let response = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].response === "Accepted") {
                    response =
                      "<div style='color: forestgreen; font-weight: bold'>Accepted</div>";
                  } else if (data[x].response === "Denied") {
                    response =
                      "<div style='color: red; font-weight: bold'>Denied</div>";
                  } else {
                    response =
                      "<div style='color: dimgrey; font-weight: bold'>Pending</div>";
                  }
                  requests.push({
                    request_id: data[x].request_id,
                    request_date: data[x].request_date,
                    requested_by:
                      data[x].surname[0] +
                      ", " +
                      data[x].first_name[0] +
                      " (" +
                      data[x].requested_by +
                      ")",
                    rrl_roster: data[x].roster,
                    rrl_shift: data[x].shift,
                    rrl_location: data[x].location,
                    swap_roster: data[x].swap_roster,
                    swap_shift: data[x].swap_shift,
                    swap_location: data[x].swap_location,
                    substitute:
                      data[x].surname[1] +
                      ", " +
                      data[x].first_name[1] +
                      " (" +
                      data[x].substitute +
                      ")",
                    rrl_start_date: data[x].rrl_start_date,
                    rrl_return_date: data[x].rrl_return_date,
                    swap_start_date: data[x].swap_start_date,
                    swap_return_date: data[x].swap_return_date,
                    response: response,
                  });
                }
              }
            }
            grid.define("data", requests);
            grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  employees_subject.subscribe(function (data) {
    $$("applications-page").$$("swap_employee").define("options", data);
    $$("applications-page").$$("swap_employee").refresh();
  });
  function getRRLBookings(payId, type, callback) {
    let grid = "";
    if (type === "from") {
      grid = $$("applications-page").$$("from_dates_grid");
    } else if (type === "swap") {
      grid = $$("applications-page").$$("swap_dates_grid");
    }
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/employee_RRL_bookings",
        { pay_id: payId },
        {
          error: function (err) {},
          success: function (results) {
            let values = JSON.parse(results);
            let startDate = "";
            let returnDate = "";
            let employeeName = "";
            let swapRoster = "";
            let swapShift = "";
            let swapLocation = "";
            let swap_startDate = "";
            let swap_returnDate = "";
            let swapWeekNo = "";
            if (values.length > 0) {
              values.forEach(function (result) {
                employeeName = "";
                swapRoster = "";
                swapShift = "";
                swapLocation = "";
                swap_startDate = "";
                swap_returnDate = "";
                swapWeekNo = "";
                if (result.rrl_week_no == 1) {
                  startDate = result.booking_first_date;
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_week_no == 2) {
                  startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_week_no == 3) {
                  startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_week_no == 4) {
                  startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(32, "days")
                    .format("DD/MM/YYYY");
                }
                if (result.rrl_swap_week_no == 1) {
                  swap_startDate = result.swap_booking_first_date;
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_swap_week_no == 2) {
                  swap_startDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_swap_week_no == 3) {
                  swap_startDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_swap_week_no == 4) {
                  swap_startDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(32, "days")
                    .format("DD/MM/YYYY");
                }
                swapWeekNo = result.rrl_swap_week_no;
                if (result.surname == null) {
                  employeeName = "";
                  swapRoster = "";
                  swapShift = "";
                  swapLocation = "";
                  swap_startDate = "";
                  swap_returnDate = "";
                } else {
                  employeeName =
                    result.rrl_swap_pay_id +
                    " - " +
                    result.surname +
                    ", " +
                    result.first_name;
                  swapRoster = result.swap_roster;
                  swapShift = result.swap_shift;
                  swapLocation = result.swap_location;
                }
                grid.add({
                  booking_id: result.booking_id,
                  group_colour: "",
                  leave_type: "RRL",
                  start_date: startDate,
                  return_date: returnDate,
                  week_no: result.rrl_week_no,
                  roster: result.roster,
                  shift: result.shift,
                  location: result.location,
                  rrl_swapee: employeeName,
                  swap_start_date: swap_startDate,
                  swap_return_date: swap_returnDate,
                  swap_week_no: swapWeekNo,
                  swap_roster: swapRoster,
                  swap_shift: swapShift,
                  swap_location: swapLocation,
                });
              });
              grid.adjustColumn("rrl_swapee", "data");
              grid.adjustColumn("roster", "data");
              grid.adjustColumn("shift", "data");
              grid.adjustColumn("location", "data");
              grid.adjustColumn("swap_roster", "data");
              grid.adjustColumn("swap_shift", "data");
              grid.adjustColumn("swap_location", "data");
              callback(grid);
            } else {
              callback(grid);
            }
          },
        },
      );
  }
  function setRRLGrouping(grid) {
    let currBookingId = "";
    let cssProfile = "";
    let x = 0;
    grid.eachRow(function (id) {
      if (
        currBookingId === "" ||
        (currBookingId === grid.getItem(id).booking_id && x === 0)
      ) {
        cssProfile = "rrl_grid_group_1";
        x = 0;
      } else if (
        currBookingId === "" ||
        (currBookingId === grid.getItem(id).booking_id && x === 1)
      ) {
        cssProfile = "rrl_grid_group_2";
        x = 1;
      } else if (
        currBookingId === "" ||
        (currBookingId !== grid.getItem(id).booking_id && x === 0)
      ) {
        cssProfile = "rrl_grid_group_2";
        x = 1;
      } else if (
        currBookingId === "" ||
        (currBookingId !== grid.getItem(id).booking_id && x === 1)
      ) {
        cssProfile = "rrl_grid_group_1";
        x = 0;
      }
      grid.addCellCss(id, "group_colour", cssProfile);
      currBookingId = grid.getItem(id).booking_id;
    });
  }
  return {
    initialise: function () {
      initApplication();
    },
    getRRLBookings: function (pay_id, type, callback) {
      getRRLBookings(pay_id, type, callback);
    },
    setRRLGrouping: function (grid) {
      setRRLGrouping(grid);
    },
    loadRRLRequests: function () {
      loadRRLRequests();
    },
  };
})();
