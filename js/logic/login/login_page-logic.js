let login = (function () {
    function initApplication() {
        createLoaderWindow();
        eventHandlers();
        loadLoginMessage();
        getAllPublicHolidays(function (results) {
            public_holiday_dates_array = results;
        });

        $$("login-page")
            .$$("dev_text")
            .define(
                "template",
                "<strong>SAPPHIRE</strong> Rostering & Availability",
            );
        $$("login-page").$$("dev_text").refresh();


    }

    function eventHandlers() {
        $$("login-page")
            .$$("btnLogin")
            .attachEvent("onItemClick", async function (id) {
                $$("loader-window").show();
                $$("login-page").$$("btnLogin").disable();

                try {
                    // Step 1: Call the backend to check if user is already authenticated
                    const res = await fetch(server_url + '/login/me', {
                        method: 'GET',
                        credentials: 'include' // important: sends cookies
                    });


                    if (res.ok) {

                        window.location.reload();

                    } else if (res.status === 401) {

                        window.location.href = server_url + "/login/auth";

                    } else {
                        alert("Error during login, please try again!");
                        routes.navigate("login", {trigger: true});
                    }
                } catch (err) {
                    console.error('Login check failed:', err);
                    alert('Something went wrong. Please try again.');
                }

            });
    }

    function loadLoginMessage() {
        webix
            .ajax()
            .sync()
            .get(
                server_url + "/admin/login_message",
                {},
                {
                    error: function (err) {
                        serverOnline = false;
                        $$("login-page").$$("btnLogin").disable();
                        $$("login-page").$$("internet_status").show();
                    },
                    success: function (result) {
                        serverOnline = true;
                        $$("login-page").$$("btnLogin").enable();
                        $$("login-page").$$("internet_status").hide();

                        login_message = JSON.parse(result);

                        $$("login-page")
                            .$$("login_message")
                            .setValue(login_message[0].login_message);
                    },
                },
            );
    }


    return {
        initialise: function () {
            initApplication();
        }
    };
})();
