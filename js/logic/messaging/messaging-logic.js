let messaging = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let sms_send_count = 0;
  let auto_refresh;
  let sms_txt_length = 210;
  let overtimeFatigueMode = false;
  let sel_shift_hours = 0;
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "Arial", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let sms_text = "";
  let selected_msg_date = "";
  function initApplication() {
    eventHandlers();
    defineRanks();
    $$("messaging-page").$$("no_response").setValue(1);
    $$("messaging-page").$$("msg_date").setValue(new Date());
    exp_grid = $$("messaging-page").$$("grid_responses");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["SMS message: " + sms_text]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "SMS message date: " + selected_msg_date,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "List of Responders to SMS message",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("messaging-page")
      .$$("adv_msg_filters")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        if (newValue == 1) {
          $$("messaging-page").$$("adv_msg_search_date").show();
          $$("messaging-page").$$("adv_msg_search_time").show();
          $$("messaging-page").$$("adv_msg_search_station").show();
          $$("messaging-page").$$("msg_search_date").disable();
        } else {
          $$("messaging-page").$$("adv_msg_search_date").hide();
          $$("messaging-page").$$("adv_msg_search_time").hide();
          $$("messaging-page").$$("adv_msg_search_station").hide();
          $$("messaging-page").$$("msg_search_date").enable();
        }
      });
    $$("messaging-page")
      .$$("classification_filter")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        $$("messaging-page").$$("btnSearchCrew").callEvent("onItemClick");
      });
    $$("messaging-page")
      .$$("btnOvertimeFatigueMode")
      .attachEvent("onItemClick", function (id, e) {
        if (
          user_permission_level === 1 ||
          user_permission_level === 2 ||
          user_permission_level === 3 ||
          user_permission_level === 4
        ) {
          let roster_filter = $$("messaging-page")
            .$$("roster_filter")
            .getValue();
          let grid = $$("messaging-page").$$("grid_recipients");
          if (this.config.icon == "fas fa-toggle-off") {
            overtimeFatigueMode = true;
            this.define("icon", "fas fa-toggle-on");
            this.refresh();
            $$("messaging-page").$$("leave_filter").hide();
            $$("messaging-page").$$("no_response").disable();
            if (roster_filter === "Metro") {
              $$("messaging-page")
                .$$("station_filters")
                .define("options", [
                  "Central",
                  "North",
                  "West",
                  "South",
                  "North Central",
                  "South Central",
                ]);
              $$("messaging-page").$$("station_filters").refresh();
            }
            $$("messaging-page")
              .$$("preset_messages")
              .define("options", ["Stage 1", "Stage 2", "Stage 3", "Stage 4"]);
            $$("messaging-page").$$("preset_messages").refresh();
            $$("messaging-page").$$("leave_filter").setValue(0);
            grid.showColumn("sm_and_date");
            grid.showColumn("fatigue_hours");
            grid.showColumn("fatigue_hours_shift");
            grid.showColumn("hs_1_pref");
            $$("messaging-page").$$("btnDayWork").show();
            $$("messaging-page").$$("classification_filter").show();
            $$("messaging-page").$$("no_response").setValue(0);
            $$("messaging-page").$$("shift_req_filter").show();
          } else {
            overtimeFatigueMode = false;
            this.define("icon", "fas fa-toggle-off");
            this.refresh();
            $$("messaging-page").$$("leave_filter").show();
            $$("messaging-page").$$("no_response").enable();
            if (roster_filter === "Metro") {
              $$("messaging-page")
                .$$("station_filters")
                .define("options", ["Central", "Southern", "Northern"]);
              $$("messaging-page").$$("station_filters").refresh();
            }
            $$("messaging-page")
              .$$("preset_messages")
              .define("options", ["Custom", "Recall", "Operations"]);
            $$("messaging-page").$$("preset_messages").refresh();
            grid.hideColumn("sm_and_date");
            grid.hideColumn("fatigue_hours");
            grid.hideColumn("fatigue_hours_shift");
            grid.hideColumn("hs_1_pref");
            $$("messaging-page").$$("btnDayWork").hide();
            $$("messaging-page").$$("classification_filter").hide();
            $$("messaging-page").$$("roster_filter").setValue("");
            $$("messaging-page").$$("skill_filter_1").setValue("");
            $$("messaging-page").$$("shift_req_filter").hide();
          }
          grid.clearAll();
          $$("messaging-page").$$("station_filters").setValue("");
          $$("messaging-page").$$("rank_filter").setValue("");
          $$("messaging-page").$$("location_filter").setValue("");
          $$("messaging-page").$$("msg_text").setValue("");
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else {
          webix.alert("You don't have permission to use this function!");
        }
      });
    $$("messaging-page")
      .$$("btnDayWork")
      .attachEvent("onItemClick", function (id, e) {
        $$("messaging-page")
          .$$("roster_filter")
          .setValue([
            "APTC",
            "Comms",
            "Community Safety & Resilience",
            "Corporate Services",
            "Executive",
            "Infrastructure & Logistics",
            "Metro Ops Central",
            "Metro Ops Outer",
            "Operational Training",
            "Organisational Development",
            "OTR",
            "Regional Operations",
          ]);
        $$("messaging-page").$$("skill_filter_1").setValue("OSM");
      });
    $$("messaging-page")
      .$$("rank_filter")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        let selection = $$("messaging-page").$$("preset_messages").getValue();
        if (selection === "Recall") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "OPERATIONS RECALL\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                newValue +
                "\n" +
                "Reply YES within 10 minutes",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else if (selection === "Stage 1") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                newValue +
                "\n" +
                "Reply YES within 6 hours. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else if (selection === "Stage 2") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                newValue +
                "\n" +
                "Reply YES within 4 hours. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else if (selection === "Stage 3") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                newValue +
                "\n" +
                "Reply YES within 3 hours prior to commencement of shift. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else if (selection === "Stage 4") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                newValue +
                "\n" +
                "Reply YES within 10 minutes. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        }
      });
    $$("messaging-page")
      .$$("station_filters")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        if (overtimeFatigueMode === true) {
          if (newValue == "Central") {
            $$("messaging-page").$$("location_filter").setValue("Adelaide");
          } else if (newValue == "North") {
            $$("messaging-page")
              .$$("location_filter")
              .setValue(["Salisbury", "Elizabeth", "Gawler"]);
          } else if (newValue == "West") {
            $$("messaging-page")
              .$$("location_filter")
              .setValue([
                "Woodville",
                "Port Adelaide",
                "Largs North",
                "Prospect",
              ]);
          } else if (newValue == "South") {
            $$("messaging-page")
              .$$("location_filter")
              .setValue([
                "Camden Park",
                "O'Halloran Hill",
                "Noarlunga",
                "Seaford",
              ]);
          } else if (newValue == "North Central") {
            $$("messaging-page")
              .$$("location_filter")
              .setValue(["Paradise", "Oakden", "Golden Grove", "Angle Park"]);
          } else if (newValue == "South Central") {
            $$("messaging-page")
              .$$("location_filter")
              .setValue([
                "Beulah Park",
                "St Marys",
                "Glen Osmond",
                "Brooklyn Park",
              ]);
          }
        } else {
          if (newValue == "Central") {
            getLocationsList("Central Command", function (response) {
              $$("messaging-page").$$("location_filter").setValue(response);
            });
          } else if (newValue == "Southern") {
            getLocationsList("Southern Command", function (response) {
              $$("messaging-page").$$("location_filter").setValue(response);
            });
          } else if (newValue == "Northern") {
            getLocationsList("Northern Command", function (response) {
              $$("messaging-page").$$("location_filter").setValue(response);
            });
          }
        }
      });
    $$("messaging-page")
      .$$("no_response")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        let char_count = $$("messaging-page").$$("msg_text").getValue().length;
        if (newValue === 1) {
          $$("messaging-page")
            .$$("char_count")
            .setValue(char_count + " / 305 characters");
          $$("messaging-page")
            .$$("msg_text")
            .define("attributes", { maxlength: 350 });
          $$("messaging-page").$$("msg_text").refresh();
          sms_txt_length = 350;
        } else {
          $$("messaging-page")
            .$$("char_count")
            .setValue(char_count + " / 210 characters");
          $$("messaging-page")
            .$$("msg_text")
            .define("attributes", { maxlength: 210 });
          $$("messaging-page").$$("msg_text").refresh();
          sms_txt_length = 210;
        }
        if (char_count > sms_txt_length) {
          $$("messaging-page").$$("msg_text").setValue("");
          if (sms_txt_length === 210) {
            $$("messaging-page")
              .$$("char_count")
              .setValue("0 / 210 characters");
          } else {
            $$("messaging-page")
              .$$("char_count")
              .setValue("0 / 305 characters");
          }
          $$("messaging-page").$$("msg_text").focus();
        }
      });
    $$("messaging-page")
      .$$("auto_refresh")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        if (newValue === 1) {
          start_timer_auto_refresh();
        } else {
          clearInterval(auto_refresh);
        }
      });
    $$("messaging-page")
      .$$("show_fatigue")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        let grid = $$("messaging-page").$$("grid_responses");
        let grid2 = $$("messaging-page").$$("grid_messages");
        let selectedRow = grid2.getSelectedItem();
        if (newValue === 1) {
          if (
            user_permission_level === 1 ||
            user_permission_level === 2 ||
            user_permission_level === 3 ||
            user_permission_level === 4
          ) {
            grid.showColumn("fatigue_hours");
            grid.showColumn("fatigue_hours_shift");
            grid.showColumn("overtime_hours");
            grid.showColumn("sm_and_date");
            grid.showColumn("hs_1_pref");
          } else {
            webix.alert({
              text: "You don't have permission to use this function!",
              width: 450,
            });
            this.setValue(0);
          }
        } else {
          grid.hideColumn("fatigue_hours");
          grid.hideColumn("fatigue_hours_shift");
          grid.hideColumn("overtime_hours");
          grid.hideColumn("sm_and_date");
          grid.hideColumn("hs_1_pref");
        }
        if (selectedRow !== undefined) {
          let message_id = selectedRow.message_id;
          let date_msg_string = selectedRow.msg_text;
          let date_in_msg_pos = date_msg_string.search("Date: ");
          let filter_date = selectedRow.msg_date;
          let fatigue_date;
          if (date_in_msg_pos > 0) {
            fatigue_date =
              date_msg_string.slice(date_in_msg_pos + 6, date_in_msg_pos + 16) +
              " 08:00";
          } else {
            fatigue_date = selectedRow.msg_date;
          }
          if (
            moment(fatigue_date, "DD/MM/YYYY HH:mm", true).isValid() === false
          ) {
            fatigue_date = moment(selectedRow.msg_date, "YYYYMMDD").format(
              "DD/MM/YYYY 08:00",
            );
          }
          getResponses(message_id, filter_date, fatigue_date);
        }
      });
    $$("messaging-page")
      .$$("hide_no_responses")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        let grid = $$("messaging-page").$$("grid_messages");
        let selectedRow = grid.getSelectedItem();
        if (selectedRow !== undefined) {
          let message_id = selectedRow.message_id;
          let filter_date = selectedRow.msg_date;
          let fatigue_date;
          if (date_in_msg_pos > 0) {
            fatigue_date =
              date_msg_string.slice(date_in_msg_pos + 6, date_in_msg_pos + 16) +
              " 08:00";
          } else {
            fatigue_date = selectedRow.msg_date;
          }
          if (
            moment(fatigue_date, "DD/MM/YYYY HH:mm", true).isValid() === false
          ) {
            fatigue_date = moment(selectedRow.msg_date, "YYYYMMDD").format(
              "DD/MM/YYYY 08:00",
            );
          }
          getResponses(message_id, filter_date, fatigue_date);
        }
      });
    $$("messaging-page")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "SMS Responses List (" + moment().format("DD-MM-YYYY") + ")",
            styles: true,
            heights: true,
            ignore: { select: true },
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("messaging-page")
      .$$("preset_messages")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        let no_response = $$("messaging-page").$$("no_response").getValue();
        let char_count = $$("messaging-page").$$("msg_text").getValue().length;
        let msg_ranks = $$("messaging-page").$$("rank_filter").getValue();
        if (newValue == "Custom") {
          $$("messaging-page").$$("msg_text").setValue("");
          $$("messaging-page").$$("no_response").setValue(1);
          if (no_response == 1) {
            $$("messaging-page")
              .$$("char_count")
              .setValue("0 / 305 characters");
            $$("messaging-page")
              .$$("msg_text")
              .define("attributes", { maxlength: 350 });
            $$("messaging-page").$$("msg_text").refresh();
            sms_txt_length = 350;
          } else {
            $$("messaging-page")
              .$$("char_count")
              .setValue("0 / 210 characters");
            $$("messaging-page")
              .$$("msg_text")
              .define("attributes", { maxlength: 210 });
            $$("messaging-page").$$("msg_text").refresh();
            sms_txt_length = 210;
          }
          $$("messaging-page").$$("btnSendMsg").disable();
        } else if (newValue == "Recall") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "OPERATIONS RECALL\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                msg_ranks +
                "\n" +
                "Reply YES within 10 mins",
            );
          if (no_response == 1) {
            $$("messaging-page")
              .$$("char_count")
              .setValue("66 / 305 characters");
            $$("messaging-page")
              .$$("msg_text")
              .define("attributes", { maxlength: 350 });
            $$("messaging-page").$$("msg_text").refresh();
            sms_txt_length = 350;
          } else {
            $$("messaging-page")
              .$$("char_count")
              .setValue("66 / 210 characters");
            $$("messaging-page")
              .$$("msg_text")
              .define("attributes", { maxlength: 210 });
            $$("messaging-page").$$("msg_text").refresh();
            sms_txt_length = 210;
          }
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
          $$("messaging-page").$$("btnSendMsg").enable();
          $$("messaging-page").$$("no_response").setValue(0);
        } else if (newValue == "Operations") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "OPERATIONS\n" +
                "Attention Crew Member,\n" +
                "The position has been filled and you are not required, unless you have been contacted\n" +
                "DUTY OFFICER",
            );
          if (no_response == 1) {
            $$("messaging-page")
              .$$("char_count")
              .setValue("132 / 305 characters");
            $$("messaging-page")
              .$$("msg_text")
              .define("attributes", { maxlength: 350 });
            $$("messaging-page").$$("msg_text").refresh();
            sms_txt_length = 350;
          } else {
            $$("messaging-page")
              .$$("char_count")
              .setValue("132 / 210 characters");
            $$("messaging-page")
              .$$("msg_text")
              .define("attributes", { maxlength: 210 });
            $$("messaging-page").$$("msg_text").refresh();
            sms_txt_length = 210;
          }
          $$("messaging-page").$$("btnSendMsg").enable();
          $$("messaging-page").$$("no_response").setValue(1);
          if (char_count > sms_txt_length) {
            $$("messaging-page").$$("msg_text").setValue("");
            if (sms_txt_length === 210) {
              $$("messaging-page")
                .$$("char_count")
                .setValue("0 / 210 characters");
            } else {
              $$("messaging-page")
                .$$("char_count")
                .setValue("0 / 305 characters");
            }
            $$("messaging-page").$$("msg_text").focus();
          }
        } else if (newValue === "Stage 1") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                msg_ranks +
                "\n" +
                "Reply YES within 6 hours. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else if (newValue === "Stage 2") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                msg_ranks +
                "\n" +
                "Reply YES within 4 hours. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else if (newValue === "Stage 3") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                msg_ranks +
                "\n" +
                "Reply YES within 3 hours prior to commencement of shift. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        } else if (newValue === "Stage 4") {
          $$("messaging-page")
            .$$("msg_text")
            .setValue(
              "RECALL MESSAGE\n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "Ranks: " +
                msg_ranks +
                "\n" +
                "Reply YES within 10 minutes. You will be notified via SMS if you are successful",
            );
          $$("messaging-page").$$("msg_text").callEvent("onTimedKeyPress");
        }
      });
    $$("messaging-page")
      .$$("preset_responder_messages")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        if (newValue == "Custom") {
          $$("messaging-page").$$("responders_message_text").setValue("");
          $$("messaging-page")
            .$$("responders_char_count")
            .setValue("0 / 305 characters");
          $$("messaging-page").$$("btnSendMsgToResponders").disable();
        } else if (newValue == "Operations") {
          $$("messaging-page")
            .$$("responders_message_text")
            .setValue(
              "OPERATIONS\n" +
                "Attention Crew Member,\n" +
                "The position has been filled and you are not required, unless you have been contacted\n" +
                "DUTY OFFICER",
            );
          $$("messaging-page")
            .$$("responders_char_count")
            .setValue("132 / 305 characters");
          $$("messaging-page").$$("btnSendMsgToResponders").enable();
        } else if (newValue == "Recall") {
          $$("messaging-page")
            .$$("responders_message_text")
            .setValue(
              "RECALL - A recall has been confirmed for \n" +
                "Station: \n" +
                "Date: \n" +
                "Time: \n" +
                "You are expected to report for duty at the normal time. If this overtime is no longer required you will be notified by SMS\n" +
                "NOTE: There is no overtime paid if notified within 90 minutes of shift start",
            );
          $$("messaging-page")
            .$$("responders_char_count")
            .setValue("265 / 305 characters");
          $$("messaging-page").$$("btnSendMsgToResponders").enable();
        }
      });
    $$("messaging-page")
      .$$("grid_recipients")
      .attachEvent("onCheck", function (row, column, state) {
        let grid = $$("messaging-page").$$("grid_recipients");
        let sel_count = 0;
        grid.eachRow(function (row) {
          if (grid.getItem(row).select == 1) {
            sel_count += 1;
          }
        });
        sms_send_count = sel_count;
        $$("messaging-page")
          .$$("records_count")
          .define(
            "template",
            grid.count() +
              " records found " +
              " - " +
              sel_count +
              " records selected",
          );
        $$("messaging-page").$$("records_count").refresh();
      });
    $$("messaging-page")
      .$$("msg_text")
      .attachEvent("onTimedKeyPress", function () {
        let char_count = $$("messaging-page").$$("msg_text").getValue().length;
        let no_response = $$("messaging-page").$$("no_response").getValue();
        if (no_response == 1) {
          $$("messaging-page")
            .$$("char_count")
            .setValue(char_count + " / 305 characters");
        } else {
          $$("messaging-page")
            .$$("char_count")
            .setValue(char_count + " / 210 characters");
        }
        if (char_count > 0) {
          $$("messaging-page").$$("btnSendMsg").enable();
        } else {
          $$("messaging-page").$$("btnSendMsg").disable();
        }
      });
    $$("messaging-page")
      .$$("msg_text")
      .attachEvent("onPaste", function (text) {
        let char_count = $$("messaging-page").$$("msg_text").getValue().length;
        let no_response = $$("messaging-page").$$("no_response").getValue();
        if (no_response == 1) {
          $$("messaging-page")
            .$$("char_count")
            .setValue(char_count + " / 305 characters");
        } else {
          $$("messaging-page")
            .$$("char_count")
            .setValue(char_count + " / 210 characters");
        }
        if (char_count > 0) {
          $$("messaging-page").$$("btnSendMsg").enable();
        } else {
          $$("messaging-page").$$("btnSendMsg").disable();
        }
      });
    $$("messaging-page")
      .$$("responders_message_text")
      .attachEvent("onTimedKeyPress", function () {
        let char_count = $$("messaging-page")
          .$$("responders_message_text")
          .getValue().length;
        $$("messaging-page")
          .$$("responders_char_count")
          .setValue(char_count + " / 305 characters");
        if (char_count > 0) {
          $$("messaging-page").$$("btnSendMsgToResponders").enable();
        } else {
          $$("messaging-page").$$("btnSendMsgToResponders").disable();
        }
      });
    $$("messaging-page")
      .$$("responders_message_text")
      .attachEvent("onPaste", function (text) {
        let char_count = $$("messaging-page")
          .$$("responders_message_text")
          .getValue().length;
        $$("messaging-page")
          .$$("responders_char_count")
          .setValue(char_count + " / 305 characters");
        if (char_count > 0) {
          $$("messaging-page").$$("btnSendMsgToResponders").enable();
        } else {
          $$("messaging-page").$$("btnSendMsgToResponders").disable();
        }
      });
    $$("messaging-page")
      .$$("roster_filter")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        $$("messaging-page").$$("station_filters").hide();
        if (newValue.length == 1) {
          $$("messaging-page").$$("shift_filter").enable();
          $$("messaging-page").$$("location_filter").enable();
          load_shifts(newValue[0]);
          if (newValue[0] == "Metro") {
            if (overtimeFatigueMode === true) {
              $$("messaging-page")
                .$$("station_filters")
                .define("options", [
                  "Central",
                  "North",
                  "West",
                  "South",
                  "North Central",
                  "South Central",
                ]);
              $$("messaging-page").$$("station_filters").refresh();
            } else {
              $$("messaging-page")
                .$$("station_filters")
                .define("options", ["Central", "Southern", "Northern"]);
              $$("messaging-page").$$("station_filters").refresh();
            }
            $$("messaging-page").$$("station_filters").show();
          }
        } else if (newValue.length > 1) {
          $$("messaging-page").$$("shift_filter").disable();
          $$("messaging-page").$$("location_filter").disable();
          $$("messaging-page").$$("shift_filter").setValue("");
          $$("messaging-page").$$("location_filter").setValue("");
        } else {
          $$("messaging-page").$$("shift_filter").disable();
          $$("messaging-page").$$("location_filter").disable();
          $$("messaging-page").$$("shift_filter").setValue("");
          $$("messaging-page").$$("location_filter").setValue("");
        }
      });
    $$("messaging-page")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("messaging-page").$$("roster_filter").getValue();
        load_shifts(roster);
      });
    $$("messaging-page")
      .$$("location_filter")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        $$("messaging-page").$$("station_filters").setValue("");
      });
    $$("messaging-page")
      .$$("btnSearchCrew")
      .attachEvent("onItemClick", function (id, e) {
        let msg_date = $$("messaging-page").$$("msg_date").getValue();
        let msg_rosters = $$("messaging-page").$$("roster_filter").getValue();
        let msg_shifts = $$("messaging-page").$$("shift_filter").getValue();
        let msg_locations = $$("messaging-page")
          .$$("location_filter")
          .getValue();
        let msg_ranks = $$("messaging-page").$$("rank_filter").getValue();
        let msg_skill_1 = $$("messaging-page").$$("skill_filter_1").getValue();
        let msg_skill_2 = $$("messaging-page").$$("skill_filter_2").getValue();
        let msg_inc_leave = $$("messaging-page").$$("leave_filter").getValue();
        let shift_req_filter = $$("messaging-page")
          .$$("shift_req_filter")
          .getValue();
        let rosters_array = msg_rosters.split(",");
        let shifts_array = msg_shifts.split(",");
        let locations_array = msg_locations.split(",");
        let ranks_array = msg_ranks.split(",");
        if (rosters_array.length == 1 && rosters_array[0] == "") {
          webix.alert("You must select at least 1 roster!");
        } else {
          if (rosters_array.length == 1 && rosters_array[0] != "") {
            if (shifts_array[0] != "") {
              if (locations_array[0] != "") {
                if (ranks_array[0] != "") {
                  if (overtimeFatigueMode === false) {
                    getEmployeeList(
                      msg_date,
                      msg_rosters,
                      msg_shifts,
                      msg_locations,
                      msg_ranks,
                      msg_skill_1,
                      msg_skill_2,
                      msg_inc_leave,
                    );
                  } else if (
                    overtimeFatigueMode === true &&
                    (shift_req_filter == "Day" || shift_req_filter == "Night")
                  ) {
                    getEmployeeList(
                      msg_date,
                      msg_rosters,
                      msg_shifts,
                      msg_locations,
                      msg_ranks,
                      msg_skill_1,
                      msg_skill_2,
                      msg_inc_leave,
                    );
                  } else {
                    webix.alert("You must select a Shift Period!");
                  }
                } else {
                  webix.alert("You must select at least 1 rank!");
                }
              } else {
                webix.alert("You must select at least 1 location!");
              }
            } else {
              webix.alert("You must select at least 1 shift!");
            }
          } else {
            if (ranks_array[0] != "") {
              if (overtimeFatigueMode === false) {
                getEmployeeList(
                  msg_date,
                  msg_rosters,
                  null,
                  null,
                  msg_ranks,
                  msg_skill_1,
                  msg_skill_2,
                  msg_inc_leave,
                );
              } else {
                webix.alert({
                  text: "You can't select more than 1 roster when using 'Overtime & Fatigue Mode'",
                  width: 520,
                });
              }
            } else {
              webix.alert("You must select at least 1 rank!");
            }
          }
        }
      });
    $$("messaging-page")
      .$$("btnSendMsg")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 5 || user_permission_level === 6) {
          webix.alert("You don't have permission to Send messages!");
        } else {
          if (live_site === true) {
            let msg_date = $$("messaging-page").$$("msg_date").getValue();
            let msg_rosters = $$("messaging-page")
              .$$("roster_filter")
              .getValue();
            let msg_shifts = $$("messaging-page").$$("shift_filter").getValue();
            let msg_locations = $$("messaging-page")
              .$$("location_filter")
              .getValue();
            let msg_ranks = $$("messaging-page").$$("rank_filter").getValue();
            let msg_skill_1 = $$("messaging-page")
              .$$("skill_filter_1")
              .getValue();
            let msg_skill_2 = $$("messaging-page")
              .$$("skill_filter_2")
              .getValue();
            let no_response = $$("messaging-page").$$("no_response").getValue();
            if (sms_send_count > 0) {
              webix.confirm({
                title: "Confirm sending bulk SMS message",
                ok: "Yes",
                cancel: "No",
                width: 550,
                text:
                  "You are about to send an SMS message to " +
                  sms_send_count +
                  " employee(s).</br></br>Do you want to Proceed?",
                callback: function (result) {
                  switch (result) {
                    case true:
                      sendMessage(
                        msg_date,
                        msg_rosters,
                        msg_shifts,
                        msg_locations,
                        msg_ranks,
                        msg_skill_1,
                        msg_skill_2,
                        no_response,
                      );
                  }
                },
              });
            } else {
              webix.alert(
                "You must select at least 1 employee to send an SMS message!",
              );
            }
          } else {
            webix.alert("This function is not available on the TEST site!");
          }
        }
      });
    $$("messaging-page")
      .$$("btnSendMsgToResponders")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("messaging-page").$$("grid_responses");
        let responder_count = 0;
        grid.eachRow(function (row) {
          if (grid.getItem(row).select == 1) {
            responder_count += 1;
          }
        });
        if (user_permission_level === 5 || user_permission_level === 6) {
          webix.alert("You don't have permission to Send messages!");
        } else {
          if (responder_count > 0) {
            webix.confirm({
              title: "Confirm sending SMS to selected responders",
              ok: "Yes",
              cancel: "No",
              width: 550,
              text:
                "You are about to send an SMS message to the " +
                responder_count +
                " selected responder(s).</br></br>Do you want to Proceed?",
              callback: function (result) {
                switch (result) {
                  case true:
                    sendResponderMessage();
                }
              },
            });
          } else {
            webix.alert(
              "You must select at least 1 respondent to send an SMS message!",
            );
          }
        }
      });
    $$("messaging-page")
      .$$("btnSearchLog")
      .attachEvent("onItemClick", function (id, e) {
        let search_date = $$("messaging-page").$$("msg_search_date").getValue();
        let grid = $$("messaging-page").$$("grid_responses");
        grid.clearAll();
        loadMessages(search_date);
      });
    $$("messaging-page")
      .$$("grid_messages")
      .attachEvent("onItemClick", function (id, e, node) {
        let selectedRow = this.getItem(id);
        let message_id = selectedRow.message_id;
        sms_text = selectedRow.msg_text;
        selected_msg_date = selectedRow.sent_date;
        let date_msg_string = selectedRow.msg_text;
        let date_in_msg_pos = date_msg_string.search("Date: ");
        let filter_date = selectedRow.msg_date;
        let fatigue_date;
        if (date_in_msg_pos > 0) {
          fatigue_date =
            date_msg_string.slice(date_in_msg_pos + 6, date_in_msg_pos + 16) +
            " 08:00";
        } else {
          fatigue_date = selectedRow.msg_date;
        }
        if (
          moment(fatigue_date, "DD/MM/YYYY HH:mm", true).isValid() === false
        ) {
          fatigue_date = moment(selectedRow.msg_date, "YYYYMMDD").format(
            "DD/MM/YYYY 08:00",
          );
        }
        if (sms_text.includes("18:00") || sms_text.includes("1800")) {
          sel_shift_hours = 14;
        } else {
          sel_shift_hours = 10;
        }
        getResponses(message_id, filter_date, fatigue_date);
      });
  }
  function getLocationsList(location, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/get_locations",
        { group_name: location },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let values = JSON.parse(results);
            callback(values[0].locations);
          },
        },
      );
  }
  function getResponses(message_id, filter_date, fatigue_date) {
    $$("loader-window").show();
    let grid = $$("messaging-page").$$("grid_responses");
    let response_filter = $$("messaging-page")
      .$$("hide_no_responses")
      .getValue();
    let fatigue_filter = $$("messaging-page").$$("show_fatigue").getValue();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/messaging/get_responses",
        {
          message_id: message_id,
          msg_date: filter_date,
          response: response_filter,
          fatigue: fatigue_filter,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let responses = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let all_skills = "";
              let fatigueHours;
              let overtimeHours;
              let extra_hours;
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].all_skill_codes !== null) {
                    all_skills = data[x].all_skill_codes.replaceAll("'", "");
                  }
                  if (fatigue_filter == 1) {
                    if (data[x].response == "Yes") {
                      getFatigueHours(
                        data[x].responder_id,
                        fatigue_date,
                        function (result) {
                          fatigueHours = result;
                        },
                      );
                      overtimeHours = data[x].overtime_hours;
                    } else {
                      fatigueHours = "-";
                      overtimeHours = "-";
                    }
                  } else {
                    fatigueHours = "-";
                    overtimeHours = "-";
                  }
                  if (data[x].overtime_hrs_balance != null) {
                    extra_hours = data[x].overtime_hrs_balance;
                    let balance_end_date = moment(
                      data[x].overtime_hrs_date,
                    ).add(1, "years");
                    let checkFilterDate = moment(
                      filter_date,
                      "YYYYMMDD 12:00",
                    ).toDate();
                    if (
                      moment(checkFilterDate).isBetween(
                        moment(data[x].overtime_hrs_date).toDate(),
                        moment(balance_end_date).toDate(),
                      )
                    ) {
                      overtimeHours = overtimeHours + extra_hours;
                    }
                  }
                  if (
                    fatigueHours != undefined &&
                    fatigueHours != null &&
                    fatigueHours != "-"
                  ) {
                    fatigueHours = fatigueHours.toFixed(2);
                  }
                  responses.push({
                    message_id: data[x].message_id,
                    pay_id: data[x].responder_id,
                    mobile_phone: data[x].personal_mobile_no,
                    name: empName,
                    overtime_hours: overtimeHours,
                    fatigue_hours: fatigueHours,
                    rank: data[x].rank,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    skills: all_skills,
                    response_date: data[x].response_date,
                    response: data[x].response,
                    responder_phone: data[x].personal_mobile_no,
                    hs_1_pref: data[x].hs_1_pref,
                  });
                }
              }
            }
            grid.define("data", responses);
            grid.refresh();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (parseFloat(record.fatigue_hours) + sel_shift_hours <= 76) {
                grid.addCellCss(row, "fatigue_hours", "fatigue_green");
              } else if (
                parseFloat(record.fatigue_hours) <= 76 &&
                parseFloat(record.fatigue_hours) + sel_shift_hours > 76
              ) {
                grid.addCellCss(row, "fatigue_hours", "fatigue_orange");
              } else if (parseFloat(record.fatigue_hours) > 76) {
                grid.addCellCss(row, "fatigue_hours", "fatigue_red");
              } else {
                grid.addCellCss(row, "fatigue_hours", "fatigue_green");
              }
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  function defineRanks() {
    let select = $$("messaging-page").$$("rank_filter");
    let ranks = [];
    all_rank_types.forEach(function (value) {
      ranks.push({ id: value.id, value: value.value });
    });
    ranks.pop();
    select.define("options", ranks);
    select.refresh();
    select.getPopup().queryView({ labelRight: "Select all" }).show();
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("messaging-page").$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        if (value.roster_name !== "Long Term Leave") {
          options.push(value.roster_name);
        }
      });
      select.define("options", options);
      select.refresh();
      select.getPopup().queryView({ labelRight: "Select all" }).show();
    }
  });
  skill_codes_subject.subscribe(function (data) {
    let select = $$("messaging-page").$$("skill_filter_1");
    let select2 = $$("messaging-page").$$("skill_filter_2");
    let results = JSON.parse(data);
    let skill_codes = [];
    for (let x = 0; x < results.length; x++) {
      skill_codes.push({ id: results[x].code, value: results[x].code });
    }
    select.define("options", skill_codes);
    select.refresh();
    select2.define("options", skill_codes);
    select2.refresh();
  });
  function valueSort(a, b) {
    return a.value.localeCompare(b.value);
  }
  locations_subject.subscribe(function (data) {
    let results = JSON.parse(data);
    let locations = [];
    for (let x = 0; x < results.length; x++) {
      if (
        results[x].station_id > 20 &&
        results[x].station_id <= 50 &&
        results[x].station_id != 38
      ) {
        locations.push({ id: results[x].station_id, value: results[x].name });
      }
    }
    locations.push({ id: 20, value: "Adelaide" });
    locations.push({ id: 38, value: "APTC" });
    locations.push({ id: 70, value: "Mt Gambier" });
    locations.sort(valueSort);
    locations.unshift({ id: "", value: "" });
    $$("messaging-page")
      .$$("adv_msg_search_station")
      .define("options", locations);
    $$("messaging-page").$$("adv_msg_search_station").refresh();
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                $$("messaging-page")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("messaging-page").$$("shift_filter").refresh();
                $$("messaging-page")
                  .$$("shift_filter")
                  .getPopup()
                  .queryView({ labelRight: "Select all" })
                  .show();
                $$("messaging-page")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("messaging-page").$$("location_filter").refresh();
                $$("messaging-page")
                  .$$("location_filter")
                  .getPopup()
                  .queryView({ labelRight: "Select all" })
                  .show();
              }
            }
          },
        },
      );
  }
  function getEmployeeList(
    msg_date,
    msg_rosters,
    msg_shifts,
    msg_locations,
    msg_ranks,
    msg_skill_1,
    msg_skill_2,
    include_leave,
  ) {
    $$("loader-window").show();
    let grid = $$("messaging-page").$$("grid_recipients");
    let shift_req_filter = "";
    let classification_filter = "Show All";
    grid.clearAll();
    $$("messaging-page")
      .$$("records_count")
      .define("template", "0 matching employee records found!");
    $$("messaging-page").$$("records_count").refresh();
    if (overtimeFatigueMode === true) {
      include_leave = 0;
      shift_req_filter = $$("messaging-page").$$("shift_req_filter").getValue();
      classification_filter = $$("messaging-page")
        .$$("classification_filter")
        .getValue();
    } else {
      classification_filter = "Show All";
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/messaging/get_employees",
        {
          msg_date: msg_date,
          msg_rosters: msg_rosters,
          msg_shifts: msg_shifts,
          msg_locations: msg_locations,
          msg_ranks: msg_ranks,
          msg_skill_1: msg_skill_1,
          msg_skill_2: msg_skill_2,
          include_leave: include_leave,
          overtimeFatigueMode: overtimeFatigueMode,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let employees = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let all_skills = "";
              let fatigueLessShift;
              let fatigueHoursPlusShift;
              let fatigueDate = "";
              let add_hours = 0;
              let sm_and_date = "";
              if (data.length > 0) {
                if (shift_req_filter === "Day") {
                  add_hours = 10;
                  fatigueDate = moment(msg_date, "YYYY-MM-DD").format(
                    "DD/MM/YYYY 08:00",
                  );
                } else if (shift_req_filter === "Night") {
                  add_hours = 14;
                  fatigueDate = moment(msg_date, "YYYY-MM-DD").format(
                    "DD/MM/YYYY 18:00",
                  );
                } else {
                  add_hours = 10;
                  fatigueDate = moment(msg_date, "YYYY-MM-DD").format(
                    "DD/MM/YYYY 08:00",
                  );
                }
                for (let x = 0; x < data.length; x++) {
                  if (overtimeFatigueMode === true) {
                    getFatigueHours(
                      data[x].pay_id,
                      fatigueDate,
                      function (result) {
                        fatigueLessShift = result;
                        fatigueHoursPlusShift = result + add_hours;
                      },
                    );
                    if (
                      data[x].sm_code == null ||
                      data[x].sm_code == undefined
                    ) {
                      sm_and_date = "";
                    } else {
                      sm_and_date =
                        data[x].sm_code +
                        "&nbsp;&nbsp;on&nbsp;&nbsp;" +
                        moment(data[x].sm_code_date, "YYYYMMDD").format(
                          "DD/MM/YYYY",
                        );
                    }
                  }
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].all_skill_codes !== null) {
                    all_skills = data[x].all_skill_codes.replaceAll("'", "");
                  }
                  if (classification_filter === "Show All") {
                    employees.push({
                      pay_id: data[x].pay_id,
                      name: empName,
                      rank: data[x].ra_rank,
                      roster: data[x].roster,
                      shift: data[x].shift,
                      location: data[x].location,
                      sm_and_date: sm_and_date,
                      phone: data[x].personal_mobile_no,
                      skills: all_skills,
                      fatigue_hours: fatigueLessShift,
                      fatigue_hours_shift: fatigueHoursPlusShift,
                      hs_1_pref: data[x].hs_1_pref,
                    });
                  } else if (classification_filter === "Green Only") {
                    if (fatigueHoursPlusShift <= 76) {
                      employees.push({
                        pay_id: data[x].pay_id,
                        name: empName,
                        rank: data[x].ra_rank,
                        roster: data[x].roster,
                        shift: data[x].shift,
                        location: data[x].location,
                        sm_and_date: sm_and_date,
                        phone: data[x].personal_mobile_no,
                        skills: all_skills,
                        fatigue_hours: fatigueLessShift,
                        fatigue_hours_shift: fatigueHoursPlusShift,
                        hs_1_pref: data[x].hs_1_pref,
                      });
                    }
                  } else if (classification_filter === "Green & Orange") {
                    if (
                      fatigueHoursPlusShift <= 76 ||
                      (fatigueLessShift <= 76 && fatigueHoursPlusShift > 76)
                    ) {
                      employees.push({
                        pay_id: data[x].pay_id,
                        name: empName,
                        rank: data[x].ra_rank,
                        roster: data[x].roster,
                        shift: data[x].shift,
                        location: data[x].location,
                        sm_and_date: sm_and_date,
                        phone: data[x].personal_mobile_no,
                        skills: all_skills,
                        fatigue_hours: fatigueLessShift,
                        fatigue_hours_shift: fatigueHoursPlusShift,
                        hs_1_pref: data[x].hs_1_pref,
                      });
                    }
                  }
                }
              }
            }
            grid.define("data", employees);
            grid.refresh();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (record.fatigue_hours_shift <= 76) {
                grid.addCellCss(row, "fatigue_hours", "fatigue_green");
              } else if (
                record.fatigue_hours <= 76 &&
                record.fatigue_hours_shift > 76
              ) {
                grid.addCellCss(row, "fatigue_hours", "fatigue_orange");
              } else if (record.fatigue_hours > 76) {
                grid.addCellCss(row, "fatigue_hours", "fatigue_red");
              } else {
                grid.addCellCss(row, "fatigue_hours", "fatigue_green");
              }
            });
            grid.getHeaderContent("master_select").check();
            $$("messaging-page")
              .$$("records_count")
              .define(
                "template",
                employees.length +
                  " records found " +
                  " - " +
                  employees.length +
                  " records selected",
              );
            $$("messaging-page").$$("records_count").refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  function sendResponderMessage() {
    let grid = $$("messaging-page").$$("grid_responses");
    let responders_array = [];
    grid.eachRow(function (row) {
      if (grid.getItem(row).select == 1) {
        const responders_object = {};
        responders_object.pay_id = grid.getItem(row).pay_id;
        responders_object.mobile_no = grid.getItem(row).responder_phone;
        responders_array.push(responders_object);
      }
    });
    let msg_body = $$("messaging-page")
      .$$("responders_message_text")
      .getValue();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .post(
        server_url + "/messaging/send_response_message",
        {
          subject: "SAPPHIRE",
          message: msg_body,
          recipients_array: responders_array,
        },
        {
          error: function (err) {
            webix.alert("There was an error sending the message!");
          },
          success: function (results) {
            webix.alert("SMS message has been sent successfully!");
            $$("messaging-page").$$("responders_message_text").setValue("");
          },
        },
      );
  }
  function sendMessage(
    msg_date,
    msg_rosters,
    msg_shifts,
    msg_locations,
    msg_ranks,
    msg_skill_1,
    msg_skill_2,
    no_response,
  ) {
    let grid = $$("messaging-page").$$("grid_recipients");
    let sel_count = 0;
    let recipients_array = [];
    grid.eachRow(function (row) {
      if (grid.getItem(row).select == 1) {
        const recipients_object = {};
        recipients_object.pay_id = grid.getItem(row).pay_id;
        recipients_object.mobile_no = grid.getItem(row).phone;
        recipients_array.push(recipients_object);
        sel_count += 1;
      }
    });
    if (sel_count > 0) {
      let msg_id = formatUuid(getRandomValuesFunc());
      let msg_body = $$("messaging-page").$$("msg_text").getValue();
      let messageId = msg_id.slice(0, 8) + moment().format("ss");
      saveMessage(
        messageId,
        msg_body,
        msg_date,
        msg_rosters,
        msg_shifts,
        msg_locations,
        msg_ranks,
        msg_skill_1,
        msg_skill_2,
        sel_count,
        no_response,
        function (response) {
          if (response == "ok") {
            webix
              .ajax()
              .headers({ Authorization: "Bearer " + api_key })
              .post(
                server_url + "/messaging/send_messages",
                {
                  msg_id: messageId,
                  subject: "SAPPHIRE",
                  message: msg_body,
                  recipients_array: recipients_array,
                  no_response: no_response,
                },
                {
                  error: function (err) {
                    if (err == "Forbidden" || err == "Too many requests") {
                      webix.alert({
                        text:
                          "Error: " +
                          err +
                          " - You have reached the daily or bulk SMS send limit!",
                        width: 650,
                      });
                    } else {
                      webix.alert(
                        "There was an error sending one or more SMS messages!",
                      );
                    }
                  },
                  success: function (results) {
                    webix.alert("SMS message has been sent successfully!");
                    $$("messaging-page").$$("msg_date").setValue(new Date());
                    $$("messaging-page").$$("roster_filter").setValue("");
                    $$("messaging-page").$$("shift_filter").setValue("");
                    $$("messaging-page").$$("location_filter").setValue("");
                    $$("messaging-page").$$("rank_filter").setValue("");
                    $$("messaging-page").$$("skill_filter_1").setValue("");
                    $$("messaging-page").$$("skill_filter_2").setValue("");
                    $$("messaging-page").$$("leave_filter").setValue(0);
                    $$("messaging-page").$$("no_response").setValue(0);
                    grid.clearAll();
                  },
                },
              );
          } else {
            webix.alert("There was an error sending the message!");
          }
        },
      );
    } else {
      webix.alert(
        "You must select at least 1 employee to send an SMS message!",
      );
    }
  }
  function saveMessage(
    messageId,
    msg_body,
    msg_date,
    msg_rosters,
    msg_shifts,
    msg_locations,
    msg_ranks,
    msg_skill_1,
    msg_skill_2,
    sel_count,
    no_response,
    callback,
  ) {
    let shift_req_filter = "";
    if (overtimeFatigueMode === true) {
      shift_req_filter = $$("messaging-page").$$("shift_req_filter").getValue();
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .post(
        server_url + "/messaging/save_message",
        {
          msg_id: messageId,
          msg_date: moment(msg_date).format("YYYYMMDD"),
          rosters: msg_rosters,
          shifts: msg_shifts,
          locations: msg_locations,
          ranks: msg_ranks,
          subject: "SAPPHIRE",
          body: msg_body,
          no_of_recipients: sel_count,
          sender_id: user_logged_in,
          type: "SMS",
          skill_code_1: msg_skill_1,
          skill_code_2: msg_skill_2,
          no_response: no_response,
          shift_period: shift_req_filter,
        },
        {
          error: function (err) {
            callback("error");
          },
          success: function (result) {
            $$("messaging-page").$$("msg_text").setValue("");
            $$("messaging-page")
              .$$("char_count")
              .setValue("0 / 305 characters");
            $$("messaging-page")
              .$$("msg_text")
              .define("attributes", { maxlength: 350 });
            $$("messaging-page").$$("msg_text").refresh();
            $$("messaging-page").$$("btnSendMsg").disable();
            callback("ok");
          },
        },
      );
  }
  function loadMessages(search_date) {
    let adv_msg_filters = $$("messaging-page").$$("adv_msg_filters").getValue();
    let adv_msg_search_date;
    let adv_msg_search_time;
    let adv_msg_search_station;
    if (adv_msg_filters == 1) {
      adv_msg_search_date = $$("messaging-page")
        .$$("adv_msg_search_date")
        .getValue();
      adv_msg_search_time = $$("messaging-page")
        .$$("adv_msg_search_time")
        .getText();
      adv_msg_search_station = $$("messaging-page")
        .$$("adv_msg_search_station")
        .getText();
    }
    $$("loader-window").show();
    let grid = $$("messaging-page").$$("grid_messages");
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/messaging/get_messages",
        {
          sent_date: search_date,
          adv_msg_filters: adv_msg_filters,
          adv_msg_search_date: adv_msg_search_date,
          adv_msg_search_time: adv_msg_search_time,
          adv_msg_search_station: adv_msg_search_station,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let messages = [];
            if (results) {
              let data = JSON.parse(results);
              let response_required = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].no_response == true) {
                    response_required = "False";
                  } else {
                    response_required = "True";
                  }
                  messages.push({
                    message_id: data[x].message_id,
                    msg_date: data[x].msg_date,
                    sent_date: data[x].sent_date,
                    msg_text: data[x].body,
                    msg_response_req: response_required,
                    msg_sender: data[x].sender_id,
                    msg_send_count: data[x].no_of_recipients,
                  });
                }
              }
            }
            grid.define("data", messages);
            grid.refresh();
            grid.adjustRowHeight("msg_text", true);
            $$("loader-window").hide();
          },
        },
      );
  }
  function start_timer_auto_refresh() {
    auto_refresh = setInterval(function () {
      let grid = $$("messaging-page").$$("grid_messages");
      let selectedRow = grid.getSelectedItem();
      if (selectedRow !== undefined) {
        let message_id = selectedRow.message_id;
        let date_msg_string = selectedRow.msg_text;
        let date_in_msg_pos = date_msg_string.search("Date: ");
        let filter_date = selectedRow.msg_date;
        let fatigue_date;
        if (date_in_msg_pos > 0) {
          fatigue_date =
            date_msg_string.slice(date_in_msg_pos + 6, date_in_msg_pos + 16) +
            " 08:00";
        } else {
          fatigue_date = selectedRow.msg_date;
        }
        if (
          moment(fatigue_date, "DD/MM/YYYY HH:mm", true).isValid() === false
        ) {
          fatigue_date = moment(selectedRow.msg_date, "YYYYMMDD").format(
            "DD/MM/YYYY 08:00",
          );
        }
        getResponses(message_id, filter_date, fatigue_date);
      }
    }, 1e4);
  }
  function getFatigueHours(payId, rspDate, callback) {
    let firstDate = moment(rspDate, "DD/MM/YYYY").format("YYYYMMDD");
    let totalHours = 0;
    let startDate = "";
    let finishDate = "";
    let todayHours = 0;
    getRspShiftStartTime(payId, firstDate, function (results) {
      if (results.length > 0) {
        let currHH = moment(rspDate, "DD/MM/YYYY HH:mm").format("HH:mm");
        let currDec = moment.duration(currHH).asHours();
        if (results[0].shift_type == "off") {
          finishDate = moment(moment(firstDate).subtract(1, "days")).format(
            "YYYYMMDD",
          );
          startDate = moment(moment(firstDate).subtract(8, "days")).format(
            "YYYYMMDD",
          );
        } else if (results[0].shift_type == "day") {
          if (currDec >= 8) {
            todayHours = currDec - 8;
            todayHours = parseFloat(todayHours.toFixed(2));
            finishDate = moment(moment(firstDate).subtract(1, "days")).format(
              "YYYYMMDD",
            );
            startDate = moment(moment(firstDate).subtract(7, "days")).format(
              "YYYYMMDD",
            );
          } else {
            finishDate = moment(moment(firstDate).subtract(1, "days")).format(
              "YYYYMMDD",
            );
            startDate = moment(moment(firstDate).subtract(8, "days")).format(
              "YYYYMMDD",
            );
          }
        } else if (results[0].shift_type == "night") {
          if (currDec >= 18) {
            todayHours = currDec - 18;
            todayHours = parseFloat(todayHours.toFixed(2));
            finishDate = firstDate;
            startDate = moment(moment(firstDate).subtract(7, "days")).format(
              "YYYYMMDD",
            );
          } else {
            finishDate = moment(moment(firstDate).subtract(1, "days")).format(
              "YYYYMMDD",
            );
            startDate = moment(moment(firstDate).subtract(8, "days")).format(
              "YYYYMMDD",
            );
          }
        }
      } else {
        finishDate = moment(moment(firstDate).subtract(1, "days")).format(
          "YYYYMMDD",
        );
        startDate = moment(moment(firstDate).subtract(8, "days")).format(
          "YYYYMMDD",
        );
      }
    });
    getRAandBookings(payId, startDate, finishDate, function (results) {
      if (results.length > 0) {
        results.forEach(function (value) {
          if (
            value.booking_type == "sick_leave" ||
            value.booking_type == "leave_request" ||
            value.booking_type == "standby"
          ) {
          } else if (value.booking_type == "overtime") {
            if (value.roster === "Port Pirie") {
              if (value.shift_type == "day" || value.shift_type == "night") {
                totalHours = totalHours + 24;
              }
            } else {
              if (value.shift_type === "day") {
                totalHours = totalHours + 10;
              } else if (value.shift_type === "night") {
                totalHours = totalHours + 14;
              }
            }
            if (
              value.leave_type_code == "DRILL" ||
              value.leave_type_code == "REL"
            ) {
            } else {
              totalHours = totalHours + value.hours;
            }
          } else if (value.booking_type == "standby_link") {
            totalHours = totalHours + value.hours;
          } else {
            if (value.roster === "Port Pirie") {
              if (value.shift_type == "day" || value.shift_type == "night") {
                totalHours = totalHours + 24;
              }
            } else {
              if (value.shift_type === "day") {
                if (
                  value.roster == "Corporate Services" ||
                  value.roster == "Community Safety & Resilience" ||
                  value.roster == "Executive" ||
                  value.roster == "Infrastructure & Logistics" ||
                  value.roster == "Metro Ops Central" ||
                  value.roster == "Metro Ops Outer" ||
                  value.roster == "Operational Training" ||
                  value.roster == "Organisational Development" ||
                  value.roster == "Regional Operations"
                ) {
                  totalHours = totalHours + 8;
                } else {
                  totalHours = totalHours + 10;
                }
              } else if (value.shift_type === "night") {
                totalHours = totalHours + 14;
              }
            }
          }
          if (value.bk_period2 != value.bk_period) {
            if (
              value.booking_type2 == "sick_leave" ||
              value.booking_type2 == "leave_request" ||
              value.booking_type2 == "standby"
            ) {
            } else if (value.booking_type2 == "overtime") {
              if (
                value.leave_type_code2 != "DRILL" ||
                value.leave_type_code2 != "REL"
              ) {
                totalHours = totalHours + value.hours2;
              }
            } else if (value.booking_type2 == "standby_link") {
              totalHours = totalHours + value.hours2;
            } else {
              if (value.roster === "Port Pirie") {
                if (value.bk_period2 == "day" || value.bk_period2 == "night") {
                  totalHours = totalHours + 24;
                }
              } else {
                if (value.bk_period2 === "day") {
                  if (
                    value.roster == "Corporate Services" ||
                    value.roster == "Community Safety & Resilience" ||
                    value.roster == "Executive" ||
                    value.roster == "Infrastructure & Logistics" ||
                    value.roster == "Metro Ops Central" ||
                    value.roster == "Metro Ops Outer" ||
                    value.roster == "Operational Training" ||
                    value.roster == "Organisational Development" ||
                    value.roster == "Regional Operations"
                  ) {
                    totalHours = totalHours + 8;
                  } else {
                    totalHours = totalHours + 10;
                  }
                } else if (value.bk_period2 === "night") {
                  totalHours = totalHours + 14;
                }
              }
            }
          }
        });
      }
      callback(totalHours + todayHours);
    });
  }
  function getRspShiftStartTime(payId, rosterDate, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/get_rsp_shift_info",
        { pay_id: payId, date_string: rosterDate },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let value = JSON.parse(results);
            if (value) {
              callback(value);
            } else {
              callback([]);
            }
          },
        },
      );
  }
  function getRAandBookings(payId, startDate, finishDate, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/get_ra_bookings",
        {
          pay_id: payId,
          start_date_string: startDate,
          end_date_string: finishDate,
        },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let value = JSON.parse(results);
            if (value) {
              callback(value);
            } else {
              callback([]);
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
