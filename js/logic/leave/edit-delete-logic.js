let editDeleteBookings = (function () {
    function initApplication() {
        eventHandlers();
        let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
        let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
        $$("bookings-page").$$("booking_edit").$$("from").setValue(startOfMonth);
        $$("bookings-page").$$("booking_edit").$$("to").setValue(endOfMonth);
    }

    function eventHandlers() {
        $$("bookings-page")
            .$$("booking_edit")
            .$$("all_rosters")
            .attachEvent("onChange", function (newv) {
                if (newv === 1) {
                    $$("bookings-page").$$("booking_edit").$$("rosters").disable();
                    $$("bookings-page").$$("booking_edit").$$("rosters").setValue("");
                } else {
                    $$("bookings-page").$$("booking_edit").$$("rosters").enable();
                }
            });
        $$("bookings-page")
            .$$("booking_edit")
            .$$("btn_search")
            .attachEvent("onItemClick", function (id, e) {
                getBookings();
            });
        $$("bookings-page")
            .$$("booking_edit")
            .$$("btn_delete_days")
            .attachEvent("onItemClick", function (id, e) {
                let selectedId = $$("bookings-page")
                    .$$("booking_edit")
                    .$$("search_results")
                    .getSelectedId();
                let selectedRow = $$("bookings-page")
                    .$$("booking_edit")
                    .$$("search_results")
                    .getSelectedItem(selectedId);
                if (selectedId) {
                    let bookingInfo =
                        selectedRow[0].name +
                        " (" +
                        selectedRow[0].service_no +
                        ")" +
                        "</br>" +
                        selectedRow[0].roster +
                        " - " +
                        selectedRow[0].shift +
                        " - " +
                        selectedRow[0].location +
                        "</br>Total Hours: " +
                        selectedRow[0].total_hours;
                    $$("search-booking_details").define("template", bookingInfo);
                    $$("search-booking_details").refresh();
                    $$("search-bookings-window").show();
                    getBookingDays(selectedRow[0].booking_id);
                } else {
                    webix.alert("No booking selected!");
                }
            });
        $$("bookings-page")
            .$$("booking_edit")
            .$$("btn_delete")
            .attachEvent("onItemClick", function (id, e) {
                let bookingInfo = $$("bookings-page")
                    .$$("booking_edit")
                    .$$("search_results")
                    .getSelectedItem(id);
                let bookingType = "";
                let booking_date = moment();
                let bk_first_date = moment(
                    bookingInfo[0].booking_first_date,
                    "DD/MM/YYYY H:mm",
                ).format("YYYYMMDD");
                let lsl_end_date = "";
                let booking_status = "";
                let leaveTypeCode = "";
                let single_selected_date = "";
                let booking_pay_id = 0;
                let travel_processed = "";
                let approved_denied_date = "";
                let recurring_link_id = "";
                if (bookingInfo[0].booking_id != "") {
                    getBookingInfo(
                        bookingInfo[0].booking_id,
                        bk_first_date,
                        function (values) {
                            booking_pay_id = values[0].pay_id;
                            bookingType = values[0].booking_type;
                            booking_date = moment(
                                values[0].booking_first_date,
                                "DD/MM/YYYY HH:mm",
                            ).toDate();
                            lsl_end_date = moment(
                                values[0].booking_last_date,
                                "DD/MM/YYYY HH:mm",
                            ).toDate();
                            booking_status = values[0].status;
                            leaveTypeCode = values[0].leave_type_code;
                            single_selected_date = moment(
                                values[0].start_date,
                                "DD/MM/YYYY HH:mm",
                            ).toDate();
                            travel_processed = values[0].travel_processed;
                            approved_denied_date = moment(
                                values[0].approved_denied_date,
                                "DD/MM/YYYY HH:mm",
                            ).toDate();
                            recurring_link_id = values[0].recurring_link_id;
                        },
                    );
                    if (leaveTypeCode === "RRL") {
                        booking_pay_id = bookingInfo[0].service_no;
                    }

                    getUserPL(function (result) {
                        if (result === 6) {
                            if (booking_pay_id != user_logged_in) {
                                webix.alert({
                                    text: "You can only delete your own bookings!",
                                    width: 350,
                                });
                            } else {
                                checkDeletePermissions(
                                    "leaveTab",
                                    bookingInfo[0].booking_id,
                                    bookingType,
                                    leaveTypeCode,
                                    booking_status,
                                    single_selected_date,
                                    booking_date,
                                    lsl_end_date,
                                    travel_processed,
                                    approved_denied_date,
                                    recurring_link_id,
                                    booking_pay_id,
                                    function (response) {
                                        if (response === "allow") {
                                            webix.confirm({
                                                title: "Confirm Delete",
                                                text:
                                                    "Delete the selected booking for " +
                                                    bookingInfo[0].name +
                                                    "?",
                                                width: 500,
                                                callback: function (result) {
                                                    if (result == true) {
                                                        deleteBooking(
                                                            bookingInfo[0].booking_id,
                                                            booking_status,
                                                            recurring_link_id,
                                                            booking_pay_id,
                                                        );
                                                    }
                                                },
                                            });
                                        }
                                    },
                                );
                            }
                        } else {
                            checkDeletePermissions(
                                "leaveTab",
                                bookingInfo[0].booking_id,
                                bookingType,
                                leaveTypeCode,
                                booking_status,
                                single_selected_date,
                                booking_date,
                                lsl_end_date,
                                travel_processed,
                                approved_denied_date,
                                recurring_link_id,
                                booking_pay_id,
                                function (response) {
                                    if (response === "allow") {
                                        webix.confirm({
                                            title: "Confirm Delete",
                                            text:
                                                "Delete the selected booking for " +
                                                bookingInfo[0].name +
                                                "?",
                                            width: 500,
                                            callback: function (result) {
                                                if (result == true) {
                                                    deleteBooking(
                                                        bookingInfo[0].booking_id,
                                                        booking_status,
                                                        recurring_link_id,
                                                        booking_pay_id,
                                                    );
                                                }
                                            },
                                        });
                                    }
                                }
                            );
                        }
                    })
                }
            });

        $$("bookings-page")
            .$$("booking_edit")
            .$$("search_results")
            .attachEvent("onItemDblClick", function (id, e, node) {
                let selectedRow = $$("bookings-page")
                    .$$("booking_edit")
                    .$$("search_results")
                    .getSelectedItem(id);
                let bookingInfo =
                    selectedRow[0].name +
                    " (" +
                    selectedRow[0].service_no +
                    ")" +
                    "</br>" +
                    selectedRow[0].roster +
                    " - " +
                    selectedRow[0].shift +
                    " - " +
                    selectedRow[0].location +
                    "</br>Total Hours: " +
                    selectedRow[0].total_hours;
                $$("search-booking_details").define("template", bookingInfo);
                $$("search-booking_details").refresh();
                $$("search-bookings-window").show();
                getBookingDays(selectedRow[0].booking_id);
            });
        $$("btn_search-booking_days_close").attachEvent(
            "onItemClick",
            function (id, e) {
                $$("search-booking_details").define("template", "");
                $$("search-booking_details").refresh();
                $$("search-bookings-window").hide();
            },
        );
        $$("search-booking_days").attachEvent(
            "onItemClick",
            function (id, e, node) {
                let bookingType = "";
                let selectedRow = $$("search-booking_days").getSelectedItem(id);
                let bk_first_date = moment(
                    selectedRow[0].start_date,
                    "DD/MM/YYYY H:mm",
                ).format("YYYYMMDD");
                let booking_date = "";
                let lsl_end_date = "";
                let booking_status = "";
                let leaveTypeCode = "";
                let single_selected_date = "";
                let booking_pay_id = 0;
                let travel_processed = "";
                let approved_denied_date = "";
                let recurring_link_id = "";
                if (id.column == "day_delete") {
                    selectedRow = $$("search-booking_days").getSelectedItem(id);
                    if (selectedRow[0].deleted == null) {
                        if (selectedRow[0].code == "RRL") {
                            webix.alert(
                                "You can't delete individual days from an RRL booking!",
                            );
                        } else {
                            if (selectedRow[0].booking_id != "") {
                                getBookingInfo(
                                    selectedRow[0].booking_id,
                                    bk_first_date,
                                    function (values) {
                                        booking_pay_id = values[0].pay_id;
                                        bookingType = values[0].booking_type;
                                        booking_date = moment(
                                            values[0].booking_first_date,
                                            "DD/MM/YYYY HH:mm",
                                        ).toDate();
                                        lsl_end_date = moment(
                                            values[0].booking_last_date,
                                            "DD/MM/YYYY HH:mm",
                                        ).toDate();
                                        booking_status = values[0].status;
                                        leaveTypeCode = values[0].leave_type_code;
                                        single_selected_date = moment(
                                            values[0].start_date,
                                            "DD/MM/YYYY HH:mm",
                                        ).toDate();
                                        travel_processed = values[0].travel_processed;
                                        approved_denied_date = moment(
                                            values[0].approved_denied_date,
                                            "DD/MM/YYYY HH:mm",
                                        ).toDate();
                                        recurring_link_id = values[0].recurring_link_id;
                                    },
                                );
                                if (user_permission_level === 6) {
                                    if (booking_pay_id != user_logged_in) {
                                        webix.alert({
                                            text: "You can only delete your own bookings!",
                                            width: 350,
                                        });
                                    } else {
                                        checkDeletePermissions(
                                            "leaveTab",
                                            selectedRow[0].booking_id,
                                            bookingType,
                                            leaveTypeCode,
                                            booking_status,
                                            single_selected_date,
                                            booking_date,
                                            lsl_end_date,
                                            travel_processed,
                                            approved_denied_date,
                                            recurring_link_id,
                                            function (response) {
                                                if (response === "allow") {
                                                    webix.confirm({
                                                        title: "Confirm Day Delete",
                                                        text:
                                                            "Delete the booking for the date " +
                                                            moment(
                                                                selectedRow[0].start_date,
                                                                "DD/MM/YYYY",
                                                            ).format("DD/MM/YYYY") +
                                                            "?",
                                                        width: 500,
                                                        callback: function (result) {
                                                            if (result == true) {
                                                                deleteBookingDay(
                                                                    selectedRow[0].booking_id,
                                                                    bk_first_date,
                                                                    selectedRow[0].status,
                                                                    "",
                                                                );
                                                                getBookings();
                                                            }
                                                        },
                                                    });
                                                }
                                            },
                                        );
                                    }
                                } else {
                                    checkDeletePermissions(
                                        "leaveTab",
                                        selectedRow[0].booking_id,
                                        bookingType,
                                        leaveTypeCode,
                                        booking_status,
                                        single_selected_date,
                                        booking_date,
                                        lsl_end_date,
                                        travel_processed,
                                        approved_denied_date,
                                        recurring_link_id,
                                        function (response) {
                                            if (response === "allow") {
                                                webix.confirm({
                                                    title: "Confirm Day Delete",
                                                    text:
                                                        "Delete the booking for the date " +
                                                        moment(
                                                            selectedRow[0].start_date,
                                                            "DD/MM/YYYY",
                                                        ).format("DD/MM/YYYY") +
                                                        "?",
                                                    width: 500,
                                                    callback: function (result) {
                                                        if (result == true) {
                                                            deleteBookingDay(
                                                                selectedRow[0].booking_id,
                                                                bk_first_date,
                                                                selectedRow[0].status,
                                                                "",
                                                            );
                                                            getBookings();
                                                        }
                                                    },
                                                });
                                            }
                                        },
                                    );
                                }
                            }
                        }
                    }
                }
            },
        );
    }

    rosters_subject.subscribe(function (data) {
        let select = $$("bookings-page").$$("booking_edit").$$("rosters");
        if (data) {
            let options = [];
            let roster_names = JSON.parse(data);
            roster_names.forEach(function (value) {
                options.push(value.roster_name);
            });
            select.define("options", options);
            select.refresh();
        }
    });

    function deleteBookingDay(bookingId, dateString, status, mode) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .del(
                server_url + "/bookings/booking_day",
                {
                    booking_id: bookingId,
                    date_string: dateString,
                    status: status,
                    deleted_by: user_logged_in,
                    bk_deleted_day: dateString,
                },
                {
                    error: function (err) {
                    },
                    success: function () {
                        webix.message({
                            text: "Booking day was deleted!",
                            type: "success",
                            expire: 1500,
                        });
                        getBookingDays(bookingId);
                        if (mode != "save_booking") {
                            if (ro_view_showing == true) {
                                availabilityReport.loadReport(
                                    $$("schedule-page").$$("grid_ro_view_report"),
                                    function (callback) {
                                        if (callback == "ok") {
                                            schedule.reload_roster();
                                        }
                                    },
                                );
                            } else {
                                schedule.reload_roster();
                            }
                        }
                    },
                },
            );
    }

    function deleteLSLBooking(
        bookingId,
        status,
        booking_date,
        period,
        leaveTypeCode,
        payId,
    ) {
        let dateString = "";
        let dateStrings = "";
        for (let x = 0; x < period; x++) {
            dateString = moment(booking_date).add(x, "days");
            dateString = moment(dateString).format("YYYYMMDD");
            dateStrings = dateStrings + "," + "'" + dateString + "'";
        }
        dateStrings = dateStrings.replace(",", "(");
        dateStrings = dateStrings + ")";
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .del(
                server_url + "/bookings/lsl_booking_period",
                {
                    booking_id: bookingId,
                    status: status,
                    date_strings: dateStrings,
                    deleted_by: user_logged_in,
                    pay_id: payId,
                },
                {
                    error: function (err) {
                    },
                    success: function () {
                        webix.message({
                            text: leaveTypeCode + " tour was deleted!",
                            type: "success",
                            expire: 1500,
                        });
                        if (ro_view_showing == true) {
                            availabilityReport.loadReport(
                                $$("schedule-page").$$("grid_ro_view_report"),
                                function (callback) {
                                    if (callback == "ok") {
                                        functionsPopup.closePopup();
                                        schedule.reload_roster();
                                    }
                                },
                            );
                        } else {
                            functionsPopup.closePopup();
                            schedule.reload_roster();
                        }
                    },
                },
            );
    }

    function getBookingDays(bookingId) {
        let grid = $$("search-booking_days");
        grid.clearAll();
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/bookings/booking_days",
                {booking_id: bookingId},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        let result = JSON.parse(results);
                        let search_results = [];
                        let del_icon = "";
                        let del_count = 0;
                        if (result.length > 0) {
                            for (let x = 0; x < result.length; x++) {
                                if (result[x].day_deleted == true) {
                                    del_icon = "";
                                    del_count += 1;
                                } else {
                                    del_icon = "<span class = 'webix_icon fas fa-trash'></span>";
                                }
                                search_results.push({
                                    booking_id: result[x].booking_id,
                                    code: result[x].leave_type_code,
                                    start_date: result[x].start_date,
                                    end_date: result[x].end_date,
                                    shift_hours: result[x].hours,
                                    day_delete: del_icon,
                                    status: result[x].status,
                                    deleted: result[x].day_deleted,
                                });
                            }
                            grid.define("data", search_results);
                            grid.refresh();
                            $$("search-days_count").define(
                                "template",
                                "Booking days: " +
                                result.length +
                                "  (Active: " +
                                (result.length - del_count) +
                                " | Deleted: " +
                                del_count +
                                ")",
                            );
                            $$("search-days_count").refresh();
                        } else {
                            $$("search-days_count").define(
                                "template",
                                "No. of booking days: 0",
                            );
                            $$("search-days_count").refresh();
                        }
                    },
                },
            );
    }

    function getBookings() {
        let fromDate = $$("bookings-page").$$("booking_edit").$$("from").getValue();
        let toDate = $$("bookings-page").$$("booking_edit").$$("to").getValue();
        let roster = "";
        let filter = $$("bookings-page")
            .$$("booking_edit")
            .$$("all_rosters")
            .getValue();
        if (filter == 1) {
            roster = "";
        } else {
            roster = $$("bookings-page").$$("booking_edit").$$("rosters").getText();
        }
        $$("bookings-page").$$("booking_edit").$$("search_results").clearAll();
        $$("bookings-page")
            .$$("booking_edit")
            .$$("search_count")
            .define("template", "");
        $$("bookings-page").$$("booking_edit").$$("search_count").refresh();
        $$("loader-window").show();
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/bookings",
                {
                    start_date: moment(fromDate).format("YYYY-MM-DD 00:00:01"),
                    end_date: moment(toDate).format("YYYY-MM-DD 23:59:59"),
                    roster: roster,
                },
                {
                    error: function (err) {
                        $$("loader-window").hide();
                    },
                    success: function (results) {
                        let result = JSON.parse(results);
                        let search_results = [];
                        let location = "";
                        let middleName = "";
                        if (result.length > 0) {
                            for (let x = 0; x < result.length; x++) {
                                if (result[x].location === null) {
                                    location = "";
                                } else {
                                    location = result[x].location;
                                }
                                if (result[x].middle_name === null) {
                                    middleName = "";
                                } else {
                                    middleName = result[x].middle_name;
                                }
                                search_results.push({
                                    booking_id: result[x].booking_id,
                                    service_no: result[x].pay_id,
                                    name:
                                        result[x].surname +
                                        ", " +
                                        result[x].first_name +
                                        " " +
                                        middleName,
                                    rank: result[x].rank,
                                    roster: result[x].roster,
                                    shift: result[x].shift,
                                    location: location,
                                    booking_first_date: result[x].booking_first_date,
                                    booking_last_date: result[x].booking_last_date,
                                    total_hours: result[x].total_hours,
                                    code: result[x].leave_type_code,
                                    date_created: result[x].date_created,
                                });
                            }
                            $$("bookings-page")
                                .$$("booking_edit")
                                .$$("search_results")
                                .define("data", search_results);
                            $$("bookings-page")
                                .$$("booking_edit")
                                .$$("search_results")
                                .refresh();
                            $$("bookings-page")
                                .$$("booking_edit")
                                .$$("search_count")
                                .define("template", result.length + " records found");
                            $$("bookings-page")
                                .$$("booking_edit")
                                .$$("search_count")
                                .refresh();
                            $$("loader-window").hide();
                        } else {
                            $$("loader-window").hide();
                        }
                    },
                },
            );
    }

    function deleteBooking(booking_id, status, recurring_link_id, payId) {
        let dateString = "";
        if (sel_delete_period == "future") {
            dateString = globalSelectedDate;
        } else {
            dateString = 0;
        }
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .del(
                server_url + "/bookings",
                {
                    booking_id: booking_id,
                    status: status,
                    recurring_link_id: recurring_link_id,
                    deleted_by: user_logged_in,
                    date_string: dateString,
                    pay_id: payId,
                },
                {
                    error: function (err) {
                    },
                    success: function () {
                        if (ro_view_showing == true) {
                            availabilityReport.loadReport(
                                $$("schedule-page").$$("grid_ro_view_report"),
                                function (callback) {
                                    if (callback == "ok") {
                                        functionsPopup.closePopup();
                                        schedule.reload_roster();
                                    }
                                },
                            );
                        } else {
                            functionsPopup.closePopup();
                            schedule.reload_roster();
                        }
                        webix.message({
                            text: "Booking was deleted!",
                            type: "success",
                            expire: 1500,
                        });
                        getBookings();
                    },
                },
            );
    }

    return {
        initialise: function () {
            initApplication();
        },
        deleteBookingDayOnly: function (bookingId, dateString, status, mode) {
            deleteBookingDay(bookingId, dateString, status, mode);
        },
        deleteWholeBooking: function (bookingId, status, recurring_link_id, payId) {
            deleteBooking(bookingId, status, recurring_link_id, payId);
        },
        deleteLSLBookingPeriod: function (
            bookingId,
            status,
            booking_date,
            period,
            leaveTypeCode,
            payId,
        ) {
            deleteLSLBooking(
                bookingId,
                status,
                booking_date,
                period,
                leaveTypeCode,
                payId,
            );
        },
    };
})();
