let leaveLogs = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster;
  let exp_shift;
  let exp_location;
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("bookings-page")
      .$$("booking_log")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("bookings-page").$$("booking_log").$$("to_date").setValue(endOfMonth);
    $$("bookings-page").$$("booking_log").$$("employee_filter").setValue(1);
    $$("bookings-page")
      .$$("booking_log")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page").$$("booking_log").$$("status_filter").setValue(1);
    $$("bookings-page").$$("booking_log").$$("leave_filter").setValue("ALL");
    exp_grid = $$("bookings-page").$$("booking_log").$$("leave-logs_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report period " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Leave Logs Report for " +
            exp_roster +
            " | " +
            exp_shift +
            " | " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("booking_log")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        $$("loader-window").show();
        setTimeout(function () {
          exp_fromDate = $$("bookings-page")
            .$$("booking_log")
            .$$("from_date")
            .getValue();
          exp_toDate = $$("bookings-page")
            .$$("booking_log")
            .$$("to_date")
            .getValue();
          exp_roster = $$("bookings-page")
            .$$("booking_log")
            .$$("roster_filter")
            .getText();
          exp_shift = $$("bookings-page")
            .$$("booking_log")
            .$$("shift_filter")
            .getText();
          exp_location = $$("bookings-page")
            .$$("booking_log")
            .$$("location_filter")
            .getText();
          if (exp_grid.count() > 0) {
            webix.toExcel(exp_grid, {
              filename:
                "Leave Logs Report (" + moment().format("DD-MM-YYYY") + ")",
              styles: true,
              heights: true,
            });
            $$("loader-window").hide();
          } else {
            $$("loader-window").hide();
            webix.alert("No data to Export!");
          }
        }, 100);
      });
    $$("bookings-page")
      .$$("booking_log")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("bookings-page").$$("booking_log").$$("shift_filter").setValue("");
        $$("bookings-page")
          .$$("booking_log")
          .$$("location_filter")
          .setValue("");
        $$("bookings-page").$$("booking_log").$$("inc_comms").hide();
        if (newv == "-- All Rosters --") {
          $$("bookings-page").$$("booking_log").$$("shift_filter").hide();
          $$("bookings-page").$$("booking_log").$$("location_filter").hide();
        } else if (newv == "Metro") {
          $$("bookings-page").$$("booking_log").$$("inc_comms").show();
          $$("bookings-page").$$("booking_log").$$("shift_filter").show();
          $$("bookings-page").$$("booking_log").$$("location_filter").show();
          $$("bookings-page")
            .$$("booking_log")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("bookings-page")
            .$$("booking_log")
            .$$("location_filter")
            .setValue("-- All Stations --");
        } else {
          $$("bookings-page").$$("booking_log").$$("shift_filter").show();
          $$("bookings-page").$$("booking_log").$$("location_filter").show();
          $$("bookings-page")
            .$$("booking_log")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("bookings-page")
            .$$("booking_log")
            .$$("location_filter")
            .setValue("-- All Stations --");
        }
        load_shifts(newv);
      });
    $$("bookings-page")
      .$$("booking_log")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("bookings-page")
          .$$("booking_log")
          .$$("roster_filter")
          .getValue();
        let location = $$("bookings-page")
          .$$("booking_log")
          .$$("location_filter")
          .getValue();
        if (roster == "-- All Rosters --") {
          getBookings();
        } else {
          if (location == "") {
            webix.alert("You must specify a 'Location'.");
          } else {
            getBookings();
          }
        }
      });
    $$("bookings-page")
      .$$("booking_log")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("bookings-page")
          .$$("booking_log")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  employees_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("booking_log").$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({
          id: value.id,
          value: value.value + " (" + value.id + ")",
        });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("booking_log").$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  leave_types_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("booking_log").$$("leave_filter");
    if (data) {
      let options = [];
      let leave_types = JSON.parse(data);
      options.push({ id: "ALL", value: "-- All Leave Types --" });
      leave_types.forEach(function (value) {
        options.push({
          id: value.code,
          value: value.code + " - " + value.description,
        });
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                shiftArray.unshift("-- All Shifts --");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("bookings-page")
                  .$$("booking_log")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("bookings-page")
                  .$$("booking_log")
                  .$$("shift_filter")
                  .refresh();
                $$("bookings-page")
                  .$$("booking_log")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("bookings-page")
                  .$$("booking_log")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function getBookings() {
    let pay_id = $$("bookings-page")
      .$$("booking_log")
      .$$("employee_filter")
      .getValue();
    let fromDate = $$("bookings-page")
      .$$("booking_log")
      .$$("from_date")
      .getValue();
    let toDate = $$("bookings-page").$$("booking_log").$$("to_date").getValue();
    let roster = $$("bookings-page")
      .$$("booking_log")
      .$$("roster_filter")
      .getText();
    let shift = $$("bookings-page")
      .$$("booking_log")
      .$$("shift_filter")
      .getText();
    let location = $$("bookings-page")
      .$$("booking_log")
      .$$("location_filter")
      .getText();
    let leave_type = $$("bookings-page")
      .$$("booking_log")
      .$$("leave_filter")
      .getValue();
    let filter = $$("bookings-page")
      .$$("booking_log")
      .$$("status_filter")
      .getText();
    let grid = $$("bookings-page").$$("booking_log").$$("leave-logs_grid");
    let inc_comms = $$("bookings-page")
      .$$("booking_log")
      .$$("inc_comms")
      .getValue();
    grid.clearAll();
    $$("loader-window").show();
    if (leave_type === "RRL") {
      grid.hideColumn("total_hours");
      grid.showColumn("days");
    } else {
      grid.hideColumn("days");
      grid.showColumn("total_hours");
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/logs",
        {
          pay_id: pay_id,
          start_date: moment(fromDate).format("YYYY-MM-DD 00:00:01"),
          end_date: moment(toDate).format("YYYY-MM-DD 23:59:59"),
          roster: roster,
          shift: shift,
          location: location,
          leave_type: leave_type,
          status: filter,
          inc_comms: inc_comms,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let result = JSON.parse(results);
            let search_results = [];
            let location = "";
            let middleName = "";
            let delete_icon = "";
            let comments = "";
            let curr_status = "";
            let deleted_by = "";
            let approved_denied_by = "";
            let booking_first_date = "";
            let booking_last_date = "";
            let created_date = "";
            let deleted_date = "";
            let deleted_period = "";
            let approved_denied_date = "";
            if (result.length > 0) {
              for (let x = 0; x < result.length; x++) {
                if (result[x].location === null) {
                  location = "";
                } else {
                  location = result[x].location;
                }
                if (result[x].middle_name === null) {
                  middleName = "";
                } else {
                  middleName = result[x].middle_name;
                }
                if (result[x].deleted == true) {
                  delete_icon =
                    "<span class = 'delete_request_true webix_icon fas fa-minus-circle'></span>";
                  curr_status = "Deleted";
                  deleted_date = moment(
                    result[x].deleted_date,
                    "DD/MM/YYYY H:mm",
                  ).toDate();
                  deleted_by = result[x].deleted_by;
                  deleted_period = "ALL DAYS DELETED";
                } else if (result[x].day_deleted == true) {
                  delete_icon =
                    "<span class = 'day_delete_request_true webix_icon fas fa-minus-circle'></span>";
                  curr_status = "Day Deleted";
                  deleted_period = moment(
                    result[x].bk_deleted_day,
                    "YYYYMMDD",
                  ).format("DD/MM/YYYY");
                  deleted_by = result[x].deleted_by;
                  deleted_date = moment(
                    result[x].deleted_date,
                    "DD/MM/YYYY H:mm",
                  ).format("DD/MM/YYYY HH:mm");
                  if (deleted_period == "Invalid date") {
                    deleted_period = deleted_date;
                  }
                } else {
                  delete_icon =
                    "<span class = 'delete_request_false webix_icon fas fa-minus-circle'></span>";
                  curr_status = result[x].status;
                  deleted_date = "";
                  deleted_by = "";
                  deleted_period = "";
                }
                if (
                  result[x].request_comments == "" ||
                  result[x].request_comments == null
                ) {
                  comments = result[x].comments;
                } else {
                  comments = result[x].request_comments;
                }
                booking_first_date = moment(
                  result[x].booking_first_date,
                  "DD/MM/YYYY H:mm",
                ).toDate();
                booking_last_date = moment(
                  result[x].booking_last_date,
                  "DD/MM/YYYY H:mm",
                ).toDate();
                if (result[x].status != "Pending") {
                  if (result[x].approved_denied_date == "Invalid date") {
                    approved_denied_date = "";
                    approved_denied_by = "";
                  } else {
                    approved_denied_date = moment(
                      result[x].approved_denied_date,
                      "DD/MM/YYYY H:mm",
                    ).toDate();
                    approved_denied_by = result[x].approved_denied_by;
                  }
                } else {
                  approved_denied_date = "";
                  approved_denied_by = "";
                }
                created_date = moment(
                  result[x].date_created,
                  "DD/MM/YYYY H:mm",
                ).toDate();
                if (user_permission_level === 5 || user_permission_level === 6) {
                  if (result[x].pay_id == user_logged_in) {
                    search_results.push({
                      booking_id: result[x].booking_id,
                      service_no: result[x].pay_id,
                      name:
                        result[x].surname +
                        ", " +
                        result[x].first_name +
                        " " +
                        middleName,
                      rank: result[x].rank,
                      roster: result[x].roster,
                      shift: result[x].shift,
                      location: location,
                      booking_first_date: booking_first_date,
                      booking_last_date: booking_last_date,
                      total_hours: result[x].total_hours,
                      days: result[x].days,
                      code: result[x].leave_type_code,
                      leave_group: result[x].leave_group,
                      date_created: created_date,
                      deleted: delete_icon,
                      deleted_period: deleted_period,
                      status: curr_status,
                      created_by: result[x].created_by,
                      approved_denied_date: approved_denied_date,
                      approved_denied_by: approved_denied_by,
                      comments: comments,
                      date_deleted: deleted_date,
                      deleted_by: deleted_by,
                    });
                  }
                } else {
                  search_results.push({
                    booking_id: result[x].booking_id,
                    service_no: result[x].pay_id,
                    name:
                      result[x].surname +
                      ", " +
                      result[x].first_name +
                      " " +
                      middleName,
                    rank: result[x].rank,
                    roster: result[x].roster,
                    shift: result[x].shift,
                    location: location,
                    booking_first_date: booking_first_date,
                    booking_last_date: booking_last_date,
                    total_hours: result[x].total_hours,
                    days: result[x].days,
                    code: result[x].leave_type_code,
                    leave_group: result[x].leave_group,
                    date_created: created_date,
                    deleted: delete_icon,
                    deleted_period: deleted_period,
                    status: curr_status,
                    created_by: result[x].created_by,
                    approved_denied_date: approved_denied_date,
                    approved_denied_by: approved_denied_by,
                    comments: comments,
                    date_deleted: deleted_date,
                    deleted_by: deleted_by,
                  });
                }
              }
            }
            grid.define("data", search_results);
            grid.refresh();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (record.status == "Approved") {
                grid.addCellCss(row, "status", "approved_status");
                grid.addCellCss(row, "approved_denied_date", "approved_status");
                grid.addCellCss(row, "approved_denied_by", "approved_status");
              } else if (record.status == "Denied") {
                grid.addCellCss(row, "status", "denied_status");
                grid.addCellCss(row, "approved_denied_date", "denied_status");
                grid.addCellCss(row, "approved_denied_by", "denied_status");
              } else if (record.status == "Pending") {
                grid.addCellCss(row, "status", "pending_status");
              }
              if (record.deleted_period != "ALL DAYS DELETED") {
                grid.addCellCss(row, "deleted_period", "deleted_date");
              }
            });
            grid.adjustRowHeight("comments");
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
