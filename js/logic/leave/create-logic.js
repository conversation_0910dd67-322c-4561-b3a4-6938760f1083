let createBooking = (function () {
  let loadIndex = 1;
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("booking_create")
      .$$("import_uploader")
      .attachEvent("onBeforeFileAdd", function (upload) {
        $$("loader-window").show();
        let sheet = $$("bookings-page").$$("booking_create").$$("excel_import");
        sheet.clearAll();
        sheet.parse(upload.file, "excel");
        $$("bookings-page")
          .$$("booking_create")
          .$$("file_name")
          .define("label", upload.name + " file loaded!");
        $$("bookings-page").$$("booking_create").$$("file_name").refresh();
        loadIndex = 1;
        return false;
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("excel_import")
      .attachEvent("onAfterLoad", function () {
        if (loadIndex == 1) {
          let sheet = $$("bookings-page")
            .$$("booking_create")
            .$$("excel_import");
          let fileType = sheet.serialize();
          if (fileType[0].data0 == "Booking Report Log") {
            let rowId = 0;
            sheet.eachRow(function (row) {
              rowId += 1;
              if (rowId == 1) {
                sheet.remove(row);
                sheet.remove(row + 1);
                sheet.remove(row + 2);
                sheet.remove(row + 3);
                sheet.remove(row + 4);
                sheet.remove(row + 5);
              }
            });
            $$("bookings-page")
              .$$("booking_create")
              .$$("import_sheet")
              .enable();
            $$("bookings-page")
              .$$("booking_create")
              .$$("import_type")
              .setValue("Leave");
          } else if (fileType[0].data0 == "Leave Log") {
            let rowId = 0;
            sheet.eachRow(function (row) {
              rowId += 1;
              if (rowId == 1) {
                sheet.remove(row);
                sheet.remove(row + 1);
                sheet.remove(row + 2);
                sheet.remove(row + 3);
                sheet.remove(row + 4);
                sheet.remove(row + 5);
              }
            });
            $$("bookings-page")
              .$$("booking_create")
              .$$("import_sheet")
              .enable();
            $$("bookings-page")
              .$$("booking_create")
              .$$("import_type")
              .setValue("RRL");
          } else {
            webix.alert(
              "The imported file does not appear to be a valid Bookings or RRL Report!",
            );
            $$("bookings-page")
              .$$("booking_create")
              .$$("import_sheet")
              .disable();
          }
        }
        loadIndex += 1;
        $$("loader-window").hide();
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("clear_sheet")
      .attachEvent("onItemClick", function (id, e) {
        $$("bookings-page").$$("booking_create").$$("excel_import").clearAll();
        $$("bookings-page").$$("booking_create").$$("import_sheet").disable();
        $$("bookings-page")
          .$$("booking_create")
          .$$("file_name")
          .define("label", "Import Booking Reports from Gartan");
        $$("bookings-page").$$("booking_create").$$("file_name").refresh();
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("import_sheet")
      .attachEvent("onItemClick", function (id, e) {
        importBookings();
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        getEmployees();
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("day_hours")
      .attachEvent("onItemClick", function (id) {
        let chkValue = $$("bookings-page")
          .$$("booking_create")
          .$$("day_hours")
          .getValue();
        let startDate = $$("bookings-page")
          .$$("booking_create")
          .$$("start_date")
          .getValue();
        if (chkValue === 1) {
          $$("bookings-page")
            .$$("booking_create")
            .$$("start_time")
            .setValue("08:00");
          $$("bookings-page")
            .$$("booking_create")
            .$$("end_time")
            .setValue("18:00");
          $$("bookings-page")
            .$$("booking_create")
            .$$("night_hours")
            .setValue(0);
          $$("bookings-page").$$("booking_create").$$("start_time").disable();
          $$("bookings-page").$$("booking_create").$$("end_time").disable();
          $$("bookings-page")
            .$$("booking_create")
            .$$("end_date")
            .setValue(startDate);
        } else {
          $$("bookings-page")
            .$$("booking_create")
            .$$("night_hours")
            .setValue(0);
          if (
            $$("bookings-page")
              .$$("booking_create")
              .$$("night_hours")
              .getValue() === 0
          ) {
            $$("bookings-page").$$("booking_create").$$("start_time").enable();
            $$("bookings-page").$$("booking_create").$$("end_time").enable();
          }
        }
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("night_hours")
      .attachEvent("onItemClick", function (id) {
        let chkValue = $$("bookings-page")
          .$$("booking_create")
          .$$("night_hours")
          .getValue();
        let startDate = $$("bookings-page")
          .$$("booking_create")
          .$$("start_date")
          .getValue();
        let endDate = moment(startDate).add(1, "days");
        if (chkValue === 1) {
          $$("bookings-page")
            .$$("booking_create")
            .$$("start_time")
            .setValue("18:00");
          $$("bookings-page")
            .$$("booking_create")
            .$$("end_time")
            .setValue("08:00");
          $$("bookings-page").$$("booking_create").$$("day_hours").setValue(0);
          $$("bookings-page").$$("booking_create").$$("start_time").disable();
          $$("bookings-page").$$("booking_create").$$("end_time").disable();
          $$("bookings-page")
            .$$("booking_create")
            .$$("end_date")
            .setValue(new Date(endDate));
        } else {
          $$("bookings-page").$$("booking_create").$$("day_hours").setValue(0);
          if (
            $$("bookings-page")
              .$$("booking_create")
              .$$("day_hours")
              .getValue() === 0
          ) {
            $$("bookings-page").$$("booking_create").$$("start_time").enable();
            $$("bookings-page").$$("booking_create").$$("end_time").enable();
          }
        }
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("search_text")
      .attachEvent("onEnter", function (ev) {
        getEmployees();
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("btn_save")
      .attachEvent("onItemClick", function (id, e) {
        let rightColumnData = $$("bookings-page")
          .$$("booking_create")
          .$$("search_results")
          .$$("right")
          .data.serialize();
        checkCreatePermissions(
          "leaveTab",
          "leave_request",
          function (response) {
            if (response === "Approved" || response === "Pending") {
              saveBookings(rightColumnData, response);
            }
          },
        );
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("roster_name")
      .attachEvent("onChange", function (newv, oldv) {
        $$("bookings-page").$$("booking_create").$$("shift_type").setValue("");
        $$("bookings-page").$$("booking_create").$$("locations").setValue("");
        let rosterName = $$("bookings-page")
          .$$("booking_create")
          .$$("roster_name")
          .getText();
        load_shifts(rosterName);
        let list = $$("bookings-page")
          .$$("booking_create")
          .$$("search_results");
        list.$$("left").clearAll();
        list.$$("right").clearAll();
        list.$$("left").data.clearAll();
        list.$$("right").data.clearAll();
        $$("bookings-page")
          .$$("booking_create")
          .$$("search_count")
          .define("template", "0 records found");
        $$("bookings-page").$$("booking_create").$$("search_count").refresh();
        $$("bookings-page")
          .$$("booking_create")
          .$$("selected_count")
          .define("template", "0 records selected");
        $$("bookings-page").$$("booking_create").$$("selected_count").refresh();
        list.setValue([]);
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("shift_type")
      .attachEvent("onChange", function (newv, oldv) {
        let list = $$("bookings-page")
          .$$("booking_create")
          .$$("search_results");
        list.$$("left").clearAll();
        list.$$("right").clearAll();
        list.$$("left").data.clearAll();
        list.$$("right").data.clearAll();
        $$("bookings-page")
          .$$("booking_create")
          .$$("search_count")
          .define("template", "0 records found");
        $$("bookings-page").$$("booking_create").$$("search_count").refresh();
        $$("bookings-page")
          .$$("booking_create")
          .$$("selected_count")
          .define("template", "0 records selected");
        $$("bookings-page").$$("booking_create").$$("selected_count").refresh();
        list.setValue([]);
        displayShiftSequence();
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("locations")
      .attachEvent("onChange", function (newv, oldv) {
        let list = $$("bookings-page")
          .$$("booking_create")
          .$$("search_results");
        list.$$("left").clearAll();
        list.$$("right").clearAll();
        list.$$("left").data.clearAll();
        list.$$("right").data.clearAll();
        $$("bookings-page")
          .$$("booking_create")
          .$$("search_count")
          .define("template", "0 records found");
        $$("bookings-page").$$("booking_create").$$("search_count").refresh();
        $$("bookings-page")
          .$$("booking_create")
          .$$("selected_count")
          .define("template", "0 records selected");
        $$("bookings-page").$$("booking_create").$$("selected_count").refresh();
        list.setValue([]);
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("start_date")
      .attachEvent("onChange", function (newv, oldv) {
        let start_date = $$("bookings-page")
          .$$("booking_create")
          .$$("start_date")
          .getValue();
        let endDate = moment(start_date).add(1, "days");
        let dayValue = $$("bookings-page")
          .$$("booking_create")
          .$$("day_hours")
          .getValue();
        let nightValue = $$("bookings-page")
          .$$("booking_create")
          .$$("night_hours")
          .getValue();
        let shift = $$("bookings-page")
          .$$("booking_create")
          .$$("shift_type")
          .getValue();
        if (newv !== oldv) {
          if (dayValue == 1) {
            $$("bookings-page")
              .$$("booking_create")
              .$$("end_date")
              .setValue(start_date);
          } else if (nightValue == 1) {
            $$("bookings-page")
              .$$("booking_create")
              .$$("end_date")
              .setValue(new Date(endDate));
          }
        }
        if (shift != "") {
          displayShiftSequence();
        }
      });
    $$("bookings-page")
      .$$("booking_create")
      .$$("search_results")
      .attachEvent("onChange", function () {
        let rightColumnData = $$("bookings-page")
          .$$("booking_create")
          .$$("search_results")
          .$$("right")
          .data.serialize();
        $$("bookings-page")
          .$$("booking_create")
          .$$("selected_count")
          .define("template", rightColumnData.length + " records selected");
        $$("bookings-page").$$("booking_create").$$("selected_count").refresh();
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("booking_create").$$("roster_name");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  leave_types_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("booking_create").$$("leave_type");
    if (data) {
      let options = [];
      let leave_types = JSON.parse(data);
      leave_types.forEach(function (value) {
        if (
          value.code == "RRL" ||
          value.code == "LSL" ||
          value.type == "Sickness"
        ) {
        } else {
          options.push({
            id: value.code,
            value: value.code + " - " + value.description,
            bk_type: value.type,
          });
        }
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function displayShiftSequence() {
    let grid = $$("bookings-page").$$("booking_create").$$("shift_sequence");
    let roster = $$("bookings-page")
      .$$("booking_create")
      .$$("roster_name")
      .getText();
    let shift = $$("bookings-page")
      .$$("booking_create")
      .$$("shift_type")
      .getText();
    let startDate = $$("bookings-page")
      .$$("booking_create")
      .$$("start_date")
      .getValue();
    let period = "8 Days";
    let start_date = moment(startDate).format("DD-MM-YYYY");
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/roster",
        {
          roster_name: roster,
          shift_name: shift,
          start_date: start_date,
          time_period: period,
        },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let data = JSON.parse(results);
              let nowDate = new Date();
              let curr_date = moment(nowDate).format("YYYYMMDD");
              let sel_date_col = 0;
              for (let y = 1; y < data.columns.length; y++) {
                if (data.columns[y].header.date == curr_date) {
                  data.columns[y].header.css = "curr_day";
                  sel_date_col = y;
                }
                if (
                  public_holiday_dates_array.some(
                    (public_holiday_dates_array) =>
                      public_holiday_dates_array.date_string ==
                      data.columns[y].header.date,
                  )
                ) {
                  data.columns[y].header.css = "ph_day";
                }
                data.columns[y].cssFormat = function (
                  value,
                  config,
                  row_id,
                  column_id,
                ) {
                  if (value != null) {
                    value = value.toString();
                    if (value.indexOf("fa-sun") > 0) {
                      if (y == sel_date_col) {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "#15317E !important",
                          "border-width": "1px 2px 1px 2px !important",
                        };
                      } else {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "dimgrey !important",
                          "border-width": "1px !important",
                        };
                      }
                    } else if (value.indexOf("fa-moon") > 0) {
                      if (y == sel_date_col) {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "#15317E !important",
                          "border-width": "1px 2px 1px 2px !important",
                        };
                      } else {
                        return {
                          "background-color": "#C3EBFE",
                          "border-color": "dimgrey !important",
                          "border-width": "1px !important",
                        };
                      }
                    } else if (value.indexOf("fa-ban") > 0) {
                      if (y == sel_date_col) {
                        return {
                          "background-color": "#EAE9E9",
                          "border-color": "#15317E !important",
                          "border-width": "1px 2px 1px 2px !important",
                        };
                      } else {
                        return {
                          "background-color": "#EAE9E9",
                          "border-color": "dimgrey !important",
                          "border-width": "1px !important",
                        };
                      }
                    } else if (parseInt(column_id) >= 1) {
                      if (row_id !== 1) {
                        let found = false;
                        let columnIndex = 0;
                        if (found === true) {
                          if (
                            config.roster_arrangement[columnIndex]
                              .shift_type === "day"
                          ) {
                            if (y == sel_date_col) {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "#15317E !important",
                                "border-width": "1px 2px 1px 2px !important",
                              };
                            } else {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "dimgrey !important",
                                "border-width": "1px !important",
                              };
                            }
                          } else if (
                            config.roster_arrangement[columnIndex]
                              .shift_type === "night"
                          ) {
                            if (y == sel_date_col) {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "#15317E !important",
                                "border-width": "1px 2px 1px 2px !important",
                              };
                            } else {
                              return {
                                "background-color": "#C3EBFE",
                                "border-color": "dimgrey !important",
                                "border-width": "1px !important",
                              };
                            }
                          } else if (
                            config.roster_arrangement[columnIndex]
                              .shift_type === "off"
                          ) {
                            if (y == sel_date_col) {
                              return {
                                "background-color": "#EAE9E9",
                                "border-color": "#15317E !important",
                                "border-width": "1px 2px 1px 2px !important",
                              };
                            } else {
                              return {
                                "background-color": "#EAE9E9",
                                "border-color": "dimgrey !important",
                                "border-width": "1px !important",
                              };
                            }
                          }
                        } else {
                          if (y == sel_date_col) {
                            return {
                              "background-color": "#FFFFFF !important",
                              "border-color": "#15317E !important",
                              "border-width": "1px 2px 1px 2px !important",
                              color: "#FFFFFF !important",
                            };
                          } else {
                            return {
                              "background-color": "#FFFFFF !important",
                              "border-color": "dimgrey !important",
                              "border-width": "1px !important",
                              color: "#FFFFFF !important",
                            };
                          }
                        }
                      }
                    }
                  }
                };
              }
              grid.config.columns = data.columns;
              grid.config.columns.splice(0, 4);
              grid.refreshColumns();
              let shift_data = data.shift_days;
              let iconRows = [];
              let iconCol = "";
              if (shift_data.length > 0) {
                for (let x = 1; x <= shift_data.length; x++) {
                  if (
                    shift_data[x - 1].roster == "Port Pirie" &&
                    shift_data[x - 1].icon == "sun"
                  ) {
                    iconCol = '{"id": 1,';
                    iconCol = iconCol + '"' + x + '":';
                    iconCol = iconCol + '"<span class=';
                    iconCol =
                      iconCol +
                      "'webix_icon fas fa-sun'></span></br><span class='webix_icon fas fa-moon'>";
                    iconCol = iconCol + '</span>"';
                    iconCol = iconCol + "}";
                  } else {
                    iconCol = '{"id": 1,';
                    iconCol = iconCol + '"' + x + '":';
                    iconCol = iconCol + '"<span class=';
                    iconCol =
                      iconCol +
                      "'webix_icon fas fa-" +
                      shift_data[x - 1].icon +
                      "'>";
                    iconCol = iconCol + '</span>"';
                    iconCol = iconCol + "}";
                  }
                  iconRows.push(JSON.parse(iconCol));
                }
                grid.parse(iconRows);
              }
            }
          },
        },
      );
  }
  function importBookings() {
    let sheet = $$("bookings-page").$$("booking_create").$$("excel_import");
    let bk_type = $$("bookings-page")
      .$$("booking_create")
      .$$("import_type")
      .getValue();
    let count = sheet.count();
    let record = "";
    let errorReport = [];
    if (count > 1) {
      $$("loader-window").show();
      setTimeout(function () {
        let noOfDays = 0;
        let booking_id = "";
        let importStartDate = $$("bookings-page")
          .$$("booking_create")
          .$$("import_start_date")
          .getValue();
        let import_start_date = "";
        let bk_start_date = "";
        let booking_first_date = "";
        let booking_last_date = "";
        let rank = "";
        let roster = "";
        let shift = "";
        let location = "";
        let patternArray = [];
        let y = 0;
        let pay_id = 0;
        let leave_type_code = "";
        let leave_type_description = "";
        let booking_type = "";
        let comments = "";
        let created_by = 0;
        let created_date = "";
        let check_time_dateString = "";
        let bookingsArray = [];
        import_start_date = moment(importStartDate).format("YYYY-MM-DD HH:mm");
        check_time_dateString = moment(
          import_start_date,
          "YYYY-MM-DD HH:mm",
        ).format("MM-DD-YYYY");
        count = 0;
        sheet.eachRow(function (row) {
          record = sheet.getItem(row);
          if (record.data9 != "SB" && record.data9 != "RRL") {
            let errorEntry = {};
            pay_id = record.data0;
            rank = record.data2;
            roster = record.data3;
            shift = record.data4;
            location = record.data5;
            leave_type_code = record.data9;
            comments = record.data10;
            created_by = record.data11;
            created_date = moment(record.data12, "DD/MM/YY HH:mm").format(
              "YYYY-MM-DD HH:mm",
            );
            bk_start_date = moment(record.data6, "DD/MM/YYYY HH:mm").format(
              "YYYY-MM-DD HH:mm",
            );
            booking_last_date = moment(record.data7, "DD/MM/YYYY HH:mm").format(
              "YYYY-MM-DD HH:mm",
            );
            if (rank == "CMD") {
              if (roster == "Metro") {
                if (location == "Adelaide") {
                  roster = "Metro";
                  location = "MCO Central";
                } else if (location == "Seaford") {
                  roster = "Metro";
                  location = "MCO South";
                } else if (location == "Salisbury") {
                  roster = "Metro";
                  location = "MCO North";
                }
              }
            }
            if (moment(bk_start_date).isBefore(moment(import_start_date))) {
              getTimesForShift(
                check_time_dateString,
                roster,
                shift,
                function (times) {
                  let shiftInfo = JSON.parse(times);
                  booking_first_date =
                    import_start_date.slice(0, 10) +
                    " " +
                    shiftInfo[0].start_time;
                },
              );
            } else {
              booking_first_date = bk_start_date;
            }
            if (bk_type == "RRL") {
              leave_type_description = "Rostered Recreation Leave";
              booking_type = "leave_request";
            } else {
              if (leave_type_code == "ARL") {
                leave_type_description = "Accrued Recreation Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "LSL") {
                leave_type_description = "Long Service Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "TOIL") {
                leave_type_description = "Time Off In Lieu";
                booking_type = "leave_request";
              } else if (leave_type_code == "PHOL") {
                leave_type_description = "Public Holiday Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "SB") {
                leave_type_description = "Relief Stand By - Covered on standby";
                booking_type = "leave_request";
              } else if (leave_type_code == "VSBT") {
                leave_type_description =
                  "Relief Stand By - Virtual standby taken";
                booking_type = "leave_request";
              } else if (leave_type_code == "RET") {
                leave_type_description = "Retention Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "OL") {
                leave_type_description = "Owed Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "J") {
                leave_type_description = "Jury Duty Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "E") {
                leave_type_description = "Event Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "LD") {
                leave_type_description = "Light Duties";
                booking_type = "leave_request";
              } else if (leave_type_code == "XLSS") {
                leave_type_description =
                  "Extra Leave - Long service single days extra to numbers";
                booking_type = "leave_request";
              } else if (leave_type_code == "ULSS") {
                leave_type_description =
                  "Unprocessed long service leave single";
                booking_type = "leave_request";
              } else if (leave_type_code == "XARL") {
                leave_type_description =
                  "Extra Leave - Accrued leave extra to numbers";
                booking_type = "leave_request";
              } else if (leave_type_code == "XLSL") {
                leave_type_description =
                  "Extra Leave - Long service leave greater than 4 days extra to numbers";
                booking_type = "leave_request";
              } else if (leave_type_code == "XPHL") {
                leave_type_description =
                  "Extra Leave - Public holiday leave extra to numbers";
                booking_type = "leave_request";
              } else if (leave_type_code == "XRET") {
                leave_type_description =
                  "Extra Leave - Retention leave extra to numbers";
                booking_type = "leave_request";
              } else if (leave_type_code == "XTOI") {
                leave_type_description =
                  "Extra Leave - Time off in lieu extra to numbers";
                booking_type = "leave_request";
              } else if (leave_type_code == "ULSL") {
                leave_type_description =
                  "Unprocessed Leave - Unprocessed long service leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "UPHL") {
                leave_type_description =
                  "Unprocessed Leave - Unprocessed public holiday leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "URET") {
                leave_type_description =
                  "Unprocessed Leave - Unprocessed retention leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "AWOL") {
                leave_type_description = "Absent Without Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "ADOP") {
                leave_type_description = "Adoption Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "LWOP") {
                leave_type_description = "Leave Without Pay";
                booking_type = "leave_request";
              } else if (leave_type_code == "MATH") {
                leave_type_description = "Maternity Leave Half Pay";
                booking_type = "leave_request";
              } else if (leave_type_code == "MATP") {
                leave_type_description = "Maternity Leave Paid";
                booking_type = "leave_request";
              } else if (leave_type_code == "SPEC") {
                leave_type_description = "Military Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "NWD") {
                leave_type_description = "Non Work Day";
                booking_type = "leave_request";
              } else if (leave_type_code == "OTHE") {
                leave_type_description = "Other Reasons Approved";
                booking_type = "leave_request";
              } else if (leave_type_code == "PPLS") {
                leave_type_description = "Paid Partner Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "PUR4") {
                leave_type_description = "Purchased Leave 4/5";
                booking_type = "leave_request";
              } else if (leave_type_code == "PURA") {
                leave_type_description = "Purchased Leave 48/52";
                booking_type = "leave_request";
              } else if (leave_type_code == "GAD") {
                leave_type_description = "UGAD";
                booking_type = "leave_request";
              } else if (leave_type_code == "SPEC") {
                leave_type_description = "Union Leave";
                booking_type = "leave_request";
              } else if (leave_type_code == "WC") {
                leave_type_description = "Work Cover";
                booking_type = "leave_request";
              } else if (leave_type_code == "BERE") {
                leave_type_description = "Bereavement Leave";
                booking_type = "sick_leave";
              } else if (leave_type_code == "FAML") {
                leave_type_description = "Family Leave";
                booking_type = "sick_leave";
              } else if (leave_type_code == "SIC") {
                leave_type_description = "Sick Leave No Medical Certificate";
                booking_type = "sick_leave";
              } else if (leave_type_code == "SICM") {
                leave_type_description = "Sick Leave With Medical Certificate";
                booking_type = "sick_leave";
              } else if (leave_type_code == "SLUP") {
                leave_type_description =
                  "Unpaid Sick Leave No Medical Certificate";
                booking_type = "sick_leave";
              } else if (leave_type_code == "SLUW") {
                leave_type_description =
                  "Unpaid Sick Leave With Medical Certificate";
                booking_type = "sick_leave";
              } else if (leave_type_code == "LSLS") {
                leave_type_description = "Long Service Leave Single";
                booking_type = "leave_request";
              } else if (leave_type_code == "SOIL") {
                leave_type_description = "Shift Off In Lieu";
                booking_type = "leave_request";
              } else if (leave_type_code == "RRL") {
                leave_type_description = "Rostered Recreation Leave";
                booking_type = "leave_request";
              }
            }
            noOfDays = moment(booking_last_date, "YYYY-MM-DD HH:mm").diff(
              moment(booking_first_date, "YYYY-MM-DD HH:mm"),
              "d",
              true,
            );
            if (noOfDays > 0) {
              booking_id = formatUuid(getRandomValuesFunc());
              getShiftDetails(
                roster,
                shift,
                booking_first_date,
                function (result) {
                  if (result == "no shift") {
                    errorEntry.pay_id = pay_id;
                    errorEntry.employee = record.data1;
                    errorEntry.roster = roster;
                    errorEntry.shift = shift;
                    errorEntry.location = location;
                    errorEntry.start_date = bk_start_date;
                    errorEntry.type = leave_type_code;
                    errorEntry.error =
                      "No Shift exists that matches this booking entry";
                    errorReport.push(errorEntry);
                  } else if (result == "date error") {
                    errorEntry.pay_id = pay_id;
                    errorEntry.employee = record.data1;
                    errorEntry.roster = roster;
                    errorEntry.shift = shift;
                    errorEntry.location = location;
                    errorEntry.start_date = bk_start_date;
                    errorEntry.type = leave_type_code;
                    errorEntry.error =
                      "This booking's Start Date is before the existing Shift's Start Date";
                    errorReport.push(errorEntry);
                  } else {
                    y = result.dayNo;
                    patternArray = result.array.data;
                    count += 1;
                    let bookingObject = {};
                    bookingObject.booking_id = booking_id;
                    bookingObject.pay_id = pay_id;
                    bookingObject.leave_type_code = leave_type_code;
                    bookingObject.leave_type_description =
                      leave_type_description;
                    bookingObject.booking_type = booking_type;
                    bookingObject.roster = roster;
                    bookingObject.shift = shift;
                    bookingObject.location = location;
                    bookingObject.comments = comments;
                    bookingObject.booking_first_date = booking_first_date;
                    bookingObject.booking_last_date = booking_last_date;
                    bookingObject.created_by = created_by;
                    bookingObject.created_date = created_date;
                    bookingObject.noOfDays = noOfDays;
                    bookingObject.patternArray = patternArray;
                    bookingObject.arraySize = result.array.data.length;
                    bookingObject.y = y;
                    bookingsArray.push(bookingObject);
                  }
                },
              );
            }
          }
        });
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/bookings/import_bookings",
            { bookingsArray: bookingsArray },
            {
              error: function (err) {
                $$("loader-window").hide();
              },
              success: function (result) {
                $$("loader-window").hide();
                webix.alert({
                  text:
                    count +
                    " bookings were successfully imported!</br></br>Note: it can take several minutes for the server to process all the entries so some</br> of the imported bookings may not appear in the system immediately.",
                  width: 500,
                });
              },
            },
          );
      }, 250);
    } else {
      webix.alert("No data was found in the imported file!");
    }
  }
  function getShiftDetails(roster, shift, startDate, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/shifts/info",
        { roster: roster, shift: shift },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            if (results) {
              let values = JSON.parse(results);
              if (values.data.length > 0) {
                if (
                  moment(startDate).isSameOrAfter(
                    values.data[0].seq_start_date,
                  ) === true
                ) {
                  getDaySeqNo(
                    values.data[0].shift_id,
                    startDate,
                    function (data) {
                      let result = JSON.parse(data);
                      callback({
                        array: values,
                        dayNo: result.data[0].seq_number,
                      });
                    },
                  );
                } else {
                  callback("date error");
                }
              } else {
                callback("no shift");
              }
            }
          },
        },
      );
  }
  function saveBookings(employeeList, status) {
    if (
      $$("bookings-page").$$("booking_create").$$("create_booking").validate()
    ) {
      if (employeeList.length > 0) {
        $$("loader-window").show();
        let bookingId = "";
        let allSaved = false;
        let bookingForm = $$("bookings-page")
          .$$("booking_create")
          .$$("create_booking")
          .getValues();
        let noOfDays = 0;
        let noOfHours = 0;
        let startDate = "";
        let endDate = "";
        let dateString = "";
        let beginDateTime =
          moment(bookingForm.start_date.slice(0, 10)).format("YYYY-MM-DD") +
          " " +
          bookingForm.start_time.slice(0, 5);
        let endDateTime =
          moment(bookingForm.end_date.slice(0, 10)).format("YYYY-MM-DD") +
          " " +
          bookingForm.end_time.slice(0, 5);
        let leave_description = $$("bookings-page")
          .$$("booking_create")
          .$$("leave_type")
          .getText();
        let leave_type_code = bookingForm.leave_type;
        let codeLength = leave_type_code.length;
        let leave_type_desc = leave_description.substr(codeLength + 3);
        let bookingType = "";
        let list_index = 1;
        let one_hour = 36e5;
        let date1_ms = moment.utc(beginDateTime, "YYYY-MM-DD HH:mm").valueOf();
        let date2_ms = moment.utc(endDateTime, "YYYY-MM-DD HH:mm").valueOf();
        let difference_ms = date2_ms - date1_ms;
        let total_days = moment(date2_ms).diff(date1_ms, "days", true);
        noOfHours = Math.round(difference_ms / one_hour);
        noOfDays = Math.ceil(total_days);
        employeeList.forEach(function (element) {
          bookingId = formatUuid(getRandomValuesFunc());
          element.booking_id = bookingId;
        });
        if (
          moment(beginDateTime, "YYYY-MM-DD HH:mm", true).isValid() === false
        ) {
          $$("loader-window").hide();
          webix.alert(
            "Start Date Error!</br>Please check the Start date & time then try again.",
          );
          throw new Error("Start Date Error!");
        }
        if (moment(endDateTime, "YYYY-MM-DD HH:mm", true).isValid() === false) {
          $$("loader-window").hide();
          webix.alert(
            "End Date Error!</br>Please check the End date & time then try again.",
          );
          throw new Error("End Date Error!");
        }
        if (moment(endDateTime).isAfter(beginDateTime)) {
          for (let x = 0; x < noOfDays; x++) {
            startDate = moment(bookingForm.start_date.slice(0, 10)).add(x, "d");
            endDate = moment(bookingForm.end_date.slice(0, 10)).add(x, "d");
            dateString = moment(startDate).format("YYYYMMDD");
            let shift_hours = 0;
            let startTime = bookingForm.start_time.slice(0, 5);
            let endTime = bookingForm.end_time.slice(0, 5);
            let bk_period = "";
            let shiftTime = startTime;
            if (bookingForm.day_hours == 1) {
              bk_period = "day";
            } else if (bookingForm.night_hours == 1) {
              bk_period = "night";
            }
            if (bk_period == "") {
              if (shiftTime._i >= "06:00" && shiftTime._i < "11:59") {
                bk_period = "day";
              } else if (shiftTime._i >= "17:30" && shiftTime._i < "23:59") {
                bk_period = "night";
              }
            }
            let dailyStartDate =
              moment(startDate).format("YYYY-MM-DD") + " " + startTime;
            let dailyEndDate =
              moment(endDate).format("YYYY-MM-DD") + " " + endTime;
            endDate = moment(endDate).format("YYYY-MM-DD") + " " + endTime;
            shift_hours = moment(dailyEndDate).diff(
              dailyStartDate,
              "hours",
              true,
            );
            if (shift_hours == 0) {
              shift_hours = noOfHours;
            }
            for (let x = 0; x < employeeList.length; x++) {
              webix
                .ajax()
                .headers({ Authorization: "Bearer " + api_key })
                .sync()
                .post(
                  server_url + "/bookings/create",
                  {
                    booking_id: employeeList[x].booking_id,
                    pay_id: employeeList[x].id,
                    leave_type_code: bookingForm.leave_type,
                    leave_type_description: leave_type_desc,
                    roster: bookingForm.roster_name,
                    shift: bookingForm.shift_type,
                    location: bookingForm.locations,
                    start_date:
                      moment(startDate).format("YYYY-MM-DD") + " " + startTime,
                    end_date: endDate,
                    diff_hours: shift_hours,
                    date_string: dateString,
                    booking_first_date: beginDateTime,
                    booking_last_date: endDateTime,
                    bk_period: bk_period,
                    comments: bookingForm.comments,
                    created_by: user_logged_in,
                    booking_type: bookingType,
                    moved_roster: "",
                    moved_shift: "",
                    moved_location: "",
                    status: status,
                    list_index: list_index,
                    sm_travel_ppe: 0,
                    sm_travel_notice: 0,
                    deleted: false,
                    with_standby: 0,
                  },
                  {
                    error: function (err) {
                      $$("loader-window").hide();
                    },
                    success: function (result) {
                      allSaved = true;
                      if (live_site === true) {
                        if (status == "Approved") {
                          getEmployeeData(
                            employeeList[x].id,
                            function (values) {
                              let properSurname = toProperCase(
                                values[0].surname,
                              );
                              sendEmail(
                                "SAPPHIRE<<EMAIL>>",
                                values[0].notifications_email,
                                "RE: Booking (Approved)",
                                "Booking " +
                                  bookingForm.leave_type +
                                  " was <b>Approved</b> for " +
                                  values[0].first_name +
                                  " " +
                                  properSurname +
                                  " from " +
                                  moment(beginDateTime).format(
                                    "DD/MM/YYYY H:mm",
                                  ) +
                                  " to " +
                                  moment(endDateTime).format(
                                    "DD/MM/YYYY H:mm",
                                  ) +
                                  " by " +
                                  user_logged_in +
                                  " on " +
                                  moment().format("DD/MM/YYYY H:mm") +
                                  "",
                                "",
                                values[0].first_name + " " + properSurname,
                                "Please contact Workforce <NAME_EMAIL> for any queries regarding this booking.",
                                "Regards, The SAPPHIRE Team",
                              );
                            },
                          );
                        }
                      }
                    },
                  },
                );
            }
          }
        } else {
          $$("loader-window").hide();
          webix.alert(
            "The 'End Date & Time' cannot be before the 'Start Date & Time'",
          );
        }
        if (allSaved === true) {
          webix.alert("Booking(s) Saved!");
          $$("loader-window").hide();
          if (ro_view_showing == true) {
            availabilityReport.loadReport(
              $$("schedule-page").$$("grid_ro_view_report"),
              function (callback) {
                if (callback == "ok") {
                  functionsPopup.closePopup();
                  schedule.reload_roster();
                }
              },
            );
          } else {
            functionsPopup.closePopup();
            schedule.reload_roster();
          }
          $$("bookings-page").$$("booking_create").$$("create_booking").clear();
          $$("bookings-page")
            .$$("booking_create")
            .$$("start_date")
            .setValue(new Date());
          $$("bookings-page")
            .$$("booking_create")
            .$$("start_time")
            .setValue("08:00");
          $$("bookings-page")
            .$$("booking_create")
            .$$("end_date")
            .setValue(new Date());
          $$("bookings-page")
            .$$("booking_create")
            .$$("end_time")
            .setValue("18:00");
          $$("bookings-page")
            .$$("booking_create")
            .$$("search_text")
            .setValue("");
          $$("bookings-page").$$("booking_create").$$("day_hours").setValue(1);
          $$("bookings-page")
            .$$("booking_create")
            .$$("search_results")
            .$$("left")
            .data.clearAll();
          $$("bookings-page")
            .$$("booking_create")
            .$$("search_results")
            .$$("right")
            .data.clearAll();
          $$("bookings-page")
            .$$("booking_create")
            .$$("search_count")
            .define("template", "0 records found");
          $$("bookings-page").$$("booking_create").$$("search_count").refresh();
          $$("bookings-page")
            .$$("booking_create")
            .$$("selected_count")
            .define("template", "0 records selected");
          $$("bookings-page")
            .$$("booking_create")
            .$$("selected_count")
            .refresh();
        }
      } else {
        webix.alert("You must select at least one employee!");
      }
    }
  }
  function getEmployees() {
    let roster = $$("bookings-page")
      .$$("booking_create")
      .$$("roster_name")
      .getText();
    let shift = $$("bookings-page")
      .$$("booking_create")
      .$$("shift_type")
      .getText();
    let location = $$("bookings-page")
      .$$("booking_create")
      .$$("locations")
      .getText();
    if (roster == "" || shift == "" || location == "") {
      webix.alert(
        "You must specify a Roster, Shift & Location before you can search for Employees!",
      );
    } else {
      $$("loader-window").show();
      let list = $$("bookings-page").$$("booking_create").$$("search_results");
      let searchText = $$("bookings-page")
        .$$("booking_create")
        .$$("search_text")
        .getValue();
      let searchType = "";
      if (isNaN(searchText)) {
        searchType = "surname";
      } else {
        searchType = "pay_id";
      }
      list.$$("left").clearAll();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/bookings/search",
          {
            search_type: searchType,
            search_value: searchText,
            roster: roster,
            shift: shift,
            location: location,
          },
          {
            error: function (err) {
              $$("loader-window").hide();
            },
            success: function (results) {
              let result = JSON.parse(results);
              let search_results = [];
              let middle_name = "";
              if (result.length > 0) {
                for (let x = 0; x < result.length; x++) {
                  if (result[x].middle_name === null) {
                    middle_name = "";
                  } else {
                    middle_name = result[x].middle_name;
                  }
                  search_results.push({
                    id: result[x].pay_id,
                    value:
                      result[x].surname +
                      ", " +
                      result[x].first_name +
                      " " +
                      middle_name,
                    rank: result[x].rank,
                  });
                }
              } else {
                webix.alert({
                  text:
                    "No employee(s) were found matching '" +
                    searchText +
                    "' at the selected Roster / Shift / Location",
                  width: 500,
                });
              }
              list.parse(search_results);
              $$("bookings-page")
                .$$("booking_create")
                .$$("search_count")
                .define("template", result.length + " records found");
              $$("bookings-page")
                .$$("booking_create")
                .$$("search_count")
                .refresh();
              $$("loader-window").hide();
            },
          },
        );
    }
  }
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                $$("bookings-page")
                  .$$("booking_create")
                  .$$("shift_type")
                  .define("options", shiftOptions);
                $$("bookings-page")
                  .$$("booking_create")
                  .$$("shift_type")
                  .refresh();
                $$("bookings-page")
                  .$$("booking_create")
                  .$$("locations")
                  .define("options", locationOptions);
                $$("bookings-page")
                  .$$("booking_create")
                  .$$("locations")
                  .refresh();
              }
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
