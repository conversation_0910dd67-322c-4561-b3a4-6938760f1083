let leaveRequests = (function () {
  let pageNo = 0;
  let noOfPages = 1;
  let totalRecords = 0;
  function initApplication() {
    eventHandlers();
    $$("bookings-page")
      .$$("booking_requests")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page").$$("booking_requests").$$("status_filter").setValue(2);
    $$("bookings-page").$$("booking_requests").$$("type_filter").setValue(1);
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("booking_requests")
      .$$("btn_prev_page")
      .attachEvent("onItemClick", function (id, e) {
        if (pageNo > 0) {
          let roster = $$("bookings-page")
            .$$("booking_requests")
            .$$("roster_filter")
            .getText();
          let status = $$("bookings-page")
            .$$("booking_requests")
            .$$("status_filter")
            .getText();
          let type = $$("bookings-page")
            .$$("booking_requests")
            .$$("type_filter")
            .getText();
          $$("bookings-page")
            .$$("booking_requests")
            .$$("selected_request")
            .define("template", "Selected Request: None");
          $$("bookings-page")
            .$$("booking_requests")
            .$$("selected_request")
            .refresh();
          $$("bookings-page")
            .$$("booking_requests")
            .$$("request_id")
            .setValue("");
          pageNo = pageNo - 1;
          loadBookingRequests(roster, status, type);
        }
      });
    $$("bookings-page")
      .$$("booking_requests")
      .$$("btn_next_page")
      .attachEvent("onItemClick", function (id, e) {
        if (totalRecords > 1e3 && pageNo + 1 < noOfPages) {
          let roster = $$("bookings-page")
            .$$("booking_requests")
            .$$("roster_filter")
            .getText();
          let status = $$("bookings-page")
            .$$("booking_requests")
            .$$("status_filter")
            .getText();
          let type = $$("bookings-page")
            .$$("booking_requests")
            .$$("type_filter")
            .getText();
          $$("bookings-page")
            .$$("booking_requests")
            .$$("selected_request")
            .define("template", "Selected Request: None");
          $$("bookings-page")
            .$$("booking_requests")
            .$$("selected_request")
            .refresh();
          $$("bookings-page")
            .$$("booking_requests")
            .$$("request_id")
            .setValue("");
          pageNo = pageNo + 1;
          loadBookingRequests(roster, status, type);
        }
      });
    $$("bookings-page")
      .$$("booking_requests")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("bookings-page")
          .$$("booking_requests")
          .$$("roster_filter")
          .getText();
        let status = $$("bookings-page")
          .$$("booking_requests")
          .$$("status_filter")
          .getText();
        let type = $$("bookings-page")
          .$$("booking_requests")
          .$$("type_filter")
          .getText();
        $$("bookings-page")
          .$$("booking_requests")
          .$$("selected_request")
          .define("template", "Selected Request: None");
        $$("bookings-page")
          .$$("booking_requests")
          .$$("selected_request")
          .refresh();
        $$("bookings-page")
          .$$("booking_requests")
          .$$("request_id")
          .setValue("");
        pageNo = 0;
        loadBookingRequests(roster, status, type);
      });
    $$("bookings-page")
      .$$("booking_requests")
      .$$("booking-requests_grid")
      .attachEvent("onItemClick", function (id, e, node) {
        let selectedRow = this.getItem(id);
        $$("bookings-page")
          .$$("booking_requests")
          .$$("selected_request")
          .define(
            "template",
            "Selected Request: <strong>" +
              selectedRow.type +
              "</strong> for " +
              selectedRow.employee,
          );
        $$("bookings-page")
          .$$("booking_requests")
          .$$("selected_request")
          .refresh();
        $$("bookings-page")
          .$$("booking_requests")
          .$$("request_id")
          .setValue(selectedRow.booking_id);
        $$("bookings-page").$$("booking_requests").$$("btn_approve").enable();
        $$("bookings-page").$$("booking_requests").$$("btn_deny").enable();
      });
    $$("bookings-page")
      .$$("booking_requests")
      .$$("btn_approve")
      .attachEvent("onItemClick", function (id, e) {
        let requestBookingId = $$("bookings-page")
          .$$("booking_requests")
          .$$("request_id")
          .getValue();
        let newStatus = "Approved";
        let comments = $$("bookings-page")
          .$$("booking_requests")
          .$$("request_comments")
          .getValue();
        let rowId = $$("bookings-page")
          .$$("booking_requests")
          .$$("booking-requests_grid")
          .getSelectedId();
        let selectedRow = $$("bookings-page")
          .$$("booking_requests")
          .$$("booking-requests_grid")
          .getItem(rowId);
        let row_pay_id = selectedRow.service_no;
        let row_type = selectedRow.type;
        let row_start_date = moment(selectedRow.start_date).format(
          "DD/MM/YYYY H:mm",
        );
        let row_end_date = moment(selectedRow.end_date).format(
          "DD/MM/YYYY H:mm",
        );
        let noOfDays = 0;
        let checkDate = "";
        let sm_booking_found = false;
        noOfDays = moment(selectedRow.end_date).diff(
          selectedRow.start_date,
          "days",
          true,
        );
        for (let x = 0; x < noOfDays; x++) {
          checkDate = moment(selectedRow.start_date).add(x, "days");
          checkDate = moment(checkDate).format("YYYYMMDD");
          getUserBookings(row_pay_id, checkDate, function (results) {
            if (results.length > 0) {
              for (let x = 0; x < results.length; x++) {
                if (results[x].booking_type == "staff_movement") {
                  if (results[x].date_string == checkDate) {
                    sm_booking_found = true;
                    webix
                      .ajax()
                      .headers({ Authorization: "Bearer " + api_key })
                      .sync()
                      .del(
                        server_url + "/bookings/booking_day",
                        {
                          booking_id: results[x].booking_id,
                          date_string: results[x].date_string,
                          status: "Approved",
                          deleted_by: user_logged_in,
                        },
                        {
                          error: function (err) {},
                          success: function () {
                            getRADetails(
                              selectedRow.service_no,
                              checkDate,
                              false,
                              function (result) {
                                if (result.length > 0) {
                                  let roster = result[0].roster;
                                  let shift = result[0].shift;
                                  let location = result[0].location;
                                  updateBookingLocation(
                                    selectedRow.booking_id,
                                    roster,
                                    shift,
                                    location,
                                    checkDate,
                                    function (response) {
                                      if (response == "ok") {
                                        webix.message({
                                          text: "Booking day was moved successfully!",
                                          type: "success",
                                          expire: 2e3,
                                        });
                                      } else {
                                        webix.message({
                                          text: "Booking day was not able to be moved!",
                                          type: "error",
                                          expire: 2e3,
                                        });
                                      }
                                    },
                                  );
                                }
                              },
                            );
                          },
                        },
                      );
                  }
                } else if (results[x].leave_type_code == "SB") {
                  comments = comments + " (with standby)";
                }
              }
            }
          });
        }
        if (sm_booking_found == true) {
          webix.message({
            text: "Staff Movement booking was found & deleted!",
            type: "success",
            expire: 2e3,
          });
        }
        updateRequestStatus(requestBookingId, newStatus, comments);
        if (live_site === true) {
          getEmployeeData(row_pay_id, function (values) {
            let properSurname = toProperCase(values[0].surname);
            sendEmail(
              "SAPPHIRE<<EMAIL>>",
              values[0].notifications_email,
              "RE: Booking (Approved)",
              "Booking " +
                row_type +
                " was <b>Approved</b> for " +
                values[0].first_name +
                " " +
                properSurname +
                " from " +
                row_start_date +
                " to " +
                row_end_date +
                " by " +
                user_logged_in +
                " on " +
                moment().format("DD/MM/YYYY H:mm") +
                "",
              "Comment: " + comments,
              values[0].first_name + " " + properSurname,
              "Please contact Workforce <NAME_EMAIL> for any queries regarding this booking.",
              "Regards, The SAPPHIRE Team",
            );
          });
        }
      });
    $$("bookings-page")
      .$$("booking_requests")
      .$$("btn_deny")
      .attachEvent("onItemClick", function (id, e) {
        let requestBookingId = $$("bookings-page")
          .$$("booking_requests")
          .$$("request_id")
          .getValue();
        let newStatus = "Denied";
        let comments = $$("bookings-page")
          .$$("booking_requests")
          .$$("request_comments")
          .getValue();
        let rowId = $$("bookings-page")
          .$$("booking_requests")
          .$$("booking-requests_grid")
          .getSelectedId();
        let selectedRow = $$("bookings-page")
          .$$("booking_requests")
          .$$("booking-requests_grid")
          .getItem(rowId);
        let row_pay_id = selectedRow.service_no;
        let row_type = selectedRow.type;
        let row_start_date = moment(selectedRow.start_date).format(
          "DD/MM/YYYY H:mm",
        );
        let row_end_date = moment(selectedRow.end_date).format(
          "DD/MM/YYYY H:mm",
        );
        let ext_message = "";
        updateRequestStatus(requestBookingId, newStatus, comments);
        if (live_site === true) {
          if (comments != "") {
            ext_message = "Comment: " + comments;
          } else {
            ext_message =
              "Note: Leave was denied due to insufficient leave balance!";
          }
          getEmployeeData(row_pay_id, function (values) {
            let properSurname = toProperCase(values[0].surname);
            sendEmail(
              "SAPPHIRE<<EMAIL>>",
              values[0].notifications_email,
              "RE: Booking (Denied)",
              "Booking " +
                row_type +
                " was <b>Denied</b> for " +
                values[0].first_name +
                " " +
                properSurname +
                " from " +
                row_start_date +
                " to " +
                row_end_date +
                " by " +
                user_logged_in +
                " on " +
                moment().format("DD/MM/YYYY H:mm") +
                "",
              ext_message,
              values[0].first_name + " " + properSurname,
              "Please contact Workforce <NAME_EMAIL> for any queries regarding this booking.",
              "Regards, The SAPPHIRE Team",
            );
          });
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("booking_requests").$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function updateBookingLocation(
    bookingId,
    newRoster,
    newShift,
    newLocation,
    dateString,
    callback,
  ) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .put(
        server_url + "/bookings/move_booking",
        {
          booking_id: bookingId,
          roster: newRoster,
          shift: newShift,
          location: newLocation,
          date_string: dateString,
        },
        {
          error: function (err) {
            callback("error");
          },
          success: function (result) {
            callback("ok");
          },
        },
      );
  }
  function updateRequestStatus(bookingId, newStatus, comments) {
    let roster = $$("bookings-page")
      .$$("booking_requests")
      .$$("roster_filter")
      .getText();
    let status = $$("bookings-page")
      .$$("booking_requests")
      .$$("status_filter")
      .getText();
    let type = $$("bookings-page")
      .$$("booking_requests")
      .$$("type_filter")
      .getText();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/bookings/booking_requests",
        {
          booking_id: bookingId,
          status: newStatus,
          approved_denied_by: user_logged_in,
          request_comments: comments,
        },
        {
          error: function (err) {},
          success: function (results) {
            webix.alert("Booking request status changed successfully!");
            $$("bookings-page")
              .$$("booking_requests")
              .$$("selected_request")
              .define("template", "Selected Request: None");
            $$("bookings-page")
              .$$("booking_requests")
              .$$("selected_request")
              .refresh();
            $$("bookings-page")
              .$$("booking_requests")
              .$$("request_id")
              .setValue("");
            $$("bookings-page")
              .$$("booking_requests")
              .$$("request_comments")
              .setValue("");
            loadBookingRequests(roster, status, type);
            if (ro_view_showing == true) {
              availabilityReport.loadReport(
                $$("schedule-page").$$("grid_ro_view_report"),
                function (callback) {
                  if (callback == "ok") {
                    functionsPopup.closePopup();
                    schedule.reload_roster();
                  }
                },
              );
            } else {
              functionsPopup.closePopup();
              schedule.reload_roster();
            }
          },
        },
      );
  }
  function loadBookingRequests(roster, status, type) {
    let grid = $$("bookings-page")
      .$$("booking_requests")
      .$$("booking-requests_grid");
    let start_date_format = "";
    let end_date_format = "";
    let created_date_format = "";
    let approved_date_format = "";
    let grid_info = grid.getState();
    let sort_order = grid_info.sort;
    let perPage = 1e3;
    let pageLabel = "";
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/booking_requests",
        {
          roster: roster,
          status: status,
          type: type,
          pageNo: pageNo,
          perPage: perPage,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            if (results != "[]") {
              let values = JSON.parse(results);
              let status_icon = "";
              let delete_icon = "";
              let middleName = "";
              totalRecords = values[0].TotalRecords;
              noOfPages = Math.ceil(totalRecords / perPage);
              if (totalRecords < perPage) {
                pageLabel = "Page 1/1";
              } else {
                pageLabel = "Page " + Math.ceil(pageNo + 1) + "/" + noOfPages;
              }
              if (totalRecords > perPage) {
                $$("bookings-page")
                  .$$("booking_requests")
                  .$$("search_count")
                  .define(
                    "template",
                    "Showing " +
                      perPage +
                      " records from " +
                      totalRecords +
                      " total requests",
                  );
              } else {
                $$("bookings-page")
                  .$$("booking_requests")
                  .$$("search_count")
                  .define(
                    "template",
                    "Showing " +
                      totalRecords +
                      " records from " +
                      totalRecords +
                      " total requests",
                  );
              }
              $$("bookings-page")
                .$$("booking_requests")
                .$$("search_count")
                .refresh();
              $$("bookings-page")
                .$$("booking_requests")
                .$$("page_no")
                .setValue(pageLabel);
              values.forEach(function (result) {
                if (result.status == "Pending") {
                  status_icon =
                    "<span class = 'approved_pending webix_icon fas fa-clock'></span>";
                } else if (result.status == "Approved") {
                  status_icon =
                    "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>";
                } else if (result.status == "Denied") {
                  status_icon =
                    "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>";
                }
                if (result.deleted == true) {
                  delete_icon =
                    "<span class = 'delete_request_true webix_icon fas fa-minus-circle'></span>";
                } else {
                  delete_icon =
                    "<span class = 'delete_request_false webix_icon fas fa-minus-circle'></span>";
                }
                if (result.middle_name === null) {
                  middleName = "";
                } else {
                  middleName = result.middle_name;
                }
                start_date_format = moment(
                  result.booking_first_date,
                  "DD/MM/YYYY HH:mm",
                ).toDate();
                end_date_format = moment(
                  result.booking_last_date,
                  "DD/MM/YYYY HH:mm",
                ).toDate();
                created_date_format = moment(
                  result.requested_date,
                  "DD/MM/YYYY HH:mm",
                ).toDate();
                if (result.approved_date == "Invalid date") {
                  approved_date_format = "";
                } else {
                  approved_date_format = moment(
                    result.approved_date,
                    "DD/MM/YYYY HH:mm",
                  ).toDate();
                }
                grid.add({
                  booking_id: result.booking_id,
                  service_no: result.pay_id,
                  employee:
                    result.surname +
                    ", " +
                    result.first_name +
                    " " +
                    middleName,
                  rank: result.rank,
                  roster: result.roster,
                  shift: result.shift,
                  location: result.location,
                  start_date: start_date_format,
                  end_date: end_date_format,
                  hours: result.total_hours,
                  type: result.leave_type_code,
                  status: status_icon,
                  deleted: delete_icon,
                  requested_date: created_date_format,
                  approved_date: approved_date_format,
                  approved_by: result.approved_denied_by,
                });
              });
              if (sort_order != undefined) {
                if (
                  sort_order.id == "start_date" ||
                  sort_order.id == "end_date" ||
                  sort_order.id == "requested_date" ||
                  sort_order.id == "approved_date"
                ) {
                  grid.sort(sort_order.id, sort_order.dir, "date");
                } else {
                  grid.sort(sort_order.id, sort_order.dir);
                }
              }
            } else {
              $$("bookings-page")
                .$$("booking_requests")
                .$$("search_count")
                .define("template", "0 requests found");
              $$("bookings-page")
                .$$("booking_requests")
                .$$("search_count")
                .refresh();
            }
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
