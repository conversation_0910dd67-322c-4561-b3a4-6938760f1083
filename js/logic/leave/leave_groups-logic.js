let leaveGroups = (function () {
  let loadIndex = 1;
  let newSearch = true;
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  function initApplication() {
    eventHandlers();
    loadLeaveGroups();
    $$("bookings-page")
      .$$("leave_groups")
      .$$("to_date")
      .setValue(webix.Date.add(new Date(), 5, "year"));
    $$("bookings-page")
      .$$("leave_groups")
      .$$("RRL_end_date")
      .setValue(webix.Date.add(new Date(), 1, "day"));
    $$("bookings-page")
      .$$("leave_groups")
      .$$("group_return_date")
      .setValue(webix.Date.add(new Date(), 32, "day"));
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_delete_search")
      .attachEvent("onItemClick", function (id, e) {
        let payId = $$("bookings-page")
          .$$("leave_groups")
          .$$("delete_search_employee")
          .getValue();
        getEmployeeRRLBookings(payId);
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("bookings-page")
          .$$("leave_groups")
          .$$("leave_groups_grid");
        let groupGrid = $$("bookings-page")
          .$$("leave_groups")
          .$$("leave_dates_grid");
        let selectedRow = groupGrid.getSelectedItem();
        let fromDate = selectedRow.start_date;
        let toDate = selectedRow.return_date;
        let leaveGroup = $$("bookings-page")
          .$$("leave_groups")
          .$$("leave_group_list")
          .getText();
        let defaultHandler = grid.$exportView;
        grid.$exportView = function (options) {
          let returnValue = defaultHandler.apply(this, arguments);
          if (returnValue[0] && returnValue[0].exportData) {
            returnValue[0].exportData.unshift([""]);
            returnValue[0].styles.unshift({});
            returnValue[0].exportData.unshift([
              "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
            ]);
            returnValue[0].styles.unshift(customReportStyle);
            returnValue[0].exportData.unshift([
              "Group " +
                leaveGroup +
                " between dates " +
                fromDate +
                " - " +
                toDate,
            ]);
            returnValue[0].styles.unshift(customReportStyle);
            returnValue[0].exportData.unshift(["RRL Group Report"]);
            returnValue[0].styles.unshift(customReportStyle);
          }
          return returnValue;
        };
        if (grid.count() > 0) {
          webix.toExcel(grid, {
            filename: "WFR Rostering Report - " + leaveGroup + " RRL Group",
            styles: true,
            heights: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("import_uploader")
      .attachEvent("onBeforeFileAdd", function (upload) {
        if (user_permission_level === 1) {
          $$("loader-window").show();
          let sheet = $$("bookings-page").$$("leave_groups").$$("excel_import");
          sheet.clearAll();
          sheet.parse(upload.file, "excel");
          $$("bookings-page")
            .$$("leave_groups")
            .$$("file_name")
            .define("label", upload.name + " file loaded!");
          $$("bookings-page").$$("leave_groups").$$("file_name").refresh();
          loadIndex = 1;
          return false;
        } else {
          webix.alert({
            text: "You don't have the required permission level for this function!",
            width: 550,
          });
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("create_RRL_uploader")
      .attachEvent("onBeforeFileAdd", function (upload) {
        if (user_permission_level === 1) {
          $$("loader-window").show();
          let sheet = $$("bookings-page")
            .$$("leave_groups")
            .$$("excel_RRL_bookings_import");
          sheet.clearAll();
          sheet.parse(upload.file, "excel");
          $$("bookings-page")
            .$$("leave_groups")
            .$$("RRL_file_name")
            .define("label", upload.name + " file loaded!");
          $$("bookings-page").$$("leave_groups").$$("RRL_file_name").refresh();
          loadIndex = 1;
          return false;
        } else {
          webix.alert({
            text: "You don't have the required permission level for this function!",
            width: 550,
          });
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("excel_import")
      .attachEvent("onAfterLoad", function () {
        if (loadIndex == 1) {
          let sheet = $$("bookings-page").$$("leave_groups").$$("excel_import");
          let fileType = sheet.serialize();
          if (fileType[0].data0 == "detnumber") {
            let rowId = 0;
            sheet.eachRow(function (row) {
              rowId += 1;
              if (rowId == 1) {
                sheet.remove(row);
                sheet.remove(row + 1);
                sheet.remove(row + 2);
              }
            });
            $$("bookings-page").$$("leave_groups").$$("import_sheet").enable();
          } else {
            webix.alert(
              "The imported file does not appear to be a valid RRL group list!",
            );
            $$("bookings-page").$$("leave_groups").$$("import_sheet").disable();
          }
        }
        loadIndex += 1;
        $$("loader-window").hide();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("excel_RRL_bookings_import")
      .attachEvent("onAfterLoad", function () {
        if (loadIndex == 1) {
          let sheet = $$("bookings-page")
            .$$("leave_groups")
            .$$("excel_RRL_bookings_import");
          let fileType = sheet.serialize();
          if (fileType[0].data0 == "detnumber") {
            let rowId = 0;
            sheet.eachRow(function (row) {
              rowId += 1;
              if (rowId == 1) {
                sheet.remove(row);
                sheet.remove(row + 1);
              }
            });
            $$("bookings-page")
              .$$("leave_groups")
              .$$("import_RRL_sheet")
              .enable();
          } else {
            webix.alert(
              "The imported file does not appear to be a valid Employee RRL group list!",
            );
            $$("bookings-page")
              .$$("leave_groups")
              .$$("import_RRL_sheet")
              .disable();
          }
        }
        loadIndex += 1;
        $$("loader-window").hide();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("clear_employee_RRL_sheet")
      .attachEvent("onItemClick", function (id, e) {
        $$("bookings-page")
          .$$("leave_groups")
          .$$("excel_RRL_bookings_import")
          .clearAll();
        $$("bookings-page").$$("leave_groups").$$("import_RRL_sheet").disable();
        $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_file_name")
          .define("label", "Create RRL Bookings");
        $$("bookings-page").$$("leave_groups").$$("RRL_file_name").refresh();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("clear_sheet")
      .attachEvent("onItemClick", function (id, e) {
        $$("bookings-page").$$("leave_groups").$$("excel_import").clearAll();
        $$("bookings-page").$$("leave_groups").$$("import_sheet").disable();
        $$("bookings-page")
          .$$("leave_groups")
          .$$("file_name")
          .define("label", "Import RRL Group List");
        $$("bookings-page").$$("leave_groups").$$("file_name").refresh();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("import_sheet")
      .attachEvent("onItemClick", function (id, e) {
        importRRLs();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("import_RRL_sheet")
      .attachEvent("onItemClick", function (id, e) {
        createRRLBookingsFromImport();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("from_date")
      .attachEvent("onChange", function (newv, oldv) {
        $$("bookings-page").$$("leave_groups").$$("to_date").setValue(newv);
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let groupName = $$("bookings-page")
          .$$("leave_groups")
          .$$("leave_group_list")
          .getText();
        let startDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("from_date")
          .getValue();
        let endDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("to_date")
          .getValue();
        if (groupName !== "") {
          if (moment(startDate).isSameOrAfter(endDate)) {
            webix.alert(
              "The 'To Date' cannot be the same or before the 'From Date'!",
            );
          } else {
            getGroupDatesList(groupName, "search", startDate, endDate);
          }
        } else {
          webix.alert("You must select a 'Leave Group' before you can search!");
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_save")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_start_date")
          .getValue();
        let toDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_end_date")
          .getValue();
        let effectDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_effective_date")
          .getValue();
        let new_group = $$("bookings-page")
          .$$("leave_groups")
          .$$("select_group")
          .getText();
        let payId = $$("bookings-page")
          .$$("leave_groups")
          .$$("search_employee")
          .getValue();
        if (moment(fromDate).isSameOrAfter(toDate)) {
          webix.alert(
            "The 'End Date' cannot be the same or before the 'Official Start Date'!",
          );
        } else if (moment(effectDate).isSameOrAfter(toDate)) {
          webix.alert(
            "The 'End Date' cannot be the same or before the 'Effective Start Date'!",
          );
        } else {
          if (user_permission_level === 1 || user_permission_level === 2) {
            if (
              $$("bookings-page")
                .$$("leave_groups")
                .$$("frmLeaveGroups")
                .validate()
            ) {
              let date_diff = moment(toDate).diff(fromDate, "days");
              webix.confirm({
                title: "Add Employee to New Group!",
                ok: "Yes",
                cancel: "No",
                width: 650,
                text:
                  "You have selected to add the selected employee to the group <strong>" +
                  new_group +
                  "</strong> for a period of <strong>" +
                  date_diff +
                  "</strong> days</br>starting Officially on <strong>" +
                  moment(fromDate).format("DD/MM/YYYY") +
                  "</strong> however the RRL bookings will only be assigned between the</br>'Effective Start Date' of <strong>" +
                  moment(effectDate).format("DD/MM/YYYY") +
                  "</strong> and the 'End Date' of <strong>" +
                  moment(toDate).format("DD/MM/YYYY") +
                  "</strong></br></br>Do you want to proceed?</br>",
                callback: function (result) {
                  switch (result) {
                    case true:
                      let old_group = $$("bookings-page")
                        .$$("leave_groups")
                        .$$("current_leave_group")
                        .getValue();
                      updateExistingLeaveGroupDate(
                        payId,
                        fromDate,
                        old_group,
                        function (result) {},
                      );
                      assignLeaveGroup(
                        fromDate,
                        toDate,
                        effectDate,
                        function (result) {
                          if (result == "ok") {
                            let grid = $$("bookings-page")
                              .$$("leave_groups")
                              .$$("RRL_dates_grid");
                            getRRLGroupLogs(payId);
                            if (grid.count() > 0) {
                              $$("bookings-page")
                                .$$("leave_groups")
                                .$$("RRL_dates_sv")
                                .scrollTo(0, 0);
                              webix.confirm({
                                title: "Create RRL bookings?",
                                text: "The selected employee has now been assigned to the selected Leave Group!</br>Would you like to automatically create the RRL bookings for the date periods listed in the grid?",
                                type: "confirm-warning",
                                ok: "Yes",
                                cancel: "No",
                                callback: function (response) {
                                  if (response === true) {
                                    $$("loader-window").show();
                                    setTimeout(function () {
                                      let rrl_cycles = grid.count();
                                      if (rrl_cycles > 0) {
                                        deleteExistingRRLBookings(
                                          payId,
                                          effectDate,
                                          toDate,
                                          function (result) {
                                            if (result.response == "OK") {
                                              createRRLBooking(
                                                payId,
                                                new_group,
                                              );
                                            }
                                          },
                                        );
                                      } else {
                                        $$("loader-window").hide();
                                        webix.alert(
                                          "No RRL bookings were created!",
                                        );
                                        newSearch = false;
                                        $$("bookings-page")
                                          .$$("leave_groups")
                                          .$$("RRL_start_date")
                                          .setValue(new Date());
                                        $$("bookings-page")
                                          .$$("leave_groups")
                                          .$$("RRL_effective_date")
                                          .setValue(new Date());
                                        $$("bookings-page")
                                          .$$("leave_groups")
                                          .$$("RRL_end_date")
                                          .setValue(new Date());
                                        $$("bookings-page")
                                          .$$("leave_groups")
                                          .$$("select_group")
                                          .setValue("");
                                        $$("bookings-page")
                                          .$$("leave_groups")
                                          .$$("search_employee")
                                          .setValue("");
                                        newSearch = true;
                                      }
                                    }, 250);
                                  } else {
                                    webix.alert({
                                      text: "The selected employee has now been assigned to the selected</br>Leave Group but no RRL bookings were created!",
                                      width: 500,
                                    });
                                    newSearch = false;
                                    $$("bookings-page")
                                      .$$("leave_groups")
                                      .$$("RRL_start_date")
                                      .setValue(new Date());
                                    $$("bookings-page")
                                      .$$("leave_groups")
                                      .$$("RRL_effective_date")
                                      .setValue(new Date());
                                    $$("bookings-page")
                                      .$$("leave_groups")
                                      .$$("RRL_end_date")
                                      .setValue(new Date());
                                    $$("bookings-page")
                                      .$$("leave_groups")
                                      .$$("select_group")
                                      .setValue("");
                                    $$("bookings-page")
                                      .$$("leave_groups")
                                      .$$("search_employee")
                                      .setValue("");
                                    newSearch = true;
                                  }
                                },
                              });
                            } else {
                              webix.alert({
                                text: "The selected employee has now been assigned to the selected Leave Group.</br>No RRL bookings needed to be created for this period!",
                                width: 550,
                              });
                              newSearch = false;
                              $$("bookings-page")
                                .$$("leave_groups")
                                .$$("RRL_start_date")
                                .setValue(new Date());
                              $$("bookings-page")
                                .$$("leave_groups")
                                .$$("RRL_effective_date")
                                .setValue(new Date());
                              $$("bookings-page")
                                .$$("leave_groups")
                                .$$("RRL_end_date")
                                .setValue(new Date());
                              $$("bookings-page")
                                .$$("leave_groups")
                                .$$("select_group")
                                .setValue("");
                              $$("bookings-page")
                                .$$("leave_groups")
                                .$$("search_employee")
                                .setValue("");
                              newSearch = true;
                            }
                          }
                        },
                      );
                  }
                },
              });
            }
          } else {
            webix.alert({
              text: "You don't have the required permission level for this function!",
              width: 550,
            });
          }
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("auto_generate_group")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == 1) {
          $$("bookings-page").$$("leave_groups").$$("period_years").enable();
        } else {
          $$("bookings-page").$$("leave_groups").$$("period_years").disable();
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("group_start_date")
      .attachEvent("onChange", function (newv) {
        let dateValue = new Date(newv);
        let group_name = $$("bookings-page")
          .$$("leave_groups")
          .$$("group_name")
          .getValue();
        let returnDate = "";
        if (group_name.includes("MG")) {
          returnDate = webix.Date.add(dateValue, 24, "day");
        } else {
          returnDate = webix.Date.add(dateValue, 32, "day");
        }
        $$("bookings-page")
          .$$("leave_groups")
          .$$("group_return_date")
          .setValue(returnDate);
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_save_group")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          if (
            $$("bookings-page")
              .$$("leave_groups")
              .$$("frmCreateGroup")
              .validate()
          ) {
            saveLeaveGroup();
          }
        } else {
          webix.alert({
            text: "You don't have the required permission level for this function!",
            width: 550,
          });
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("search_employee")
      .attachEvent("onChange", function (newv) {
        if (newSearch === true) {
          let dateString = $$("bookings-page")
            .$$("leave_groups")
            .$$("RRL_start_date")
            .getValue();
          dateString = moment(dateString).format("YYYYMMDD");
          $$("bookings-page").$$("leave_groups").$$("btn_save").enable();
          getRank(newv, "assign");
          getLeaveGroup(newv, "assign");
          getRADetails(newv, dateString, false, function (RAInfo) {
            if (RAInfo.length > 0) {
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_roster")
                .setValue(RAInfo[0].roster);
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_shift")
                .setValue(RAInfo[0].shift);
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_location")
                .setValue(RAInfo[0].location);
              $$("bookings-page").$$("leave_groups").$$("btn_save").enable();
            } else {
              webix.alert({
                text: "A 'Roster Arrangement' for the selected user and date period does not exist. You must create a 'Roster Arrangement' for the date period selected or change the date period and try again!",
                width: 600,
              });
              $$("bookings-page").$$("leave_groups").$$("btn_save").disable();
            }
          });
          getRRLGroupLogs(newv);
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("select_group")
      .attachEvent("onChange", function (newv) {
        let selected_group = $$("bookings-page")
          .$$("leave_groups")
          .$$("select_group")
          .getText();
        let startDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_start_date")
          .getValue();
        let endDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_end_date")
          .getValue();
        if (selected_group != "") {
          getGroupDatesList(selected_group, "assign", startDate, endDate);
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("leave_group_list")
      .attachEvent("onChange", function (newv) {
        $$("bookings-page")
          .$$("leave_groups")
          .$$("leave_groups_grid")
          .clearAll();
        $$("bookings-page")
          .$$("leave_groups")
          .$$("leave_dates_grid")
          .clearAll();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("RRL_effective_date")
      .attachEvent("onChange", function (newv) {
        $$("bookings-page").$$("leave_groups").$$("btn_save").enable();
        let selected_group = $$("bookings-page")
          .$$("leave_groups")
          .$$("select_group")
          .getText();
        let startDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_start_date")
          .getValue();
        let endDate;
        let dateString = moment(startDate).format("YYYYMMDD");
        let payId = $$("bookings-page")
          .$$("leave_groups")
          .$$("search_employee")
          .getValue();
        $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_end_date")
          .setValue(newv);
        endDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_end_date")
          .getValue();
        if (selected_group != "") {
          getGroupDatesList(selected_group, "assign", startDate, endDate);
        }
        getRADetails(payId, dateString, false, function (RAInfo) {
          if (RAInfo.length > 0) {
            $$("bookings-page")
              .$$("leave_groups")
              .$$("curr_roster")
              .setValue(RAInfo[0].roster);
            $$("bookings-page")
              .$$("leave_groups")
              .$$("curr_shift")
              .setValue(RAInfo[0].shift);
            $$("bookings-page")
              .$$("leave_groups")
              .$$("curr_location")
              .setValue(RAInfo[0].location);
            $$("bookings-page").$$("leave_groups").$$("btn_save").enable();
          } else {
            webix.alert({
              text: "A 'Roster Arrangement' for the selected user and date period does not exist. You must create a 'Roster Arrangement' for the date period selected or change the date period and try again!",
              width: 600,
            });
            $$("bookings-page").$$("leave_groups").$$("btn_save").disable();
          }
        });
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("RRL_start_date")
      .attachEvent("onChange", function (newv) {
        $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_effective_date")
          .setValue(newv);
        $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_end_date")
          .setValue(newv);
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("RRL_end_date")
      .attachEvent("onChange", function (newv) {
        $$("bookings-page").$$("leave_groups").$$("btn_save").enable();
        let selected_group = $$("bookings-page")
          .$$("leave_groups")
          .$$("select_group")
          .getText();
        let startDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_effective_date")
          .getValue();
        let endDate = $$("bookings-page")
          .$$("leave_groups")
          .$$("RRL_end_date")
          .getValue();
        if (selected_group != "") {
          getGroupDatesList(selected_group, "assign", startDate, endDate);
        }
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("leave_dates_grid")
      .attachEvent("onItemClick", function (id, e, node) {
        let selected_group = $$("bookings-page")
          .$$("leave_groups")
          .$$("leave_group_list")
          .getText();
        let selectedRow = this.getItem(id);
        getGroupList(
          selected_group,
          selectedRow.start_date,
          selectedRow.return_date,
        );
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_delete_unselect_all")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("bookings-page")
          .$$("leave_groups")
          .$$("employee_rrl_bookings_grid");
        grid.eachRow(function (row) {
          let record = grid.getItem(row);
          record.select = 0;
        });
        grid.refresh();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_delete_select_all")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("bookings-page")
          .$$("leave_groups")
          .$$("employee_rrl_bookings_grid");
        grid.eachRow(function (row) {
          let record = grid.getItem(row);
          record.select = 1;
        });
        grid.refresh();
      });
    $$("bookings-page")
      .$$("leave_groups")
      .$$("btn_delete_rrls")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("bookings-page")
          .$$("leave_groups")
          .$$("employee_rrl_bookings_grid");
        let rowCount = 0;
        grid.eachRow(function (row) {
          let record = grid.getItem(row);
          if (record.select == 1) {
            rowCount += 1;
          }
        });
        if (rowCount > 0) {
          deleteSelectedRRLBookings();
        } else {
          webix.alert("You must select at least 1 RRL tour to delete!");
        }
      });
  }
  function getRRLGroupLogs(pay_id) {
    let grid = $$("bookings-page").$$("leave_groups").$$("employee_group_logs");
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/get_leave_group_logs",
        { pay_id: pay_id },
        {
          error: function (err) {},
          success: function (results) {
            let values = JSON.parse(results);
            values.forEach(function (result) {
              grid.add({
                id: result.id,
                group: result.leave_group,
                official_date: result.start_date,
                effective_date: result.effective_date,
                end_date: result.end_date,
                created_by: result.created_by,
                created_date: result.created_date,
              });
            });
          },
        },
      );
  }
  function importRRLs() {
    let sheet = $$("bookings-page").$$("leave_groups").$$("excel_import");
    let rrlArray = [];
    $$("loader-window").show();
    sheet.eachRow(function (row) {
      const record = sheet.getItem(row);
      const rrlObject = {};
      rrlObject.pay_id = record.data0;
      rrlObject.leave_group = record.data2;
      rrlObject.startDate = moment().format("YYYY-MM-DD 12:00");
      rrlObject.endDate = moment().add(5, "years").format("YYYY-MM-DD 12:00");
      rrlObject.createdBy = user_logged_in;
      rrlArray.push(rrlObject);
    });
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .post(
        server_url + "/bookings/assign_create_rrl",
        { rrl_array: rrlArray },
        {
          error: function (err) {
            $$("loader-window").hide();
            webix.alert({
              text: "There was an error importing the RRLs, please try again!",
              width: 550,
            });
          },
          success: function (result) {
            $$("loader-window").hide();
            webix.alert({
              text: "All RRLs were Imported successfully!",
              width: 550,
            });
          },
        },
      );
  }
  function updateExistingLeaveGroupDate(payId, fromDate, oldGroup, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .put(
        server_url + "/bookings/update_old_group",
        { pay_id: payId, end_date: fromDate, leave_group: oldGroup },
        {
          error: function (err) {
            $$("loader-window").hide();
            callback("error");
          },
          success: function (result) {
            $$("loader-window").hide();
            callback("ok");
          },
        },
      );
  }
  function createRRLBooking(pay_id, groupName) {
    $$("loader-window").show();
    $$("bookings-page").$$("leave_groups").$$("btn_save").disable();
    let grid = $$("bookings-page").$$("leave_groups").$$("RRL_dates_grid");
    let bookingId = "";
    let startDate = "";
    let endDate = "";
    let dateString = "";
    let check_date = "";
    let booking_first_date = new Date();
    let booking_last_date = new Date();
    let comments = "Auto generated when assigned to leave group";
    let roster = "";
    let shift = "";
    let location = "";
    let allSaved = true;
    let y = 0;
    let week_no = 0;
    let dayNo = 1;
    let list_index = 1;
    let no_RA = false;
    let daysPeriod = 0;
    if (groupName.includes("MG")) {
      daysPeriod = 24;
    } else {
      daysPeriod = 32;
    }
    grid.eachRow(function (row) {
      let record = grid.getItem(row);
      booking_first_date =
        moment(record.start_date, "DD/MM/YYYY").format("YYYY-MM-DD") + " 08:00";
      booking_last_date =
        moment(record.return_date, "DD/MM/YYYY").format("YYYY-MM-DD") +
        " 08:00";
      startDate = booking_first_date;
      bookingId = formatUuid(getRandomValuesFunc());
      y = 0;
      check_date = moment(record.start_date, "DD/MM/YYYY").format("YYYYMMDD");
      getRADetails(pay_id, check_date, false, function (result) {
        if (result.length > 0) {
          roster = result[0].roster;
          shift = result[0].shift;
          location = result[0].location;
        } else {
          no_RA = true;
        }
      });
      if (no_RA === false) {
        for (let x = 0; x < daysPeriod; x++) {
          if (x >= 0 && x < 8) {
            week_no = 1;
          } else if (x >= 8 && x < 16) {
            week_no = 2;
          } else if (x >= 16 && x < 24) {
            week_no = 3;
          } else if (x >= 24 && x < 32) {
            week_no = 4;
          }
          startDate =
            moment(startDate).add(y, "days").format("YYYY-MM-DD") + " 08:00";
          endDate =
            moment(startDate).add(1, "days").format("YYYY-MM-DD") + " 08:00";
          dateString = moment(startDate).format("YYYYMMDD");
          if (dayNo == 8) {
            dayNo = 0;
          }
          dayNo += 1;
          webix
            .ajax()
            .headers({ Authorization: "Bearer " + api_key })
            .sync()
            .post(
              server_url + "/bookings/create",
              {
                booking_id: bookingId,
                pay_id: pay_id,
                leave_type_code: "RRL",
                leave_type_description: "Rostered Recreation Leave",
                roster: roster,
                shift: shift,
                location: location,
                start_date: startDate,
                end_date: endDate,
                diff_hours: 24,
                date_string: dateString,
                booking_first_date: booking_first_date,
                booking_last_date: booking_last_date,
                bk_period: "both",
                comments: comments,
                created_by: user_logged_in,
                booking_type: "leave_request",
                moved_roster: "",
                moved_shift: "",
                moved_location: "",
                actup_rank: "",
                rrl_week_no: week_no,
                status: "Approved",
                list_index: list_index,
                sm_travel_ppe: 0,
                sm_travel_notice: 0,
                deleted: false,
                with_standby: 0,
              },
              {
                error: function (err) {
                  allSaved = false;
                },
                success: function (result) {},
              },
            );
          y = +1;
        }
      }
    });
    if (allSaved === true) {
      $$("loader-window").hide();
      $$("bookings-page").$$("leave_groups").$$("RRL_dates_grid").clearAll();
      $$("bookings-page").$$("leave_groups").$$("btn_save").enable();
      if (ro_view_showing == true) {
        availabilityReport.loadReport(
          $$("schedule-page").$$("grid_ro_view_report"),
          function (callback) {
            if (callback == "ok") {
              functionsPopup.closePopup();
              schedule.reload_roster();
            }
          },
        );
      } else {
        functionsPopup.closePopup();
        schedule.reload_roster();
      }
      webix.alert("RRL bookings were created successfully!");
      newSearch = false;
      $$("bookings-page")
        .$$("leave_groups")
        .$$("RRL_start_date")
        .setValue(new Date());
      $$("bookings-page")
        .$$("leave_groups")
        .$$("RRL_effective_date")
        .setValue(new Date());
      $$("bookings-page")
        .$$("leave_groups")
        .$$("RRL_end_date")
        .setValue(new Date());
      $$("bookings-page").$$("leave_groups").$$("select_group").setValue("");
      $$("bookings-page").$$("leave_groups").$$("search_employee").setValue("");
      newSearch = true;
    } else {
      $$("loader-window").hide();
      webix.alert("There was an error saving the RRL bookings!");
      $$("bookings-page").$$("leave_groups").$$("btn_save").enable();
    }
  }
  function deleteExistingRRLBookings(payId, fromDate, toDate, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .del(
        server_url + "/bookings/delete_rrl",
        {
          pay_id: payId,
          from_date: moment(fromDate).format("YYYY-MM-DD mm:ss"),
          to_date: moment(toDate).format("YYYY-MM-DD mm:ss"),
          leave_type_code: "RRL",
        },
        {
          error: function (err) {},
          success: function (results) {
            let result = JSON.parse(results);
            callback(result);
          },
        },
      );
  }
  function deleteSelectedRRLBookings() {
    let payId = $$("bookings-page")
      .$$("leave_groups")
      .$$("delete_search_employee")
      .getValue();
    let found_error = false;
    $$("loader-window").show();
    setTimeout(function () {
      let grid = $$("bookings-page")
        .$$("leave_groups")
        .$$("employee_rrl_bookings_grid");
      grid.eachRow(function (row) {
        let record = grid.getItem(row);
        if (record.select == 1) {
          webix
            .ajax()
            .headers({ Authorization: "Bearer " + api_key })
            .sync()
            .del(
              server_url + "/bookings/delete_selected_rrls",
              {
                booking_id: record.booking_id,
                rrl_week_no: record.week_no,
                deleted_by: user_logged_in,
              },
              {
                error: function (err) {
                  $$("loader-window").hide();
                  found_error = true;
                },
                success: function (results) {},
              },
            );
        }
      });
      $$("loader-window").hide();
      if (found_error === false) {
        webix.alert("Selected RRL bookings were deleted successfully!");
      } else {
        webix.alert(
          "There was an error deleting 1 or more selected RRL bookings!",
        );
      }
      getEmployeeRRLBookings(payId);
    }, 250);
  }
  function saveLeaveGroup() {
    let form = $$("bookings-page")
      .$$("leave_groups")
      .$$("frmCreateGroup")
      .getValues();
    let noYears = 1;
    let startDate = form.group_start_date.slice(0, 10) + " 08:00";
    let returnDate = form.group_return_date.slice(0, 10) + " 08:00";
    let saveSuccess = false;
    if (form.auto_generate_group == 0) {
      noYears = 1;
    } else {
      noYears = form.period_years;
    }
    for (let y = 1; y <= noYears; y++) {
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .sync()
        .post(
          server_url + "/bookings/create_leave_group",
          {
            name: form.group_name,
            start_date: moment(startDate).format("YYYY-MM-DD mm:ss"),
            return_date: moment(returnDate).format("YYYY-MM-DD mm:ss"),
            created_by: user_logged_in,
          },
          {
            error: function (err) {},
            success: function (results) {
              saveSuccess = true;
            },
          },
        );
      if (form.group_name.includes("MG")) {
        startDate = moment(startDate).add(256, "days");
        returnDate = moment(startDate).add(24, "days");
      } else {
        startDate = moment(startDate).add(208, "days");
        returnDate = moment(startDate).add(32, "days");
      }
    }
    if (saveSuccess === true) {
      $$("bookings-page").$$("leave_groups").$$("frmCreateGroup").clear();
      $$("bookings-page")
        .$$("leave_groups")
        .$$("group_start_date")
        .setValue(new Date());
      $$("bookings-page")
        .$$("leave_groups")
        .$$("group_return_date")
        .setValue(new Date());
      $$("bookings-page").$$("leave_groups").$$("period_years").setValue(1);
      loadLeaveGroups();
    }
  }
  function assignLeaveGroup(fromDate, toDate, effectDate, callback) {
    let form = $$("bookings-page")
      .$$("leave_groups")
      .$$("frmLeaveGroups")
      .getValues();
    let selected_group = $$("bookings-page")
      .$$("leave_groups")
      .$$("select_group")
      .getText();
    let dateOverlapStart = false;
    let dateOverlapEnd = false;
    let overlapDate = "";
    let grid = $$("bookings-page").$$("leave_groups").$$("employee_group_logs");
    grid.eachRow(function (row) {
      const items = grid.getItem(row);
      if (
        moment(fromDate).isSame(moment(items.end_date, "DD/MM/YYYY"), "day")
      ) {
        dateOverlapStart = true;
        overlapDate = moment(fromDate).format("DD/MM/YYYY");
      } else if (
        moment(toDate).isSame(moment(items.official_date, "DD/MM/YYYY"), "day")
      ) {
        dateOverlapEnd = true;
        overlapDate = moment(toDate).format("DD/MM/YYYY");
      }
    });
    if (dateOverlapStart === false && dateOverlapEnd === false) {
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .sync()
        .post(
          server_url + "/bookings/assign_leave_group",
          {
            pay_id: form.search_employee,
            leave_group: selected_group,
            start_date: fromDate,
            end_date: toDate,
            effective_date: effectDate,
            created_by: user_logged_in,
          },
          {
            error: function (err) {
              callback("error");
            },
            success: function (results) {
              callback("ok");
            },
          },
        );
    } else if (dateOverlapStart === true) {
      webix.alert({
        text:
          "The 'Official Start Date' of this period should not be the same as the 'End Date' of an existing period!<br><br>Note: In this case " +
          overlapDate +
          " is the same as the 'End Date' of an existing RRL period.<br>This should be the following day or any subsequent date.",
        width: 640,
      });
      callback("error");
    } else if (dateOverlapEnd === true) {
      webix.alert({
        text:
          "The 'End Date' of this period should not be the same as the 'Official Start Date' of an existing period!<br><br>Note: In this case " +
          overlapDate +
          " is the same as the 'Official Date' of an existing RRL period.<br>This should be the previous day or any prior date.",
        width: 640,
      });
      callback("error");
    }
  }
  function deleteLeaveGroupEntry(payId, group, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .del(
        server_url + "/bookings/delete_rrl_group_entry",
        { pay_id: payId, leave_group: group },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            callback("ok");
          },
        },
      );
  }
  function getEmployeeRRLBookings(payId) {
    let grid = $$("bookings-page")
      .$$("leave_groups")
      .$$("employee_rrl_bookings_grid");
    grid.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/RRL_bookings_for_deletion",
        { pay_id: payId },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let values = JSON.parse(results);
            let startDate = "";
            let endDate = "";
            values.forEach(function (result) {
              if (result.rrl_week_no == 1) {
                startDate = result.booking_first_date;
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 2) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 3) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 4) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(32, "days")
                  .format("DD/MM/YYYY");
              }
              grid.add({
                booking_id: result.booking_id,
                type: "RRL",
                week_no: result.rrl_week_no,
                start_date: startDate,
                return_date: endDate,
                days: "8",
              });
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  function getGroupList(groupName, fromDate, toDate) {
    let grid = $$("bookings-page").$$("leave_groups").$$("leave_groups_grid");
    fromDate = moment(fromDate, "DD/MM/YYYY").format("YYYY-MM-DD");
    toDate = moment(toDate, "DD/MM/YYYY").format("YYYY-MM-DD");
    grid.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/leave_group_results",
        {
          leave_group: groupName,
          start_date: moment(fromDate).format("YYYY-MM-DD"),
          end_date: moment(toDate).format("YYYY-MM-DD"),
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let values = JSON.parse(results);
            let employeeName = "";
            let startDate = "";
            let endDate = "";
            $$("bookings-page")
              .$$("leave_groups")
              .$$("search_count")
              .define("template", values.length + " records found");
            $$("bookings-page").$$("leave_groups").$$("search_count").refresh();
            values.forEach(function (result) {
              if (result.middle_name == null) {
                employeeName = result.surname + ", " + result.first_name;
              } else {
                employeeName =
                  result.surname +
                  ", " +
                  result.first_name +
                  " " +
                  result.middle_name;
              }
              if (result.rrl_week_no == 1) {
                startDate = result.booking_first_date;
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 2) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 3) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 4) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
                endDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(32, "days")
                  .format("DD/MM/YYYY");
              }
              grid.add({
                booking_id: result.booking_id,
                service_no: result.pay_id,
                rank: result.rank,
                employee: employeeName,
                week_no: result.rrl_week_no,
                start_date: startDate,
                return_date: endDate,
                days: "8",
              });
            });
            setGrouping(grid);
            $$("loader-window").hide();
          },
        },
      );
  }
  function getGroupDatesList(groupName, type, startDate, endDate) {
    let grid = "";
    if (type == "search") {
      grid = $$("bookings-page").$$("leave_groups").$$("leave_dates_grid");
    } else if (type == "assign") {
      grid = $$("bookings-page").$$("leave_groups").$$("RRL_dates_grid");
    }
    grid.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/leave_dates_results",
        { name: groupName, start_date: startDate, end_date: endDate },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let values = JSON.parse(results);
            values.forEach(function (result) {
              grid.add({
                id: result.id,
                leave_type: "RRL",
                start_date: moment(result.start_date).format("DD/MM/YYYY"),
                return_date: moment(result.return_date).format("DD/MM/YYYY"),
              });
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  employees_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("leave_groups").$$("search_employee");
    let select2 = $$("bookings-page")
      .$$("leave_groups")
      .$$("delete_search_employee");
    let employee_list = [];
    if (data) {
      data.forEach(function (value) {
        employee_list.push({
          id: value.id,
          value: value.value + " (" + value.id + ")",
        });
      });
      select.define("options", employee_list);
      select2.define("options", employee_list);
      select.refresh();
      select2.refresh();
    }
  });
  function setGrouping(grid) {
    let currBookingId = "";
    let cssProfile = "";
    let x = 0;
    grid.eachRow(function (id) {
      if (
        currBookingId === "" ||
        (currBookingId === grid.getItem(id).booking_id && x === 0)
      ) {
        cssProfile = "rrl_period_group_1";
        x = 0;
      } else if (
        currBookingId === "" ||
        (currBookingId === grid.getItem(id).booking_id && x === 1)
      ) {
        cssProfile = "rrl_period_group_2";
        x = 1;
      } else if (
        currBookingId === "" ||
        (currBookingId !== grid.getItem(id).booking_id && x === 0)
      ) {
        cssProfile = "rrl_period_group_2";
        x = 1;
      } else if (
        currBookingId === "" ||
        (currBookingId !== grid.getItem(id).booking_id && x === 1)
      ) {
        cssProfile = "rrl_period_group_1";
        x = 0;
      }
      grid.addRowCss(id, cssProfile);
      currBookingId = grid.getItem(id).booking_id;
    });
  }
  function loadLeaveGroups() {
    let list = $$("bookings-page")
      .$$("leave_groups")
      .$$("leave_group_list")
      .getPopup()
      .getList();
    let select = $$("bookings-page")
      .$$("leave_groups")
      .$$("select_group")
      .getPopup()
      .getList();
    list.clearAll();
    select.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/group_list",
        {},
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let values = JSON.parse(results);
            values.forEach(function (value) {
              list.add({ id: value.id, value: value.name });
              select.add({ id: value.id, value: value.name });
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  function createRRLBookingsFromImport() {
    let sheet = $$("bookings-page")
      .$$("leave_groups")
      .$$("excel_RRL_bookings_import");
    let bookingId = "";
    let startDate = "";
    let endDate = "";
    let dateString = "";
    let check_date = "";
    let booking_first_date = new Date();
    let booking_last_date = new Date();
    let roster = "";
    let shift = "";
    let location = "";
    let y = 0;
    let week_no = 0;
    let rowNo = 0;
    let pay_id = 0;
    let leave_group = "";
    let rrl_group_array = [];
    let emp_rrl_array = [];
    $$("loader-window").show();
    webix.message({
      id: "rrl_counter",
      text: "Processing RRL entries...",
      type: "debug",
      expire: -1,
    });
    setTimeout(function () {
      sheet.eachRow(function (row) {
        rowNo += 1;
        emp_rrl_array = [];
        const record = sheet.getItem(row);
        let no_RA = false;
        pay_id = record.data0;
        leave_group = record.data11;
        getRRLGroupDates(leave_group, function (response) {
          rrl_group_array = response;
          rrl_group_array.forEach(function (row) {
            booking_first_date =
              moment(row.start_date, "DD/MM/YYYY").format("YYYY-MM-DD") +
              " 08:00";
            booking_last_date =
              moment(row.return_date, "DD/MM/YYYY").format("YYYY-MM-DD") +
              " 08:00";
            startDate = booking_first_date;
            bookingId = formatUuid(getRandomValuesFunc());
            check_date = moment(row.start_date, "DD/MM/YYYY").format(
              "YYYYMMDD",
            );
            getRADetails(pay_id, check_date, false, function (result) {
              if (result.length > 0) {
                roster = result[0].roster;
                shift = result[0].shift;
                location = result[0].location;
              } else {
                no_RA = true;
              }
            });
            if (no_RA === false) {
              y = 0;
              for (let x = 0; x < 32; x++) {
                const emp_rrl_object = {};
                if (x >= 0 && x < 8) {
                  week_no = 1;
                } else if (x >= 8 && x < 16) {
                  week_no = 2;
                } else if (x >= 16 && x < 24) {
                  week_no = 3;
                } else if (x >= 24 && x < 32) {
                  week_no = 4;
                }
                startDate =
                  moment(startDate).add(y, "days").format("YYYY-MM-DD") +
                  " 08:00";
                endDate =
                  moment(startDate).add(1, "days").format("YYYY-MM-DD") +
                  " 08:00";
                dateString = moment(startDate).format("YYYYMMDD");
                emp_rrl_object.booking_id = bookingId;
                emp_rrl_object.pay_id = pay_id;
                emp_rrl_object.roster = roster;
                emp_rrl_object.shift = shift;
                emp_rrl_object.location = location;
                emp_rrl_object.start_date = startDate;
                emp_rrl_object.end_date = endDate;
                emp_rrl_object.date_string = dateString;
                emp_rrl_object.booking_first_date = booking_first_date;
                emp_rrl_object.booking_last_date = booking_last_date;
                emp_rrl_object.comments = "Auto generated via Import";
                emp_rrl_object.created_by = user_logged_in;
                emp_rrl_object.rrl_week_no = week_no;
                emp_rrl_array.push(emp_rrl_object);
                y = +1;
              }
            }
          });
        });
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .sync()
          .post(
            server_url + "/bookings/import_rrl_bookings",
            { bookingsArray: emp_rrl_array },
            {
              error: function (err) {},
              success: function (result) {
                webix.message.pull["rrl_counter"].firstChild.innerHTML =
                  "Now processing RRL " + rowNo;
              },
            },
          );
      });
      webix.message.hide("rrl_counter");
      $$("loader-window").hide();
      webix.alert({
        text: "RRL bookings were successfully imported!",
        width: 500,
      });
    }, 250);
  }
  function getRRLGroupDates(groupName, callback) {
    let RRL_array = [];
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/leave_dates_results",
        { name: groupName, start_date: "2021-01-26", end_date: "2024-12-31" },
        {
          error: function (err) {},
          success: function (results) {
            let values = JSON.parse(results);
            values.forEach(function (result) {
              let RRL_object = {};
              RRL_object.group = result.name;
              RRL_object.start_date = moment(result.start_date).format(
                "DD/MM/YYYY",
              );
              RRL_object.return_date = moment(result.return_date).format(
                "DD/MM/YYYY",
              );
              RRL_array.push(RRL_object);
            });
            callback(RRL_array);
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
