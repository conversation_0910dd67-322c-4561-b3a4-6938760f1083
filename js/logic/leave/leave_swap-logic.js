let leaveSwap = (function () {
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("leave_swap")
      .$$("from_employee")
      .attachEvent("onChange", function (newv, oldv) {
        getRRLBookings(newv, "from", function (grid) {
          setRRLGrouping(grid);
        });
      });
    $$("bookings-page")
      .$$("leave_swap")
      .$$("swap_employee")
      .attachEvent("onChange", function (newv, oldv) {
        getRRLBookings(newv, "swap", function (grid) {
          setRRLGrouping(grid);
        });
      });
    $$("bookings-page")
      .$$("leave_swap")
      .$$("swap_btn_save")
      .attachEvent("onItemClick", function (id, e) {
        let fromWeekCount = $$("bookings-page")
          .$$("leave_swap")
          .$$("from_dates_grid")
          .getSelectedItem(true);
        let swapWeekCount = $$("bookings-page")
          .$$("leave_swap")
          .$$("swap_dates_grid")
          .getSelectedItem(true);
        if (fromWeekCount.length > 0 && swapWeekCount.length > 0) {
          let fromPayId = $$("bookings-page")
            .$$("leave_swap")
            .$$("from_employee")
            .getValue();
          let swapPayId = $$("bookings-page")
            .$$("leave_swap")
            .$$("swap_employee")
            .getValue();
          let fromLeaveGroup = fromWeekCount[0].leave_group;
          let swapLeaveGroup = swapWeekCount[0].leave_group;
          let fromBookingId = "";
          let swapBookingId = "";
          let fromWeekNo = 0;
          let swapWeekNo = 0;
          let fromLocation = "";
          let swapLocation = "";
          let fromShift = fromWeekCount[0].shift;
          let swapShift = swapWeekCount[0].shift;
          let fromRoster = fromWeekCount[0].roster;
          let swapRoster = swapWeekCount[0].roster;
          let properSurname = "";
          let properSwapSurname = "";
          let comments = "";
          if (
            fromRoster == "Metro" ||
            fromRoster == "Port Pirie" ||
            fromRoster == "Mt Gambier" ||
            fromRoster == "Comms"
          ) {
            if (
              swapRoster == "Metro" ||
              swapRoster == "Port Pirie" ||
              swapRoster == "Mt Gambier" ||
              swapRoster == "Comms"
            ) {
              if (!fromPayId || !swapPayId) {
                webix.alert({
                  text: "You must select both Employees before you can process a leave swap!",
                  width: 500,
                });
              } else if (fromShift != swapShift) {
                webix.alert({
                  text: "You can't perform an RRL swap when the selected tours are for different <strong>Shifts</strong>!",
                  width: 500,
                });
              } else if (fromRoster != swapRoster) {
                webix.alert({
                  text: "You can't perform an RRL swap when the selected tours are for different <strong>Rosters</strong>!",
                  width: 500,
                });
              } else if (!fromLeaveGroup || !swapLeaveGroup) {
                webix.alert({
                  text: "One of the selected tours does not have an RRL group assigned therefore this leave swap can't be done!",
                  width: 650,
                });
              } else {
                if (fromLeaveGroup.slice(0, 1) !== swapLeaveGroup.slice(0, 1)) {
                  webix.alert({
                    text: "The selected Employees are from different RRL Leave Groups.</br>You will need to complete a 'SP16 Form' to request this leave swap!",
                    width: 500,
                  });
                } else {
                  if (fromWeekCount.length == 0 || swapWeekCount.length == 0) {
                    webix.alert({
                      text: "You must select at least 1 week period for both Employees before you can process a leave swap!",
                      width: 500,
                    });
                  } else {
                    let checkFromStartDate = moment(
                      fromWeekCount[0].start_date,
                      "DD/MM/YYYY",
                    );
                    let checkSwapStartDate = moment(
                      swapWeekCount[0].start_date,
                      "DD/MM/YYYY",
                    );
                    let daysDiff = checkSwapStartDate.diff(
                      checkFromStartDate,
                      "days",
                      true,
                    );
                    if (Math.abs(daysDiff) < 365) {
                      if (fromPayId == swapPayId) {
                        webix.alert({
                          text: "You must select 2 different Employees to process a leave swap!",
                          width: 500,
                        });
                      } else {
                        checkForBookings(
                          fromPayId,
                          checkSwapStartDate,
                          function (results) {
                            if (results.length == 0) {
                              checkForBookings(
                                swapPayId,
                                checkFromStartDate,
                                function (results) {
                                  if (results.length == 0) {
                                    $$("loader-window").show();
                                    setTimeout(function () {
                                      fromBookingId =
                                        fromWeekCount[0].booking_id;
                                      swapBookingId =
                                        swapWeekCount[0].booking_id;
                                      fromWeekNo = fromWeekCount[0].week_no;
                                      swapWeekNo = swapWeekCount[0].week_no;
                                      fromLocation = fromWeekCount[0].location;
                                      swapLocation = swapWeekCount[0].location;
                                      comments = $$("bookings-page")
                                        .$$("leave_swap")
                                        .$$("swap_comments")
                                        .getValue();
                                      let fromEmployeeName = $$("bookings-page")
                                        .$$("leave_swap")
                                        .$$("from_employee")
                                        .getText();
                                      let fromNameSplit =
                                        fromEmployeeName.split(",");
                                      let fromComments =
                                        "swapped with " +
                                        fromNameSplit[0] +
                                        ", " +
                                        fromNameSplit[1].slice(1, 2) +
                                        " (" +
                                        fromPayId +
                                        ") - " +
                                        comments;
                                      swapRRLBookings(
                                        fromWeekCount[0].booking_id,
                                        fromWeekCount[0].week_no,
                                        swapPayId,
                                        fromPayId,
                                        swapBookingId,
                                        swapWeekNo,
                                        swapLocation,
                                        fromComments,
                                        function (response) {
                                          if (response == "ok") {
                                            if (live_site === true) {
                                              getEmployeeData(
                                                fromPayId,
                                                function (values) {
                                                  properSurname = toProperCase(
                                                    values[0].surname,
                                                  );
                                                  getEmployeeData(
                                                    swapPayId,
                                                    function (swap_values) {
                                                      properSwapSurname =
                                                        toProperCase(
                                                          swap_values[0]
                                                            .surname,
                                                        );
                                                      sendEmail(
                                                        "SAPPHIRE<<EMAIL>>",
                                                        swap_values[0]
                                                          .notifications_email,
                                                        "RE: Leave Swap",
                                                        "The following Leave Swap was created for " +
                                                          swap_values[0]
                                                            .first_name +
                                                          " " +
                                                          properSwapSurname +
                                                          " with " +
                                                          values[0].first_name +
                                                          " " +
                                                          properSurname,
                                                        "Leave Swap dates: WAS " +
                                                          swapWeekCount[0]
                                                            .start_date +
                                                          " - " +
                                                          swapWeekCount[0]
                                                            .return_date +
                                                          " | " +
                                                          "NOW " +
                                                          fromWeekCount[1]
                                                            .start_date +
                                                          " - " +
                                                          fromWeekCount[1]
                                                            .return_date,
                                                        swap_values[0]
                                                          .first_name +
                                                          " " +
                                                          properSwapSurname,
                                                        "Please contact Workforce <NAME_EMAIL> for any queries regarding this leave swap.",
                                                        "Regards, The SAPPHIRE Team",
                                                      );
                                                    },
                                                  );
                                                },
                                              );
                                            }
                                            setTimeout(function () {
                                              getRRLBookings(
                                                fromPayId,
                                                "from",
                                                function (grid) {
                                                  setRRLGrouping(grid);
                                                },
                                              );
                                            }, 150);
                                          }
                                        },
                                      );
                                      let swapEmployeeName = $$("bookings-page")
                                        .$$("leave_swap")
                                        .$$("swap_employee")
                                        .getText();
                                      let swapNameSplit =
                                        swapEmployeeName.split(",");
                                      let swapComments =
                                        "swapped with " +
                                        swapNameSplit[0] +
                                        ", " +
                                        swapNameSplit[1].slice(1, 2) +
                                        " (" +
                                        swapPayId +
                                        ") - " +
                                        comments;
                                      swapRRLBookings(
                                        swapWeekCount[0].booking_id,
                                        swapWeekCount[0].week_no,
                                        fromPayId,
                                        swapPayId,
                                        fromBookingId,
                                        fromWeekNo,
                                        fromLocation,
                                        swapComments,
                                        function (response) {
                                          if (response == "ok") {
                                            if (live_site === true) {
                                              getEmployeeData(
                                                swapPayId,
                                                function (values) {
                                                  properSurname = toProperCase(
                                                    values[0].surname,
                                                  );
                                                  getEmployeeData(
                                                    fromPayId,
                                                    function (swap_values) {
                                                      properSwapSurname =
                                                        toProperCase(
                                                          swap_values[0]
                                                            .surname,
                                                        );
                                                      sendEmail(
                                                        "SAPPHIRE<<EMAIL>>",
                                                        swap_values[0]
                                                          .notifications_email,
                                                        "RE: Leave Swap",
                                                        "The following Leave Swap was created for " +
                                                          swap_values[0]
                                                            .first_name +
                                                          " " +
                                                          properSwapSurname +
                                                          " with " +
                                                          values[0].first_name +
                                                          " " +
                                                          properSurname,
                                                        "Leave Swap dates: WAS " +
                                                          fromWeekCount[0]
                                                            .start_date +
                                                          " - " +
                                                          fromWeekCount[0]
                                                            .return_date +
                                                          " | " +
                                                          "NOW " +
                                                          swapWeekCount[1]
                                                            .start_date +
                                                          " - " +
                                                          swapWeekCount[1]
                                                            .return_date,
                                                        swap_values[0]
                                                          .first_name +
                                                          " " +
                                                          properSwapSurname,
                                                        "Please contact Workforce <NAME_EMAIL> for any queries regarding this leave swap.",
                                                        "Regards, The SAPPHIRE Team",
                                                      );
                                                    },
                                                  );
                                                },
                                              );
                                            }
                                            setTimeout(function () {
                                              getRRLBookings(
                                                swapPayId,
                                                "swap",
                                                function (grid) {
                                                  setRRLGrouping(grid);
                                                },
                                              );
                                            }, 150);
                                          }
                                        },
                                      );
                                      $$("bookings-page")
                                        .$$("leave_swap")
                                        .$$("swap_comments")
                                        .setValue("");
                                      $$("loader-window").hide();
                                      webix.alert({
                                        text: "RRL swaps were successful!",
                                        width: 400,
                                      });
                                    }, 250);
                                  } else {
                                    let bookingsList = "";
                                    let employeeName = $$("bookings-page")
                                      .$$("leave_swap")
                                      .$$("swap_employee")
                                      .getText();
                                    results.forEach(function (result) {
                                      bookingsList =
                                        bookingsList +
                                        "</br>" +
                                        result.start_date +
                                        " - " +
                                        "<b>" +
                                        result.leave_type_code +
                                        "</b>" +
                                        " (" +
                                        result.leave_type_description +
                                        ")";
                                    });
                                    webix.alert({
                                      text:
                                        "The following bookings were found for <strong>" +
                                        employeeName +
                                        "</strong></br>during the tour period you have selected to swap;</br>" +
                                        "<div style='text-align: left'>" +
                                        bookingsList +
                                        "</div>" +
                                        "</br></br>The above bookings will have to be deleted before you can process this leave swap!",
                                      width: 620,
                                    });
                                  }
                                },
                              );
                            } else {
                              let bookingsList = "";
                              let employeeName = $$("bookings-page")
                                .$$("leave_swap")
                                .$$("from_employee")
                                .getText();
                              results.forEach(function (result) {
                                bookingsList =
                                  bookingsList +
                                  "</br>" +
                                  result.start_date +
                                  " - " +
                                  "<b>" +
                                  result.leave_type_code +
                                  "</b>" +
                                  " (" +
                                  result.leave_type_description +
                                  ")";
                              });
                              webix.alert({
                                text:
                                  "The following bookings were found for <strong>" +
                                  employeeName +
                                  "</strong></br>during the tour period you have selected to swap;</br>" +
                                  "<div style='text-align: left'>" +
                                  bookingsList +
                                  "</div>" +
                                  "</br></br>The above bookings will have to be deleted before you can process this leave swap!",
                                width: 620,
                              });
                            }
                          },
                        );
                      }
                    } else {
                      webix.alert({
                        text: "The date periods selected are more than 12 months apart!</br>This will need to be applied via SP26 directly to Workforce Rostering for ACFO approval.",
                        width: 550,
                      });
                    }
                  }
                }
              }
            } else {
              let swapEmployeeName = $$("bookings-page")
                .$$("leave_swap")
                .$$("swap_employee")
                .getText();
              webix.alert({
                text:
                  "You can only perform an RRL leave swap for Metro, Comms, Port Pirie or Mt Gambier rosters!</br><strong>" +
                  swapEmployeeName +
                  "</strong> is at <strong>" +
                  swapRoster +
                  "</strong> for the selected tour",
                width: 550,
              });
            }
          } else {
            let fromEmployeeName = $$("bookings-page")
              .$$("leave_swap")
              .$$("from_employee")
              .getText();
            webix.alert({
              text:
                "You can only perform an RRL leave swap for Metro, Comms, Port Pirie or Mt Gambier rosters!</br><strong>" +
                fromEmployeeName +
                "</strong> is at <strong>" +
                fromRoster +
                "</strong> for the selected tour",
              width: 550,
            });
          }
        } else {
          webix.alert({
            text: "You need to select/highlight a tour entry for each employee to process a swap!",
            width: 550,
          });
        }
      });
  }
  function setRRLGrouping(grid) {
    let currBookingId = "";
    let cssProfile = "";
    let x = 0;
    grid.eachRow(function (id) {
      if (
        currBookingId === "" ||
        (currBookingId === grid.getItem(id).booking_id && x === 0)
      ) {
        cssProfile = "rrl_grid_group_1";
        x = 0;
      } else if (
        currBookingId === "" ||
        (currBookingId === grid.getItem(id).booking_id && x === 1)
      ) {
        cssProfile = "rrl_grid_group_2";
        x = 1;
      } else if (
        currBookingId === "" ||
        (currBookingId !== grid.getItem(id).booking_id && x === 0)
      ) {
        cssProfile = "rrl_grid_group_2";
        x = 1;
      } else if (
        currBookingId === "" ||
        (currBookingId !== grid.getItem(id).booking_id && x === 1)
      ) {
        cssProfile = "rrl_grid_group_1";
        x = 0;
      }
      grid.addCellCss(id, "group_colour", cssProfile);
      currBookingId = grid.getItem(id).booking_id;
    });
  }
  function checkForBookings(payId, startDate, callback) {
    let fromDate = moment(startDate, "DD/MM/YYYY").format("YYYYMMDD");
    let toDate = moment(startDate, "DD/MM/YYYY").add(7, "days");
    toDate = moment(toDate).format("YYYYMMDD");
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/ra_bookings",
        { pay_id: payId, start_date: fromDate, end_date: toDate },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let value = JSON.parse(results);
            if (value) {
              callback(value);
            } else {
              callback([]);
            }
          },
        },
      );
  }
  function swapRRLBookings(
    bookingId,
    weekNo,
    payId,
    swapPayId,
    swapBookingId,
    swapWeekNo,
    swapLocation,
    comments,
    callback,
  ) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .put(
        server_url + "/bookings/rrl_swap",
        {
          booking_id: bookingId,
          pay_id: swapPayId,
          leave_type_code: "RRL",
          rrl_swap_pay_id: payId,
          rrl_week_no: weekNo,
          rrl_swap_week_no: swapWeekNo,
          rrl_swap_booking_id: swapBookingId,
          location: swapLocation,
          comments: comments,
          approved_denied_by: user_logged_in,
        },
        {
          error: function (err) {},
          success: function () {
            callback("ok");
          },
        },
      );
  }
  function getRRLBookings(payId, type, callback) {
    let grid = "";
    let selEmployee = "";
    if (type === "from") {
      grid = $$("bookings-page").$$("leave_swap").$$("from_dates_grid");
      selEmployee = $$("bookings-page")
        .$$("leave_swap")
        .$$("from_employee")
        .getText();
    } else if (type === "swap") {
      grid = $$("bookings-page").$$("leave_swap").$$("swap_dates_grid");
      selEmployee = $$("bookings-page")
        .$$("leave_swap")
        .$$("swap_employee")
        .getText();
    }
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/employee_RRL_bookings",
        { pay_id: payId },
        {
          error: function (err) {},
          success: function (results) {
            let values = JSON.parse(results);
            let startDate = "";
            let returnDate = "";
            let employeeName = "";
            let swapRoster = "";
            let swapShift = "";
            let swapLocation = "";
            let swap_startDate = "";
            let swap_returnDate = "";
            let swapWeekNo = "";
            let swap_group = "";
            if (values.length > 0) {
              values.forEach(function (result) {
                employeeName = "";
                swapRoster = "";
                swapShift = "";
                swapLocation = "";
                swap_startDate = "";
                swap_returnDate = "";
                swapWeekNo = "";
                swap_group = "";
                if (result.rrl_week_no == 1) {
                  startDate = result.booking_first_date;
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_week_no == 2) {
                  startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_week_no == 3) {
                  startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_week_no == 4) {
                  startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                  returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                    .add(32, "days")
                    .format("DD/MM/YYYY");
                }
                if (result.rrl_swap_week_no == 1) {
                  swap_startDate = result.swap_booking_first_date;
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_swap_week_no == 2) {
                  swap_startDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(8, "days")
                    .format("DD/MM/YYYY");
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_swap_week_no == 3) {
                  swap_startDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(16, "days")
                    .format("DD/MM/YYYY");
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                } else if (result.rrl_swap_week_no == 4) {
                  swap_startDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(24, "days")
                    .format("DD/MM/YYYY");
                  swap_returnDate = moment(
                    result.swap_booking_first_date,
                    "DD/MM/YYYY",
                  )
                    .add(32, "days")
                    .format("DD/MM/YYYY");
                }
                swapWeekNo = result.rrl_swap_week_no;
                if (result.surname == null) {
                  employeeName = "";
                  swapRoster = "";
                  swapShift = "";
                  swapLocation = "";
                  swap_startDate = "";
                  swap_returnDate = "";
                  swap_group = "";
                } else {
                  employeeName =
                    result.rrl_swap_pay_id +
                    " - " +
                    result.surname +
                    ", " +
                    result.first_name;
                  swapRoster = result.swap_roster;
                  swapShift = result.swap_shift;
                  swapLocation = result.swap_location;
                  swap_group = result.swap_group;
                }
                if (moment(startDate, "DD/MM/YYYY").isSameOrAfter(moment())) {
                  grid.add({
                    booking_id: result.booking_id,
                    group_colour: "",
                    leave_type: "RRL",
                    leave_group: result.leave_group,
                    rank: result.curr_rank,
                    start_date: startDate,
                    return_date: returnDate,
                    week_no: result.rrl_week_no,
                    roster: result.roster,
                    shift: result.shift,
                    location: result.location,
                    rrl_swapee: employeeName,
                    swap_group: swap_group,
                    swap_start_date: swap_startDate,
                    swap_return_date: swap_returnDate,
                    swap_week_no: swapWeekNo,
                    swap_roster: swapRoster,
                    swap_shift: swapShift,
                    swap_location: swapLocation,
                  });
                }
              });
              if (grid.count() > 0) {
                grid.adjustColumn("rrl_swapee", "data");
                grid.adjustColumn("roster", "data");
                grid.adjustColumn("shift", "data");
                grid.adjustColumn("location", "data");
                grid.adjustColumn("swap_roster", "data");
                grid.adjustColumn("swap_shift", "data");
                grid.adjustColumn("swap_location", "data");
              } else {
                webix.alert({
                  text:
                    "No RRL tours found for " +
                    selEmployee +
                    " (" +
                    payId +
                    ")",
                  width: 450,
                });
              }
              callback(grid);
            } else {
              webix.alert({
                text:
                  "No RRL tours found for " + selEmployee + " (" + payId + ")",
                width: 450,
              });
              callback(grid);
            }
          },
        },
      );
  }
  employees_subject.subscribe(function (data) {
    $$("bookings-page")
      .$$("leave_swap")
      .$$("from_employee")
      .define("options", data);
    $$("bookings-page").$$("leave_swap").$$("from_employee").refresh();
    $$("bookings-page")
      .$$("leave_swap")
      .$$("swap_employee")
      .define("options", data);
    $$("bookings-page").$$("leave_swap").$$("swap_employee").refresh();
  });
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
