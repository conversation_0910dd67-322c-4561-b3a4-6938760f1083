let dayWorkRequests = (function () {
  let pageNo = 0;
  let noOfPages = 1;
  let totalRecords = 0;
  function initApplication() {
    eventHandlers();
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page").$$("day_work_requests").$$("status_filter").setValue(2);
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("btn_prev_page")
      .attachEvent("onItemClick", function (id, e) {
        if (pageNo > 0) {
          let roster = $$("bookings-page")
            .$$("day_work_requests")
            .$$("roster_filter")
            .getText();
          let status = $$("bookings-page")
            .$$("day_work_requests")
            .$$("status_filter")
            .getText();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("selected_request")
            .define("template", "Selected Request: None");
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("selected_request")
            .refresh();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("request_id")
            .setValue("");
          pageNo = pageNo - 1;
          loadDayWorkRequests(roster, status);
        }
      });
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("btn_next_page")
      .attachEvent("onItemClick", function (id, e) {
        if (totalRecords > 1e3 && pageNo + 1 < noOfPages) {
          let roster = $$("bookings-page")
            .$$("day_work_requests")
            .$$("roster_filter")
            .getText();
          let status = $$("bookings-page")
            .$$("day_work_requests")
            .$$("status_filter")
            .getText();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("selected_request")
            .define("template", "Selected Request: None");
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("selected_request")
            .refresh();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("request_id")
            .setValue("");
          pageNo = pageNo + 1;
          loadDayWorkRequests(roster, status);
        }
      });
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("bookings-page")
          .$$("day_work_requests")
          .$$("roster_filter")
          .getText();
        let status = $$("bookings-page")
          .$$("day_work_requests")
          .$$("status_filter")
          .getText();
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("selected_request")
          .define("template", "Selected Request: None");
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("selected_request")
          .refresh();
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("booking_id")
          .setValue("");
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("service_no")
          .setValue("");
        pageNo = 0;
        loadDayWorkRequests(roster, status);
      });
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("day_work-requests_grid")
      .attachEvent("onItemClick", function (id, e, node) {
        let selectedRow = this.getItem(id);
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("selected_request")
          .define(
            "template",
            "Selected Request: <strong>" +
              selectedRow.leave_type_code +
              "</strong> for " +
              selectedRow.employee,
          );
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("selected_request")
          .refresh();
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("booking_id")
          .setValue(selectedRow.booking_id);
        $$("bookings-page")
          .$$("day_work_requests")
          .$$("service_no")
          .setValue(selectedRow.service_no);
        if (
          selectedRow.status ==
            "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>" ||
          selectedRow.status ==
            "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>"
        ) {
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("btn_approve")
            .disable();
          $$("bookings-page").$$("day_work_requests").$$("btn_deny").disable();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("wfr_btn_approve")
            .disable();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("wfr_btn_deny")
            .disable();
        } else {
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("btn_approve")
            .enable();
          $$("bookings-page").$$("day_work_requests").$$("btn_deny").enable();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("wfr_btn_approve")
            .enable();
          $$("bookings-page")
            .$$("day_work_requests")
            .$$("wfr_btn_deny")
            .enable();
        }
      });
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("btn_approve")
      .attachEvent("onItemClick", function (id, e) {
        let requestBookingId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("booking_id")
          .getValue();
        let row_pay_id = $$("bookings-page")
          .$$("day_work_requests")
          .$$("service_no")
          .getValue();
        let request_comments = $$("bookings-page")
          .$$("day_work_requests")
          .$$("request_comments")
          .getValue();
        let rowId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getSelectedId();
        let selectedRow = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getItem(rowId);
        let row_type = selectedRow.type;
        let row_start_date = moment(selectedRow.start_date).format(
          "DD/MM/YYYY H:mm",
        );
        let row_end_date = moment(selectedRow.end_date).format(
          "DD/MM/YYYY H:mm",
        );
        if (
          selectedRow.approved_by != "" &&
          selectedRow.approved_by != null &&
          selectedRow.approved_by != undefined
        ) {
          webix.alert({
            text: "The selected Day Work roster has already been approved by a supervisor!",
            width: 350,
          });
        } else {
          updateRequestStatus(
            requestBookingId,
            row_pay_id,
            request_comments,
            false,
            "Pending",
            function (response) {
              if (response == "ok") {
                if (live_site === true) {
                  getEmployeeData(row_pay_id, function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Day Work (Pre-Approved)",
                      "Day Work roster " +
                        row_type +
                        " has been <b>Pre-Approved</b> for " +
                        values[0].first_name +
                        " " +
                        properSurname +
                        " from " +
                        row_start_date +
                        " to " +
                        row_end_date +
                        " by " +
                        user_logged_in +
                        " on " +
                        moment().format("DD/MM/YYYY H:mm") +
                        "",
                      "Comment: " + request_comments,
                      values[0].first_name + " " + properSurname,
                      "The above Day Work roster has been pre-approved by your supervisor and has been forwarded to Workforce Rostering for final approval!",
                      "Regards, The SAPPHIRE Team",
                    );
                  });
                }
              }
            },
          );
        }
      });
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("wfr_btn_approve")
      .attachEvent("onItemClick", function (id, e) {
        let requestBookingId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("booking_id")
          .getValue();
        let row_pay_id = $$("bookings-page")
          .$$("day_work_requests")
          .$$("service_no")
          .getValue();
        let request_comments = $$("bookings-page")
          .$$("day_work_requests")
          .$$("wfr_request_comments")
          .getValue();
        let rowId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getSelectedId();
        let selectedRow = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getItem(rowId);
        let row_type = selectedRow.type;
        let row_start_date = moment(selectedRow.start_date).format(
          "DD/MM/YYYY H:mm",
        );
        let row_end_date = moment(selectedRow.end_date).format(
          "DD/MM/YYYY H:mm",
        );
        if (
          selectedRow.approved_by == "" ||
          selectedRow.approved_by == null ||
          selectedRow.approved_by == undefined
        ) {
          webix.alert({
            text: "You can't approve a Day Work roster until it has been pre-approved by a supervisor first!",
            width: 350,
          });
        } else {
          updateRequestStatus(
            requestBookingId,
            row_pay_id,
            request_comments,
            true,
            "Approved",
            function (response) {
              if (response == "ok") {
                if (live_site === true) {
                  getEmployeeData(row_pay_id, function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Day Work (Approved)",
                      "Day Work roster " +
                        row_type +
                        " has been <b>Approved</b> for " +
                        values[0].first_name +
                        " " +
                        properSurname +
                        " from " +
                        row_start_date +
                        " to " +
                        row_end_date +
                        " by " +
                        user_logged_in +
                        " on " +
                        moment().format("DD/MM/YYYY H:mm") +
                        "",
                      "Comment: " + request_comments,
                      values[0].first_name + " " + properSurname,
                      "The above Day Work roster has been approved by Workforce Rostering!",
                      "Regards, The SAPPHIRE Team",
                    );
                  });
                }
              }
            },
          );
        }
      });
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("btn_deny")
      .attachEvent("onItemClick", function (id, e) {
        let requestBookingId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("booking_id")
          .getValue();
        let row_pay_id = $$("bookings-page")
          .$$("day_work_requests")
          .$$("service_no")
          .getValue();
        let request_comments = $$("bookings-page")
          .$$("day_work_requests")
          .$$("request_comments")
          .getValue();
        let rowId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getSelectedId();
        let selectedRow = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getItem(rowId);
        let row_type = selectedRow.type;
        let row_start_date = moment(selectedRow.start_date).format(
          "DD/MM/YYYY H:mm",
        );
        let row_end_date = moment(selectedRow.end_date).format(
          "DD/MM/YYYY H:mm",
        );
        let ext_message = "";
        updateRequestStatus(
          requestBookingId,
          row_pay_id,
          request_comments,
          false,
          "Denied",
          function (response) {
            if (response == "ok") {
              if (live_site === true) {
                if (request_comments != "") {
                  ext_message = "Comment: " + request_comments;
                } else {
                  ext_message = "Note: Day Work roster was denied!";
                }
                getEmployeeData(row_pay_id, function (values) {
                  let properSurname = toProperCase(values[0].surname);
                  sendEmail(
                    "SAPPHIRE<<EMAIL>>",
                    values[0].notifications_email,
                    "RE: Day Work (Denied)",
                    "Day Work roster " +
                      row_type +
                      " was <b>Denied</b> for " +
                      values[0].first_name +
                      " " +
                      properSurname +
                      " from " +
                      row_start_date +
                      " to " +
                      row_end_date +
                      " by " +
                      user_logged_in +
                      " on " +
                      moment().format("DD/MM/YYYY H:mm") +
                      "",
                    ext_message,
                    values[0].first_name + " " + properSurname,
                    "Please contact your supervisor for any queries.",
                    "Regards, The SAPPHIRE Team",
                  );
                });
              }
            }
          },
        );
      });
    $$("bookings-page")
      .$$("day_work_requests")
      .$$("wfr_btn_deny")
      .attachEvent("onItemClick", function (id, e) {
        let requestBookingId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("booking_id")
          .getValue();
        let row_pay_id = $$("bookings-page")
          .$$("day_work_requests")
          .$$("service_no")
          .getValue();
        let request_comments = $$("bookings-page")
          .$$("day_work_requests")
          .$$("wfr_request_comments")
          .getValue();
        let rowId = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getSelectedId();
        let selectedRow = $$("bookings-page")
          .$$("day_work_requests")
          .$$("day_work-requests_grid")
          .getItem(rowId);
        let row_type = selectedRow.type;
        let row_start_date = moment(selectedRow.start_date).format(
          "DD/MM/YYYY H:mm",
        );
        let row_end_date = moment(selectedRow.end_date).format(
          "DD/MM/YYYY H:mm",
        );
        let ext_message = "";
        if (
          selectedRow.approved_by == "" ||
          selectedRow.approved_by == null ||
          selectedRow.approved_by == undefined
        ) {
          webix.alert({
            text: "You can't deny a Day Work roster until it has been actioned by a supervisor first!",
            width: 350,
          });
        } else {
          updateRequestStatus(
            requestBookingId,
            row_pay_id,
            request_comments,
            true,
            "Denied",
            function (response) {
              if (response == "ok") {
                if (live_site === true) {
                  if (request_comments != "") {
                    ext_message = "Comment: " + request_comments;
                  } else {
                    ext_message = "Note: Day Work roster was denied!";
                  }
                  getEmployeeData(row_pay_id, function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Day Work (Denied)",
                      "Day Work roster " +
                        row_type +
                        " was <b>Denied</b> for " +
                        values[0].first_name +
                        " " +
                        properSurname +
                        " from " +
                        row_start_date +
                        " to " +
                        row_end_date +
                        " by " +
                        user_logged_in +
                        " on " +
                        moment().format("DD/MM/YYYY H:mm") +
                        "",
                      ext_message,
                      values[0].first_name + " " + properSurname,
                      "Please contact Workforce Rostering if you have any queries.",
                      "Regards, The SAPPHIRE Team",
                    );
                  });
                }
              }
            },
          );
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page")
      .$$("day_work_requests")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function updateRequestStatus(
    bookingId,
    payId,
    requestComments,
    wfr,
    newStatus,
    callback,
  ) {
    let roster = $$("bookings-page")
      .$$("day_work_requests")
      .$$("roster_filter")
      .getText();
    let status = $$("bookings-page")
      .$$("day_work_requests")
      .$$("status_filter")
      .getText();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/bookings/day_work_requests",
        {
          booking_id: bookingId,
          pay_id: payId,
          approved_denied_by: user_logged_in,
          request_comments: requestComments,
          wfr: wfr,
          status: newStatus,
        },
        {
          error: function (err) {},
          success: function (results) {
            webix.alert("Day Work roster status changed successfully!");
            $$("bookings-page")
              .$$("day_work_requests")
              .$$("selected_request")
              .define("template", "Selected Request: None");
            $$("bookings-page")
              .$$("day_work_requests")
              .$$("selected_request")
              .refresh();
            $$("bookings-page")
              .$$("day_work_requests")
              .$$("booking_id")
              .setValue("");
            $$("bookings-page")
              .$$("day_work_requests")
              .$$("service_no")
              .setValue("");
            $$("bookings-page")
              .$$("day_work_requests")
              .$$("request_comments")
              .setValue("");
            $$("bookings-page")
              .$$("day_work_requests")
              .$$("wfr_request_comments")
              .setValue("");
            loadDayWorkRequests(roster, status);
            callback("ok");
            if (ro_view_showing == true) {
              availabilityReport.loadReport(
                $$("schedule-page").$$("grid_ro_view_report"),
                function (callback) {
                  if (callback == "ok") {
                    functionsPopup.closePopup();
                    schedule.reload_roster();
                  }
                },
              );
            } else {
              functionsPopup.closePopup();
              schedule.reload_roster();
            }
          },
        },
      );
  }
  function loadDayWorkRequests(roster, status) {
    let grid = $$("bookings-page")
      .$$("day_work_requests")
      .$$("day_work-requests_grid");
    let grid_info = grid.getState();
    let sort_order = grid_info.sort;
    let perPage = 1e3;
    let pageLabel = "";
    let supervisor = 0;
    $$("loader-window").show();
    grid.clearAll();
    if (user_permission_level > 2) {
      supervisor = user_logged_in;
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/day_work_requests",
        {
          roster: roster,
          status: status,
          pageNo: pageNo,
          perPage: perPage,
          supervisor: supervisor,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            if (results != "[]") {
              let values = JSON.parse(results);
              let status_icon = "";
              let middleName = "";
              let start_date_format = "";
              let end_date_format = "";
              let created_date_format = "";
              let approved_date_format = "";
              let wfr_approved_date_format = "";
              let supervisorName = "";
              totalRecords = values.length;
              noOfPages = Math.ceil(totalRecords / perPage);
              if (totalRecords < perPage) {
                pageLabel = "Page 1/1";
              } else {
                pageLabel = "Page " + Math.ceil(pageNo + 1) + "/" + noOfPages;
              }
              if (totalRecords > perPage) {
                $$("bookings-page")
                  .$$("day_work_requests")
                  .$$("search_count")
                  .define(
                    "template",
                    "Showing " +
                      perPage +
                      " records from " +
                      totalRecords +
                      " total requests",
                  );
              } else {
                $$("bookings-page")
                  .$$("day_work_requests")
                  .$$("search_count")
                  .define(
                    "template",
                    "Showing " +
                      totalRecords +
                      " records from " +
                      totalRecords +
                      " total requests",
                  );
              }
              $$("bookings-page")
                .$$("day_work_requests")
                .$$("search_count")
                .refresh();
              $$("bookings-page")
                .$$("day_work_requests")
                .$$("page_no")
                .setValue(pageLabel);
              values.forEach(function (result) {
                if (result.status == "Pending") {
                  if (
                    result.approved_denied_by == null ||
                    result.approved_denied_by == "" ||
                    result.approved_denied_by == undefined
                  ) {
                    status_icon =
                      "<span class = 'approved_pending webix_icon fas fa-clock'></span>";
                  } else {
                    status_icon =
                      "<span class = 'approved_pending_partial webix_icon fas fa-clock'></span>";
                  }
                } else if (result.status == "Approved") {
                  status_icon =
                    "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>";
                } else if (result.status == "Denied") {
                  status_icon =
                    "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>";
                }
                if (result.middle_name === null) {
                  middleName = "";
                } else {
                  middleName = result.middle_name;
                }
                start_date_format = moment(
                  result.start_date,
                  "DD/MM/YYYY HH:mm",
                ).toDate();
                end_date_format = moment(
                  result.end_date,
                  "DD/MM/YYYY HH:mm",
                ).toDate();
                created_date_format = moment(
                  result.created_date,
                  "DD/MM/YYYY HH:mm",
                ).toDate();
                if (result.approved_denied_date == "Invalid date") {
                  approved_date_format = "";
                } else {
                  approved_date_format = moment(
                    result.approved_denied_date,
                    "DD/MM/YYYY HH:mm",
                  ).toDate();
                }
                if (result.wfr_approved_denied_date == "Invalid date") {
                  wfr_approved_date_format = "";
                } else {
                  wfr_approved_date_format = moment(
                    result.wfr_approved_denied_date,
                    "DD/MM/YYYY HH:mm",
                  ).toDate();
                }
                supervisorName =
                  result.sup_first_name + " " + result.sup_surname;
                grid.add({
                  booking_id: result.booking_id,
                  service_no: result.pay_id,
                  employee:
                    result.surname +
                    ", " +
                    result.first_name +
                    " " +
                    middleName,
                  roster: result.roster,
                  shift: result.shift,
                  location: result.location,
                  start_date: start_date_format,
                  end_date: end_date_format,
                  hours: result.hours,
                  leave_type_code: result.leave_type_code,
                  status: status_icon,
                  supervisor: supervisorName,
                  requested_date: created_date_format,
                  approved_date: approved_date_format,
                  approved_by: result.approved_denied_by,
                  wfr_approved_date: wfr_approved_date_format,
                  wfr_approved_by: result.wfr_approved_denied_by,
                });
              });
              if (sort_order != undefined) {
                if (
                  sort_order.id == "start_date" ||
                  sort_order.id == "end_date" ||
                  sort_order.id == "requested_date" ||
                  sort_order.id == "approved_date"
                ) {
                  grid.sort(sort_order.id, sort_order.dir, "date");
                } else {
                  grid.sort(sort_order.id, sort_order.dir);
                }
              }
              grid.adjustColumn("employee");
              grid.adjustColumn("roster");
              grid.adjustColumn("shift");
              grid.adjustColumn("location");
            } else {
              $$("bookings-page")
                .$$("day_work_requests")
                .$$("search_count")
                .define("template", "0 requests found");
              $$("bookings-page")
                .$$("day_work_requests")
                .$$("search_count")
                .refresh();
            }
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
