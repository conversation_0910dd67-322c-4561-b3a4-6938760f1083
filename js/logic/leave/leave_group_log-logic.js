let leaveGroupsLogs = (function () {
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster;
  let exp_shift;
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("bookings-page").$$("leave_log").$$("from_date").setValue(startOfMonth);
    $$("bookings-page").$$("leave_log").$$("to_date").setValue(endOfMonth);
    $$("bookings-page")
      .$$("leave_log")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page")
      .$$("leave_log")
      .$$("shift_filter")
      .setValue("-- All Shifts --");
    $$("bookings-page").$$("leave_log").$$("status_filter").setValue(1);
    grid = $$("bookings-page").$$("leave_log").$$("leave-logs_grid");
    let defaultHandler = grid.$exportView;
    grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "RRL Report for " +
            exp_roster +
            " + (" +
            exp_shift +
            ") between " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Leave Group Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("leave_log")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("bookings-page")
          .$$("leave_log")
          .$$("from_date")
          .getValue();
        exp_toDate = $$("bookings-page")
          .$$("leave_log")
          .$$("to_date")
          .getValue();
        exp_roster = $$("bookings-page")
          .$$("leave_log")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("bookings-page")
          .$$("leave_log")
          .$$("shift_filter")
          .getText();
        let filename =
          "Leave Group Logs (" +
          moment(exp_fromDate).format("DD-MM-YYYY") +
          " - " +
          moment(exp_toDate).format("DD-MM-YYYY") +
          ")";
        if (grid.count() > 0) {
          webix.toExcel(grid, {
            filename: filename,
            styles: true,
            heights: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("bookings-page")
      .$$("leave_log")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("bookings-page")
          .$$("leave_log")
          .$$("roster_filter")
          .getText();
        let shift = $$("bookings-page")
          .$$("leave_log")
          .$$("shift_filter")
          .getText();
        let status = $$("bookings-page")
          .$$("leave_log")
          .$$("status_filter")
          .getText();
        let startDate = $$("bookings-page")
          .$$("leave_log")
          .$$("from_date")
          .getValue();
        let endDate = $$("bookings-page")
          .$$("leave_log")
          .$$("to_date")
          .getValue();
        listRRLBookings(roster, shift, status, startDate, endDate);
      });
    $$("bookings-page")
      .$$("leave_log")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv);
        if (newv == "-- All Rosters --") {
          $$("bookings-page").$$("leave_log").$$("shift_filter").hide();
        } else {
          $$("bookings-page").$$("leave_log").$$("shift_filter").show();
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("leave_log").$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let shiftOptions = [];
                shiftOptions.push("-- All Shifts --");
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                $$("bookings-page")
                  .$$("leave_log")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("bookings-page")
                  .$$("leave_log")
                  .$$("shift_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function listRRLBookings(roster, shift, status, fromDate, toDate) {
    let grid = $$("bookings-page").$$("leave_log").$$("leave-logs_grid");
    grid.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/rrl_bookings",
        { roster: roster, shift: shift, status: status, type: "RRL" },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let values = JSON.parse(results);
            let delete_icon = "";
            let employeeName = "";
            let startDate = "";
            let returnDate = "";
            values.forEach(function (result) {
              if (result.deleted == true) {
                delete_icon =
                  "<span class = 'delete_rrl_request_true webix_icon fas fa-minus-circle'></span>";
              } else {
                delete_icon = "";
              }
              if (result.middle_name == null) {
                employeeName = result.surname + ", " + result.first_name;
              } else {
                employeeName =
                  result.surname +
                  ", " +
                  result.first_name +
                  " " +
                  result.middle_name;
              }
              if (result.rrl_week_no == 1) {
                startDate = result.booking_first_date;
                returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 2) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
                returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 3) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
                returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 4) {
                startDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
                returnDate = moment(result.booking_first_date, "DD/MM/YYYY")
                  .add(32, "days")
                  .format("DD/MM/YYYY");
              }
              if (
                moment(startDate, "DD/MM/YYYY 08:00").isSameOrAfter(
                  moment(fromDate, "YYYY-MM-DD HH:mm"),
                ) &&
                moment(startDate, "DD/MM/YYYY 08:00").isSameOrBefore(
                  moment(toDate, "YYYY-MM-DD HH:mm"),
                )
              ) {
                grid.add({
                  booking_id: result.booking_id,
                  service_no: result.pay_id,
                  employee: employeeName,
                  rank: result.curr_rank,
                  roster: result.roster,
                  shift: result.shift,
                  location: result.location,
                  start_date: startDate,
                  end_date: returnDate,
                  week_no: result.rrl_week_no,
                  length: result.total_days,
                  type: "RRL",
                  created_date: result.created_date,
                  deleted_by: result.deleted_by,
                  deleted_date: result.date_deleted,
                });
              }
            });
            if (status == "Deleted") {
              grid.showColumn("deleted_by");
              grid.showColumn("deleted_date");
            } else {
              grid.hideColumn("deleted_by");
              grid.hideColumn("deleted_date");
            }
            grid.adjustColumn("employee", "data");
            grid.adjustColumn("roster", "data");
            grid.adjustColumn("shift", "data");
            grid.adjustColumn("location", "data");
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
