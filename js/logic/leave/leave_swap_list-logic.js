let leaveSwapList = (function () {
  function initApplication() {
    eventHandlers();
    $$("bookings-page")
      .$$("leave_swap_list")
      .$$("from_date")
      .setValue(new Date());
    $$("bookings-page")
      .$$("leave_swap_list")
      .$$("to_date")
      .setValue(webix.Date.add(new Date(), 1, "day"));
    $$("bookings-page")
      .$$("leave_swap_list")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page").$$("leave_swap_list").$$("status_filter").setValue(4);
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("leave_swap_list")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("bookings-page")
          .$$("leave_swap_list")
          .$$("roster_filter")
          .getText();
        let status = $$("bookings-page")
          .$$("leave_swap_list")
          .$$("status_filter")
          .getText();
        let startDate = $$("bookings-page")
          .$$("leave_swap_list")
          .$$("from_date")
          .getValue();
        let endDate = $$("bookings-page")
          .$$("leave_swap_list")
          .$$("to_date")
          .getValue();
        listRRLSwaps(roster, status, startDate, endDate);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("leave_swap_list").$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function listRRLSwaps(roster, status, startDate, endDate) {
    let grid = $$("bookings-page")
      .$$("leave_swap_list")
      .$$("leave-swap-list_grid");
    grid.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/rrl_swaps",
        {
          roster: roster,
          status: status,
          start_date: startDate,
          end_date: endDate,
          type: "RRL",
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let values = JSON.parse(results);
            let status_icon = "";
            let startDate = moment();
            let returnDate = moment();
            let employeeName = "";
            let weekNo = "";
            let swapRoster = "";
            let swapShift = "";
            let swapLocation = "";
            let swap_employeeName = "";
            let swap_startDate = "";
            let swap_returnDate = "";
            let swapWeekNo = "";
            let swap_group = "";
            values.forEach(function (result) {
              if (result.status == "Pending") {
                status_icon =
                  "<span class = 'approved_pending webix_icon fas fa-clock'></span>";
              } else if (result.status == "Approved") {
                status_icon =
                  "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>";
              } else if (result.status == "Denied") {
                status_icon =
                  "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>";
              }
              if (result.rrl_week_no == 1) {
                startDate = result.booking_first_date[0];
                returnDate = moment(result.booking_first_date[0], "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 2) {
                startDate = moment(result.booking_first_date[0], "DD/MM/YYYY")
                  .add(8, "days")
                  .format("DD/MM/YYYY");
                returnDate = moment(result.booking_first_date[0], "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 3) {
                startDate = moment(result.booking_first_date[0], "DD/MM/YYYY")
                  .add(16, "days")
                  .format("DD/MM/YYYY");
                returnDate = moment(result.booking_first_date[0], "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_week_no == 4) {
                startDate = moment(result.booking_first_date[0], "DD/MM/YYYY")
                  .add(24, "days")
                  .format("DD/MM/YYYY");
                returnDate = moment(result.booking_first_date[0], "DD/MM/YYYY")
                  .add(32, "days")
                  .format("DD/MM/YYYY");
              }
              if (result.rrl_swap_week_no == 1) {
                swap_startDate = result.booking_first_date[1];
                swap_returnDate = moment(
                  result.booking_first_date[1],
                  "DD/MM/YYYY",
                )
                  .add(8, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_swap_week_no == 2) {
                swap_startDate = moment(
                  result.booking_first_date[1],
                  "DD/MM/YYYY",
                )
                  .add(8, "days")
                  .format("DD/MM/YYYY");
                swap_returnDate = moment(
                  result.booking_first_date[1],
                  "DD/MM/YYYY",
                )
                  .add(16, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_swap_week_no == 3) {
                swap_startDate = moment(
                  result.booking_first_date[1],
                  "DD/MM/YYYY",
                )
                  .add(16, "days")
                  .format("DD/MM/YYYY");
                swap_returnDate = moment(
                  result.booking_first_date[1],
                  "DD/MM/YYYY",
                )
                  .add(24, "days")
                  .format("DD/MM/YYYY");
              } else if (result.rrl_swap_week_no == 4) {
                swap_startDate = moment(
                  result.booking_first_date[1],
                  "DD/MM/YYYY",
                )
                  .add(24, "days")
                  .format("DD/MM/YYYY");
                swap_returnDate = moment(
                  result.booking_first_date[1],
                  "DD/MM/YYYY",
                )
                  .add(32, "days")
                  .format("DD/MM/YYYY");
              }
              swapWeekNo = result.rrl_swap_week_no;
              if (result.surname[0] == null) {
                employeeName = "";
                weekNo = "";
                swapRoster = "";
                swapShift = "";
                swapLocation = "";
                swap_startDate = "";
                swap_returnDate = "";
                swap_group = "";
              } else {
                if (result.middle_name[0] == null) {
                  employeeName =
                    result.surname[0] + ", " + result.first_name[0];
                } else {
                  employeeName =
                    result.surname[0] +
                    ", " +
                    result.first_name[0] +
                    " " +
                    result.middle_name[0];
                }
                if (result.middle_name[1] == null) {
                  swap_employeeName =
                    result.surname[1] + ", " + result.first_name[1];
                } else {
                  swap_employeeName =
                    result.surname[1] +
                    ", " +
                    result.first_name[1] +
                    " " +
                    result.middle_name[1];
                }
                swapRoster = result.roster[1];
                swapShift = result.shift[1];
                swapLocation = result.location[1];
                swap_group = result.swap_group;
              }
              grid.add({
                booking_id: result.booking_id,
                service_no: result.pay_id,
                employee: employeeName,
                original_start_date: startDate,
                original_end_date: returnDate,
                week_no: result.rrl_week_no,
                roster: result.roster[0],
                shift: result.shift[0],
                location: result.location[0],
                swap_employee: swap_employeeName,
                swap_group: swap_group,
                swap_start_date: swap_startDate,
                swap_end_date: swap_returnDate,
                swap_week_no: swapWeekNo,
                swap_roster: swapRoster,
                swap_shift: swapShift,
                swap_location: swapLocation,
                status: status_icon,
              });
            });
            grid.adjustColumn("employee", "data");
            grid.adjustColumn("swap_employee", "data");
            grid.adjustColumn("roster", "data");
            grid.adjustColumn("shift", "data");
            grid.adjustColumn("location", "data");
            grid.adjustColumn("swap_roster", "data");
            grid.adjustColumn("swap_shift", "data");
            grid.adjustColumn("swap_location", "data");
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
