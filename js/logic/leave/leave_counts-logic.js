let leaveCounts = (function () {
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("from")
      .setValue(startOfMonth);
    $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("to")
      .setValue(endOfMonth);
    $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("rosters")
      .setValue("Metro");
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv);
      });
    $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let shift = $$("bookings-page")
          .$$("booking_leave_counts")
          .$$("shift")
          .getText();
        if (shift == "") {
          webix.alert("You must select a Shift!");
        } else {
          getLeaveCounts();
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("booking_leave_counts").$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                $$("bookings-page")
                  .$$("booking_leave_counts")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("bookings-page")
                  .$$("booking_leave_counts")
                  .$$("shift")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function getLeaveCounts() {
    $$("loader-window").show();
    let grid = $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("leave_counts_grid");
    let fromDate = $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("from")
      .getValue();
    let toDate = $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("to")
      .getValue();
    let roster = $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("rosters")
      .getText();
    let shift = $$("bookings-page")
      .$$("booking_leave_counts")
      .$$("shift")
      .getText();
    let ranks = "";
    let leave_types = "";
    grid.clearAll();
    if (moment(toDate).isAfter(fromDate)) {
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/bookings/leave_counts",
          {
            from_date: fromDate,
            to_date: toDate,
            roster: roster,
            shift: shift,
          },
          {
            error: function (err) {},
            success: function (result) {
              let values = JSON.parse(result);
              let alternate = 0;
              let getId = 0;
              let count = 0;
              for (let x = 0; x < values.length; x++) {
                ranks = "CMD/SO";
                leave_types = "ARL/PHOL/RET/TOIL/SOIL/UPHL/URET";
                grid.add({
                  date: moment(values[x].date_string, "YYYYMMDD").format(
                    "DD/MM/YYYY",
                  ),
                  ranks: ranks,
                  leave_types: leave_types,
                  booked: values[x].booked_cmd_arl,
                  pending: values[x].pending_cmd_arl,
                });
                ranks = "CMD/SO";
                leave_types = "LSL/LSLS/ULSL/LSLH";
                grid.add({
                  date: moment(values[x].date_string, "YYYYMMDD").format(
                    "DD/MM/YYYY",
                  ),
                  ranks: ranks,
                  leave_types: leave_types,
                  booked: values[x].booked_cmd_lsl,
                  pending: values[x].pending_cmd_lsl,
                });
                count = grid.data.count();
                getId = grid.getIdByIndex(count - 2);
                grid.addSpan(getId, "ranks", 1, 2);
                ranks = "SCOP/COP/COFF";
                leave_types = "ARL/PHOL/RET/TOIL/SOIL/UPHL/URET";
                grid.add({
                  date: moment(values[x].date_string, "YYYYMMDD").format(
                    "DD/MM/YYYY",
                  ),
                  ranks: ranks,
                  leave_types: leave_types,
                  booked: values[x].booked_scop_arl,
                  pending: values[x].pending_scop_arl,
                });
                ranks = "SCOP/COP/COFF";
                leave_types = "LSL/LSLS/ULSL/LSLH";
                grid.add({
                  date: moment(values[x].date_string, "YYYYMMDD").format(
                    "DD/MM/YYYY",
                  ),
                  ranks: ranks,
                  leave_types: leave_types,
                  booked: values[x].booked_scop_lsl,
                  pending: values[x].pending_scop_lsl,
                });
                count = grid.data.count();
                getId = grid.getIdByIndex(count - 2);
                grid.addSpan(getId, "ranks", 1, 2);
                ranks = "SFF/FF/MOFF/MOP";
                leave_types = "ARL/PHOL/RET/TOIL/SOIL/UPHL/URET";
                grid.add({
                  date: moment(values[x].date_string, "YYYYMMDD").format(
                    "DD/MM/YYYY",
                  ),
                  ranks: ranks,
                  leave_types: leave_types,
                  booked: values[x].booked_ff_arl,
                  pending: values[x].pending_ff_arl,
                });
                ranks = "SFF/FF/MOFF/MOP";
                leave_types = "LSL/LSLS/ULSL/LSLH";
                grid.add({
                  date: moment(values[x].date_string, "YYYYMMDD").format(
                    "DD/MM/YYYY",
                  ),
                  ranks: ranks,
                  leave_types: leave_types,
                  booked: values[x].booked_sff_lsl,
                  pending: values[x].pending_sff_lsl,
                });
                count = grid.data.count();
                getId = grid.getIdByIndex(count - 2);
                grid.addSpan(getId, "ranks", 1, 2);
                count = grid.data.count();
                getId = grid.getIdByIndex(count - 6);
                if (alternate === 0) {
                  grid.addSpan(getId, "date", 1, 6, null, "leave_count_grid_1");
                  alternate = 1;
                } else {
                  grid.addSpan(getId, "date", 1, 6, null, "leave_count_grid_2");
                  alternate = 0;
                }
              }
              grid.refresh();
              $$("loader-window").hide();
            },
          },
        );
    } else {
      webix.alert("The 'To Date' cannot be equal to or before the 'From Date'");
      $$("loader-window").hide();
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
