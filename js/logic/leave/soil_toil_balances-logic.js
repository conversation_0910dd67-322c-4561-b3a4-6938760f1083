let soilToilBalances = (function () {
  let selectedPayId = 0;
  let selectedEmp = "";
  let filter_text = "";
  let filter_id = "";
  let loadIndex = 1;
  let mode = "Save";
  let adj_type = "";
  let exp_grid;
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "Arial", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
    3: { font: { name: "Arial", sz: 10, bold: true } },
  };
  function initApplication() {
    eventHandlers();
    exp_grid = $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("grid-soil_toil_balances");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report Date: " + moment().format("ddd, DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Leave Balances (SOIL / TOIL / PHOL)",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "SOIL/TOIL/PHOL Balances (" + moment().format("DD-MM-YYYY") + ")",
            styles: true,
            heights: true,
            ignore: { edit: true },
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("import_uploader")
      .attachEvent("onBeforeFileAdd", function (upload) {
        $$("loader-window").show();
        let sheet = $$("bookings-page")
          .$$("soil_toil_balances")
          .$$("excel_import");
        sheet.clearAll();
        sheet.parse(upload.file, "excel");
        loadIndex = 1;
        return false;
      });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("excel_import")
      .attachEvent("onAfterLoad", function () {
        if (loadIndex == 1) {
          $$("bookings-page")
            .$$("soil_toil_balances")
            .$$("import_sheet")
            .enable();
        }
        loadIndex += 1;
        $$("loader-window").hide();
      });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("clear_sheet")
      .attachEvent("onItemClick", function (id, e) {
        $$("bookings-page")
          .$$("soil_toil_balances")
          .$$("excel_import")
          .clearAll();
        $$("bookings-page")
          .$$("soil_toil_balances")
          .$$("import_sheet")
          .disable();
      });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("import_sheet")
      .attachEvent("onItemClick", function (id, e) {
        let leave_type = $$("bookings-page")
          .$$("soil_toil_balances")
          .$$("soil_toil_radio")
          .getValue();
        importSOILTOILRecords(leave_type);
      });
    $$("grid-soil_toil_logs").attachEvent("onItemClick", function (id, e) {
      let type = $$("soil_toil_radio").getValue();
      $$("st_new_record_label").define(
        "label",
        "<span style='font-size: 16px; color: dimgrey'>New Balance Adjustment for <strong>" +
          type +
          "</strong></span>",
      );
      $$("st_new_record_label").refresh();
      $$("frm_soil_toil_adjustment").clear();
      $$("soil_toil_accum_date").setValue(new Date());
      $$("btn_soil_toil_adjustment_save").define("label", "Save");
      $$("btn_soil_toil_adjustment_save").refresh();
      $$("soil_toil_qty_total").hide();
      mode = "Save";
    });
    $$("btn_edit_soil_toil_entry").attachEvent("onItemClick", function (id) {
      let grid = $$("grid-soil_toil_logs");
      let selected_row = grid.getSelectedItem();
      let type = $$("soil_toil_radio").getValue();
      if (grid.count() > 0) {
        if (selected_row != undefined) {
          $$("frm_soil_toil_adjustment").setValues({
            soil_toil_id: selected_row.id,
            soil_toil_qty_accum: selected_row.accum_hours,
            soil_toil_qty_actual: selected_row.actual_hours,
            soil_toil_qty_total: selected_row.total_hours,
            soil_toil_accum_date: moment(
              selected_row.accumulated_date,
              "DD/MM/YYYY",
            ).toDate(),
            soil_toil_adjustment_comments: selected_row.comments,
          });
          $$("soil_toil_qty_total").show();
          $$("st_new_record_label").define(
            "label",
            "<span style='font-size: 16px; color: dimgrey'>Edit Selected <strong>" +
              type +
              "</strong> Log Entry Details</span>",
          );
          $$("st_new_record_label").refresh();
          $$("btn_soil_toil_adjustment_save").define("label", "Update");
          $$("btn_soil_toil_adjustment_save").refresh();
          mode = "Update";
          adj_type = selected_row.adjustment_type;
        } else {
          webix.alert("No log entry selected!");
        }
      } else {
        webix.alert("There are no log entries to edit!");
      }
    });
    $$("btn_delete_soil_toil_entry").attachEvent("onItemClick", function (id) {
      if (user_permission_level === 1 || user_permission_level === 2) {
        let grid = $$("grid-soil_toil_logs");
        if (grid.count() > 0) {
          let row_id = grid.getSelectedId();
          let rowInfo = grid.getItem(row_id);
          let newValue = $$("soil_toil_radio").getValue();
          if (row_id != undefined) {
            webix.confirm({
              title: "Delete Log Entry",
              ok: "Yes",
              cancel: "No",
              width: 500,
              text: "Are you sure you want to delete the selected Log Entry?",
              callback: function (result) {
                switch (result) {
                  case true:
                    webix
                      .ajax()
                      .headers({ Authorization: "Bearer " + api_key })
                      .del(
                        server_url + "/admin/delete_soil_toil_log_entry",
                        { id: rowInfo.id, pay_id: selectedPayId },
                        {
                          error: function (err) {
                            webix.alert(
                              "There was an error deleting the selected entry!",
                            );
                          },
                          success: function () {
                            grid.clearAll();
                            getRecord(selectedPayId, selectedEmp, newValue);
                          },
                        },
                      );
                }
              },
            });
          } else {
            webix.alert("No log entry selected!");
          }
        } else {
          webix.alert("There are no log entries to edit!");
        }
      } else {
        webix.alert(
          "You don't have permission to 'Delete' any SOIL or TOIL log entries!",
        );
      }
    });
    $$("btn_soil_toil_adjustment_save").attachEvent(
      "onItemClick",
      function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          if (mode === "Save") {
            saveSoilToil();
          } else if (mode === "Update") {
            updateSoilToilRecord();
          }
        } else {
          webix.alert(
            "You don't have permission to Save/Edit any SOIL or TOIL log entries!",
          );
        }
      },
    );
    $$("soil_toil_radio").attachEvent("onChange", function (newv, oldv) {
      let grid = $$("grid-soil_toil_logs");
      grid.clearAll();
      getRecord(selectedPayId, selectedEmp, newv);
    });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("name_search_field")
      .attachEvent("onTimedKeyPress", function () {
        const value = this.getValue().toUpperCase();
        filter_text = this.getValue().toUpperCase();
        let filtered;
        $$("bookings-page")
          .$$("soil_toil_balances")
          .$$("grid-soil_toil_balances")
          .filter(function (obj) {
            if (obj.employee.indexOf(value) !== -1)
              filtered = obj.employee.indexOf(value) !== -1;
            return obj.employee.indexOf(value) !== -1;
          });
        if (!filtered) webix.message("No matching records!");
      });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("pay_id_search_field")
      .attachEvent("onTimedKeyPress", function () {
        const value = this.getValue().toString();
        filter_id = this.getValue().toString();
        let filtered;
        let pid_length = value.length;
        $$("bookings-page")
          .$$("soil_toil_balances")
          .$$("grid-soil_toil_balances")
          .filter(function (obj) {
            if (
              obj.pay_id.toString().substring(0, pid_length).indexOf(value) !==
              -1
            )
              filtered =
                obj.pay_id
                  .toString()
                  .substring(0, pid_length)
                  .indexOf(value) !== -1;
            return (
              obj.pay_id.toString().substring(0, pid_length).indexOf(value) !==
              -1
            );
          });
        if (!filtered) webix.message("No matching records!");
      });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("grid-soil_toil_balances")
      .attachEvent("onItemDblClick", function (id) {
        let selected_row = $$("bookings-page")
          .$$("soil_toil_balances")
          .$$("grid-soil_toil_balances")
          .getSelectedItem();
        if (selected_row) {
          selectedPayId = selected_row.pay_id;
          selectedEmp = selected_row.employee;
          if (
            selectedPayId == user_logged_in ||
            user_permission_level === 1 ||
            user_permission_level == 2
          ) {
            $$("soil_toil_radio").setValue("SOIL");
            let grid = $$("grid-soil_toil_logs");
            grid.clearAll();
            getRecord(selectedPayId, selectedEmp, "SOIL");
          } else {
            webix.alert("You can only view your own SOIL/TOIL logs!");
          }
        }
      });
    $$("bookings-page")
      .$$("soil_toil_balances")
      .$$("grid-soil_toil_balances")
      .attachEvent("onItemClick", function (id) {
        if (id.column == "edit") {
          let selected_row = $$("bookings-page")
            .$$("soil_toil_balances")
            .$$("grid-soil_toil_balances")
            .getSelectedItem();
          if (selected_row) {
            selectedPayId = selected_row.pay_id;
            selectedEmp = selected_row.employee;
            if (
              selectedPayId == user_logged_in ||
              user_permission_level == 1 ||
              user_permission_level == 2
            ) {
              $$("soil_toil_radio").setValue("SOIL");
              let grid = $$("grid-soil_toil_logs");
              grid.clearAll();
              getRecord(selectedPayId, selectedEmp, "SOIL");
            } else {
              webix.alert("You can only view your own SOIL/TOIL logs!");
            }
          }
        }
      });
    $$("btn_soil_toil_close").attachEvent("onItemClick", function (id, e) {
      $$("frm_soil_toil_adjustment").clear();
      $$("soil_toil_accum_date").setValue(new Date());
      $$("soil_toil-popup").hide();
    });
  }
  function updateSoilToilRecord() {
    let type = $$("soil_toil_radio").getValue();
    if ($$("frm_soil_toil_adjustment").validate()) {
      let frmValues = $$("frm_soil_toil_adjustment").getValues();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .put(
          server_url + "/admin/update_soil_toil_record",
          {
            id: frmValues.soil_toil_id,
            actual_hours: frmValues.soil_toil_qty_actual,
            accum_hours: frmValues.soil_toil_qty_accum,
            total_hours: frmValues.soil_toil_qty_total,
            comments: frmValues.soil_toil_adjustment_comments,
            updated_by: user_logged_in,
            accum_date: frmValues.soil_toil_accum_date,
            adj_type: adj_type,
          },
          {
            error: function (err) {},
            success: function (result) {
              $$("st_new_record_label").define(
                "label",
                "<span style='font-size: 16px; color: dimgrey'>New Balance Adjustment for <strong>" +
                  type +
                  "</strong></span>",
              );
              $$("st_new_record_label").refresh();
              $$("frm_soil_toil_adjustment").clear();
              $$("soil_toil_accum_date").setValue(new Date());
              $$("btn_soil_toil_adjustment_save").define("label", "Save");
              $$("btn_soil_toil_adjustment_save").refresh();
              $$("soil_toil_qty_total").hide();
              mode = "Save";
              getRecord(selectedPayId, selectedEmp, type);
            },
          },
        );
    }
  }
  function importSOILTOILRecords(leave_type) {
    let sheet = $$("bookings-page").$$("soil_toil_balances").$$("excel_import");
    let SoilToilArray = [];
    let currPayId = "";
    $$("loader-window").show();
    sheet.eachRow(function (row) {
      let adj_type = "";
      let set_date = "";
      let actualHours = 0;
      let accum_hours = 0;
      const record = sheet.getItem(row);
      const SoilToilObject = {};
      if (record.data0 != "" && record.data1 != "") {
        currPayId = record.data0;
      }
      if (leave_type == "SOIL") {
        if (record.data9.includes("Deleted")) {
        } else if (record.data8 === "") {
        } else {
          if (record.data3 == "" && record.data7 != "") {
            adj_type = "Redeemed";
            set_date = record.data6;
            actualHours = -Math.abs(record.data7);
            accum_hours = -Math.abs(record.data7);
          } else if (record.data3 != "" && record.data7 == "") {
            adj_type = "Accumulated";
            set_date = record.data2;
            if (record.data3 == "") {
              if (record.data4 == 10) {
                actualHours = 4;
              } else {
                actualHours = 0;
              }
            } else {
              actualHours = Math.abs(record.data3);
            }
            if (record.data4 == "") {
              if (record.data3 < 4) {
                accum_hours = 0;
              } else {
                accum_hours = 10;
              }
            } else {
              accum_hours = Math.abs(record.data4);
            }
          }
          SoilToilObject.pay_id = currPayId;
          SoilToilObject.leave_type = "SOIL";
          SoilToilObject.adjustment_type = adj_type;
          SoilToilObject.actual_hours = actualHours;
          SoilToilObject.accum_hours = accum_hours;
          SoilToilObject.total_hours = record.data8;
          SoilToilObject.accum_date = set_date;
          SoilToilObject.createdBy = user_logged_in;
          SoilToilObject.adjustment_date = moment().add(1, "milliseconds");
          SoilToilObject.comments = record.data9;
          SoilToilObject.updated_by = user_logged_in;
          SoilToilArray.push(SoilToilObject);
        }
      } else if (leave_type == "TOIL") {
        if (record.data9.includes("DELETED")) {
        } else if (record.data8 === "") {
        } else {
          if (record.data3 == "" && record.data7 != "") {
            adj_type = "Redeemed";
            set_date = record.data6;
            actualHours = -Math.abs(record.data7);
            accum_hours = -Math.abs(record.data7);
          } else if (record.data4 != "" && record.data7 == "") {
            adj_type = "Accumulated";
            set_date = record.data2;
            if (record.data3 == "") {
              actualHours = Math.abs(record.data4);
            } else {
              actualHours = Math.abs(record.data3);
            }
            accum_hours = Math.abs(record.data4);
          }
          SoilToilObject.pay_id = currPayId;
          SoilToilObject.leave_type = "TOIL";
          SoilToilObject.adjustment_type = adj_type;
          SoilToilObject.actual_hours = actualHours;
          SoilToilObject.accum_hours = accum_hours;
          SoilToilObject.total_hours = record.data8;
          SoilToilObject.accum_date = set_date;
          SoilToilObject.createdBy = user_logged_in;
          SoilToilObject.adjustment_date = moment().add(1, "milliseconds");
          SoilToilObject.comments = record.data9;
          SoilToilObject.updated_by = user_logged_in;
          SoilToilArray.push(SoilToilObject);
        }
      }
    });
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .post(
        server_url + "/bookings/import_soil_toil_records",
        { soil_toil_array: SoilToilArray },
        {
          error: function (err) {
            $$("loader-window").hide();
            webix.alert({
              text:
                "There was an error importing the " +
                leave_type +
                " records, please try again!",
              width: 600,
            });
          },
          success: function (result) {
            $$("loader-window").hide();
            webix.alert({
              text:
                "All " + leave_type + " records were Imported successfully!",
              width: 600,
            });
          },
        },
      );
  }
  function loadSoilToilRecords(callback) {
    $$("loader-window").show();
    setTimeout(function () {
      let grid = $$("bookings-page")
        .$$("soil_toil_balances")
        .$$("grid-soil_toil_balances");
      grid.clearAll();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .sync()
        .get(
          server_url + "/admin/list_soil_toil_balances",
          {},
          {
            error: function (err) {
              $$("loader-window").hide();
            },
            success: function (results) {
              if (results) {
                let records = JSON.parse(results);
                let soil_toil_records = [];
                let employeeName = "";
                let soil_bal = 0;
                let toil_bal = 0;
                if (records.length > 0) {
                  for (let x = 0; x < records.length; x++) {
                    if (records[x].middle_name == null) {
                      employeeName =
                        records[x].surname + ", " + records[x].first_name;
                    } else {
                      employeeName =
                        records[x].surname +
                        ", " +
                        records[x].first_name +
                        " " +
                        records[x].middle_name;
                    }
                    if (records[x].soil_balance == null) {
                      soil_bal = 0;
                    } else {
                      soil_bal = records[x].soil_balance;
                    }
                    if (records[x].toil_balance == null) {
                      toil_bal = 0;
                    } else {
                      toil_bal = records[x].toil_balance;
                    }
                    if (
                      user_permission_level == 1 ||
                      user_permission_level == 2 ||
                      user_permission_level == 3
                    ) {
                      soil_toil_records.push({
                        pay_id: records[x].pay_id,
                        employee: employeeName,
                        roster: records[x].roster,
                        shift: records[x].shift,
                        location: records[x].location,
                        soil_balance: soil_bal,
                        toil_balance: toil_bal,
                      });
                    } else {
                      if (records[x].pay_id == user_logged_in) {
                        soil_toil_records.push({
                          pay_id: records[x].pay_id,
                          employee: employeeName,
                          roster: records[x].roster,
                          shift: records[x].shift,
                          location: records[x].location,
                          soil_balance: soil_bal,
                          toil_balance: toil_bal,
                        });
                      }
                    }
                  }
                  grid.define("data", soil_toil_records);
                  grid.refresh();
                  $$("loader-window").hide();
                  callback("loaded");
                } else {
                  $$("loader-window").hide();
                  callback("nothing");
                }
              } else {
                $$("loader-window").hide();
                callback("nothing");
              }
            },
          },
        );
    }, 250);
  }
  function getRecord(payId, employee, type) {
    let grid = $$("grid-soil_toil_logs");
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/load_soil_toil_logs",
        { pay_id: payId, leave_type: type },
        {
          error: function (err) {
            webix.alert("There was an error loading this record!");
          },
          success: function (results) {
            $$("soil_toil-popup").show();
            grid.clearAll();
            $$("st_record_label").define(
              "label",
              "<span class='header_font'>SOIL/TOIL/PHOL Balance Logs</span>",
            );
            $$("st_record_label").refresh();
            $$("st_employee_record_label").define(
              "label",
              "<span style='font-size: 17px; color: dimgrey'>Showing <strong>" +
                type +
                "</strong> logs for " +
                employee +
                "</span>",
            );
            $$("st_employee_record_label").refresh();
            $$("st_employee_record_label").resize();
            $$("st_new_record_label").define(
              "label",
              "<span style='font-size: 16px; color: dimgrey'>New Balance Adjustment for <strong>" +
                type +
                "</strong></span>",
            );
            $$("st_new_record_label").refresh();
            if (results) {
              let values = JSON.parse(results);
              let curr_balance = 0;
              values.forEach(function (result) {
                grid.add({
                  id: result.id,
                  leave_type: result.leave_type,
                  adjustment_date: result.adjustment_date,
                  adjustment_type: result.adjustment_type,
                  actual_hours: result.actual_hours,
                  accum_hours: result.accum_hours,
                  total_hours: result.total_hours,
                  updated_by: result.updated_by,
                  comments: result.comments,
                  accumulated_date: result.accum_date,
                });
                curr_balance = curr_balance + result.accum_hours;
              });
              grid.eachRow(function (row) {
                let record = grid.getItem(row);
                if (record.adjustment_type == "Accumulated") {
                  if (record.actual_hours < 4 && record.accum_hours == 0) {
                    grid.addCellCss(row, "actual_hours", "low_soil");
                  } else if (
                    record.actual_hours < 4 &&
                    record.accum_hours == 10
                  ) {
                    grid.addCellCss(row, "actual_hours", "enough_soil");
                  }
                  grid.addCellCss(row, "adjustment_type", "soil_type_accum");
                } else if (record.adjustment_type == "Redeemed") {
                  grid.addCellCss(row, "adjustment_type", "soil_type_redeem");
                }
              });
              $$("st_employee_balance").define(
                "label",
                "<span style='font-size: 17px; color: black'>Current " +
                  type +
                  " Balance = <strong>" +
                  curr_balance +
                  "</strong> hours</span>",
              );
              $$("st_employee_balance").refresh();
              $$("st_employee_balance").resize();
            }
          },
        },
      );
  }
  function saveSoilToil() {
    let frmValues = $$("frm_soil_toil_adjustment").getValues();
    let type = $$("soil_toil_radio").getValue();
    let last_balance = 0;
    if ($$("frm_soil_toil_adjustment").validate()) {
      getCurrBalance(type, selectedPayId, function (result) {
        if (result != "error") {
          last_balance = result.total_hours;
        }
      });
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .post(
          server_url + "/admin/add_soil_toil_adjustment",
          {
            pay_id: selectedPayId,
            adjustment_type: "Accumulated",
            leave_type: type,
            actual_hours: frmValues.soil_toil_qty_actual,
            accum_hours: frmValues.soil_toil_qty_accum,
            total_hours: last_balance + frmValues.soil_toil_qty_accum,
            comments: frmValues.soil_toil_adjustment_comments,
            updated_by: user_logged_in,
            accum_date: frmValues.soil_toil_accum_date,
          },
          {
            error: function (err) {
              webix.alert("There was an error saving this adjustment!");
            },
            success: function () {
              $$("frm_soil_toil_adjustment").clear();
              $$("soil_toil_accum_date").setValue(new Date());
              $$("soil_toil-popup").hide();
              loadSoilToilRecords(function (response) {
                if (response == "loaded") {
                  let filtered;
                  let pid_length = filter_id.length;
                  if (filter_text != "") {
                    $$("bookings-page")
                      .$$("soil_toil_balances")
                      .$$("grid-soil_toil_balances")
                      .filter(function (obj) {
                        if (obj.employee.indexOf(filter_text) !== -1)
                          filtered = obj.employee.indexOf(filter_text) !== -1;
                        return obj.employee.indexOf(filter_text) !== -1;
                      });
                  } else if (filter_id != "") {
                    $$("bookings-page")
                      .$$("soil_toil_balances")
                      .$$("grid-soil_toil_balances")
                      .filter(function (obj) {
                        if (
                          obj.pay_id
                            .toString()
                            .substring(0, pid_length)
                            .indexOf(filter_id) !== -1
                        )
                          filtered =
                            obj.pay_id
                              .toString()
                              .substring(0, pid_length)
                              .indexOf(filter_id) !== -1;
                        return (
                          obj.pay_id
                            .toString()
                            .substring(0, pid_length)
                            .indexOf(filter_id) !== -1
                        );
                      });
                  }
                }
              });
            },
          },
        );
    }
  }
  function getCurrBalance(type, payId, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/get_soil_toil_balance",
        { pay_id: payId, leave_type: type },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            if (results) {
              let value = JSON.parse(results);
              callback(value[0]);
            } else {
              callback([]);
            }
          },
        },
      );
  }
  function reverseSoilToilEntry(type, bookingId, bkDate) {
    let last_balance = 0;
    let hours = 0;
    getCurrBalance(type, selected_employee_info.pay_id, function (result) {
      if (result != "error") {
        last_balance = result.total_hours;
      }
    });
    if (type == "SOIL") {
      hours = 10;
    } else if (type == "TOIL") {
      hours = 8;
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .post(
        server_url + "/admin/add_soil_toil_adjustment",
        {
          pay_id: selected_employee_info.pay_id,
          adjustment_type: "Accumulated",
          leave_type: type,
          actual_hours: hours,
          accum_hours: hours,
          total_hours: last_balance + hours,
          comments:
            "Auto entry, from " +
            type +
            " deletion on " +
            moment(bkDate, "YYYYMMDD").format("DD/MM/YYYY"),
          updated_by: user_logged_in,
          accum_date: moment().format("YYYYMMDD"),
          linked_booking_id: bookingId,
        },
        {
          error: function (err) {},
          success: function () {
            webix.alert({
              text:
                "Note: The " +
                hours +
                " hours that was redeemed for this booking</br>has now been credited back to the " +
                type +
                " balance!",
              width: 450,
            });
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
    loadSoilToilRecords: function (response) {
      $$("bookings-page")
        .$$("soil_toil_balances")
        .$$("pay_id_search_field")
        .setValue("");
      $$("bookings-page")
        .$$("soil_toil_balances")
        .$$("name_search_field")
        .setValue("");
      loadSoilToilRecords(response);
    },
    reverseSoilToilEntry: function (type, bookingId, bkDate) {
      reverseSoilToilEntry(type, bookingId, bkDate);
    },
  };
})();
