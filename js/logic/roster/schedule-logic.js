let schedule = (function () {
    let shiftArray = [];
    let locationsArray = [];
    let gridCount = 0;
    let totals_loaded = false;
    let mouse_y = 0;
    let mouse_x = 0;

    function initApplication() {
        eventHandlers();
        load_shifts("Metro");
    }

    function eventHandlers() {


       $$("btn_ro_view").attachEvent("onItemClick", function (id, e) {
            $$("btn_load_totals").hide();
            totals_loaded = false;
            let roster = $$("schedule-page").$$("schedule_rosters").getValue();
            let shift = $$("schedule-page").$$("schedule_shifts").getValue();
            if (ro_view_showing == false) {
                let currDate = $$("schedule-page").$$("schedule_date").getValue();
                globalSelectedDate = moment(currDate).format("YYYYMMDD");
                if (shift != "-- All Shifts --" && shift != "") {
                    ro_view_showing = true;
                    $$("schedule-page").$$("schedule_display_type").setValue("8 Days");
                    if (roster == "Metro") {
                        $$("schedule-page")
                            .$$("schedule_locations")
                            .setValue("All Stations");
                    }
                    $$("schedule-page").$$("schedule_display_type").disable();
                    $$("schedule-page").$$("grid_ro_view_report").clearAll();
                    $$("schedule-page").$$("ro_view_layout").show();
                    availabilityReport.loadReport(
                        $$("schedule-page").$$("grid_ro_view_report"),
                        function (callback) {
                            if (callback == "ok") {
                                load_roster();
                            }
                        },
                    );
                } else {
                    webix.alert({
                        text: "You can't use the 'RO View' when 'All Shifts' or no shift is selected. Please select a single shift!",
                        width: 600,
                    });
                }
            } else {
                ro_view_showing = false;
                $$("schedule-page").$$("ro_view_layout").hide();
                $$("schedule-page").$$("schedule_display_type").enable();
            }
        });
        $$("schedule-page")
            .$$("schedule_rosters")
            .attachEvent("onChange", function (newv, oldv) {
                functionsPopup.closePopup();
                $$("schedule-page").$$("schedule_locations").setValue("");
                $$("schedule-page").$$("schedule_shifts").setValue("");
                load_shifts(newv);
                if (newv == "OTR") {
                    $$("schedule-page").$$("schedule_shifts").setValue("OTR");
                }
                if (newv == "Metro") {
                    $$("btn_ro_view").enable();
                } else {
                    $$("btn_ro_view").disable();
                }
            });
        $$("schedule-page")
            .$$("schedule_shifts")
            .attachEvent("onChange", function (newv) {
                let roster_name = $$("schedule-page").$$("schedule_rosters").getValue();
                toggleAllShifts(newv);
                getShiftGroupLocations(roster_name, newv);
                functionsPopup.closePopup();
                setTimeout(function () {
                    if (ro_view_showing == true) {
                        if (newv == "-- All Shifts --") {
                            webix.alert(
                                "You can't select 'All Shifts' while using the RO View!",
                            );
                            $$("schedule-page").$$("schedule_shifts").setValue("");
                        }
                    }
                }, 250);
            });
        $$("schedule-page")
            .$$("schedule_locations")
            .attachEvent("onChange", function (newv, oldv) {
                let shift = $$("schedule-page").$$("schedule_shifts").getValue();
                toggleAllStations(newv);
                functionsPopup.closePopup();
                setTimeout(function () {
                    if (newv != "") {
                        if (shift != "") {
                            if (ro_view_showing == true) {
                                availabilityReport.loadReport(
                                    $$("schedule-page").$$("grid_ro_view_report"),
                                    function (callback) {
                                        if (callback == "ok") {
                                            load_roster();
                                        }
                                    },
                                );
                            } else {
                                load_roster();
                            }
                        }
                    }
                }, 250);
            });
        $$("schedule-page")
            .$$("schedule_date")
            .attachEvent("onChange", function (newv, oldv) {
                functionsPopup.closePopup();
                if (ro_view_showing == true) {
                    globalSelectedDate = moment(newv).format("YYYYMMDD");
                    availabilityReport.loadReport(
                        $$("schedule-page").$$("grid_ro_view_report"),
                        function (callback) {
                            if (callback == "ok") {
                                load_roster();
                            }
                        },
                    );
                } else {
                    load_roster();
                }
            });
        $$("schedule-page")
            .$$("schedule_display_type")
            .attachEvent("onChange", function (newv, oldv) {
                functionsPopup.closePopup();
                if (ro_view_showing == false) {
                    load_roster();
                }
            });
        $$("schedule-page")
            .$$("date_next")
            .attachEvent("onItemClick", function (id, e) {
                functionsPopup.closePopup();
                let currDate = $$("schedule-page").$$("schedule_date").getValue();
                let currPeriod = $$("schedule-page")
                    .$$("schedule_display_type")
                    .getText();
                let newDate = new Date();
                if (currPeriod === "Day") {
                    newDate = moment(currDate).add(1, "days").toDate();
                } else if (currPeriod === "8 Days") {
                    newDate = moment(currDate).add(8, "days").toDate();
                } else if (currPeriod === "16 Days") {
                    newDate = moment(currDate).add(16, "days").toDate();
                } else if (currPeriod === "32 Days") {
                    newDate = moment(currDate).add(32, "days").toDate();
                }
                $$("schedule-page").$$("schedule_date").setValue(newDate);
            });
        $$("schedule-page")
            .$$("date_prev")
            .attachEvent("onItemClick", function (id, e) {
                functionsPopup.closePopup();
                let currDate = $$("schedule-page").$$("schedule_date").getValue();
                let currPeriod = $$("schedule-page")
                    .$$("schedule_display_type")
                    .getText();
                let newDate = new Date();
                if (currPeriod === "Day") {
                    newDate = moment(currDate).add(-1, "days").toDate();
                } else if (currPeriod === "8 Days") {
                    newDate = moment(currDate).add(-8, "days").toDate();
                } else if (currPeriod === "16 Days") {
                    newDate = moment(currDate).add(-16, "days").toDate();
                } else if (currPeriod === "32 Days") {
                    newDate = moment(currDate).add(-32, "days").toDate();
                }
                $$("schedule-page").$$("schedule_date").setValue(newDate);
            });
        $$("schedule-page")
            .$$("refresh")
            .attachEvent("onItemClick", function (id, e) {
                let shift = $$("schedule-page").$$("schedule_shifts").getValue();
                let location = $$("schedule-page").$$("schedule_locations").getValue();
                functionsPopup.closePopup();
                if (shift != "" && location != "") {
                    if (ro_view_showing == true) {
                        availabilityReport.loadReport(
                            $$("schedule-page").$$("grid_ro_view_report"),
                            function (callback) {
                                if (callback == "ok") {
                                    load_roster();
                                }
                            },
                        );
                    } else {
                        load_roster();
                    }
                }
            });
        $$("schedule-page")
            .$$("payNo_filter")
            .attachEvent("onChange", function (newv, oldv) {
                let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
                let shift_name = $$("schedule-page").$$("schedule_shifts").getText();
                let location = $$("schedule-page").$$("schedule_locations").getValue();
                if (roster_name == "" || shift_name == "" || location == "") {
                } else {
                    for (let x = 0; x < gridCount; x++) {
                        if (newv == 0) {
                            $$("roster_grid_" + x).hideColumn("pay_id");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").hideColumn("pay_id");
                            }
                        } else {
                            $$("roster_grid_" + x).showColumn("pay_id");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").showColumn("pay_id");
                            }
                        }
                    }
                }
            });
        $$("schedule-page")
            .$$("rank_filter")
            .attachEvent("onChange", function (newv, oldv) {
                let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
                let shift_name = $$("schedule-page").$$("schedule_shifts").getText();
                let location = $$("schedule-page").$$("schedule_locations").getValue();
                if (roster_name == "" || shift_name == "" || location == "") {
                } else {
                    for (let x = 0; x < gridCount; x++) {
                        if (newv == 0) {
                            $$("roster_grid_" + x).hideColumn("rank");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").hideColumn("rank");
                            }
                        } else {
                            $$("roster_grid_" + x).showColumn("rank");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").showColumn("rank");
                            }
                        }
                    }
                }
            });
        $$("schedule-page")
            .$$("skill_filter")
            .attachEvent("onChange", function (newv, oldv) {
                let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
                let shift_name = $$("schedule-page").$$("schedule_shifts").getText();
                let location = $$("schedule-page").$$("schedule_locations").getValue();
                if (roster_name == "" || shift_name == "" || location == "") {
                } else {
                    for (let x = 0; x < gridCount; x++) {
                        if (newv == 0) {
                            $$("roster_grid_" + x).hideColumn("skills");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").hideColumn("skills");
                            }
                        } else {
                            $$("roster_grid_" + x).showColumn("skills");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").showColumn("skills");
                            }
                        }
                    }
                }
            });
        $$("schedule-page")
            .$$("covid_filter")
            .attachEvent("onChange", function (newv, oldv) {
                let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
                let shift_name = $$("schedule-page").$$("schedule_shifts").getText();
                let location = $$("schedule-page").$$("schedule_locations").getValue();
                if (roster_name == "" || shift_name == "" || location == "") {
                } else {
                    for (let x = 0; x < gridCount; x++) {
                        if (newv == 0) {
                            $$("roster_grid_" + x).hideColumn("covid");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").hideColumn("covid");
                            }
                        } else {
                            $$("roster_grid_" + x).showColumn("covid");
                            if (
                                location == "All Stations" ||
                                location == "Central Command" ||
                                location == "Northern Command" ||
                                location == "Southern Command" ||
                                location == "All Regional Stations" ||
                                location == "Yorke & Mid North" ||
                                location == "Far North & Eyre" ||
                                location == "Riverland & Central" ||
                                location == "Limestone Coast" ||
                                location == "All Long Term Leave" ||
                                location == "All MCO Stations" ||
                                location == "All OTR"
                            ) {
                                $$("totals_grid").showColumn("covid");
                            }
                        }
                    }
                }
            });
        $$("overtime_filter").attachEvent("onChange", function (newv, oldv) {
            functionsPopup.closePopup();
            if (ro_view_showing == true) {
                availabilityReport.loadReport(
                    $$("schedule-page").$$("grid_ro_view_report"),
                    function (callback) {
                        if (callback == "ok") {
                            load_roster();
                        }
                    },
                );
            } else {
                load_roster();
            }
        });
        $$("btn_load_totals").attachEvent("onItemClick", function (id, e) {
            if (totals_loaded === false) {
                let start_date = $$("schedule-page").$$("schedule_date").getText();
                let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
                let shift_name = $$("schedule-page").$$("schedule_shifts").getText();
                let time_period = $$("schedule-page")
                    .$$("schedule_display_type")
                    .getText();
                let startOfPeriod;
                let end_date;
                switch (time_period) {
                    case "Day":
                        startOfPeriod = moment(start_date, "DD-MM-YYYY");
                        end_date = start_date;
                        break;
                    case "8 Days":
                        startOfPeriod = moment(start_date, "DD-MM-YYYY");
                        end_date = moment(startOfPeriod, "DD-MM-YYYY")
                            .add(8, "days")
                            .format("DD-MM-YYYY");
                        break;
                    case "16 Days":
                        startOfPeriod = moment(start_date, "DD-MM-YYYY");
                        end_date = moment(startOfPeriod, "DD-MM-YYYY")
                            .add(16, "days")
                            .format("DD-MM-YYYY");
                        break;
                    case "32 Days":
                        startOfPeriod = moment(start_date, "DD-MM-YYYY");
                        end_date = moment(startOfPeriod, "DD-MM-YYYY")
                            .add(32, "days")
                            .format("DD-MM-YYYY");
                        break;
                    default:
                        startOfPeriod = moment(start_date, "DD-MM-YYYY");
                        end_date = start_date;
                }
                if (ro_view_showing == false) {
                    load_grand_totals(
                        roster_name,
                        shift_name,
                        start_date,
                        end_date,
                        time_period,
                        startOfPeriod,
                        function (result) {
                            if (result == "OK") {
                                let scroll_view_height =
                                    $$("schedule-page").$$("roster_grid_layout").$height;
                                $$("schedule-page").$$("roster_grid_layout").config.height =
                                    scroll_view_height + 230;
                                $$("schedule-page").$$("roster_grid_layout").adjust();
                                loading_roster = false;
                                totals_loaded = true;
                                $$("schedule-page").$$("roster_scroll_view").scrollTo(0, 1e4);
                            } else {
                                webix.alert("There was an error loading some of the totals!");
                                loading_roster = false;
                            }
                        },
                    );
                } else {
                    let scroll_view_height =
                        $$("schedule-page").$$("roster_grid_layout").$height;
                    $$("schedule-page").$$("roster_grid_layout").config.height =
                        scroll_view_height + 70;
                    $$("schedule-page").$$("roster_grid_layout").adjust();
                    $$("loader-window").hide();
                    loading_roster = false;
                }
            }
        });
    }

    rosters_subject.subscribe(function (data) {
        let select = $$("schedule-page").$$("schedule_rosters");
        if (data) {
            let options = [];
            let roster_names = JSON.parse(data);
            roster_names.forEach(function (value) {
                options.push(value.roster_name);
            });
            select.define("options", options);
            select.refresh();
        }
    });

    function toggleAllShifts(value) {
        let schedule_locations = $$("schedule-page")
            .$$("schedule_locations")
            .getList().config.data;
        let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
        if (value === "-- All Shifts --") {
            if (schedule_locations[0].id === "All Stations") {
                schedule_locations.shift();
                schedule_locations.shift();
                schedule_locations.shift();
                schedule_locations.shift();
                schedule_locations.shift();
            } else if (schedule_locations[0].id === "All Long Term Leave") {
                schedule_locations = schedule_locations.filter(
                    (person) => person.value != "-- All Long Term Leave --",
                );
            } else if (schedule_locations[0].id === "All OTR") {
                schedule_locations = schedule_locations.filter(
                    (person) => person.value != "-- All OTR --",
                );
            }
        } else {
            if (schedule_locations.length > 0) {
                if (roster_name == "Metro") {
                    if (schedule_locations[0].id !== "All Stations") {
                        schedule_locations.unshift({
                            id: "All MCO Stations",
                            value: "-- All MCO Stations --",
                        });
                        schedule_locations.unshift({
                            id: "Southern Command",
                            value: "-- Southern Command --",
                        });
                        schedule_locations.unshift({
                            id: "Northern Command",
                            value: "-- Northern Command --",
                        });
                        schedule_locations.unshift({
                            id: "Central Command",
                            value: "-- Central Command --",
                        });
                        schedule_locations.unshift({
                            id: "All Stations",
                            value: "-- All Stations --",
                        });
                    }
                } else if (roster_name == "Long Term Leave") {
                    schedule_locations.unshift({
                        id: "All Long Term Leave",
                        value: "-- All Long Term Leave --",
                    });
                } else if (roster_name == "OTR") {
                    schedule_locations.unshift({id: "All OTR", value: "-- All OTR --"});
                }
            }
        }
        $$("schedule-page")
            .$$("schedule_locations")
            .define("options", schedule_locations);
        $$("schedule-page").$$("schedule_locations").refresh();
    }

    function getShiftGroupLocations(roster, shift) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/admin/shift_group_locations",
                {roster: roster, shift: shift},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        if (results != "[]") {
                            let values = JSON.parse(results);
                            let locationsList = values[0].locations.split(",");
                            let locationsArray = [];
                            locationsList.forEach(function (value) {
                                locationsArray.push({id: value, value: value});
                            });
                            if (locationsArray.length > 1) {
                                if (roster == "Regional Operations") {
                                    locationsArray.unshift({
                                        id: shift,
                                        value: "-- All " + shift + " Stations --",
                                    });
                                } else if (roster == "OTR") {
                                    locationsArray.unshift({
                                        id: "All OTR",
                                        value: "-- All OTR --",
                                    });
                                }
                            }
                            $$("schedule-page")
                                .$$("schedule_locations")
                                .define("options", locationsArray);
                            $$("schedule-page").$$("schedule_locations").refresh();
                        }
                    },
                },
            );
    }

    function toggleAllStations(value) {
        let schedule_shifts = $$("schedule-page").$$("schedule_shifts").getList()
            .config.data;
        let rosterName = $$("schedule-page").$$("schedule_rosters").getValue();
        if (
            value == "All Stations" ||
            value == "Central Command" ||
            value == "Northern Command" ||
            value == "Southern Command" ||
            value == "Yorke & Mid North" ||
            value == "Far North & Eyre" ||
            value == "Riverland & Central" ||
            value == "Limestone Coast" ||
            value == "All Long Term Leave" ||
            value == "All MCO Stations" ||
            value == "All OTR"
        ) {
            if (schedule_shifts[0].id === "-- All Shifts --") {
                schedule_shifts.shift();
                $$("schedule-page")
                    .$$("schedule_shifts")
                    .define("options", schedule_shifts);
                $$("schedule-page").$$("schedule_shifts").refresh();
            }
        } else {
            if (schedule_shifts.length > 0) {
                if (schedule_shifts[0].id !== "-- All Shifts --") {
                    if (rosterName !== "Regional Operations" && rosterName !== "OTR") {
                        schedule_shifts.unshift({
                            id: "-- All Shifts --",
                            value: "-- All Shifts --",
                        });
                    }
                    $$("schedule-page")
                        .$$("schedule_shifts")
                        .define("options", schedule_shifts);
                    $$("schedule-page").$$("schedule_shifts").refresh();
                }
            }
        }
    }

    function gridItemClick(grid, gridId, id, type, event) {

        mouse_y = event.pageY;
        mouse_x = event.pageX;

        for (let x = 0; x < gridCount; x++) {
            if (gridId !== x) {
                $$("roster_grid_" + x).clearSelection();
            }
        }
        webix.UIManager.setFocus(grid);
        let currRoster = $$("schedule-page").$$("schedule_rosters").getText();
        let currShift = $$("schedule-page").$$("schedule_shifts").getText();
        let currLocation = $$("schedule-page").$$("schedule_locations").getValue();
        selectedShift = shiftArray[gridId];
        selectedLocation = locationsArray[gridId];
        if (currShift == "-- All Shifts --") {
            currShift = selectedShift;
        }
        if (
            currLocation == "All Stations" ||
            currLocation == "Central Command" ||
            currLocation == "Northern Command" ||
            currLocation == "Southern Command" ||
            currLocation == "All Regional Stations" ||
            currLocation == "Yorke & Mid North" ||
            currLocation == "Far North & Eyre" ||
            currLocation == "Riverland & Central" ||
            currLocation == "Limestone Coast" ||
            currLocation == "All Long Term Leave" ||
            currLocation == "All MCO Stations" ||
            currLocation == "All OTR"
        ) {
            currLocation = selectedLocation;
        }
        if (!currRoster || !currShift || !currLocation) {
            webix.alert({
                text: "The functions toolbar won't appear if a Roster, Shift & Location are not selected!",
                width: 550,
            });
        } else {
            if (type == "cell") {
                overtimeMode = false;
                $$("main_functions_list").enable();
                if (
                    currRoster !== "Metro" &&
                    currRoster !== "Comms" &&
                    currRoster !== "Port Pirie" &&
                    currRoster !== "Mt Gambier" &&
                    currRoster !== "OTR" &&
                    currRoster !== "Long Term Leave"
                ) {
                    $$("main_functions_list").enableItem("day_work");
                    if (user_permission_level === 1 || user_permission_level === 2) {
                        $$("main_functions_list").disableItem("staff_movement");
                        $$("main_functions_list").disableItem("overtime");
                        $$("main_functions_list").disableItem("standbys");
                        $$("main_functions_list").disableItem("actups");
                    } else if (
                        user_permission_level === 3 ||
                        user_permission_level === 4 ||
                        user_permission_level === 5
                    ) {
                        $$("main_functions_list").disableItem("staff_movement");
                        $$("main_functions_list").disableItem("overtime");
                        $$("main_functions_list").disableItem("standbys");
                        $$("main_functions_list").disableItem("actups");
                        $$("main_functions_list").disableItem("day_work");
                    } else if (user_permission_level === 6) {
                        $$("main_functions_list").disableItem("staff_movement");
                        $$("main_functions_list").disableItem("overtime");
                        $$("main_functions_list").disableItem("standbys");
                        $$("main_functions_list").disableItem("actups");
                        $$("main_functions_list").disableItem("day_work");
                    }
                } else {
                    $$("main_functions_list").disableItem("day_work");
                    $$("day_work_form").hide();
                    if (user_permission_level === 1 || user_permission_level === 2) {
                        $$("main_functions_list").enableItem("staff_movement");
                        $$("main_functions_list").enableItem("overtime");
                        $$("main_functions_list").enableItem("standbys");
                        $$("main_functions_list").enableItem("actups");
                    } else if (
                        user_permission_level === 3 ||
                        user_permission_level === 4 ||
                        user_permission_level === 5
                    ) {
                        $$("main_functions_list").enableItem("staff_movement");
                        $$("main_functions_list").enableItem("overtime");
                        $$("main_functions_list").enableItem("standbys");
                        $$("main_functions_list").enableItem("actups");
                        $$("main_functions_list").disableItem("day_work");
                    } else if (user_permission_level === 6) {
                        $$("main_functions_list").disableItem("staff_movement");
                        $$("main_functions_list").disableItem("overtime");
                        $$("main_functions_list").disableItem("standbys");
                        $$("main_functions_list").disableItem("actups");
                        $$("main_functions_list").disableItem("day_work");
                    }
                }
                let cell_css = grid.getCss(id.row, id.column);
                cell_css = cell_css.split(" ");
                cell_css = cell_css[1];
                cell_css = "." + cell_css;
                let background = $(cell_css).css("background-color");
                let actingRank = "";
                let topBarLabel = "";
                let middleName = "";
                let selectedEmployee = grid.getSelectedItem(id);
                selected_employee_info = {
                    surname: selectedEmployee[0].surname,
                    middle_name: selectedEmployee[0].middle_name,
                    first_name: selectedEmployee[0].first_name,
                    pay_id: selectedEmployee[0].pay_id,
                    rank: selectedEmployee[0].rank,
                    notifications_email: selectedEmployee[0].notifications_email,
                    personal_phone: selectedEmployee[0].personal_phone,
                    work_email_address: selectedEmployee[0].work_email_address,
                    hide_phone: selectedEmployee[0].hide_phone,
                    skills: selectedEmployee[0].skills,
                    home_suburb: selectedEmployee[0].home_suburb,
                    position_number: selectedEmployee[0].position_number,
                };
                if (selectedEmployee[0].middle_name == null) {
                    middleName = "";
                } else {
                    middleName = selectedEmployee[0].middle_name;
                }
                let columnNo = id.column;
                let selectedDate = grid.getColumnConfig(columnNo);
                let dateStamp = moment(
                    selectedDate.header[0].date,
                    "YYYY-MM-DD",
                ).toDate();
                dateStamp = moment(dateStamp).format("DD/MM/YYYY");
                globalSelectedDate = selectedDate.header[0].date;
                if (background.substring(0, 18) == "rgb(255, 255, 255)") {
                    selectedDayType = "no_day";
                    functionsPopup.closePopup();
                } else if (background.substring(0, 18) === "rgb(234, 233, 233)") {
                    selectedDayType = "off_day";
                    daySeqNumber = 0;
                    functionsPopup.closePopup();
                    const row = grid.getItem(id);
                    const value = row[id.column];
                    if (value === "RC"){
                        getUserBookings(
                            selected_employee_info.pay_id, globalSelectedDate, function (bookingArray) {
                                let no_of_bookings = bookingArray.length;
                                if (no_of_bookings > 0) {
                                    let rc_info = "";
                                    bookingArray.forEach(function (result) {
                                        if (result.leave_type_code === "RC"){
                                            rc_info = "Date: <strong>" + result.start_date + "</strong>"
                                                + "<br>Roster: <strong>" + result.roster + "</strong>"
                                                + "<br>Shift: <strong>" + result.shift + "</strong>"
                                                + "<br>Location: <strong>" + result.location + "</strong>"
                                                + "<br>Comments: <strong>" + result.comments + "</strong>"
                                                + "<br><br>Created By: <strong>" + result.created_by + "</strong>" + " on " + "<strong>" + result.created_date + "</strong>";
                                        }
                                    });
                                    showRCInfoPopup(rc_info);
                                }
                            });
                    }
                } else {
                    let dayType = grid.getItem(1);
                    let icon = dayType[columnNo];
                    selectedShiftType = icon;
                    if (
                        id.row === 1 ||
                        id.column === "names" ||
                        id.column === "pay_id" ||
                        id.column === "rank" ||
                        id.column === "skills" ||
                        id.column === "covid"
                    ) {
                        functionsPopup.closePopup();
                        if (id.column === "names") {
                            if (background.substring(0, 18) !== "rgb(21,49,126)") {
                                if (selectedEmployee[0].surname != "") {
                                    showEmployeeInfoPopup();
                                    return;
                                }
                            }
                        } else if (id.column === "pay_id") {
                            return;
                        } else if (id.column === "rank") {
                            return;
                        } else if (id.column === "skills") {
                            return;
                        } else if (id.column === "covid") {
                            return;
                        } else {
                            let dayCell = grid.getItem(1);
                            let dayIcon = dayCell[id.column];
                            if (
                                dayIcon.includes("sun") == true ||
                                dayIcon.includes("moon") == true
                            ) {
                                if (ro_view_showing == false) {
                                    availabilityReport.loadReport(
                                        $$("grid_availability_report"),
                                        function (callback) {
                                        },
                                    );
                                    $$("availability_report_window").show();
                                } else {
                                    availabilityReport.loadReport(
                                        $$("schedule-page").$$("grid_ro_view_report"),
                                        function (callback) {
                                        },
                                    );
                                }
                                return;
                            }
                        }
                    } else {

                        if (
                            background.substring(0, 18) === "rgb(195, 235, 254)" ||
                            background.substring(0, 18) === "rgb(180, 227, 128)" ||
                            background.substring(0, 18) === "rgb(255, 255, 0)"
                        ) {
                            selectedDayType = "work_day";
                        } else if (background.substring(0, 18) === "rgb(234, 233, 233)") {
                            selectedDayType = "off_day";
                        } else {
                            selectedDayType = "shift_total_cell";
                        }
                        if (icon.includes("sun")) {
                            selectedDayShiftType = "day_shift";
                        } else if (icon.includes("moon")) {
                            selectedDayShiftType = "night_shift";
                        } else if (icon.includes("ban")) {
                            selectedDayShiftType = "no_shift";
                        }
                        if (selectedDayType != "shift_total_cell") {
                            getShiftTimes(globalSelectedDate, currRoster, currShift);
                            let leaveType = $$("bookings_leave_type").getValue();
                            let selLeaveDays = $$("leave_days_radio").getValue();
                            let selSMDays = $$("shift_adjustment_days_radio").getValue();
                            if (daySeqNumber == 1 || daySeqNumber == 9) {
                                if (leaveType == "LSLS") {
                                    $$("leave_days_radio").define("options", ["1", "2", "3"]);
                                } else {
                                    $$("leave_days_radio").define("options", [
                                        "1",
                                        "2",
                                        "3",
                                        "4",
                                    ]);
                                }
                                $$("leave_days_radio").refresh();
                                $$("shift_adjustment_days_radio").define("options", [
                                    "1",
                                    "2",
                                    "3",
                                    "4",
                                    "NR",
                                ]);
                                $$("shift_adjustment_days_radio").refresh();
                            } else if (daySeqNumber == 2 || daySeqNumber == 10) {
                                $$("leave_days_radio").define("options", ["1", "2", "3"]);
                                $$("leave_days_radio").refresh();
                                $$("shift_adjustment_days_radio").define("options", [
                                    "1",
                                    "2",
                                    "3",
                                ]);
                                $$("shift_adjustment_days_radio").refresh();
                                if (selLeaveDays == 4 || selSMDays == 4 || selSMDays == "NR") {
                                    $$("leave_days_radio").setValue(3);
                                    $$("shift_adjustment_days_radio").setValue(3);
                                }
                            } else if (daySeqNumber == 3 || daySeqNumber == 11) {
                                $$("leave_days_radio").define("options", ["1", "2"]);
                                $$("leave_days_radio").refresh();
                                $$("shift_adjustment_days_radio").define("options", ["1", "2"]);
                                $$("shift_adjustment_days_radio").refresh();
                                if (
                                    selLeaveDays == 3 ||
                                    selLeaveDays == 4 ||
                                    selSMDays == 3 ||
                                    selSMDays == 4
                                ) {
                                    $$("leave_days_radio").setValue(2);
                                    $$("shift_adjustment_days_radio").setValue(2);
                                }
                            } else if (daySeqNumber == 4 || daySeqNumber == 12) {
                                $$("leave_days_radio").define("options", ["1"]);
                                $$("leave_days_radio").refresh();
                                $$("shift_adjustment_days_radio").define("options", ["1"]);
                                $$("shift_adjustment_days_radio").refresh();
                                if (
                                    selLeaveDays == 2 ||
                                    selLeaveDays == 3 ||
                                    selLeaveDays == 4 ||
                                    selSMDays == 2 ||
                                    selSMDays == 3 ||
                                    selSMDays == 4
                                ) {
                                    $$("leave_days_radio").setValue(1);
                                    $$("shift_adjustment_days_radio").setValue(1);
                                }
                            } else {
                                if (leaveType == "LSLS") {
                                    $$("leave_days_radio").define("options", ["1", "2", "3"]);
                                } else {
                                    $$("leave_days_radio").define("options", [
                                        "1",
                                        "2",
                                        "3",
                                        "4",
                                    ]);
                                }
                                $$("leave_days_radio").refresh();
                                $$("shift_adjustment_days_radio").define("options", [
                                    "1",
                                    "2",
                                    "3",
                                    "4",
                                ]);
                                $$("shift_adjustment_days_radio").refresh();
                            }
                            let menuItem = $$("main_functions_list").getSelectedItem();
                            let radioValue = "";
                            if (menuItem.id == "staff_movement") {
                                radioValue = $$("shift_adjustment_days_radio").getValue();
                            } else if (menuItem.id == "leave" || menuItem.id == "sickness") {
                                radioValue = $$("leave_days_radio").getValue();
                            } else if (
                                menuItem.id == "overtime" ||
                                menuItem.id == "standbys" ||
                                menuItem.id == "actups"
                            ) {
                                radioValue = 1;
                            }
                            if (currRoster == "Port Pirie") {
                                $$("bookings_day_hours").setValue(1);
                            } else if (currRoster == "OTR") {
                                if (
                                    daySeqNumber == 9 ||
                                    daySeqNumber == 10 ||
                                    daySeqNumber == 11 ||
                                    daySeqNumber == 12
                                ) {
                                    if (leaveType == "PPLS") {
                                        $$("lsl_days_radio").define("options", ["6"]);
                                        $$("lsl_days_radio").refresh();
                                        $$("lsl_days_radio").setValue(6);
                                        radioValue = 6;
                                    } else {
                                        $$("lsl_days_radio").define("options", [
                                            "6",
                                            "14",
                                            "20",
                                            "28",
                                        ]);
                                        $$("lsl_days_radio").refresh();
                                        if (
                                            $$("lsl_days_radio").getValue() != 6 ||
                                            $$("lsl_days_radio").getValue() != 14 ||
                                            $$("lsl_days_radio").getValue() != 20 ||
                                            $$("lsl_days_radio").getValue() != 28
                                        ) {
                                            $$("lsl_days_radio").setValue(6);
                                        }
                                    }
                                } else {
                                    if (leaveType == "PPLS") {
                                        $$("lsl_days_radio").define("options", ["8"]);
                                        $$("lsl_days_radio").refresh();
                                        $$("lsl_days_radio").setValue(8);
                                        radioValue = 8;
                                    } else {
                                        $$("lsl_days_radio").define("options", [
                                            "8",
                                            "14",
                                            "22",
                                            "28",
                                        ]);
                                        $$("lsl_days_radio").refresh();
                                        if (
                                            $$("lsl_days_radio").getValue() != 8 ||
                                            $$("lsl_days_radio").getValue() != 14 ||
                                            $$("lsl_days_radio").getValue() != 22 ||
                                            $$("lsl_days_radio").getValue() != 28
                                        ) {
                                            $$("lsl_days_radio").setValue(8);
                                        }
                                    }
                                }
                            } else if (currRoster == "Comms" && currShift == "CommCen E1") {
                                if (
                                    daySeqNumber == 1 ||
                                    daySeqNumber == 2 ||
                                    daySeqNumber == 3 ||
                                    daySeqNumber == 4
                                ) {
                                    $$("lsl_days_radio").define("options", [
                                        "6",
                                        "14",
                                        "20",
                                        "28",
                                    ]);
                                    $$("lsl_days_radio").refresh();
                                    if (
                                        $$("lsl_days_radio").getValue() != 6 ||
                                        $$("lsl_days_radio").getValue() != 14 ||
                                        $$("lsl_days_radio").getValue() != 20 ||
                                        $$("lsl_days_radio").getValue() != 28
                                    ) {
                                        $$("lsl_days_radio").setValue(6);
                                    }
                                } else {
                                    $$("lsl_days_radio").define("options", [
                                        "8",
                                        "14",
                                        "22",
                                        "28",
                                    ]);
                                    $$("lsl_days_radio").refresh();
                                    if (
                                        $$("lsl_days_radio").getValue() != 8 ||
                                        $$("lsl_days_radio").getValue() != 14 ||
                                        $$("lsl_days_radio").getValue() != 22 ||
                                        $$("lsl_days_radio").getValue() != 28
                                    ) {
                                        $$("lsl_days_radio").setValue(8);
                                    }
                                }
                            } else if (currRoster == "Comms" && currShift == "CommCen E2") {
                                if (
                                    daySeqNumber == 9 ||
                                    daySeqNumber == 10 ||
                                    daySeqNumber == 11 ||
                                    daySeqNumber == 12
                                ) {
                                    $$("lsl_days_radio").define("options", [
                                        "6",
                                        "14",
                                        "20",
                                        "28",
                                    ]);
                                    $$("lsl_days_radio").refresh();
                                    if (
                                        $$("lsl_days_radio").getValue() != 6 ||
                                        $$("lsl_days_radio").getValue() != 14 ||
                                        $$("lsl_days_radio").getValue() != 20 ||
                                        $$("lsl_days_radio").getValue() != 28
                                    ) {
                                        $$("lsl_days_radio").setValue(6);
                                    }
                                } else {
                                    $$("lsl_days_radio").define("options", [
                                        "8",
                                        "14",
                                        "22",
                                        "28",
                                    ]);
                                    $$("lsl_days_radio").refresh();
                                    if (
                                        $$("lsl_days_radio").getValue() != 8 ||
                                        $$("lsl_days_radio").getValue() != 14 ||
                                        $$("lsl_days_radio").getValue() != 22 ||
                                        $$("lsl_days_radio").getValue() != 28
                                    ) {
                                        $$("lsl_days_radio").setValue(8);
                                    }
                                }
                            }
                            setDatesForBookingForms(
                                globalSelectedDate,
                                currRoster,
                                currShift,
                                icon,
                                radioValue,
                                menuItem.id,
                            );
                            if (popupShowing === false) {
                                $$("bookings_form").hide();
                                $$("booking_info").show();
                                $$("overtime_form").hide();
                                $$("overtime_form_cont").hide();
                                $$("staff_movement_form").hide();
                                $$("standbys_form").hide();
                                $$("actups_form").hide();
                                $$("main_functions_list").select("leave");
                            }
                            if (selectedEmployee[0][columnNo] === "") {
                                $$("bookings_shift_times").setValue(
                                    "SHIFT: " +
                                    selected_shift_start_time +
                                    " - " +
                                    selected_shift_end_time,
                                );
                                $$("no_bookings").show();
                                $$("bookings_comments").setValue("");
                                $$("logs_scrollview").hide();
                            } else {
                                let movementLogsArray = [];
                                getUserBookings(
                                    selected_employee_info.pay_id,
                                    globalSelectedDate,
                                    function (bookingArray) {
                                        $$("bookings_shift_times").setValue(
                                            "SHIFT: " +
                                            selected_shift_start_time +
                                            " - " +
                                            selected_shift_end_time,
                                        );
                                        $$("logs_scrollview").show();
                                        $$("no_bookings").hide();
                                        movementLogsArray = [];
                                        let bookingId = "";
                                        let bookingStatus = "";
                                        let bookingComments = "";
                                        let approved_by = "";
                                        let approved_date = "";
                                        for (let x = 0; x < bookingArray.length; x++) {
                                            if (bookingArray[x].booking_type == "standby_link") {
                                                bookingId = bookingArray[x].linked_booking_id;
                                            } else {
                                                bookingId = bookingArray[x].booking_id;
                                            }
                                            if (bookingArray[x].status == "Pending") {
                                                bookingStatus = "<i> (Pending)</i>";
                                            } else {
                                                bookingStatus = "";
                                            }
                                            if (bookingArray[x].booking_type == "standby") {
                                                if (bookingArray[x].bk_period == "day") {
                                                    if (bookingArray[x].hours < 10) {
                                                        bookingStatus = "<i> (Partial)</i>";
                                                    } else {
                                                        bookingStatus = "";
                                                    }
                                                } else if (bookingArray[x].bk_period == "night") {
                                                    if (bookingArray[x].hours < 14) {
                                                        bookingStatus = "<i> (Partial)</i>";
                                                    } else {
                                                        bookingStatus = "";
                                                    }
                                                }
                                            }
                                            if (bookingArray[x].booking_type === "staff_movement") {
                                                bookingComments = bookingArray[x].comments;
                                                movementLogsArray.push({
                                                    paddingY: 10,
                                                    cols: [
                                                        {
                                                            view: "property",
                                                            id: "movement_logs_" + x.toString(),
                                                            css: "functions_form",
                                                            editable: true,
                                                            autoheight: true,
                                                            elements: [
                                                                {
                                                                    label: "Type",
                                                                    type: "text",
                                                                    id: "bookings_shift_type_" + x.toString(),
                                                                    value:
                                                                        bookingArray[x].leave_type_code +
                                                                        " - " +
                                                                        bookingArray[x].leave_type_description +
                                                                        bookingStatus,
                                                                },
                                                                {
                                                                    label: "From",
                                                                    type: "text",
                                                                    id: "bookings_shift_from_" + x.toString(),
                                                                    value:
                                                                        "(" +
                                                                        bookingArray[x].shift +
                                                                        ")" +
                                                                        " " +
                                                                        bookingArray[x].location,
                                                                },
                                                                {
                                                                    label: "To",
                                                                    type: "text",
                                                                    id: "bookings_shift_to_" + x.toString(),
                                                                    value:
                                                                        "(" +
                                                                        bookingArray[x].moved_shift +
                                                                        ")" +
                                                                        " " +
                                                                        bookingArray[x].moved_location,
                                                                },
                                                                {
                                                                    label: "Comments",
                                                                    type: "popup",
                                                                    id: "bookings_shift_comments_" + x.toString(),
                                                                    value: bookingComments,
                                                                },
                                                                {
                                                                    label: "Approved By",
                                                                    type: "text",
                                                                    id: "bookings_shift_info_" + x.toString(),
                                                                    css: "createdBy",
                                                                    value:
                                                                        bookingArray[x].approved_denied_by +
                                                                        " - " +
                                                                        bookingArray[x].approved_denied_date,
                                                                },
                                                            ],
                                                        },
                                                        {width: 5},
                                                        {
                                                            rows: [
                                                                {
                                                                    view: "text",
                                                                    id: "booking_info_id_" + x.toString(),
                                                                    hidden: true,
                                                                    value: bookingId,
                                                                },
                                                                {
                                                                    view: "text",
                                                                    id: "booking_id_" + x.toString(),
                                                                    hidden: true,
                                                                    value: bookingArray[x].id,
                                                                },
                                                                {
                                                                    view: "text",
                                                                    id: "booking_type_" + x.toString(),
                                                                    hidden: true,
                                                                    value: bookingArray[x].booking_type,
                                                                },
                                                                {height: 25},
                                                                {
                                                                    view: "button",
                                                                    id: "bookings_btn_delete_" + x.toString(),
                                                                    type: "icon",
                                                                    icon: "fas fa-trash-alt",
                                                                    label: "",
                                                                    width: 40,
                                                                    on: {
                                                                        onItemClick: function (id, e, node) {
                                                                            functionsPopup.btnDeleteBooking(x, id);
                                                                        },
                                                                    },
                                                                },
                                                                {},
                                                            ],
                                                        },
                                                    ],
                                                });
                                            } else {
                                                let booking_code = bookingArray[x].leave_type_code;
                                                if (bookingArray[x].booking_type == "act-up") {
                                                    actingRank = bookingArray[x].actup_rank;
                                                }
                                                if (bookingArray[x].booking_type == "sick_leave") {
                                                    if (user_logged_in == selected_employee_info.pay_id) {
                                                        bookingComments = bookingArray[x].comments;
                                                    } else {
                                                        if (
                                                            user_permission_level === 1 ||
                                                            user_permission_level === 2 ||
                                                            user_permission_level === 3 ||
                                                            user_permission_level === 4
                                                        ) {
                                                            bookingComments = bookingArray[x].comments;
                                                        } else {
                                                            bookingComments =
                                                                "<span style='font-style: italic'>{ comments have been hidden }</span>";
                                                        }
                                                    }
                                                    if (
                                                        bookingArray[x].leave_type_code == "SICM" ||
                                                        bookingArray[x].leave_type_code == "FAML"
                                                    ) {
                                                        if (bookingArray[x].statutory_declaration == 1) {
                                                            booking_code =
                                                                "<span style='background-color: darkred; padding-top: 2px; padding-bottom: 2px'>" +
                                                                booking_code +
                                                                "</span>";
                                                        }
                                                    }
                                                } else {
                                                    if (bookingArray[x].status == "Pending") {
                                                        bookingComments = bookingArray[x].comments;
                                                    } else {
                                                        if (
                                                            bookingArray[x].booking_type == "standby" ||
                                                            bookingArray[x].booking_type == "standby_link" ||
                                                            bookingArray[x].leave_type_code == "RRL" ||
                                                            bookingArray[x].booking_type == "overtime" ||
                                                            bookingArray[x].booking_type == "act-up"
                                                        ) {
                                                            bookingComments = bookingArray[x].comments;
                                                        } else {
                                                            bookingComments =
                                                                bookingArray[x].request_comments;
                                                        }
                                                    }
                                                }
                                                if (bookingArray[x].approved_denied_by == null) {
                                                    approved_by = "";
                                                    approved_date = "";
                                                } else {
                                                    approved_by = bookingArray[x].approved_denied_by;
                                                    approved_date = bookingArray[x].approved_denied_date;
                                                    if (
                                                        bookingArray[x].request_comments == null ||
                                                        bookingArray[x].request_comments == ""
                                                    ) {
                                                        if (
                                                            bookingComments !=
                                                            "<span style='font-style: italic'>{ comments have been hidden }</span>"
                                                        ) {
                                                            bookingComments = bookingArray[x].comments;
                                                        }
                                                    }
                                                }
                                                movementLogsArray.push({
                                                    paddingY: 10,
                                                    cols: [
                                                        {
                                                            view: "property",
                                                            id: "movement_logs_" + x.toString(),
                                                            css: "functions_form",
                                                            editable: true,
                                                            autoheight: true,
                                                            elements: [
                                                                {
                                                                    label: "Type",
                                                                    type: "text",
                                                                    id: "bookings_shift_type_" + x.toString(),
                                                                    value:
                                                                        booking_code +
                                                                        " - " +
                                                                        bookingArray[x].leave_type_description +
                                                                        bookingStatus,
                                                                },
                                                                {
                                                                    label: "Period",
                                                                    type: "text",
                                                                    id: "bookings_shift_dates_" + x.toString(),
                                                                    css: "shift_period",
                                                                    value:
                                                                        moment(
                                                                            bookingArray[x].booking_first_date,
                                                                        ).format("DD/MM/YYYY H:mm") +
                                                                        " - " +
                                                                        moment(
                                                                            bookingArray[x].booking_last_date,
                                                                        ).format("DD/MM/YYYY H:mm"),
                                                                },
                                                                {
                                                                    label: "Comments",
                                                                    type: "popup",
                                                                    id: "bookings_shift_comments_" + x.toString(),
                                                                    value: bookingComments,
                                                                },
                                                                {
                                                                    label: "Created By",
                                                                    type: "text",
                                                                    id: "bookings_shift_info_" + x.toString(),
                                                                    css: "createdBy",
                                                                    value:
                                                                        bookingArray[x].created_by +
                                                                        " - " +
                                                                        bookingArray[x].created_date,
                                                                },
                                                                {
                                                                    label: "Approved By",
                                                                    type: "text",
                                                                    id: "bookings_shift_info_" + x.toString(),
                                                                    css: "createdBy",
                                                                    value: approved_by + " - " + approved_date,
                                                                },
                                                            ],
                                                        },
                                                        {width: 5},
                                                        {
                                                            rows: [
                                                                {
                                                                    view: "text",
                                                                    id: "booking_info_id_" + x.toString(),
                                                                    hidden: true,
                                                                    value: bookingId,
                                                                },
                                                                {
                                                                    view: "text",
                                                                    id: "booking_id_" + x.toString(),
                                                                    hidden: true,
                                                                    value: bookingArray[x].id,
                                                                },
                                                                {
                                                                    view: "text",
                                                                    id: "booking_type_" + x.toString(),
                                                                    hidden: true,
                                                                    value: bookingArray[x].booking_type,
                                                                },
                                                                {height: 25},
                                                                {
                                                                    view: "button",
                                                                    id: "bookings_btn_delete_" + x.toString(),
                                                                    type: "icon",
                                                                    icon: "fas fa-trash-alt",
                                                                    label: "",
                                                                    width: 40,
                                                                    on: {
                                                                        onItemClick: function (id, e, node) {
                                                                            functionsPopup.btnDeleteBooking(x, id);
                                                                        },
                                                                    },
                                                                },
                                                                {height: 20},
                                                                {
                                                                    view: "button",
                                                                    type: "iconTop",
                                                                    icon: "fas fa-retweet",
                                                                    css: "sic_swap_button",
                                                                    id: "bookings_btn_sic_" + x.toString(),
                                                                    label: "SIC",
                                                                    width: 40,
                                                                    height: 50,
                                                                    hidden: true,
                                                                    on: {
                                                                        onItemClick: function (id, e, node) {
                                                                            functionsPopup.btnChangeToSIC(x, id);
                                                                        },
                                                                    },
                                                                },
                                                                {
                                                                    view: "button",
                                                                    type: "iconTop",
                                                                    icon: "fas fa-retweet",
                                                                    css: "sicm_swap_button",
                                                                    id: "bookings_btn_sicm_" + x.toString(),
                                                                    label: "SICM",
                                                                    width: 40,
                                                                    height: 50,
                                                                    hidden: true,
                                                                    on: {
                                                                        onItemClick: function (id, e, node) {
                                                                            functionsPopup.btnChangeToSICM(x, id);
                                                                        },
                                                                    },
                                                                },
                                                                {},
                                                            ],
                                                        },
                                                    ],
                                                });
                                            }
                                            webix.ui(
                                                movementLogsArray,
                                                $$("movement_logs_scrollview"),
                                            );
                                            if (
                                                bookingArray[x].leave_type_code == "SICM" ||
                                                bookingArray[x].leave_type_code == "FAML"
                                            ) {
                                                $$("bookings_btn_sic_" + x.toString()).show();
                                                $$("bookings_btn_sicm_" + x.toString()).hide();
                                            } else if (bookingArray[x].leave_type_code == "SIC") {
                                                $$("bookings_btn_sicm_" + x.toString()).show();
                                                $$("bookings_btn_sic_" + x.toString()).hide();
                                            }
                                        }
                                    },
                                );
                            }
                            if (actingRank == "") {
                                topBarLabel =
                                    dateStamp +
                                    "  -  " +
                                    selected_employee_info.pay_id +
                                    " - " +
                                    selected_employee_info.surname +
                                    ", " +
                                    selected_employee_info.first_name +
                                    " " +
                                    middleName +
                                    " (" +
                                    selectedEmployee[0].rank +
                                    ")";
                            } else {
                                topBarLabel =
                                    dateStamp +
                                    "  -  " +
                                    selected_employee_info.pay_id +
                                    " - " +
                                    selected_employee_info.surname +
                                    ", " +
                                    selected_employee_info.first_name +
                                    " " +
                                    middleName +
                                    " (A/" +
                                    actingRank +
                                    ")";
                            }
                            $$("functions-popup-label").define("label", topBarLabel);
                            $$("functions-popup-label").refresh();
                            if (popupShowing === false) {
                                $$("functions-popup").show();
                                popupShowing = true;
                                functionsPopup.loadLeaveTypes(function (result) {
                                    if (result === "ok") {
                                        $$("bookings_leave_type").setValue("ARL");
                                    }
                                });
                            }
                            $$("overtime_roster").setValue(currRoster);
                            $$("overtime_shift").setValue(currShift);
                            $$("overtime_location").setValue(currLocation);
                            $$("overtime_dt_count").define("template", "1 employee selected");
                            $$("overtime_dt_count").refresh();
                        } else {
                            functionsPopup.closePopup();
                        }
                    }
                    let selection = $$("main_functions_list").getSelectedItem();
                    $$("ARL_leave_counts_pp").hide();
                    if (selection.id != "leave" && selection.id != "sickness") {
                        $$("ARL_leave_counts").hide();
                        $$("LSL_leave_counts").hide();
                        $$("ARL_leave_counts_pp").hide();
                        $$("bookings_form").hide();
                    } else {
                        if (selection.id == "sickness") {
                            $$("ARL_leave_counts").hide();
                            $$("LSL_leave_counts").hide();
                            $$("ARL_leave_counts_pp").hide();
                            $$("no_bookings").hide();
                            $$("logs_scrollview").hide();
                        } else {
                            getLeaveCounts();
                        }
                    }
                    let skills = selected_employee_info.skills;
                    if (
                        selected_employee_info.rank == "FF" ||
                        selected_employee_info.rank == "EFF"
                    ) {
                        rank_options = [{id: "SFF", value: "SFF"}];
                        if (skills != null) {
                            if (skills.includes("MVE") == true) {
                                rank_options.push({id: "MOFF", value: "MOFF"});
                            }
                            if (
                                skills.includes("CAC") == true ||
                                skills.includes("CCO") == true
                            ) {
                                rank_options.push({id: "COFF", value: "COFF"});
                            }
                        }
                        $$("actups_rank").define("options", rank_options);
                        $$("actups_rank").refresh();
                        $$("actups_rank").setValue("SFF");
                    } else if (
                        selected_employee_info.rank == "SFF" ||
                        selected_employee_info.rank == "ESFF"
                    ) {
                        rank_options = [{id: "SO", value: "SO"}];
                        if (skills != null) {
                            if (skills.includes("MVE") == true) {
                                rank_options.push({id: "MOFF", value: "MOFF"});
                            }
                            if (
                                skills.includes("CAC") == true ||
                                skills.includes("CCO") == true
                            ) {
                                rank_options.push({id: "COFF", value: "COFF"});
                            }
                        }
                        $$("actups_rank").define("options", rank_options);
                        $$("actups_rank").refresh();
                        $$("actups_rank").setValue("SO");
                    } else if (selected_employee_info.rank == "SO") {
                        rank_options = [
                            {id: "CMD", value: "CMD"},
                            {id: "SFF", value: "SFF"},
                        ];
                        if (skills != null) {
                            if (skills.includes("MVE") == true) {
                                rank_options.push({id: "MOFF", value: "MOFF"});
                            }
                            if (
                                skills.includes("CAC") == true ||
                                skills.includes("CCO") == true
                            ) {
                                rank_options.push({id: "COFF", value: "COFF"});
                            }
                        }
                        $$("actups_rank").define("options", rank_options);
                        $$("actups_rank").refresh();
                        $$("actups_rank").setValue("CMD");
                    } else if (selected_employee_info.rank == "CMD") {
                        rank_options = [
                            {id: "ACFO", value: "ACFO"},
                            {id: "SO", value: "SO"},
                        ];
                        if (skills != null) {
                            if (skills.includes("MVE") == true) {
                                rank_options.push({id: "MOFF", value: "MOFF"});
                            }
                            if (
                                skills.includes("CAC") == true ||
                                skills.includes("CCO") == true
                            ) {
                                rank_options.push({id: "COFF", value: "COFF"});
                            }
                        }
                        $$("actups_rank").define("options", rank_options);
                        $$("actups_rank").refresh();
                        $$("actups_rank").setValue("ACFO");
                    } else if (selected_employee_info.rank == "COP") {
                        rank_options = [{id: "SCOP", value: "SCOP"}];
                        $$("actups_rank").define("options", rank_options);
                        $$("actups_rank").refresh();
                        $$("actups_rank").setValue("SCOP");
                    } else if (selected_employee_info.rank == "SCOP") {
                        rank_options = [{id: "COFF", value: "COFF"}];
                        $$("actups_rank").define("options", rank_options);
                        $$("actups_rank").refresh();
                        $$("actups_rank").setValue("COFF");
                    } else {
                        $$("actups_rank").define("options", []);
                        $$("actups_rank").refresh();
                        $$("actups_rank").setValue("");
                    }
                }
            } else {
                let columnNo = id.column;
                let selectedDate = grid.getColumnConfig(columnNo);
                globalSelectedDate = selectedDate.header[0].date;
                if (globalSelectedDate == undefined) {
                } else {
                    let dayType = grid.getItem(1);
                    let icon = dayType[columnNo];
                    let dateStamp = moment(
                        selectedDate.header[0].date,
                        "YYYYMMDD",
                    ).format("DD/MM/YYYY");
                    let topBarLabel = dateStamp;
                    selectedShiftType = icon;
                    if (icon.includes("sun")) {
                        selectedDayShiftType = "day_shift";
                    } else if (icon.includes("moon")) {
                        selectedDayShiftType = "night_shift";
                    } else if (icon.includes("ban")) {
                        selectedDayShiftType = "no_shift";
                    }
                    if (
                        selectedDayShiftType == "day_shift" ||
                        selectedDayShiftType == "night_shift"
                    ) {
                        overtimeMode = true;
                        $$("roster_grid_" + gridId).clearSelection();
                        $$("main_functions_list").disable();
                        $$("bookings_form").hide();
                        $$("booking_info").hide();
                        $$("overtime_form").show();
                        $$("staff_movement_form").hide();
                        $$("standbys_form").hide();
                        $$("actups_form").hide();
                        $$("main_functions_list").select("overtime");
                        getShiftTimes(globalSelectedDate, currRoster, currShift);
                        $$("functions-popup-label").define(
                            "label",
                            topBarLabel + " - OVERTIME MODE -",
                        );
                        $$("functions-popup-label").refresh();
                        $$("functions-popup").show();
                        $$("overtime_roster").setValue(currRoster);
                        $$("overtime_shift").setValue(currShift);
                        $$("overtime_location").setValue(currLocation);
                        $$("overtime_activity").setValue("RC");
                        $$("overtime_dt_count").define("template", "0 employees selected");
                        $$("overtime_dt_count").refresh();
                        setDatesForBookingForms(
                            globalSelectedDate,
                            currRoster,
                            currShift,
                            icon,
                            1,
                            "overtime",
                        );
                        popupShowing = true;
                    } else {
                        functionsPopup.closePopup();
                    }
                }
            }
        }
    }



    function showRCInfoPopup(rcInfo){

        if (mouse_x > 1500){
            mouse_x = mouse_x - 400;
        }

        $$("crew_info_popup").show();
        $$("crew_info_popup").setPosition(mouse_x + 20, mouse_y - 30);
        $$("sel_employee").define("template", rcInfo);
        $$("sel_employee").refresh();
    }


    function showEmployeeInfoPopup() {

        let middleName = "";
        let phone_number = "";
        if (selected_employee_info.middle_name != null) {
            middleName = selected_employee_info.middle_name;
        }
        if (selected_employee_info.hide_phone === false) {
            phone_number = selected_employee_info.personal_phone;
        } else {
            phone_number = "";
        }
        let infoText =
            "Name: " +
            selected_employee_info.first_name +
            " " +
            middleName +
            " " +
            selected_employee_info.surname +
            "</br>Pay ID: " +
            selected_employee_info.pay_id +
            "</br>Phone: " +
            phone_number +
            "</br>Email: " +
            selected_employee_info.work_email_address +
            "</br>Suburb: " +
            selected_employee_info.home_suburb +
            "</br>Position #: " +
            selected_employee_info.position_number;
        $$("crew_info_popup").show();
        $$("crew_info_popup").setPosition(225, mouse_y - 40);
        $$("sel_employee").define("template", infoText);
        $$("sel_employee").refresh();
    }

    function load_roster() {
        $$("btn_load_totals").hide();
        totals_loaded = false;
        let roster_started = true;
        let rs_start_date = "";
        if (loading_roster === false) {
            loading_roster = true;
            $$("loader-window").show();
            windowWidth = $(window).width();
            windowHeight = $(window).height();
            $$("schedule-page").$$("roster_grid_layout").config.height = 0;
            $$("schedule-page").$$("roster_grid_layout").adjust();
            let start_date = $$("schedule-page").$$("schedule_date").getText();
            let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
            let shift_name = $$("schedule-page").$$("schedule_shifts").getText();
            let location = $$("schedule-page").$$("schedule_locations").getValue();
            let time_period = $$("schedule-page")
                .$$("schedule_display_type")
                .getText();
            let overtime_filter = $$("overtime_filter").getValue();
            let startOfPeriod;
            let end_date;
            switch (time_period) {
                case "Day":
                    startOfPeriod = moment(start_date, "DD-MM-YYYY");
                    end_date = start_date;
                    break;
                case "8 Days":
                    startOfPeriod = moment(start_date, "DD-MM-YYYY");
                    end_date = moment(startOfPeriod, "DD-MM-YYYY")
                        .add(8, "days")
                        .format("DD-MM-YYYY");
                    break;
                case "16 Days":
                    startOfPeriod = moment(start_date, "DD-MM-YYYY");
                    end_date = moment(startOfPeriod, "DD-MM-YYYY")
                        .add(16, "days")
                        .format("DD-MM-YYYY");
                    break;
                case "32 Days":
                    startOfPeriod = moment(start_date, "DD-MM-YYYY");
                    end_date = moment(startOfPeriod, "DD-MM-YYYY")
                        .add(32, "days")
                        .format("DD-MM-YYYY");
                    break;
                default:
                    startOfPeriod = moment(start_date, "DD-MM-YYYY");
                    end_date = start_date;
            }
            if (roster_name) {
                let row_height = 0;
                let cell_css = "";
                if (roster_name == "Port Pirie") {
                    row_height = 35;
                    cell_css = "port_pirie_grid";
                } else {
                    row_height = 27;
                    cell_css = "schedule_grid";
                }
                checkIfRosterStartedYet(
                    roster_name,
                    shift_name,
                    start_date,
                    function (response) {
                        if (response === "Started") {
                            roster_started = true;
                        } else {
                            roster_started = false;
                            rs_start_date = response;
                        }
                    },
                );
                if (roster_started === true) {
                    if (shift_name === "") {
                        $$("loader-window").hide();
                        loading_roster = false;
                    } else if (shift_name === "-- All Shifts --") {
                        if (location) {
                            let gridsArray = [];
                            if (shiftArray[0] === "-- All Shifts --") {
                                shiftArray.shift();
                            }
                            gridCount = shiftArray.length;
                            for (let x = 0; x < gridCount; x++) {
                                gridsArray.push({
                                    view: "datatable",
                                    id: "roster_grid_" + x.toString(),
                                    css: cell_css,
                                    select: "cell",
                                    header: true,
                                    scroll: false,
                                    navigation: false,
                                    autoheight: true,
                                    rowHeight: row_height,
                                    columns: [],
                                    data: [],
                                    on: {
                                        onItemClick: function (id, e, node) {
                                            currentGridId = x;
                                            gridItemClick($$("roster_grid_" + x.toString()), x, id, "cell", e);
                                        },
                                        onHeaderClick: function (id, header, event, target) {
                                            currentGridId = x;
                                            gridItemClick(
                                                $$("roster_grid_" + x.toString()),
                                                x,
                                                id,
                                                "header"
                                            );
                                        },
                                        onKeyPress: deleteDayOffBookings,
                                    },
                                });
                            }
                            webix.ui(
                                gridsArray,
                                $$("schedule-page").$$("roster_grid_layout"),
                            );
                            load_multiple_shifts_locations(
                                roster_name,
                                shiftArray,
                                location,
                                start_date,
                                end_date,
                                time_period,
                                startOfPeriod,
                                overtime_filter,
                            );
                        } else {
                            $$("loader-window").hide();
                            loading_roster = false;
                        }
                    } else if (
                        location === "All Stations" ||
                        location === "Central Command" ||
                        location === "Northern Command" ||
                        location === "Southern Command" ||
                        location === "All Regional Stations" ||
                        location === "Yorke & Mid North" ||
                        location === "Far North & Eyre" ||
                        location === "Riverland & Central" ||
                        location === "Limestone Coast" ||
                        location === "All Long Term Leave" ||
                        location === "All MCO Stations" ||
                        location === "All OTR"
                    ) {
                        if (shift_name) {
                            let gridsArray = [];
                            if (ro_view_showing == false) {
                                getLocationsList(location, function (locationList) {
                                    if (locationList.length > 0) {
                                        locationsArray = locationList[0].locations.split(",");
                                    }
                                });
                            } else {
                                let ro_grid = $$("schedule-page").$$("grid_ro_view_report");
                                let sel_location = "";
                                locationsArray = [];
                                ro_grid.eachRow(function (row) {
                                    let value = ro_grid.getItem(row);
                                    if (
                                        parseInt(value.total_diff) > 0 &&
                                        !value.locations.includes("TOTALS")
                                    ) {
                                        sel_location = value.locations;
                                        locationsArray.push(sel_location);
                                        locationsArray.sort();
                                    }
                                });
                            }
                            gridCount = locationsArray.length;
                            for (let x = 0; x < gridCount; x++) {
                                gridsArray.push({
                                    view: "datatable",
                                    id: "roster_grid_" + x.toString(),
                                    css: cell_css,
                                    select: "cell",
                                    header: true,
                                    scroll: false,
                                    navigation: false,
                                    autoheight: true,
                                    rowHeight: row_height,
                                    columns: [],
                                    data: [],
                                    on: {
                                        onItemClick: function (id, e, node) {
                                            currentGridId = x;
                                            gridItemClick(
                                                $$("roster_grid_" + x.toString()),
                                                x,
                                                id,
                                                "cell",
                                                e
                                            );
                                        },
                                        onHeaderClick: function (id, header, event, target) {
                                            currentGridId = x;
                                            gridItemClick(
                                                $$("roster_grid_" + x.toString()),
                                                x,
                                                id,
                                                "header",
                                            );
                                        },
                                        onKeyPress: deleteDayOffBookings,
                                    },
                                });
                                if (x == gridCount - 1) {
                                    gridsArray.push({
                                        view: "datatable",
                                        id: "totals_grid",
                                        css: "schedule_grid",
                                        select: "cell",
                                        header: true,
                                        scroll: false,
                                        autoheight: true,
                                        rowHeight: 27,
                                        columns: [],
                                        data: [],
                                    });
                                }
                            }
                            webix.ui(
                                gridsArray,
                                $$("schedule-page").$$("roster_grid_layout"),
                            );
                            load_multiple_shifts_locations(
                                roster_name,
                                shift_name,
                                locationsArray,
                                start_date,
                                end_date,
                                time_period,
                                startOfPeriod,
                                overtime_filter,
                            );
                        }
                    } else {
                        gridCount = 1;
                        webix.ui(
                            [
                                {
                                    view: "datatable",
                                    id: "roster_grid_0",
                                    css: cell_css,
                                    select: "cell",
                                    scroll: false,
                                    autoheight: true,
                                    navigation: false,
                                    rowHeight: row_height,
                                    columns: [],
                                    data: [],
                                    on: {
                                        onItemClick: function (id, e, node) {
                                            currentGridId = 0;
                                            gridItemClick($$("roster_grid_0"), 0, id, "cell", e);
                                        },
                                        onHeaderClick: function (id, header, event, target) {
                                            currentGridId = 0;
                                            gridItemClick($$("roster_grid_0"), 0, id, "header");
                                        },
                                        onKeyPress: deleteDayOffBookings,
                                    },
                                },
                            ],
                            $$("schedule-page").$$("roster_grid_layout"),
                        );
                        load_single_shift_location(
                            $$("roster_grid_0"),
                            0,
                            roster_name,
                            shift_name,
                            location,
                            start_date,
                            end_date,
                            time_period,
                            startOfPeriod,
                            overtime_filter,
                            function () {
                                let scroll_view_height =
                                    $$("schedule-page").$$("roster_grid_layout").$height;
                                $$("schedule-page").$$("roster_grid_layout").config.height =
                                    scroll_view_height + 45;
                                $$("schedule-page").$$("roster_grid_layout").adjust();
                                $$("loader-window").hide();
                                loading_roster = false;
                                if (scroll_position.x != undefined) {
                                    $$("schedule-page")
                                        .$$("roster_scroll_view")
                                        .scrollTo(scroll_position.x, scroll_position.y);
                                    scroll_position.x = undefined;
                                    scroll_position.y = undefined;
                                }
                            },
                        );
                    }
                } else {
                    $$("loader-window").hide();
                    loading_roster = false;
                    webix.alert({
                        text:
                            "The selected Roster & Shift has not started yet!<br>Change the filter date to at least the first date of the Roster/Shift start date which is<br><strong>" +
                            rs_start_date +
                            "</strong>",
                        width: 540,
                    });
                }
            } else {
                $$("loader-window").show();
                loading_roster = false;
            }
        }
    }

    function load_single_shift_location(
        grid,
        gridId,
        roster_name,
        shift_name,
        location,
        start_date,
        end_date,
        time_period,
        startOfPeriod,
        overtime_filter,
        callback,
    ) {
        grid.clearAll();
        if (shift_name) {
            async.series(
                [
                    function (callback) {
                        webix
                            .ajax()
                            .headers({Authorization: "Bearer " + api_key})
                            .get(
                                server_url + "/schedule/roster",
                                {
                                    roster_name: roster_name,
                                    shift_name: shift_name,
                                    start_date: start_date,
                                    time_period: time_period,
                                },
                                {
                                    error: function (err) {
                                        $$("loader-window").hide();
                                        loading_roster = false;
                                        callback();
                                    },
                                    success: function (results) {
                                        if (results) {
                                            let data = JSON.parse(results);
                                            let nowDate = new Date();
                                            let curr_date = moment(nowDate).format("YYYYMMDD");
                                            let sel_date_col = 0;
                                            for (let y = 1; y < data.columns.length; y++) {
                                                if (data.columns[y].header.date == curr_date) {
                                                    data.columns[y].header.css = "curr_day";
                                                    sel_date_col = y;
                                                }
                                                if (
                                                    public_holiday_dates_array.some(
                                                        (public_holiday_dates_array) =>
                                                            public_holiday_dates_array.date_string ==
                                                            data.columns[y].header.date,
                                                    )
                                                ) {
                                                    data.columns[y].header.css = "ph_day";
                                                }
                                                data.columns[y].cssFormat = function (
                                                    value,
                                                    config,
                                                    row_id,
                                                    column_id,
                                                ) {
                                                    if (value != null) {
                                                        value = value.toString();
                                                        if (value.indexOf("fa-sun") > 0) {
                                                            if (y == sel_date_col) {
                                                                return {
                                                                    "background-color": "#C3EBFE",
                                                                    "border-color": "#15317E !important",
                                                                    "border-width": "1px 2px 1px 2px !important",
                                                                };
                                                            } else {
                                                                return {
                                                                    "background-color": "#C3EBFE",
                                                                    "border-color": "dimgrey !important",
                                                                    "border-width": "1px !important",
                                                                };
                                                            }
                                                        } else if (value.indexOf("fa-moon") > 0) {
                                                            if (y == sel_date_col) {
                                                                return {
                                                                    "background-color": "#C3EBFE",
                                                                    "border-color": "#15317E !important",
                                                                    "border-width": "1px 2px 1px 2px !important",
                                                                };
                                                            } else {
                                                                return {
                                                                    "background-color": "#C3EBFE",
                                                                    "border-color": "dimgrey !important",
                                                                    "border-width": "1px !important",
                                                                };
                                                            }
                                                        } else if (value.indexOf("fa-ban") > 0) {
                                                            if (y == sel_date_col) {
                                                                return {
                                                                    "background-color": "#EAE9E9",
                                                                    "border-color": "#15317E !important",
                                                                    "border-width": "1px 2px 1px 2px !important",
                                                                };
                                                            } else {
                                                                return {
                                                                    "background-color": "#EAE9E9",
                                                                    "border-color": "dimgrey !important",
                                                                    "border-width": "1px !important",
                                                                };
                                                            }
                                                        } else if (parseInt(column_id) >= 1) {
                                                            if (row_id !== 1) {
                                                                let found = false;
                                                                let columnIndex = 0;
                                                                let payValue = $$("schedule-page")
                                                                    .$$("payNo_filter")
                                                                    .getValue();
                                                                let rankValue = $$("schedule-page")
                                                                    .$$("rank_filter")
                                                                    .getValue();
                                                                let skillValue = $$("schedule-page")
                                                                    .$$("skill_filter")
                                                                    .getValue();
                                                                let covidValue = $$("schedule-page")
                                                                    .$$("covid_filter")
                                                                    .getValue();
                                                                let adjustmentValue = 0;
                                                                for (
                                                                    let index = 0;
                                                                    index < config.roster_arrangement.length;
                                                                    index++
                                                                ) {
                                                                    adjustmentValue =
                                                                        payValue +
                                                                        rankValue +
                                                                        skillValue +
                                                                        covidValue;
                                                                    if (
                                                                        config.roster_arrangement[index]
                                                                            .date_string ===
                                                                        data.columns[y - (4 - adjustmentValue)]
                                                                            .header[0].date
                                                                    ) {
                                                                        columnIndex = index;
                                                                        found = true;
                                                                        break;
                                                                    }
                                                                }
                                                                if (found === true) {
                                                                    if (
                                                                        config.roster_arrangement[columnIndex]
                                                                            .shift_type === "day"
                                                                    ) {
                                                                        if (y == sel_date_col) {
                                                                            if (
                                                                                config.roster_arrangement[columnIndex]
                                                                                    .acting_up === true
                                                                            ) {
                                                                                return {
                                                                                    "background-color": "#B4E380",
                                                                                    "border-color": "#15317E !important",
                                                                                    "border-width":
                                                                                        "1px 2px 1px 2px !important",
                                                                                };
                                                                            } else {
                                                                                return {
                                                                                    "background-color": "#C3EBFE",
                                                                                    "border-color": "#15317E !important",
                                                                                    "border-width":
                                                                                        "1px 2px 1px 2px !important",
                                                                                };
                                                                            }
                                                                        } else {
                                                                            if (
                                                                                config.roster_arrangement[columnIndex]
                                                                                    .acting_up === true
                                                                            ) {
                                                                                return {
                                                                                    "background-color": "#B4E380",
                                                                                    "border-color": "dimgrey !important",
                                                                                    "border-width": "1px !important",
                                                                                };
                                                                            } else {
                                                                                return {
                                                                                    "background-color": "#C3EBFE",
                                                                                    "border-color": "dimgrey !important",
                                                                                    "border-width": "1px !important",
                                                                                };
                                                                            }
                                                                        }
                                                                    } else if (
                                                                        config.roster_arrangement[columnIndex]
                                                                            .shift_type === "night"
                                                                    ) {
                                                                        if (y == sel_date_col) {
                                                                            if (
                                                                                config.roster_arrangement[columnIndex]
                                                                                    .acting_up === true
                                                                            ) {
                                                                                return {
                                                                                    "background-color": "#B4E380",
                                                                                    "border-color": "#15317E !important",
                                                                                    "border-width":
                                                                                        "1px 2px 1px 2px !important",
                                                                                };
                                                                            } else {
                                                                                return {
                                                                                    "background-color": "#C3EBFE",
                                                                                    "border-color": "#15317E !important",
                                                                                    "border-width":
                                                                                        "1px 2px 1px 2px !important",
                                                                                };
                                                                            }
                                                                        } else {
                                                                            if (
                                                                                config.roster_arrangement[columnIndex]
                                                                                    .acting_up === true
                                                                            ) {
                                                                                return {
                                                                                    "background-color": "#B4E380",
                                                                                    "border-color": "dimgrey !important",
                                                                                    "border-width": "1px !important",
                                                                                };
                                                                            } else {
                                                                                return {
                                                                                    "background-color": "#C3EBFE",
                                                                                    "border-color": "dimgrey !important",
                                                                                    "border-width": "1px !important",
                                                                                };
                                                                            }
                                                                        }
                                                                    } else if (
                                                                        config.roster_arrangement[columnIndex]
                                                                            .shift_type === "off"
                                                                    ) {
                                                                        if (y == sel_date_col) {
                                                                            return {
                                                                                "background-color": "#EAE9E9",
                                                                                "border-color": "#15317E !important",
                                                                                "border-width":
                                                                                    "1px 2px 1px 2px !important",
                                                                            };
                                                                        } else {
                                                                            return {
                                                                                "background-color": "#EAE9E9",
                                                                                "border-color": "dimgrey !important",
                                                                                "border-width": "1px !important",
                                                                            };
                                                                        }
                                                                    }
                                                                } else {
                                                                    if (y == sel_date_col) {
                                                                        return {
                                                                            "background-color": "#FFFFFF !important",
                                                                            "border-color": "#15317E !important",
                                                                            "border-width":
                                                                                "1px 2px 1px 2px !important",
                                                                            color: "#FFFFFF !important",
                                                                        };
                                                                    } else {
                                                                        return {
                                                                            "background-color": "#FFFFFF !important",
                                                                            "border-color": "dimgrey !important",
                                                                            "border-width": "1px !important",
                                                                            color: "#FFFFFF !important",
                                                                        };
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                };
                                                data.columns[0].template = function (obj) {
                                                    if (obj.id == 1 || obj.pay_id == "") {
                                                        return obj.names;
                                                    } else {
                                                        if (obj.rank == "NON") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/non_uniformed.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "CMD") {
                                                            if (obj.skills != null) {
                                                                if (obj.skills.includes("CMC")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/cmd_cab_ranked.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("CMQ")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/cmd_qualified.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/cmd.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/cmd.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "SO") {
                                                            if (obj.skills != null) {
                                                                if (obj.skills.includes("SOC")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/so_cab_ranked.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("SOQ")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/so_qualified.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/so.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/so.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "SFF") {
                                                            if (obj.skills != null) {
                                                                if (
                                                                    obj.skills.includes("MVE") &&
                                                                    obj.skills.includes("MOC")
                                                                ) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff_mop_cab_ranked.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("SFRQ")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff_recall_qualified.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("SFC")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff_cab_ranked.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (
                                                                    obj.skills.includes("CCP") &&
                                                                    obj.skills.includes("CAC")
                                                                ) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff_scop.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("SFQ")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff_qualified.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("MVE")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff_mop.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("CCP")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff_cop.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/sff.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "FF") {
                                                            if (obj.skills != null) {
                                                                if (obj.skills.includes("4FF")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/ff_4th_class.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("PFF")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/ff_probationary.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("MVE")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/ff_mop.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (obj.skills.includes("CCP")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/ff_cop.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/ff.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/ff.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "MOFF") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/moff.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "COFF") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/coff.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "COP") {
                                                            if (obj.skills != null) {
                                                                if (obj.skills.includes("CCP")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/cop.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/unknown.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/unknown.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "SCOP") {
                                                            if (obj.skills != null) {
                                                                if (
                                                                    obj.skills.includes("CCP") &&
                                                                    obj.skills.includes("CAC") &&
                                                                    obj.skills.includes("SCC")
                                                                ) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/scop_cab_ranked.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (
                                                                    obj.skills.includes("CCP") &&
                                                                    obj.skills.includes("CAC") &&
                                                                    obj.skills.includes("SCQ")
                                                                ) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/scop_qualified.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else if (
                                                                    obj.skills.includes("CCP") &&
                                                                    obj.skills.includes("CAC")
                                                                ) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/scop.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/unknown.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/unknown.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "RSO") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/retained_so.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "RFS") {
                                                            if (obj.skills != null) {
                                                                if (obj.skills.includes("RSQ")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/retained_sff_qualified.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/retained_sff.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/retained_sff.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "RFF") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/retained_ff.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "REC") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/ff_recruit.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "ACFO") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/acfo.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "CO") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/chief_officer.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "DCO") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/deputy_co.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "MAN") {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/manager.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        } else if (obj.rank == "EFF") {
                                                            if (obj.skills != null) {
                                                                if (obj.skills.includes("EXC")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/eff.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/ff.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/ff.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else if (obj.rank == "ESFF") {
                                                            if (obj.skills != null) {
                                                                if (obj.skills.includes("EXC")) {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/esff.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                } else {
                                                                    return (
                                                                        "<div class='rank_img'><img src='resources/images/sff.jpg'></div><div class='rank_label'>" +
                                                                        obj.names +
                                                                        "</div>"
                                                                    );
                                                                }
                                                            } else {
                                                                return (
                                                                    "<div class='rank_img'><img src='resources/images/sff.jpg'></div><div class='rank_label'>" +
                                                                    obj.names +
                                                                    "</div>"
                                                                );
                                                            }
                                                        } else {
                                                            return (
                                                                "<div class='rank_img'><img src='resources/images/unknown.jpg'></div><div class='rank_label'>" +
                                                                obj.names +
                                                                "</div>"
                                                            );
                                                        }
                                                    }
                                                };
                                            }
                                            grid.config.columns = data.columns;
                                            grid.refreshColumns();
                                            let shift_data = data.shift_days;
                                            let iconRows = [];
                                            let iconCol = "";
                                            let titleName = "";
                                            let currLocation = $$("schedule-page")
                                                .$$("schedule_locations")
                                                .getValue();
                                            if (
                                                currLocation == "All Stations" ||
                                                currLocation == "Central Command" ||
                                                currLocation == "Northern Command" ||
                                                currLocation == "Southern Command" ||
                                                currLocation == "All Regional Stations" ||
                                                currLocation == "Yorke & Mid North" ||
                                                currLocation == "Far North & Eyre" ||
                                                currLocation == "Riverland & Central" ||
                                                currLocation == "Limestone Coast" ||
                                                currLocation == "All Long Term Leave" ||
                                                currLocation == "All MCO Stations" ||
                                                currLocation == "All OTR"
                                            ) {
                                                titleName = location.toUpperCase();
                                            } else {
                                                titleName = (
                                                    shift_name +
                                                    " - " +
                                                    location
                                                ).toUpperCase();
                                            }
                                            if (shift_data.length > 0) {
                                                iconRows.push({
                                                    id: 1,
                                                    names: titleName,
                                                    pay_id: "Pay ID",
                                                    rank: "Rank",
                                                    skills: "Skill Codes",
                                                    covid: "Covid",
                                                    $cellCss: {
                                                        names: "roster_grid_header",
                                                        pay_id: "roster_grid_header",
                                                        rank: "roster_grid_header",
                                                        skills: "roster_grid_header",
                                                        covid: "roster_grid_header",
                                                    },
                                                });
                                                for (let x = 1; x <= shift_data.length; x++) {
                                                    if (
                                                        shift_data[x - 1].roster == "Port Pirie" &&
                                                        shift_data[x - 1].icon == "sun"
                                                    ) {
                                                        iconCol = '{"id": 1,';
                                                        iconCol = iconCol + '"' + x + '":';
                                                        iconCol = iconCol + '"<span class=';
                                                        iconCol =
                                                            iconCol +
                                                            "'webix_icon fas fa-sun'></span></br><span class='webix_icon fas fa-moon'>";
                                                        iconCol = iconCol + '</span>"';
                                                        iconCol = iconCol + "}";
                                                    } else {
                                                        iconCol = '{"id": 1,';
                                                        iconCol = iconCol + '"' + x + '":';
                                                        iconCol = iconCol + '"<span class=';
                                                        iconCol =
                                                            iconCol +
                                                            "'webix_icon fas fa-" +
                                                            shift_data[x - 1].icon +
                                                            "'>";
                                                        iconCol = iconCol + '</span>"';
                                                        iconCol = iconCol + "}";
                                                    }
                                                    iconRows.push(JSON.parse(iconCol));
                                                }
                                                grid.parse(iconRows);
                                            }
                                            let payValue = $$("schedule-page")
                                                .$$("payNo_filter")
                                                .getValue();
                                            let rankValue = $$("schedule-page")
                                                .$$("rank_filter")
                                                .getValue();
                                            let skillValue = $$("schedule-page")
                                                .$$("skill_filter")
                                                .getValue();
                                            let covidValue = $$("schedule-page")
                                                .$$("covid_filter")
                                                .getValue();
                                            for (let x = 0; x < gridCount; x++) {
                                                if (payValue == 0) {
                                                    $$("roster_grid_" + x).hideColumn("pay_id");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").hideColumn("pay_id");
                                                    }
                                                } else {
                                                    $$("roster_grid_" + x).showColumn("pay_id");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").showColumn("pay_id");
                                                    }
                                                }
                                                if (rankValue == 0) {
                                                    $$("roster_grid_" + x).hideColumn("rank");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").hideColumn("rank");
                                                    }
                                                } else {
                                                    $$("roster_grid_" + x).showColumn("rank");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").showColumn("rank");
                                                    }
                                                }
                                                if (skillValue == 0) {
                                                    $$("roster_grid_" + x).hideColumn("skills");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").hideColumn("skills");
                                                    }
                                                } else {
                                                    $$("roster_grid_" + x).showColumn("skills");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").showColumn("skills");
                                                    }
                                                }
                                                if (covidValue == 0) {
                                                    $$("roster_grid_" + x).hideColumn("covid");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").hideColumn("covid");
                                                    }
                                                } else {
                                                    $$("roster_grid_" + x).showColumn("covid");
                                                    if (location == "All Stations") {
                                                        $$("totals_grid").showColumn("covid");
                                                    }
                                                }
                                            }
                                            if (location == "") {
                                                $$("loader-window").hide();
                                                loading_roster = false;
                                            }
                                        }
                                        callback();
                                    },
                                },
                            );
                        if (location == "") {
                            $$("loader-window").hide();
                            loading_roster = false;
                        }
                    },
                    function (callback) {
                        if (location.length > 0) {
                            webix
                                .ajax()
                                .headers({Authorization: "Bearer " + api_key})
                                .get(
                                    server_url + "/schedule/roster_crew",
                                    {
                                        roster_name: roster_name,
                                        shift_name: shift_name,
                                        location: location,
                                        start_date: startOfPeriod.format("DD-MM-YYYY"),
                                        end_date: end_date,
                                        overtime_filter: overtime_filter,
                                    },
                                    {
                                        error: function (err) {
                                            callback();
                                        },
                                        success: function (results) {
                                            if (results) {
                                                let data = JSON.parse(results);
                                                try {
                                                    grid.parse(data.crews);
                                                } catch (error) {
                                                    grid.refresh();
                                                }
                                            }
                                            callback();
                                        },
                                    },
                                );
                        } else {
                            callback();
                        }
                    },
                ],
                function () {
                    callback();
                },
            );
        } else {
            callback();
        }
    }

    function getLeaveCounts() {
        let startDate = "";
        let no_of_days = 0;
        let roster = $$("schedule-page").$$("schedule_rosters").getText();
        let shift = $$("schedule-page").$$("schedule_shifts").getText();
        if (shift == "-- All Shifts --") {
            shift = selectedShift;
        }
        if ($$("bookings_start_date").isVisible() == true) {
            startDate = moment($$("bookings_start_date").getValue());
            no_of_days = moment(startDate).diff(moment(), "d");
        } else {
            startDate = moment(globalSelectedDate, "YYYYMMDD");
            no_of_days = moment(startDate).diff(moment(), "d");
        }
        getCurrLeaveCounts(no_of_days, function (callback) {
            if (callback.length > 0) {
                let ARL_max = 0;
                let LSL_max = 0;
                let ALL_max = 0;
                let ARL_booked = callback[0].ARL_booked;
                let ARL_pending = callback[0].ARL_pending;
                let LSL_booked = callback[0].LSL_booked;
                let LSL_pending = callback[0].LSL_pending;
                let ALL_booked = callback[0].ALL_booked;
                let ALL_pending = callback[0].ALL_pending;
                let ARL_booked_day = callback[0].ARL_booked_day;
                let ARL_pending_day = callback[0].ARL_pending_day;
                let ARL_booked_night = callback[0].ARL_booked_night;
                let ARL_pending_night = callback[0].ARL_pending_night;
                let ALL_booked_day = callback[0].ALL_booked_day;
                let ALL_pending_day = callback[0].ALL_pending_day;
                let ALL_booked_night = callback[0].ALL_booked_night;
                let ALL_pending_night = callback[0].ALL_pending_night;
                let ALL_booked_both = callback[0].ALL_booked_both;
                let ALL_pending_both = callback[0].ALL_pending_both;
                if (roster == "Port Pirie") {
                    if (no_of_days > 30) {
                        ARL_max = 1;
                        LSL_max = 1;
                        $$("ARL_leave_counts").setValue(
                            "<span style='color: lightblue'>DAY |</span> ARL/PHOL/RET/TOIL/SOIL - Max: " +
                            ARL_max +
                            " / Booked: " +
                            ARL_booked_day +
                            " / Pending: " +
                            ARL_pending_day,
                        );
                        $$("ARL_leave_counts_pp").setValue(
                            "<span style='color: lightblue'>NIGHT |</span> ARL/PHOL/RET/TOIL/SOIL - Max: " +
                            ARL_max +
                            " / Booked: " +
                            ARL_booked_night +
                            " / Pending: " +
                            ARL_pending_night,
                        );
                        $$("ARL_leave_counts_pp").show();
                        $$("LSL_leave_counts").setValue(
                            "LSL/LSLS/LSLH - Max: " +
                            LSL_max +
                            " / Booked: " +
                            LSL_booked +
                            " / Pending: " +
                            LSL_pending,
                        );
                    } else {
                        ALL_max = 3;
                        $$("ARL_leave_counts").setValue(
                            "<span style='color: lightblue'>DAY |</span> ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH - Max: " +
                            ALL_max +
                            " / Booked: " +
                            (ALL_booked_day + ALL_booked_both) +
                            " / Pending: " +
                            (ALL_pending_day + ALL_pending_both),
                        );
                        $$("ARL_leave_counts_pp").setValue(
                            "<span style='color: lightblue'>NIGHT |</span> ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH - Max: " +
                            ALL_max +
                            " / Booked: " +
                            (ALL_booked_night + ALL_booked_both) +
                            " / Pending: " +
                            (ALL_pending_night + ALL_pending_both),
                        );
                        $$("ARL_leave_counts_pp").show();
                    }
                } else if (roster == "Metro") {
                    if (no_of_days > 30) {
                        if (
                            selected_employee_info.rank == "SO" ||
                            selected_employee_info.rank == "CMD"
                        ) {
                            ARL_max = 3;
                            LSL_max = 3;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL - Max: " +
                                ARL_max +
                                " / Booked: " +
                                ARL_booked +
                                " / Pending: " +
                                ARL_pending,
                            );
                            $$("LSL_leave_counts").setValue(
                                "LSL/LSLS/LSLH - Max: " +
                                LSL_max +
                                " / Booked: " +
                                LSL_booked +
                                " / Pending: " +
                                LSL_pending,
                            );
                        } else if (
                            selected_employee_info.rank == "FF" ||
                            selected_employee_info.rank == "SFF" ||
                            selected_employee_info.rank == "MOFF" ||
                            selected_employee_info.rank == "ESFF" ||
                            selected_employee_info.rank == "EFF"
                        ) {
                            ARL_max = 5;
                            LSL_max = 5;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL - Max: " +
                                ARL_max +
                                " / Booked: " +
                                ARL_booked +
                                " / Pending: " +
                                ARL_pending,
                            );
                            $$("LSL_leave_counts").setValue(
                                "LSL/LSLS/LSLH - Max: " +
                                LSL_max +
                                " / Booked: " +
                                LSL_booked +
                                " / Pending: " +
                                LSL_pending,
                            );
                        }
                    } else {
                        if (
                            selected_employee_info.rank == "SO" ||
                            selected_employee_info.rank == "CMD"
                        ) {
                            ALL_max = 6;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        } else if (
                            selected_employee_info.rank == "FF" ||
                            selected_employee_info.rank == "SFF" ||
                            selected_employee_info.rank == "MOFF" ||
                            selected_employee_info.rank == "ESFF" ||
                            selected_employee_info.rank == "EFF"
                        ) {
                            ALL_max = 10;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        }
                    }
                } else if (roster == "OTR") {
                    if (no_of_days > 30) {
                        if (selected_employee_info.rank == "SO") {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        } else if (
                            selected_employee_info.rank == "FF" ||
                            selected_employee_info.rank == "SFF" ||
                            selected_employee_info.rank == "ESFF" ||
                            selected_employee_info.rank == "EFF"
                        ) {
                            ALL_max = 2;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        }
                    } else {
                        if (selected_employee_info.rank == "SO") {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        } else if (
                            selected_employee_info.rank == "FF" ||
                            selected_employee_info.rank == "SFF" ||
                            selected_employee_info.rank == "ESFF" ||
                            selected_employee_info.rank == "EFF"
                        ) {
                            ALL_max = 2;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        }
                    }
                } else if (roster == "Mt Gambier") {
                    if (no_of_days > 30) {
                        if (selected_employee_info.rank == "SO") {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        } else if (
                            selected_employee_info.rank == "FF" ||
                            selected_employee_info.rank == "SFF" ||
                            selected_employee_info.rank == "ESFF" ||
                            selected_employee_info.rank == "EFF"
                        ) {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        }
                    } else {
                        if (selected_employee_info.rank == "SO") {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        } else if (
                            selected_employee_info.rank == "FF" ||
                            selected_employee_info.rank == "SFF" ||
                            selected_employee_info.rank == "ESFF" ||
                            selected_employee_info.rank == "EFF"
                        ) {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        }
                    }
                } else if (roster == "Comms") {
                    if (
                        shift == "A Shift" ||
                        shift == "B Shift" ||
                        shift == "C Shift" ||
                        shift == "D Shift"
                    ) {
                        if (no_of_days > 30) {
                            ARL_max = 1;
                            LSL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL - Max: " +
                                ARL_max +
                                " / Booked: " +
                                ARL_booked +
                                " / Pending: " +
                                ARL_pending,
                            );
                            $$("LSL_leave_counts").setValue(
                                "LSL/LSLS/LSLH - Max: " +
                                LSL_max +
                                " / Booked: " +
                                LSL_booked +
                                " / Pending: " +
                                LSL_pending,
                            );
                        } else {
                            ALL_max = 2;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        }
                    } else if (shift == "CommCen E1" || shift == "CommCen E2") {
                        if (no_of_days > 30) {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        } else {
                            ALL_max = 1;
                            $$("ARL_leave_counts").setValue(
                                "ARL/PHOL/RET/TOIL/SOIL/LSL/LSLS/LSLH",
                            );
                            $$("LSL_leave_counts").setValue(
                                "Max: " +
                                ALL_max +
                                " / Booked: " +
                                ALL_booked +
                                " / Pending: " +
                                ALL_pending,
                            );
                        }
                    }
                }
            } else {
                $$("ARL_leave_counts").setValue(
                    "ARL/PHOL/RET/TOIL/SOIL/UPHL/URET  - Max: 0 / Booked: 0 / Pending: 0",
                );
                $$("LSL_leave_counts").setValue(
                    "LSL/LSLS/ULSL/LSLH - Max: 0 / Booked: 0 / Pending: 0",
                );
            }
            $$("ARL_leave_counts").show();
            if (roster == "Port Pirie") {
                if (no_of_days > 30) {
                    $$("LSL_leave_counts").show();
                } else {
                    $$("LSL_leave_counts").hide();
                }
            } else {
                $$("LSL_leave_counts").show();
            }
        });
    }

    function getCurrLeaveCounts(no_of_days, callback) {
        let currRoster = $$("schedule-page").$$("schedule_rosters").getText();
        let currShift = $$("schedule-page").$$("schedule_shifts").getText();
        if (currShift == "-- All Shifts --") {
            currShift = selectedShift;
        }
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/schedule/get_day_leave_counts",
                {
                    date_string: globalSelectedDate,
                    rank: selected_employee_info.rank,
                    roster: currRoster,
                    shift: currShift,
                    no_of_days: no_of_days,
                },
                {
                    error: function (err) {
                        callback("error");
                    },
                    success: function (result) {
                        if (result) {
                            let values = JSON.parse(result);
                            callback(values);
                        } else {
                            callback([]);
                        }
                    },
                },
            );
    }

    function load_grand_totals(
        roster_name,
        shift_name,
        start_date,
        end_date,
        time_period,
        startOfPeriod,
        callback,
    ) {
        let grid = $$("totals_grid");
        grid.clearAll();
        async.series(
            [
                function (callback) {
                    webix
                        .ajax()
                        .headers({Authorization: "Bearer " + api_key})
                        .get(
                            server_url + "/schedule/roster_grand_totals_header",
                            {
                                roster_name: roster_name,
                                shift_name: shift_name,
                                start_date: start_date,
                                time_period: time_period,
                            },
                            {
                                error: function (err) {
                                    $$("loader-window").hide();
                                    loading_roster = false;
                                    callback();
                                },
                                success: function (results) {
                                    if (results) {
                                        let data = JSON.parse(results);
                                        let nowDate = new Date();
                                        let curr_date = moment(nowDate).format("YYYYMMDD");
                                        let sel_date_col = 0;
                                        for (let y = 1; y < data.columns.length; y++) {
                                            if (data.columns[y].header.date == curr_date) {
                                                data.columns[y].header.css = "curr_day";
                                                sel_date_col = y;
                                            }
                                            if (
                                                public_holiday_dates_array.some(
                                                    (public_holiday_dates_array) =>
                                                        public_holiday_dates_array.date_string ==
                                                        data.columns[y].header.date,
                                                )
                                            ) {
                                                data.columns[y].header.css = "ph_day";
                                            }
                                            data.columns[y].cssFormat = function (
                                                value,
                                                config,
                                                row_id,
                                                column_id,
                                            ) {
                                                if (value != null) {
                                                    value = value.toString();
                                                    if (value.indexOf("fa-sun") > 0) {
                                                        if (y == sel_date_col) {
                                                            return {
                                                                "background-color": "#C3EBFE",
                                                                "border-color": "#15317E !important",
                                                                "border-width": "1px 2px 1px 2px !important",
                                                            };
                                                        } else {
                                                            return {
                                                                "background-color": "#C3EBFE",
                                                                "border-color": "dimgrey !important",
                                                                "border-width": "1px !important",
                                                            };
                                                        }
                                                    } else if (value.indexOf("fa-moon") > 0) {
                                                        if (y == sel_date_col) {
                                                            return {
                                                                "background-color": "#C3EBFE",
                                                                "border-color": "#15317E !important",
                                                                "border-width": "1px 2px 1px 2px !important",
                                                            };
                                                        } else {
                                                            return {
                                                                "background-color": "#C3EBFE",
                                                                "border-color": "dimgrey !important",
                                                                "border-width": "1px !important",
                                                            };
                                                        }
                                                    } else if (value.indexOf("fa-ban") > 0) {
                                                        if (y == sel_date_col) {
                                                            return {
                                                                "background-color": "#EAE9E9",
                                                                "border-color": "#15317E !important",
                                                                "border-width": "1px 2px 1px 2px !important",
                                                            };
                                                        } else {
                                                            return {
                                                                "background-color": "#EAE9E9",
                                                                "border-color": "dimgrey !important",
                                                                "border-width": "1px !important",
                                                            };
                                                        }
                                                    } else if (parseInt(column_id) >= 1) {
                                                        if (row_id !== 1) {
                                                            let found = false;
                                                            let columnIndex = 0;
                                                            let payValue = $$("schedule-page")
                                                                .$$("payNo_filter")
                                                                .getValue();
                                                            let rankValue = $$("schedule-page")
                                                                .$$("rank_filter")
                                                                .getValue();
                                                            let skillValue = $$("schedule-page")
                                                                .$$("skill_filter")
                                                                .getValue();
                                                            let covidValue = $$("schedule-page")
                                                                .$$("covid_filter")
                                                                .getValue();
                                                            let adjustmentValue = 0;
                                                            for (
                                                                let index = 0;
                                                                index < config.roster_arrangement.length;
                                                                index++
                                                            ) {
                                                                adjustmentValue =
                                                                    payValue +
                                                                    rankValue +
                                                                    skillValue +
                                                                    covidValue;
                                                                if (
                                                                    config.roster_arrangement[index]
                                                                        .date_string ===
                                                                    data.columns[y - (4 - adjustmentValue)]
                                                                        .header[0].date
                                                                ) {
                                                                    columnIndex = index;
                                                                    found = true;
                                                                    break;
                                                                }
                                                            }
                                                            if (found === true) {
                                                                if (
                                                                    config.roster_arrangement[columnIndex]
                                                                        .shift_type === "day"
                                                                ) {
                                                                    if (y == sel_date_col) {
                                                                        return {
                                                                            "background-color": "#C3EBFE",
                                                                            "border-color": "#15317E !important",
                                                                            "border-width":
                                                                                "1px 2px 1px 2px !important",
                                                                        };
                                                                    } else {
                                                                        return {
                                                                            "background-color": "#C3EBFE",
                                                                            "border-color": "dimgrey !important",
                                                                            "border-width": "1px !important",
                                                                        };
                                                                    }
                                                                } else if (
                                                                    config.roster_arrangement[columnIndex]
                                                                        .shift_type === "night"
                                                                ) {
                                                                    if (y == sel_date_col) {
                                                                        return {
                                                                            "background-color": "#C3EBFE",
                                                                            "border-color": "#15317E !important",
                                                                            "border-width":
                                                                                "1px 2px 1px 2px !important",
                                                                        };
                                                                    } else {
                                                                        return {
                                                                            "background-color": "#C3EBFE",
                                                                            "border-color": "dimgrey !important",
                                                                            "border-width": "1px !important",
                                                                        };
                                                                    }
                                                                } else if (
                                                                    config.roster_arrangement[columnIndex]
                                                                        .shift_type === "off"
                                                                ) {
                                                                    if (y == sel_date_col) {
                                                                        return {
                                                                            "background-color": "#EAE9E9",
                                                                            "border-color": "#15317E !important",
                                                                            "border-width":
                                                                                "1px 2px 1px 2px !important",
                                                                        };
                                                                    } else {
                                                                        return {
                                                                            "background-color": "#EAE9E9",
                                                                            "border-color": "dimgrey !important",
                                                                            "border-width": "1px !important",
                                                                        };
                                                                    }
                                                                }
                                                            } else {
                                                                if (y == sel_date_col) {
                                                                    return {
                                                                        "background-color": "#FFFFFF !important",
                                                                        "border-color": "#15317E !important",
                                                                        "border-width":
                                                                            "1px 2px 1px 2px !important",
                                                                    };
                                                                } else {
                                                                    return {
                                                                        "background-color": "#FFFFFF !important",
                                                                        "border-color": "dimgrey !important",
                                                                        "border-width": "1px !important",
                                                                    };
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            };
                                        }
                                        data.columns[0].template = function (obj) {
                                            return obj.names;
                                        };
                                        grid.config.columns = data.columns;
                                        grid.refreshColumns();
                                        let shift_data = data.shift_days;
                                        let iconRows = [];
                                        let iconCol = "";
                                        if (shift_data.length > 0) {
                                            iconRows.push({
                                                id: 1,
                                                names: "ALL STATIONS",
                                                pay_id: "",
                                                rank: "",
                                                skills: "",
                                                $cellCss: {
                                                    names: "roster_grid_header",
                                                    pay_id: "roster_grid_header",
                                                    rank: "roster_grid_header",
                                                    skills: "roster_grid_header",
                                                },
                                            });
                                            for (let x = 1; x <= shift_data.length; x++) {
                                                iconCol = '{"id": 1,';
                                                iconCol = iconCol + '"' + x + '":';
                                                iconCol = iconCol + '"<span class=';
                                                iconCol =
                                                    iconCol +
                                                    "'webix_icon fas fa-" +
                                                    shift_data[x - 1].icon +
                                                    "'>";
                                                iconCol = iconCol + '</span>"';
                                                iconCol = iconCol + "}";
                                                iconRows.push(JSON.parse(iconCol));
                                            }
                                            grid.parse(iconRows);
                                        }
                                        let payValue = $$("schedule-page")
                                            .$$("payNo_filter")
                                            .getValue();
                                        let rankValue = $$("schedule-page")
                                            .$$("rank_filter")
                                            .getValue();
                                        let skillValue = $$("schedule-page")
                                            .$$("skill_filter")
                                            .getValue();
                                        let covidValue = $$("schedule-page")
                                            .$$("covid_filter")
                                            .getValue();
                                        for (let x = 0; x < gridCount; x++) {
                                            if (payValue == 0) {
                                                $$("roster_grid_" + x).hideColumn("pay_id");
                                                $$("totals_grid").hideColumn("pay_id");
                                            } else {
                                                $$("roster_grid_" + x).showColumn("pay_id");
                                                $$("totals_grid").showColumn("pay_id");
                                            }
                                            if (rankValue == 0) {
                                                $$("roster_grid_" + x).hideColumn("rank");
                                                $$("totals_grid").hideColumn("rank");
                                            } else {
                                                $$("roster_grid_" + x).showColumn("rank");
                                                $$("totals_grid").showColumn("rank");
                                            }
                                            if (skillValue == 0) {
                                                $$("roster_grid_" + x).hideColumn("skills");
                                                $$("totals_grid").hideColumn("skills");
                                            } else {
                                                $$("roster_grid_" + x).showColumn("skills");
                                                $$("totals_grid").showColumn("skills");
                                            }
                                            if (covidValue == 0) {
                                                $$("roster_grid_" + x).hideColumn("covid");
                                                $$("totals_grid").hideColumn("covid");
                                            } else {
                                                $$("roster_grid_" + x).showColumn("covid");
                                                $$("totals_grid").showColumn("covid");
                                            }
                                        }
                                    }
                                    callback();
                                },
                            },
                        );
                },
                function (callback) {
                    webix
                        .ajax()
                        .headers({Authorization: "Bearer " + api_key})
                        .get(
                            server_url + "/schedule/roster_grand_totals",
                            {
                                roster_name: roster_name,
                                shift_name: shift_name,
                                locations: locationsArray,
                                start_date: startOfPeriod.format("DD-MM-YYYY"),
                                end_date: end_date,
                            },
                            {
                                error: function (err) {
                                    callback();
                                },
                                success: function (results) {
                                    if (results) {
                                        grid.parse(results);
                                    }
                                    callback();
                                },
                            },
                        );
                },
                function (callback) {
                    webix
                        .ajax()
                        .headers({Authorization: "Bearer " + api_key})
                        .get(
                            server_url + "/schedule/roster_total_bookings",
                            {
                                roster_name: roster_name,
                                shift_name: shift_name,
                                locations: locationsArray,
                                start_date: startOfPeriod.format("DD-MM-YYYY"),
                                no_of_days: time_period,
                            },
                            {
                                error: function (err) {
                                    callback();
                                },
                                success: function (results) {
                                    if (results) {
                                        grid.parse(results);
                                    }
                                    callback();
                                },
                            },
                        );
                },
            ],
            function () {
                callback("OK");
            },
        );
    }

    function load_multiple_shifts_locations(
        roster_name,
        shifts,
        locations,
        start_date,
        end_date,
        time_period,
        startOfPeriod,
        overtime_filter,
    ) {
        let shift_name = "";
        let location = "";
        let last_grid = 0;
        let roster_started = true;
        if (Array.isArray(shifts)) {
            location = locations;
            let x = 0;
            async.eachSeries(
                shifts,
                function (shift, callback) {
                    checkIfRosterStartedYet(
                        roster_name,
                        shift,
                        start_date,
                        function (response) {
                            if (response === "Started") {
                                roster_started = true;
                            } else {
                                roster_started = false;
                            }
                        },
                    );
                    if (roster_started === true) {
                        load_single_shift_location(
                            $$("roster_grid_" + x),
                            x,
                            roster_name,
                            shift,
                            location,
                            start_date,
                            end_date,
                            time_period,
                            startOfPeriod,
                            overtime_filter,
                            function () {
                                x++;
                                last_grid = x;
                                $$("schedule-page").$$("roster_scroll_view").scrollTo(0, 1e4);
                                callback();
                            },
                        );
                    } else {
                        callback();
                    }
                },
                function () {
                    let scroll_view_height =
                        $$("schedule-page").$$("roster_grid_layout").$height;
                    $$("schedule-page").$$("roster_grid_layout").config.height =
                        scroll_view_height + 55;
                    $$("schedule-page").$$("roster_grid_layout").adjust();
                    $$("loader-window").hide();
                    loading_roster = false;
                    if (scroll_position.x != undefined) {
                        $$("schedule-page")
                            .$$("roster_scroll_view")
                            .scrollTo(scroll_position.x, scroll_position.y);
                        scroll_position.x = undefined;
                        scroll_position.y = undefined;
                    } else {
                        $$("schedule-page").$$("roster_scroll_view").scrollTo(0, 1e4);
                    }
                },
            );
        } else if (Array.isArray(locations)) {
            shift_name = shifts;
            let x = 0;
            async.eachSeries(
                locations,
                function (location, callback) {
                    load_single_shift_location(
                        $$("roster_grid_" + x),
                        x,
                        roster_name,
                        shift_name,
                        location,
                        start_date,
                        end_date,
                        time_period,
                        startOfPeriod,
                        overtime_filter,
                        function () {
                            x++;
                            $$("schedule-page").$$("roster_scroll_view").scrollTo(0, 1e4);
                            callback();
                        },
                    );
                },
                function () {
                    let selected_location = $$("schedule-page")
                        .$$("schedule_locations")
                        .getValue();
                    if (
                        selected_location == "All Stations" ||
                        selected_location == "Central Command" ||
                        selected_location == "Northern Command" ||
                        selected_location == "Southern Command" ||
                        selected_location == "All Regional Stations" ||
                        selected_location == "Yorke & Mid North" ||
                        selected_location == "Far North & Eyre" ||
                        selected_location == "Riverland & Central" ||
                        selected_location == "Limestone Coast" ||
                        selected_location == "All Long Term Leave" ||
                        selected_location == "All MCO Stations" ||
                        selected_location == "All OTR"
                    ) {
                        let scroll_view_height =
                            $$("schedule-page").$$("roster_grid_layout").$height;
                        $$("schedule-page").$$("roster_grid_layout").config.height =
                            scroll_view_height + 170;
                        $$("schedule-page").$$("roster_grid_layout").adjust();
                    }
                    $$("loader-window").hide();
                    loading_roster = false;
                    if (
                        selected_location == "All Stations" ||
                        selected_location == "Central Command" ||
                        selected_location == "Northern Command" ||
                        selected_location == "Southern Command" ||
                        selected_location == "All Regional Stations" ||
                        selected_location == "Yorke & Mid North" ||
                        selected_location == "Far North & Eyre" ||
                        selected_location == "Riverland & Central" ||
                        selected_location == "Limestone Coast" ||
                        selected_location == "All Long Term Leave" ||
                        selected_location == "All MCO Stations" ||
                        selected_location == "All OTR"
                    ) {
                        $$("btn_load_totals").show();
                    }
                    $$("schedule-page").$$("roster_scroll_view").scrollTo(0, 1e4);
                },
            );
        }
    }

    function load_shifts(rosterName) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/schedule/shifts",
                {roster_name: rosterName},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        if (results) {
                            let shiftList = JSON.parse(results);
                            if (shiftList.data.length > 0) {
                                shiftArray = shiftList.data[0].shifts.split(",");
                                locationsArray = shiftList.data[0].locations.split(",");
                                if (
                                    rosterName != "Regional Operations" &&
                                    rosterName != "OTR"
                                ) {
                                    shiftArray.unshift("-- All Shifts --");
                                }
                                let shiftOptions = [];
                                shiftArray.forEach(function (value) {
                                    shiftOptions.push(value);
                                });
                                let locationOptions = [];
                                let locationName = "";
                                let locationSplit = [];
                                locationsArray.forEach(function (value) {
                                    locationSplit = value.split("-");
                                    locationName = locationSplit[1].trim();
                                    locationOptions.push({id: locationName, value: value});
                                });
                                if (rosterName == "Metro") {
                                    locationOptions.unshift({
                                        id: "All MCO Stations",
                                        value: "-- All MCO Stations --",
                                    });
                                    locationOptions.unshift({
                                        id: "Southern Command",
                                        value: "-- Southern Command --",
                                    });
                                    locationOptions.unshift({
                                        id: "Northern Command",
                                        value: "-- Northern Command --",
                                    });
                                    locationOptions.unshift({
                                        id: "Central Command",
                                        value: "-- Central Command --",
                                    });
                                    locationOptions.unshift({
                                        id: "All Stations",
                                        value: "-- All Stations --",
                                    });
                                } else if (rosterName == "Long Term Leave") {
                                    locationOptions.unshift({
                                        id: "All Long Term Leave",
                                        value: "-- All Long Term Leave --",
                                    });
                                } else if (rosterName == "OTR") {
                                    locationOptions.unshift({
                                        id: "All OTR",
                                        value: "-- All OTR --",
                                    });
                                }
                                $$("schedule-page")
                                    .$$("schedule_shifts")
                                    .define("options", shiftOptions);
                                $$("schedule-page").$$("schedule_shifts").refresh();
                                $$("schedule-page")
                                    .$$("schedule_locations")
                                    .define("options", locationOptions);
                                $$("schedule-page").$$("schedule_locations").refresh();
                            }
                        }
                    },
                },
            );
    }

    function getLocationsList(location, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/schedule/get_locations",
                {group_name: location},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        let value = JSON.parse(results);
                        callback(value);
                    },
                },
            );
    }

    function deleteDayOffBookings(code) {
        if (code == 46) {
            if (popupShowing === false) {
                if (
                    user_permission_level === 6 &&
                    user_logged_in != selected_employee_info.pay_id
                ) {
                    webix.alert({
                        text: "You can only delete your own bookings!",
                        width: 550,
                    });
                } else {
                    getUserBookings(
                        selected_employee_info.pay_id,
                        globalSelectedDate,
                        function (bookingArray) {
                            let no_of_bookings = bookingArray.length;
                            if (no_of_bookings > 0) {
                                $$("bookings-window-label").define(
                                    "template",
                                    "The following Bookings were found on the selected date <strong>" +
                                    moment(globalSelectedDate, "YYYYMMDD").format(
                                        "DD/MM/YYYY",
                                    ) +
                                    "</strong> for <strong>" +
                                    selected_employee_info.surname +
                                    ", " +
                                    selected_employee_info.first_name +
                                    "</strong></br>Tick the box next to each booking you want to delete.</br></br><i><strong>Note: For each selected booking only the selected day will be deleted and NOT the whole booking.</strong></i>",
                                );
                                $$("bookings-window-label").refresh();
                                let grid = $$("bookings-window_grid");
                                grid.clearAll();
                                bookingArray.forEach(function (result) {
                                    grid.add({
                                        booking_id: result.booking_id,
                                        roster: result.roster,
                                        shift: result.shift,
                                        location: result.location,
                                        bk_date: result.start_date,
                                        hours: result.hours,
                                        type: result.leave_type_code,
                                        status: result.status,
                                        comments: result.comments,
                                    });
                                });
                                grid.refreshColumns();
                                $$("bookings-window").show();
                            }
                        },
                    );
                }
            }
        }
    }

    function checkIfRosterStartedYet(
        roster_name,
        shift_name,
        start_date,
        callback,
    ) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/schedule/check_roster_started_yet",
                {roster_name: roster_name, shift_name: shift_name},
                {
                    error: function (err) {
                        callback("Started");
                    },
                    success: function (results) {
                        let value = JSON.parse(results);
                        if (value.length > 0) {
                            if (
                                moment(start_date, "DD/MM/YYYY").isBefore(
                                    moment(value[0].seq_start_date, "YYYY-MM-DD"),
                                )
                            ) {
                                callback(
                                    moment(value[0].seq_start_date, "YYYY-MM-DD").format(
                                        "DD/MM/YYYY",
                                    ),
                                );
                            } else {
                                callback("Started");
                            }
                        } else {
                            callback("Started");
                        }
                    },
                },
            );
    }

    return {
        initialise: function () {
            initApplication();
        },
        reload_roster: function () {
            load_roster();
        },
        reload_leaveCounts: function () {
            getLeaveCounts();
        },
    };
})();
