let availabilityReport = (function () {
  let ro_report_loading = false;
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("btn_availability_report_close").attachEvent(
      "onItemClick",
      function (id, e) {
        $$("availability_report_window").hide();
      },
    );
    $$("grid_availability_report").attachEvent("onItemClick", function (id, e) {
      if (user_permission_level === 1) {
        let yP = window.Event
          ? e.pageY
          : e.clientY +
            (document.documentElement.scrollTop
              ? document.documentElement.scrollTop
              : document.body.scrollTop);
        let xP = $$("availability_report_window").$view.offsetLeft;
        let item = this.getItem(id);
        let location = item.locations;
        let selCol = id.column;
        let selRank = "";
        let selType = "";
        let selValue = 0;
        if (selCol === "so_req") {
          selRank = "SO";
          selType = "Required";
          selValue = item.so_req;
        } else if (selCol === "cmd_req") {
          selRank = "CMD";
          selType = "Required";
          selValue = item.cmd_req;
        } else if (selCol === "sff_req") {
          selRank = "SFF / FF";
          selType = "Required";
          selValue = item.sff_req;
        } else if (selCol === "moff_req") {
          selRank = "MOFF";
          selType = "Required";
          selValue = item.moff_req;
        } else if (selCol === "so_diff") {
          selRank = "SO";
          selType = "Difference";
          selValue = item.so_diff;
        } else if (selCol === "cmd_diff") {
          selRank = "CMD";
          selType = "Difference";
          selValue = item.cmd_diff;
        } else if (selCol === "sff_diff") {
          selRank = "SFF / FF";
          selType = "Difference";
          selValue = item.sff_diff;
        } else if (selCol === "moff_diff") {
          selRank = "MOFF";
          selType = "Difference";
          selValue = item.moff_diff;
        } else if (selCol === "so_act") {
          selRank = "SO";
          selType = "Actual";
          selValue = item.so_act;
        } else if (selCol === "cmd_act") {
          selRank = "CMD";
          selType = "Actual";
          selValue = item.cmd_act;
        } else if (selCol === "sff_act") {
          selRank = "SFF / FF";
          selType = "Actual";
          selValue = item.sff_act;
        } else if (selCol === "moff_act") {
          selRank = "MOFF";
          selType = "Actual";
          selValue = item.moff_act;
        }
        if (selRank == "SO" || selRank == "SFF / FF") {
          if (selType === "Actual" || selType === "Required") {
            let skillCodesList = "";
            let infoText = "";
            if (selType === "Required") {
              getMinSkillCodes(location, function (response) {
                if (selRank === "SO") {
                  skillCodesList =
                    "R9O: <strong>" +
                    response.min_R9O +
                    "</strong>, A5O: <strong>" +
                    response.min_A5O +
                    "</strong>, H6O: <strong>" +
                    response.min_H6O +
                    "</strong>, C3O: <strong>" +
                    response.min_C3O +
                    "</strong>, R4O: <strong>" +
                    response.min_R4O +
                    "</strong>, GPO: <strong>" +
                    response.min_GPO +
                    "</strong>, DRO: <strong>" +
                    response.min_DRO +
                    "</strong>";
                  infoText =
                    "<div style='font-size: 16px'><strong>- REQ - " +
                    selType +
                    " Values</strong>" +
                    "<br><br>" +
                    "For: " +
                    "<strong>" +
                    location.toUpperCase() +
                    "</strong>" +
                    " - " +
                    "<strong>" +
                    selRank +
                    "</strong>" +
                    "<br><br>" +
                    "Total Required:<br>" +
                    selRank +
                    " <strong>" +
                    selValue +
                    "</strong>" +
                    "<br><br>" +
                    "Skill Codes:<br>" +
                    skillCodesList +
                    "</div>";
                } else if (selRank === "SFF / FF") {
                  skillCodesList =
                    "R9: <strong>" +
                    response.min_R9 +
                    "</strong>, A5: <strong>" +
                    response.min_A5 +
                    "</strong>, H6: <strong>" +
                    response.min_H6 +
                    "</strong>, C3: <strong>" +
                    response.min_C3 +
                    "</strong>, R4: <strong>" +
                    response.min_R4 +
                    "</strong>, GP: <strong>" +
                    response.min_GP +
                    "</strong>, ICV: <strong>" +
                    response.min_ICV +
                    "</strong>, HL: <strong>" +
                    response.min_HL +
                    "</strong>";
                  infoText =
                    "<div style='font-size: 16px'><strong>- REQ - " +
                    selType +
                    " Values</strong>" +
                    "<br><br>" +
                    "For: " +
                    "<strong>" +
                    location.toUpperCase() +
                    "</strong>" +
                    " - " +
                    "<strong>" +
                    selRank +
                    "</strong>" +
                    "<br><br>" +
                    "Total Required:<br>" +
                    "SFF: <strong>" +
                    response.min_SF +
                    "</strong>, FF: <strong>" +
                    response.min_FF +
                    "</strong><br><br>" +
                    "Skill Codes:<br>" +
                    skillCodesList +
                    "</div>";
                }
              });
            } else if (selType === "Actual") {
              let roster_name = $$("schedule-page")
                .$$("schedule_rosters")
                .getText();
              let shift_name = $$("schedule-page")
                .$$("schedule_shifts")
                .getText();
              if (globalSelectedDate == "") {
                let currDate = $$("schedule-page")
                  .$$("schedule_date")
                  .getValue();
                globalSelectedDate = moment(currDate).format("YYYYMMDD");
              }
              getActualSkillCodes(
                roster_name,
                shift_name,
                location,
                globalSelectedDate,
                selRank,
                function (response) {
                  if (selRank === "SO") {
                    skillCodesList =
                      "R9O: <strong>" +
                      response.min_R9O +
                      "</strong>, A5O: <strong>" +
                      response.min_A5O +
                      "</strong>, H6O: <strong>" +
                      response.min_H6O +
                      "</strong>, C3O: <strong>" +
                      response.min_C3O +
                      "</strong>, R4O: <strong>" +
                      response.min_R4O +
                      "</strong>, GPO: <strong>" +
                      response.min_GPO +
                      "</strong>, DRO: <strong>" +
                      response.min_DRO +
                      "</strong>";
                    infoText =
                      "<div style='font-size: 16px'><strong>- ACT - " +
                      selType +
                      " Values</strong>" +
                      "<br><br>" +
                      "For: " +
                      "<strong>" +
                      location.toUpperCase() +
                      "</strong>" +
                      " - " +
                      "<strong>" +
                      selRank +
                      "</strong>" +
                      "<br><br>" +
                      "Total Actual:<br>" +
                      selRank +
                      " <strong>" +
                      selValue +
                      "</strong>" +
                      "<br><br>" +
                      "Skill Codes:<br>" +
                      skillCodesList +
                      "</div>";
                  } else if (selRank === "SFF / FF") {
                    skillCodesList =
                      "R9: <strong>" +
                      response.min_R9 +
                      "</strong>, A5: <strong>" +
                      response.min_A5 +
                      "</strong>, H6: <strong>" +
                      response.min_H6 +
                      "</strong>, C3: <strong>" +
                      response.min_C3 +
                      "</strong>, R4: <strong>" +
                      response.min_R4 +
                      "</strong>, GP: <strong>" +
                      response.min_GP +
                      "</strong>, ICV: <strong>" +
                      response.min_ICV +
                      "</strong>, HL: <strong>" +
                      response.min_HL +
                      "</strong>";
                    infoText =
                      "<div style='font-size: 16px'><strong>- ACT - " +
                      selType +
                      " Values</strong>" +
                      "<br><br>" +
                      "For: " +
                      "<strong>" +
                      location.toUpperCase() +
                      "</strong>" +
                      " - " +
                      "<strong>" +
                      selRank +
                      "</strong>" +
                      "<br><br>" +
                      "Total Actual:<br>" +
                      "SFF: <strong>" +
                      response.act_SF +
                      "</strong>, FF: <strong>" +
                      response.act_FF +
                      "</strong><br><br>" +
                      "Skill Codes:<br>" +
                      skillCodesList +
                      "</div>";
                  }
                },
              );
            }
            $$("skill_code_info_popup").show();
            $$("skill_code_info_popup").setPosition(xP + 130, yP + 50);
            $$("sel_ar_value").define("template", infoText);
            $$("sel_ar_value").refresh();
          }
        }
      }
    });
  }
  function getActualSkillCodes(
    roster_name,
    shift_name,
    location_name,
    selDate,
    selRank,
    callback,
  ) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/get_actual_skill_codes",
        {
          roster_name: roster_name,
          shift_name: shift_name,
          location_name: location_name,
          selDate: selDate,
          selRank: selRank,
        },
        {
          error: function (err) {
            callback({});
          },
          success: function (results) {
            if (results) {
              let minValuesArray = JSON.parse(results);
              let min_values = {};
              if (selRank === "SO") {
                min_values.min_R9O =
                  minValuesArray[0].act_R9O - minValuesArray[0].act_R9O_bk;
                min_values.min_A5O =
                  minValuesArray[0].act_A5O - minValuesArray[0].act_A5O_bk;
                min_values.min_H6O =
                  minValuesArray[0].act_H6O - minValuesArray[0].act_H6O_bk;
                min_values.min_C3O =
                  minValuesArray[0].act_C3O - minValuesArray[0].act_C3O_bk;
                min_values.min_R4O =
                  minValuesArray[0].act_R4O - minValuesArray[0].act_R4O_bk;
                min_values.min_GPO =
                  minValuesArray[0].act_GPO - minValuesArray[0].act_GPO_bk;
                min_values.min_DRO =
                  minValuesArray[0].act_DRO - minValuesArray[0].act_DRO_bk;
              } else if (selRank === "SFF / FF") {
                min_values.min_R9 =
                  minValuesArray[0].act_R9 - minValuesArray[0].act_R9_bk;
                min_values.min_A5 =
                  minValuesArray[0].act_A5 - minValuesArray[0].act_A5_bk;
                min_values.min_H6 =
                  minValuesArray[0].act_H6 - minValuesArray[0].act_H6_bk;
                min_values.min_C3 =
                  minValuesArray[0].act_C3 - minValuesArray[0].act_C3_bk;
                min_values.min_R4 =
                  minValuesArray[0].act_R4 - minValuesArray[0].act_R4_bk;
                min_values.min_GP =
                  minValuesArray[0].act_GP - minValuesArray[0].act_GP_bk;
                min_values.min_ICV =
                  minValuesArray[0].act_ICV - minValuesArray[0].act_ICV_bk;
                min_values.min_HL =
                  minValuesArray[0].act_HL - minValuesArray[0].act_HL_bk;
                min_values.act_FF =
                  minValuesArray[0].act_FF - minValuesArray[0].act_FF_bk;
                min_values.act_SF =
                  minValuesArray[0].act_SF - minValuesArray[0].act_SF_bk;
              }
              callback(min_values);
            }
          },
        },
      );
  }
  function getMinSkillCodes(location, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/locations",
        {},
        {
          error: function (err) {
            callback({});
          },
          success: function (results) {
            if (results) {
              let minValuesArray = JSON.parse(results);
              let min_values = {};
              min_values = minValuesArray.find((obj) => obj.name === location);
              callback(min_values);
            }
          },
        },
      );
  }
  function loadAvailabilityReport(grid, callback) {
    if (ro_report_loading === false) {
      ro_report_loading = true;
      let roster_name = $$("schedule-page").$$("schedule_rosters").getText();
      let shift_name = $$("schedule-page").$$("schedule_shifts").getText();
      let location_name = $$("schedule-page")
        .$$("schedule_locations")
        .getValue();
      grid.clearAll();
      if (shift_name == "-- All Shifts --" && location_name == "") {
        webix.alert(
          "You must select a Shift to view the Availability Report when a Location has not been specified!",
        );
      } else {
        if (shift_name == "" && location_name == "") {
          webix.alert(
            "You must select a Shift and Location to view the Availability Report!",
          );
        } else {
          if (shift_name == "-- All Shifts --") {
            shift_name = selectedShift;
          }
          if (location_name == "All OTR") {
            location_name = selectedLocation;
          }
          if (globalSelectedDate == "") {
            let currDate = $$("schedule-page").$$("schedule_date").getValue();
            globalSelectedDate = moment(currDate).format("YYYYMMDD");
          }
          let ra_check_date = moment(globalSelectedDate, "YYYYMMDD").format(
            "MM-DD-YYYY",
          );
          let otr_shift_type = "";
          getTimesForShift(ra_check_date, "OTR", "OTR", function (response) {
            let values = JSON.parse(response);
            if (values.length > 0) {
              otr_shift_type = values[0].icon;
            } else {
              otr_shift_type = "error";
            }
          });
          webix
            .ajax()
            .headers({ Authorization: "Bearer " + api_key })
            .get(
              server_url + "/schedule/load_availability_report",
              {
                roster: roster_name,
                shift: shift_name,
                location: location_name,
                date_string: globalSelectedDate,
                otr_shift_type: otr_shift_type,
                sel_shift_type: selectedShiftType,
              },
              {
                error: function (err) {},
                success: function (results) {
                  if (results) {
                    let data = JSON.parse(results);
                    let cmd_act = 0;
                    let so_act = 0;
                    let sff_act = 0;
                    let ff_act = 0;
                    let moff_act = 0;
                    let cmd_actups = 0;
                    let so_actups = 0;
                    let sff_actups = 0;
                    let ff_actups = 0;
                    let moff_actups = 0;
                    let cmd_all_day_bks = 0;
                    let so_all_day_bks = 0;
                    let sff_all_day_bks = 0;
                    let ff_all_day_bks = 0;
                    let moff_all_day_bks = 0;
                    let cmdDiff = 0;
                    let soDiff = 0;
                    let sffDiff = 0;
                    let ffDiff = 0;
                    let moffDiff = 0;
                    let totalReq = 0;
                    let totalAct = 0;
                    let totalDiff = 0;
                    let cmd_req_total = 0;
                    let cmd_act_total = 0;
                    let cmd_diff_total = 0;
                    let so_req_total = 0;
                    let so_act_total = 0;
                    let so_diff_total = 0;
                    let sff_req_total = 0;
                    let sff_act_total = 0;
                    let sff_diff_total = 0;
                    let moff_req_total = 0;
                    let moff_act_total = 0;
                    let moff_diff_total = 0;
                    let total_req_total = 0;
                    let total_act_total = 0;
                    let total_diff_total = 0;
                    let cmd_back_to_hs = 0;
                    let so_back_to_hs = 0;
                    let sff_back_to_hs = 0;
                    let ff_back_to_hs = 0;
                    let moff_back_to_hs = 0;
                    let list_index = 0;
                    let list_index2 = 0;
                    let list_index3 = 0;
                    data.metro.forEach(function (value) {
                      cmd_actups = value.total_cmd_actups;
                      so_actups = value.total_so_actups;
                      sff_actups = value.total_sff_actups;
                      ff_actups = value.total_ff_actups;
                      moff_actups = value.total_moff_actups;
                      cmd_all_day_bks = value.total_cmd_both_bks;
                      so_all_day_bks = value.total_so_both_bks;
                      sff_all_day_bks = value.total_sff_both_bks;
                      ff_all_day_bks = value.total_ff_both_bks;
                      moff_all_day_bks = value.total_moff_both_bks;
                      cmd_back_to_hs = value.cmd_sm_back_to_hs;
                      so_back_to_hs = value.so_sm_back_to_hs;
                      sff_back_to_hs = value.sff_sm_back_to_hs;
                      ff_back_to_hs = value.ff_sm_back_to_hs;
                      moff_back_to_hs = value.moff_sm_back_to_hs;
                      cmd_act =
                        value.total_cmd_ras -
                        value.total_cmd_bks -
                        cmd_all_day_bks +
                        cmd_actups +
                        cmd_back_to_hs;
                      so_act =
                        value.total_so_ras -
                        value.total_so_bks -
                        so_all_day_bks +
                        so_actups +
                        so_back_to_hs;
                      sff_act =
                        value.total_sff_ras -
                        value.total_sff_bks -
                        sff_all_day_bks +
                        sff_actups +
                        sff_back_to_hs;
                      ff_act =
                        value.total_ff_ras -
                        value.total_ff_bks -
                        ff_all_day_bks +
                        ff_actups +
                        ff_back_to_hs;
                      moff_act =
                        value.total_moff_ras -
                        value.total_moff_bks -
                        moff_all_day_bks +
                        moff_actups +
                        moff_back_to_hs;
                      cmdDiff = cmd_act - value.min_CMD;
                      soDiff = so_act - value.min_SO;
                      sffDiff = sff_act - value.min_SF;
                      ffDiff = ff_act - value.min_FF;
                      moffDiff = moff_act - value.min_MOF;
                      totalReq =
                        value.min_CMD +
                        value.min_SO +
                        value.min_SF +
                        value.min_FF +
                        value.min_MOF;
                      totalAct = cmd_act + so_act + sff_act + ff_act + moff_act;
                      totalDiff = totalAct - totalReq;
                      cmd_req_total = cmd_req_total + value.min_CMD;
                      cmd_act_total = cmd_act_total + cmd_act;
                      cmd_diff_total = cmd_diff_total + cmdDiff;
                      so_req_total = so_req_total + value.min_SO;
                      so_act_total = so_act_total + so_act;
                      so_diff_total = so_diff_total + soDiff;
                      sff_req_total =
                        sff_req_total + (value.min_SF + value.min_FF);
                      sff_act_total = sff_act_total + (sff_act + ff_act);
                      sff_diff_total = sff_diff_total + (sffDiff + ffDiff);
                      moff_req_total = moff_req_total + value.min_MOF;
                      moff_act_total = moff_act_total + moff_act;
                      moff_diff_total = moff_diff_total + moffDiff;
                      total_req_total = total_req_total + totalReq;
                      total_act_total = total_act_total + totalAct;
                      total_diff_total = total_diff_total + totalDiff;
                      if (totalDiff < 0) {
                        list_index = 1;
                      } else if (totalDiff > 0) {
                        list_index = 2;
                      } else {
                        list_index = 3;
                      }
                      if (soDiff < 0) {
                        list_index2 = 1;
                      } else if (soDiff > 0) {
                        list_index2 = 2;
                      } else {
                        list_index2 = 3;
                      }
                      if (sffDiff + ffDiff < 0) {
                        list_index3 = 1;
                      } else if (sffDiff + ffDiff > 0) {
                        list_index3 = 2;
                      } else {
                        list_index3 = 3;
                      }
                      grid.add({
                        locations: value.name,
                        cmd_req: value.min_CMD,
                        cmd_act: cmd_act,
                        cmd_diff: cmdDiff,
                        so_req: value.min_SO,
                        so_act: so_act,
                        so_diff: soDiff,
                        sff_req: value.min_SF + value.min_FF,
                        sff_act: sff_act + ff_act,
                        sff_diff: sffDiff + ffDiff,
                        moff_req: value.min_MOF,
                        moff_act: moff_act,
                        moff_diff: moffDiff,
                        total_req: totalReq,
                        total_act: totalAct,
                        total_diff: totalDiff,
                        list_index: list_index,
                        list_index2: list_index2,
                        list_index3: list_index3,
                      });
                    });
                    let header_string = "";
                    if (
                      roster_name == "Metro" &&
                      otr_shift_type == "sun" &&
                      selectedShiftType.includes("fa-sun")
                    ) {
                      header_string =
                        "Availability Report  -  (" +
                        roster_name +
                        "  -  " +
                        shift_name +
                        ")  for  " +
                        moment(globalSelectedDate, "YYYYMMDD").format(
                          "DD/MM/YYYY",
                        ) +
                        " <i>(Note: includes OTR)</i>";
                    } else {
                      header_string =
                        "Availability Report  -  (" +
                        roster_name +
                        "  -  " +
                        shift_name +
                        ")  for  " +
                        moment(globalSelectedDate, "YYYYMMDD").format(
                          "DD/MM/YYYY",
                        );
                    }
                    let report_header_label =
                      "<span class='header_font'>" + header_string + "</span>";
                    $$("availability_report_label").define(
                      "label",
                      report_header_label,
                    );
                    $$("availability_report_label").refresh();
                    if (ro_view_showing == true) {
                      $$("schedule-page")
                        .$$("ro_view_label")
                        .define("label", report_header_label);
                      $$("schedule-page").$$("ro_view_label").refresh();
                      grid.sort([
                        { by: "list_index", dir: "asc", as: "int" },
                        { by: "list_index2", dir: "asc", as: "int" },
                        { by: "list_index3", dir: "asc", as: "int" },
                      ]);
                    } else {
                      grid.sort([
                        { by: "list_index", dir: "asc", as: "int" },
                        { by: "list_index2", dir: "asc", as: "int" },
                        { by: "list_index3", dir: "asc", as: "int" },
                      ]);
                      if (windowHeight >= 950) {
                        $$("availability_report_window").define("height", 915);
                        $$("availability_report_window").resize();
                      } else {
                        $$("availability_report_window").define("height", 605);
                        $$("availability_report_window").resize();
                      }
                    }
                    let grand_totals = [
                      {
                        locations: "<div style='float: right;'>TOTALS</div>",
                        cmd_req: cmd_req_total,
                        cmd_act: cmd_act_total,
                        cmd_diff: cmd_diff_total,
                        so_req: so_req_total,
                        so_act: so_act_total,
                        so_diff: so_diff_total,
                        sff_req: sff_req_total,
                        sff_act: sff_act_total,
                        sff_diff: sff_diff_total,
                        moff_req: moff_req_total,
                        moff_act: moff_act_total,
                        moff_diff: moff_diff_total,
                        total_req: total_req_total,
                        total_act: total_act_total,
                        total_diff: total_diff_total,
                        list_index: 4,
                      },
                    ];
                    grid.parse(JSON.stringify(grand_totals));
                    let totals_row_id = grid.getLastId();
                    grid.addRowCss(totals_row_id, "totals_row");
                    if (data.otr.length > 0) {
                      cmd_act = 0;
                      so_act = 0;
                      sff_act = 0;
                      ff_act = 0;
                      moff_act = 0;
                      cmd_actups = 0;
                      so_actups = 0;
                      sff_actups = 0;
                      ff_actups = 0;
                      moff_actups = 0;
                      cmd_all_day_bks = 0;
                      so_all_day_bks = 0;
                      sff_all_day_bks = 0;
                      ff_all_day_bks = 0;
                      moff_all_day_bks = 0;
                      cmdDiff = 0;
                      soDiff = 0;
                      sffDiff = 0;
                      ffDiff = 0;
                      moffDiff = 0;
                      totalReq = 0;
                      totalAct = 0;
                      totalDiff = 0;
                      cmd_req_total = 0;
                      cmd_act_total = 0;
                      cmd_diff_total = 0;
                      so_req_total = 0;
                      so_act_total = 0;
                      so_diff_total = 0;
                      sff_req_total = 0;
                      sff_act_total = 0;
                      sff_diff_total = 0;
                      moff_req_total = 0;
                      moff_act_total = 0;
                      moff_diff_total = 0;
                      total_req_total = 0;
                      total_act_total = 0;
                      total_diff_total = 0;
                      cmd_back_to_hs = 0;
                      so_back_to_hs = 0;
                      sff_back_to_hs = 0;
                      ff_back_to_hs = 0;
                      moff_back_to_hs = 0;
                      list_index = 0;
                      list_index2 = 0;
                      list_index3 = 0;
                      data.otr.forEach(function (value) {
                        cmd_actups = value.total_cmd_actups;
                        so_actups = value.total_so_actups;
                        sff_actups = value.total_sff_actups;
                        ff_actups = value.total_ff_actups;
                        moff_actups = value.total_moff_actups;
                        cmd_all_day_bks = value.total_cmd_both_bks;
                        so_all_day_bks = value.total_so_both_bks;
                        sff_all_day_bks = value.total_sff_both_bks;
                        ff_all_day_bks = value.total_ff_both_bks;
                        moff_all_day_bks = value.total_moff_both_bks;
                        cmd_back_to_hs = value.cmd_sm_back_to_hs;
                        so_back_to_hs = value.so_sm_back_to_hs;
                        sff_back_to_hs = value.sff_sm_back_to_hs;
                        ff_back_to_hs = value.ff_sm_back_to_hs;
                        moff_back_to_hs = value.moff_sm_back_to_hs;
                        cmd_act =
                          value.total_cmd_ras -
                          value.total_cmd_bks -
                          cmd_all_day_bks +
                          cmd_actups +
                          cmd_back_to_hs;
                        so_act =
                          value.total_so_ras -
                          value.total_so_bks -
                          so_all_day_bks +
                          so_actups +
                          so_back_to_hs;
                        sff_act =
                          value.total_sff_ras -
                          value.total_sff_bks -
                          sff_all_day_bks +
                          sff_actups +
                          sff_back_to_hs;
                        ff_act =
                          value.total_ff_ras -
                          value.total_ff_bks -
                          ff_all_day_bks +
                          ff_actups +
                          ff_back_to_hs;
                        moff_act =
                          value.total_moff_ras -
                          value.total_moff_bks -
                          moff_all_day_bks +
                          moff_actups +
                          moff_back_to_hs;
                        cmdDiff = cmd_act - value.min_CMD;
                        soDiff = so_act - value.min_SO;
                        sffDiff = sff_act - value.min_SF;
                        ffDiff = ff_act - value.min_FF;
                        moffDiff = moff_act - value.min_MOF;
                        totalReq =
                          value.min_CMD +
                          value.min_SO +
                          value.min_SF +
                          value.min_FF +
                          value.min_MOF;
                        totalAct =
                          cmd_act + so_act + sff_act + ff_act + moff_act;
                        totalDiff = totalAct - totalReq;
                        cmd_req_total = cmd_req_total + value.min_CMD;
                        cmd_act_total = cmd_act_total + cmd_act;
                        cmd_diff_total = cmd_diff_total + cmdDiff;
                        so_req_total = so_req_total + value.min_SO;
                        so_act_total = so_act_total + so_act;
                        so_diff_total = so_diff_total + soDiff;
                        sff_req_total =
                          sff_req_total + (value.min_SF + value.min_FF);
                        sff_act_total = sff_act_total + (sff_act + ff_act);
                        sff_diff_total = sff_diff_total + (sffDiff + ffDiff);
                        moff_req_total = moff_req_total + value.min_MOF;
                        moff_act_total = moff_act_total + moff_act;
                        moff_diff_total = moff_diff_total + moffDiff;
                        total_req_total = total_req_total + totalReq;
                        total_act_total = total_act_total + totalAct;
                        total_diff_total = total_diff_total + totalDiff;
                        if (totalDiff < 0) {
                          list_index = 1;
                        } else if (totalDiff > 0) {
                          list_index = 2;
                        } else {
                          list_index = 3;
                        }
                        if (soDiff < 0) {
                          list_index2 = 1;
                        } else if (soDiff > 0) {
                          list_index2 = 2;
                        } else {
                          list_index2 = 3;
                        }
                        if (sffDiff + ffDiff < 0) {
                          list_index3 = 1;
                        } else if (sffDiff + ffDiff > 0) {
                          list_index3 = 2;
                        } else {
                          list_index3 = 3;
                        }
                        grid.add({
                          locations: value.name,
                          cmd_req: value.min_CMD,
                          cmd_act: cmd_act,
                          cmd_diff: cmdDiff,
                          so_req: value.min_SO,
                          so_act: so_act,
                          so_diff: soDiff,
                          sff_req: value.min_SF + value.min_FF,
                          sff_act: sff_act + ff_act,
                          sff_diff: sffDiff + ffDiff,
                          moff_req: value.min_MOF,
                          moff_act: moff_act,
                          moff_diff: moffDiff,
                          total_req: totalReq,
                          total_act: totalAct,
                          total_diff: totalDiff,
                          list_index: list_index,
                          list_index2: list_index2,
                          list_index3: list_index3,
                        });
                      });
                      grand_totals = [
                        {
                          locations: "<div style='float: right;'>TOTALS</div>",
                          cmd_req: cmd_req_total,
                          cmd_act: cmd_act_total,
                          cmd_diff: cmd_diff_total,
                          so_req: so_req_total,
                          so_act: so_act_total,
                          so_diff: so_diff_total,
                          sff_req: sff_req_total,
                          sff_act: sff_act_total,
                          sff_diff: sff_diff_total,
                          moff_req: moff_req_total,
                          moff_act: moff_act_total,
                          moff_diff: moff_diff_total,
                          total_req: total_req_total,
                          total_act: total_act_total,
                          total_diff: total_diff_total,
                          list_index: 4,
                        },
                      ];
                      grid.parse(JSON.stringify(grand_totals));
                      totals_row_id = grid.getLastId();
                      grid.addRowCss(totals_row_id, "totals_row");
                    }
                  }
                  setTimeout(function () {
                    ro_report_loading = false;
                    callback("ok");
                  }, 250);
                },
              },
            );
        }
      }
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
    loadReport: function (grid, callback) {
      loadAvailabilityReport(grid, callback);
    },
  };
})();
