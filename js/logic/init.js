
if (window.location.href.includes("https://sapphire.mfs.sa.gov.au/")) {
    server_url = "https://sapphire.mfs.sa.gov.au/sapphire";
    live_site = true;
} else if (window.location.href.includes("https://sapphire-uat.mfs.sa.gov.au/")) {
    server_url = "https://sapphire-uat.mfs.sa.gov.au/sapphire";
    live_site = false;
} else {
    server_url = "http://localhost:6001";
    live_site = false;
}

const scriptsToLoad = [
    "js/layout/application-layout.js?v1.8.0",
    "js/logic/application-logic.js?v1.8.0",
    "js/layout/app_menu.js?v1.8.0",
    "js/logic/permissions.js?v1.8.0",
    "js/layout/admin/admin-layout.js?v1.8.0",
    "js/layout/admin/search-layout.js?v1.8.0",
    "js/layout/admin/locations-layout.js?v1.8.0",
    "js/layout/admin/location-groups-layout.js?v1.8.0",
    "js/layout/admin/shift-groups_layout.js?v1.8.0",
    "js/layout/admin/shifts-layout.js?v1.8.0",
    "js/layout/admin/rosters-layout.js?v1.8.0",
    "js/layout/admin/leave_types-layout.js?v1.8.0",
    "js/layout/admin/shift_types-layout.js?v1.8.0",
    "js/layout/admin/activity_types-layout.js?v1.8.0",
    "js/layout/admin/roster-arrangement-layout.js?v1.8.0",
    "js/layout/admin/skill_codes-layout.js?v1.8.0",
    "js/logic/admin/locations-logic.js?v1.8.0",
    "js/logic/admin/location-groups-logic.js?v1.8.0",
    "js/logic/admin/shift-groups_logic.js?v1.8.0",
    "js/logic/admin/shifts-logic.js?v1.8.0",
    "js/logic/admin/rosters-logic.js?v1.8.0",
    "js/logic/admin/roster-arrangement-logic.js?v1.8.0",
    "js/logic/admin/leave_types-logic.js?v1.8.0",
    "js/logic/admin/shift_types-logic.js?v1.8.0",
    "js/logic/admin/activity_types-logic.js?v1.8.0",
    "js/logic/admin/search-logic.js?v1.8.0",
    "js/logic/admin/skill_codes-logic.js?v1.8.0",
    "js/layout/admin/tools_public_holidays-layout.js?v1.8.0",
    "js/layout/admin/tools_login_message_layout.js?v1.8.0",
    "js/logic/admin/tools_public_holidays-logic.js?v1.8.0",
    "js/logic/admin/tools_login_message_logic.js?v1.8.0",
    "js/layout/admin/covid_vaccines-layout.js?v1.8.0",
    "js/logic/admin/covid_vaccines-logic.js?v1.8.0",
    "js/layout/leave/soil_toil_balances-layout.js?v1.8.0",
    "js/logic/leave/soil_toil_balances-logic.js?v1.8.0",
    "js/layout/reports/reports-layout.js?v1.8.0",
    "js/layout/reports/reports_bookings_layout.js?v1.8.0",
    "js/layout/reports/reports_deletion_layout.js?v1.8.0",
    "js/layout/reports/reports_sickness_layout.js?v1.8.0",
    "js/layout/reports/reports_sickness_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_overtime_layout.js?v1.8.0",
    "js/layout/reports/reports_supplementary_layout.js?v1.8.0",
    "js/layout/reports/reports_travel_layout.js?v1.8.0",
    "js/layout/reports/reports_staff_movement-layout.js?v1.8.0",
    "js/layout/reports/reports_skill_codes_layout.js?v1.8.0",
    "js/layout/reports/reports_roster_arrangements_layout.js?v1.8.0",
    "js/layout/reports/reports_ra_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_ras_by_rank_layout.js?v1.8.0",
    "js/layout/reports/reports_overtime_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_staff_overtime_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_skill_codes_availability_layout.js?v1.8.0",
    "js/layout/reports/reports_reason_overtime_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_shift_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_rrl_layout.js?v1.8.0",
    "js/layout/reports/reports_booking_errors_layout.js?v1.8.0",
    "js/layout/reports/reports_covid_vax_status_layout.js?v1.8.0",
    "js/layout/reports/reports_covid_vax_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_covid_status_summary_layout.js?v1.8.0",
    "js/layout/reports/reports_leave_taken_breakdown_layout.js?v1.8.0",
    "js/layout/reports/reports_overtime_fatigue_layout.js?v1.8.0",
    "js/layout/reports/reports_sickness_certificate_layout.js?v1.8.0",
    "js/logic/reports/reports_bookings_logic.js?v1.8.0",
    "js/logic/reports/reports_deletion_logic.js?v1.8.0",
    "js/logic/reports/reports_sickness_logic.js?v1.8.0",
    "js/logic/reports/reports_sickness_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_overtime_logic.js?v1.8.0",
    "js/logic/reports/reports_supplementary_logic.js?v1.8.0",
    "js/logic/reports/reports_travel_logic.js?v1.8.0",
    "js/logic/reports/reports_staff_movement-logic.js?v1.8.0",
    "js/logic/reports/reports_skill_codes_logic.js?v1.8.0",
    "js/logic/reports/reports_roster_arrangements_logic.js?v1.8.0",
    "js/logic/reports/reports_ra_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_ras_by_rank_logic.js?v1.8.0",
    "js/logic/reports/reports_overtime_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_staff_overtime_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_skill_codes_availability_logic.js?v1.8.0",
    "js/logic/reports/reports_reason_overtime_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_shift_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_rrl_logic.js?v1.8.0",
    "js/logic/reports/reports_booking_errors_logic.js?v1.8.0",
    "js/logic/reports/reports_covid_vax_status_logic.js?v1.8.0",
    "js/logic/reports/reports_covid_vax_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_covid_status_summary_logic.js?v1.8.0",
    "js/logic/reports/reports_leave_taken_breakdown_logic.js?v1.8.0",
    "js/logic/reports/reports_overtime_fatigue_logic.js?v1.8.0",
    "js/logic/reports/reports_sickness_certificate_logic.js?v1.8.0",
    "js/layout/reports/reports_overtime_fatigue_distance_layout.js?v1.8.0",
    "js/logic/reports/reports_overtime_fatigue_distance_logic.js?v1.8.0",
    "js/layout/leave/bookings-layout.js?v1.8.0",
    "js/layout/leave/create-layout.js?v1.8.0",
    "js/layout/leave/edit-delete-layout.js?v1.8.0",
    "js/layout/leave/leave_groups-layout.js?v1.8.0",
    "js/layout/leave/leave_swap-layout.js?v1.8.0",
    "js/layout/leave/leave_group_log-layout.js?v1.8.0",
    "js/layout/leave/leave_swap_list-layout.js?v1.8.0",
    "js/layout/leave/leave_requests-layout.js?v1.8.0",
    "js/layout/leave/leave_counts-layout.js?v1.8.0",
    "js/layout/leave/leave_log-layout.js?v1.8.0",
    "js/logic/leave/create-logic.js?v1.8.0",
    "js/logic/leave/edit-delete-logic.js?v1.8.0",
    "js/logic/leave/leave_groups-logic.js?v1.8.0",
    "js/logic/leave/leave_swap-logic.js?v1.8.0",
    "js/logic/leave/leave_group_log-logic.js?v1.8.0",
    "js/logic/leave/leave_swap_list-logic.js?v1.8.0",
    "js/logic/leave/leave_requests-logic.js?v1.8.0",
    "js/logic/leave/leave_counts-logic.js?v1.8.0",
    "js/logic/leave/leave_log-logic.js?v1.8.0",
    "js/layout/staff_movement/acting_up_log-layout.js?v1.8.0",
    "js/layout/staff_movement/staff_movement_log-layout.js?v1.8.0",
    "js/layout/staff_movement/standby_log-layout.js?v1.8.0",
    "js/logic/staff_movement/acting_up_log-logic.js?v1.8.0",
    "js/logic/staff_movement/staff_movement_log-logic.js?v1.8.0",
    "js/logic/staff_movement/standby_log-logic.js?v1.8.0",
    "js/layout/overtime/overtime_log-layout.js?v1.8.0",
    "js/logic/overtime/overtime_log-logic.js?v1.8.0",
    "js/layout/travel/travel_create-layout.js?v1.8.0",
    "js/layout/travel/travel_requests-layout.js?v1.8.0",
    "js/logic/travel/travel_create-logic.js?v1.8.0",
    "js/logic/travel/travel_requests-logic.js?v1.8.0",
    "js/layout/sickness/sickness_certificates-layout.js?v1.8.0",
    "js/logic/sickness/sickness_certificates-logic.js?v1.8.0",
    "js/layout/sickness/sick_bookings_log-layout.js?v1.8.0",
    "js/logic/sickness/sick_bookings_log-logic.js?v1.8.0",
    "js/layout/roster/schedule-layout.js?v1.8.0",
    "js/layout/roster/functions-popup-layout.js?v1.8.0",
    "js/layout/roster/crew_info_popup-layout.js?v1.8.0",
    "js/layout/roster/availability_report-layout.js?v1.8.0",
    "js/logic/roster/schedule-logic.js?v1.8.0",
    "js/logic/roster/functions-popup-logic.js?v1.8.0",
    "js/logic/roster/crew_info_popup-logic.js?v1.8.0",
    "js/logic/roster/availability_report-logic.js?v1.8.0",
    "js/layout/messaging/messaging-layout.js?v1.8.0",
    "js/logic/messaging/messaging-logic.js?v1.8.0",
    "js/layout/applications/applications-layout.js?v1.8.0",
    "js/logic/applications/applications-logic.js?v1.8.0",
    "js/layout/applications/applications-standbys-layout.js?v1.8.0",
    "js/layout/applications/applications-rrls-layout.js?v1.8.0",
    "js/layout/applications/applications-sp90-layout.js?v1.8.0",
    "js/logic/applications/applications-standbys-logic.js?v1.8.0",
    "js/logic/applications/applications-rrls-logic.js?v1.8.0",
    "js/layout/respond52/respond52_layout.js?v1.8.0",
    "js/layout/respond52/incident_report_layout.js?v1.8.0",
    "js/layout/respond52/incident_type_summary_report_layout.js?v1.8.0",
    "js/layout/respond52/incident_location_summary_report_layout.js?v1.8.0",
    "js/layout/respond52/riding_position_location_report_layout.js?v1.8.0",
    "js/logic/respond52/incident_report_logic.js?v1.8.0",
    "js/logic/respond52/incident_type_summary_report_logic.js?v1.8.0",
    "js/logic/respond52/incident_location_summary_report_logic.js?v1.8.0",
    "js/logic/respond52/riding_position_location_report_logic.js?v1.8.0",
    "js/layout/respond52/incident_info_search_report_layout.js?v1.8.0",
    "js/logic/respond52/incident_info_search_report_logic.js?v1.8.0",
    "js/layout/reports/reports_station_preferences_layout.js?v1.8.0",
    "js/logic/reports/reports_station_preferences_logic.js?v1.8.0",
    "js/layout/leave/day_work_requests-layout.js?v1.8.0",
    "js/logic/leave/day_work_requests-logic.js?v1.8.0",
    "js/layout/admin/tools_admin_settings-layout.js?v1.8.0",
    "js/logic/admin/tools_admin_settings-logic.js?v1.8.0"
];


webix.ready(async function () {
    webix.ui.fullScreen();
    webix.env.cdn = "lib";
    webix.UIManager.removeHotKey("esc");

    webix.attachEvent("onBeforeAjax", function (mode, url, data, request, headers, files, params) {
        request.withCredentials = true;
    });

    let root = webix.ui({
        cells: [loginLayout.render()],
        animate: false,
    });

    login.initialise();

    if (sessionStorage.getItem("inactivity") === "true") {
        sessionStorage.removeItem("inactivity");
        webix.confirm({
            text: "You session has timed out due to inactivity!<br><br>NOTE: You have not been signed out so if your SSO token hasn't expired you can log back in without entering a password however if you are on a shared PC and have finished using it we suggest completely signing out of your SSO account.",
            type: "alert-error",
            title: "Auto Logout Notice",
            width: 500,
            ok: "Sign Out",
            cancel: "OK",
            callback: function (result) {
                switch (result) {
                    case true:
                        window.location.href = server_url + '/login/logout';
                }
            },
        });
    } else {
        try {
            // Try calling protected endpoint to see if user is authenticated
            const res = await fetch(server_url + '/login/me', {
                method: 'GET',
                credentials: 'include' // important: sends cookies
            });

            if (res.ok) {

                const user = await res.json();

                if (user.data && Object.keys(user.data).length > 0) {

                    api_key = user.api_key;

                    async function loadScriptsSequentially(scripts) {
                        for (const src of scripts) {
                            await new Promise((resolve, reject) => {
                                const script = document.createElement("script");
                                script.src = src;
                                script.type = "text/javascript";
                                script.async = false;
                                script.onload = () => {
                                    resolve();
                                };
                                script.onerror = () => {
                                    console.error(`Failed to load: ${src}`);
                                    reject(new Error(`Script load error for ${src}`));
                                };
                                document.body.appendChild(script);
                            });
                        }
                    }

                    await loadScriptsSequentially(scriptsToLoad);

                    root.destructor();  // removes all UI elements from DOM, frees memory

                    root = webix.ui({
                        cells: [loginLayout.render(), appPageLayout.render()],
                        animate: false,
                    });

                    webix.delay(() => {

                        initViews(function (response) {
                            if (response === "loaded") {

                                Backbone.history.start();

                                $$("views").show();

                                user_logged_in = user.data.pay_id;
                                user_permission_level = user.data.permission_level;
                                user_logged_in_name = user.data.first_name + " " + toProperCase(user.data.surname);
                                user_logged_in_email = user.data.notifications_email;
                                user_logged_in_rank = user.data.rank;

                                $$("logged_in_label").define(
                                    "label",
                                    "<div class='fas fa-user' style='color: white; margin-right: 5px'></div><div class='header_font_small' style='display:inline-block'</div>" +
                                    user_logged_in_name +
                                    " | " +
                                    user_logged_in +
                                    " | " +
                                    user_logged_in_email +
                                    " | " +
                                    user_permission_level +
                                    "</div>",
                                );
                                $$("logged_in_label").refresh();

                                setLeaveViews(user.views.leave_views);
                                setAdminViews(user.views.admin_views);
                                setReportViews(user.views.report_views);
                                setMessagingViews();
                                setApplicationsViews();
                                setR52Views(user.views.r52_views);
                                loadGlobalSettings();

                                routes.navigate("schedule", {trigger: true});

                                loadingApp = false;

                                setTimeout(function () {

                                    setDefaultRosterView(user.data.pay_id, function (response) {
                                        if (response === "loaded") {
                                            setTimeout(function () {
                                                $$("schedule-page")
                                                    .$$("schedule_rosters")
                                                    .setValue(curr_user_roster);
                                                $$("schedule-page")
                                                    .$$("schedule_shifts")
                                                    .setValue(curr_user_shift);
                                                $$("schedule-page")
                                                    .$$("schedule_locations")
                                                    .setValue(curr_user_location);
                                            }, 100);
                                            if (loading_roster === false) {
                                                $$("loader-window").hide();
                                                setTimeout(function () {
                                                    $$("schedule-page")
                                                        .$$("refresh")
                                                        .callEvent("onItemClick");
                                                }, 750);
                                            }
                                        }
                                    });
                                }, 100);
                                (function () {
                                    setTimeout(function () {
                                        loginFinishLoading = true;
                                    }, 750);
                                })();
                                (function () {
                                    let timer = new IdleTimeout(timeoutPeriod, {
                                        callback: function () {
                                            console.log("logged out");
                                            sessionStorage.setItem("inactivity", "true");
                                            routes.navigate("login", {trigger: true});
                                            $$("views").hide();
                                            window.location.reload(true);
                                        },
                                    });
                                })();
                                setTimeout(function () {
                                    if (
                                        moment().format("DD/MM") == user.data.date_of_birth
                                    ) {
                                        webix
                                            .ui({
                                                view: "window",
                                                id: "happy_birthday",
                                                head: {
                                                    view: "toolbar",
                                                    elements: [
                                                        {
                                                            template:
                                                                "<div style='text-align: left; font-size: 30px; color: #007FB1; font-weight: bold;'>Hey " +
                                                                callback.data[0].first_name +
                                                                ",</div>",
                                                            height: 45,
                                                        },
                                                        {
                                                            view: "button",
                                                            type: "icon",
                                                            css: "webix_danger",
                                                            icon: "wxi-close",
                                                            width: 40,
                                                            click: function () {
                                                                $$("happy_birthday").close();
                                                            },
                                                        },
                                                    ],
                                                },
                                                close: true,
                                                modal: false,
                                                width: 855,
                                                height: 565,
                                                position: "center",
                                                body: {
                                                    template:
                                                        "<img src='../../resources/images/happy_birthday.jpg'/>",
                                                },
                                            })
                                            .show();
                                    }
                                }, 800);
                            }
                        })
                    });
                } else {
                    webix.confirm({
                        title: "Invalid Login",
                        text:
                            "Your ESO email address <strong>" +
                            data.user.eso_email_address +
                            "</strong> is not configured in SAPPHIRE.</br></br>Note: If you believe this to be an error please contact the SAPPHIRE support team or Workforce Rostering.",
                        width: 650,
                        ok: "Logout",
                        cancel: "OK",
                        callback: function (result) {
                            switch (result) {
                                case true:
                            }
                        },
                    });
                }
            }
        } catch (err) {
            console.error('Login check failed:', err);
        }
    }

    function initViews(callback) {

        renderMSTimePicker("bookings_start_time");
        renderMSTimePicker("bookings_end_time");
        renderMSTimePicker("overtime_start_time");
        renderMSTimePicker("overtime_end_time");
        renderMSTimePicker("standbys_start_time");
        renderMSTimePicker("standbys_end_time");
        renderMSTimePicker("shift_adjustment_start_time");
        renderMSTimePicker("shift_adjustment_end_time");
        renderMSTimePicker("actups_start_time");
        renderMSTimePicker("actups_end_time");

        createATWindow();
        createCVWindow();
        createLTWindow();
        createLGWindow();
        createLocWindow();
        createRABWindow();
        createRAEWindow();
        createRARWindow();
        createRosWindow();
        createTDWindow();
        createHSWindow();
        createTEWindow();
        createLBWindow();
        createNFCWindow();
        createEmpWindow();
        createSGWindow();
        createSTWindow();
        createShiftsWindow();
        createSkillCWindow();
        createPubHWindow();
        createSBookWindow();
        createSoilToilWindow();
        createAppMWindow();
        createTLogsWindow();
        createTDupsWindow();
        createARWindow();
        createSCIWindow();
        createCrewInfoWindow();
        createFuncWindow();
        createRSWindow();
        createORerWindow();
        createSRepWindow();
        createOBreakWindow();

        application.initialise();
        locations.initialise();
        locationGroups.initialise();
        shifts.initialise();
        rosters.initialise();
        leaveTypes.initialise();
        shiftTypes.initialise();
        activityTypes.initialise();
        covidVaccines.initialise();
        createBooking.initialise();
        schedule.initialise();
        adminSearch.initialise();
        rosterArrangement.initialise();
        editDeleteBookings.initialise();
        functionsPopup.initialise();
        skillCodes.initialise();
        leaveGroups.initialise();
        shiftGroups.initialise();
        leaveSwap.initialise();
        leaveGroupsLogs.initialise();
        leaveSwapList.initialise();
        leaveRequests.initialise();
        leaveCounts.initialise();
        leaveLogs.initialise();
        actingUpLogs.initialise();
        staffMovementLogs.initialise();
        standbyLogs.initialise();
        crewInfoPopup.initialise();
        publicHolidays.initialise();
        bookingsReport.initialise();
        deletionReport.initialise();
        sicknessReport.initialise();
        sicknessSummaryReport.initialise();
        overtimeReport.initialise();
        travelReport.initialise();
        availabilityReport.initialise();
        supplementaryReport.initialise();
        createTravel.initialise();
        travelRequests.initialise();
        sicknessCertificates.initialise();
        sickBookingsLog.initialise();
        staffMovementReport.initialise();
        overtimeLogs.initialise();
        loginMessage.initialise();
        skillCodesReport.initialise();
        rosterArrangementsReport.initialise();
        rosterArrangementsSummaryReport.initialise();
        rosterArrangementsByRankReport.initialise();
        overtimeSummaryReport.initialise();
        staffOvertimeSummaryReport.initialise();
        skillCodesAvailabilityReport.initialise();
        reasonOvertimeSummaryReport.initialise();
        shiftSummaryReport.initialise();
        rrlReport.initialise();
        bookingErrorsReport.initialise();
        messaging.initialise();
        applications.initialise();
        applicationsStandbys.initialise();
        applicationsRRLs.initialise();
        covidVaxStatusReport.initialise();
        covidVaxSummaryReport.initialise();
        IncidentReport.initialise();
        IncidentTypeSummaryReport.initialise();
        IncidentLocationSummaryReport.initialise();
        RidingPositionsLocationReport.initialise();
        soilToilBalances.initialise();
        IncidentInfoSearchReport.initialise();
        covidStatusSummaryReport.initialise();
        stationPreferencesReport.initialise();
        dayWorkRequests.initialise();
        adminSettings.initialise();
        overtimeFatigueReport.initialise();
        leaveTakenBreakdownReport.initialise();
        sicknessCertificateReport.initialise();
        overtimeFatigueDistanceReport.initialise();
        callback("loaded");
    }

});
