let sel_delete_period = "";


function setReportViews(report_menu_data) {
    getUserPL(function (result) {
        if (result === 5) {
            if (user_logged_in_rank === "MOFF") {
                report_menu_data.push({
                    id: "reporting_overtime_fatigue",
                    icon: "fas fa-chart-bar",
                    value: "Overtime & Fatigue Report",
                });
            }
        }
        $$("reports-page").$$("reports_sidebar").define("data", report_menu_data);
        $$("reports-page").$$("reports_sidebar").refresh();
    })
}


function setAdminViews(admin_menu_data) {
    getUserPL(function (result) {
        if (result === 1) {
            $$("admin-page").$$("admin_search").$$("btn_terminate").enable();
            $$("admin-page").$$("admin_search").$$("btn_delete").enable();
            $$("admin-page").$$("admin_search").$$("btn_add").enable();
            $$("admin-page")
                .$$("admin_search")
                .$$("search_results")
                .define("editable", true);
            $$("admin-page").$$("admin_search").$$("search_results").refresh();
            $$("btn_clear_nfc").enable();
        } else if (result === 2) {

            $$("admin-page").$$("admin_search").$$("btn_terminate").enable();
            $$("admin-page").$$("admin_search").$$("btn_delete").disable();
            $$("admin-page").$$("admin_search").$$("btn_add").enable();
            $$("admin-page")
                .$$("admin_search")
                .$$("search_results")
                .define("editable", true);
            $$("admin-page").$$("admin_search").$$("search_results").refresh();
        } else if (result === 3) {

            $$("admin-page").$$("admin_search").$$("btn_terminate").disable();
            $$("admin-page").$$("admin_search").$$("btn_delete").disable();
            $$("admin-page").$$("admin_search").$$("btn_add").disable();
            $$("admin-page")
                .$$("admin_search")
                .$$("search_results")
                .define("editable", true);
            $$("admin-page").$$("admin_search").$$("search_results").refresh();
        } else if (result === 4 || result === 5) {

            $$("admin-page").$$("admin_search").$$("btn_terminate").disable();
            $$("admin-page").$$("admin_search").$$("btn_delete").disable();
            $$("admin-page").$$("admin_search").$$("btn_add").disable();
            $$("admin-page")
                .$$("admin_search")
                .$$("search_results")
                .define("editable", false);
            $$("admin-page").$$("admin_search").$$("search_results").refresh();
        } else if (result === 6) {

            $$("main_menu").disableOption("3");
        }
        $$("admin-page").$$("admin_sidebar").define("data", admin_menu_data);
        $$("admin-page").$$("admin_sidebar").refresh();
    })
}


function setLeaveViews(leave_menu_data) {

    $$("bookings-page").$$("bookings_sidebar").define("data", leave_menu_data);
    $$("bookings-page").$$("bookings_sidebar").refresh();

    getUserPL(function (result) {
        if (result === 3 || result === 4 || result === 5) {
            $$("bookings-page").$$("sick_certificates").$$("btn_export_excel").hide();
            $$("bookings-page").$$("sick_certificates").$$("btn_print").hide();
            $$("bookings-page").$$("sick_certificates").$$("btn_email").hide();
            $$("bookings-page")
                .$$("sick_certificates")
                .$$("sickness_pending_grid")
                .hideColumn("comments");
            $$("bookings-page")
                .$$("sick_certificates")
                .$$("sickness_pending_grid")
                .hideColumn("emailed");
            $$("bookings-page")
                .$$("sick_certificates")
                .$$("sickness_pending_grid")
                .hideColumn("email_address");
        }
    })
}


function setMessagingViews() {
    getUserPL(function (result) {
        if (result === 6) {
            $$("main_menu").disableOption("5");
        }
    })
}


function setApplicationsViews() {
    getUserPL(function (result) {
        if (result === 2 || result === 3 || result === 4 || result === 5 || result === 6) {
            $$("applications-page").$$("applications_tabs").disableOption("3");
        }
        if (result === 1) {
            $$("schedule-page").$$("covid_filter").show();
        } else {
            $$("schedule-page").$$("covid_filter").hide();
        }
    })
}


function setR52Views(respond52_menu_data) {

    $$("respond52-page").$$("respond52_sidebar").define("data", respond52_menu_data);
    $$("respond52-page").$$("respond52_sidebar").refresh();

}


function checkDeletePermissions(
    funcType,
    bookingId,
    bkType,
    leaveCode,
    status,
    single_selected_date,
    booking_date,
    booking_last_date,
    travel_processed,
    approved_denied_date,
    recurring_link_id,
    payId,
    callback,
) {
    let curr_roster = $$("schedule-page").$$("schedule_rosters").getText();
    let curr_shift = $$("schedule-page").$$("schedule_shifts").getText();
    let empName =
        selected_employee_info.first_name + " " + selected_employee_info.surname;
    let months_diff = moment().diff(single_selected_date, "months", true);
    let months_past = moment(single_selected_date).diff(moment(), "months", true);
    let days_past = moment(single_selected_date).diff(moment(), "days", true);
    let tour_start_date = moment(single_selected_date).format("DD/MM/YYYY");
    let tour_end_date = moment(
        moment(single_selected_date).add(7, "days"),
    ).format("DD/MM/YYYY");
    let currTime = moment();
    let booking_start_date = moment(booking_date).format("DD/MM/YYYY HH:mm");
    let booking_end_date = moment(booking_last_date).format("DD/MM/YYYY HH:mm");
    let booking_single_date = moment(single_selected_date).format("DD/MM/YYYY HH:mm");

    getUserPL(function (result) {

        if (bkType === "leave_request") {
            if (
                leaveCode === "ARL" ||
                leaveCode === "PHOL" ||
                leaveCode === "RET" ||
                leaveCode === "SOIL" ||
                leaveCode === "TOIL" ||
                leaveCode === "LSLS" ||
                leaveCode === "ADOP" ||
                leaveCode === "ANN"
            ) {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (
                        result === 1 ||
                        result === 2 ||
                        result === 3
                    ) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                if (leaveCode === "TOIL" || leaveCode === "SOIL") {
                                                    soilToilBalances.reverseSoilToilEntry(
                                                        leaveCode,
                                                        bookingId,
                                                        globalSelectedDate,
                                                    );
                                                }
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                if (leaveCode === "TOIL" || leaveCode === "SOIL") {
                                                    soilToilBalances.reverseSoilToilEntry(
                                                        leaveCode,
                                                        bookingId,
                                                        globalSelectedDate,
                                                    );
                                                }
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        let elapsed_hours = moment(moment()).diff(
                            approved_denied_date,
                            "hours",
                            true,
                        );
                        if (elapsed_hours < 24) {
                            webix.alert({
                                text: "You cannot delete this booking because it has been less than 24 hours since it was 'Approved'",
                                width: 400,
                            });
                            callback("deny");
                        } else {
                            checkWeekends(currTime, single_selected_date, function (days_diff) {
                                if (days_diff < 0) {
                                    webix.alert({
                                        text: "You cannot delete this booking day because it is in the past!",
                                        width: 500,
                                    });
                                    callback("deny");
                                } else if (days_diff > 0 && days_diff <= 2) {
                                    webix.alert({
                                        text: "You cannot delete this booking day because it is less than 2 business days away!",
                                        width: 500,
                                    });
                                    callback("deny");
                                } else if (days_diff > 731) {
                                    webix.alert({
                                        text: "You cannot delete this booking day because it is more than 731 business days away!",
                                        width: 500,
                                    });
                                    callback("deny");
                                } else {
                                    if (funcType === "funcToolbar") {
                                        if (recurring_link_id == null) {
                                            webix
                                                .modalbox({
                                                    title:
                                                        "You are about to delete a '" +
                                                        status +
                                                        "' <strong> " +
                                                        leaveCode +
                                                        "</strong> booking for <strong>" +
                                                        empName +
                                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                                    text:
                                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                                        booking_single_date +
                                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                                        booking_start_date +
                                                        "</strong> to <strong>" +
                                                        booking_end_date +
                                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                                    buttons: ["Cancel", "One Day", "All Days"],
                                                    width: 600,
                                                })
                                                .then(function (result) {
                                                    switch (result) {
                                                        case "0":
                                                            break;
                                                        case "1":
                                                            sel_delete_period = "one";
                                                            editDeleteBookings.deleteBookingDayOnly(
                                                                bookingId,
                                                                globalSelectedDate,
                                                                status,
                                                                "",
                                                            );
                                                            if (leaveCode === "TOIL" || leaveCode === "SOIL") {
                                                                soilToilBalances.reverseSoilToilEntry(
                                                                    leaveCode,
                                                                    bookingId,
                                                                    globalSelectedDate,
                                                                );
                                                            }
                                                            callback("allow");
                                                            break;
                                                        case "2":
                                                            sel_delete_period = "all";
                                                            editDeleteBookings.deleteWholeBooking(
                                                                bookingId,
                                                                status,
                                                                null,
                                                                payId,
                                                            );
                                                            if (leaveCode === "TOIL" || leaveCode === "SOIL") {
                                                                soilToilBalances.reverseSoilToilEntry(
                                                                    leaveCode,
                                                                    bookingId,
                                                                    globalSelectedDate,
                                                                );
                                                            }
                                                            callback("allow");
                                                            break;
                                                    }
                                                });
                                        } else {
                                            webix
                                                .modalbox({
                                                    title:
                                                        "You are about to delete a '" +
                                                        status +
                                                        "' <strong> " +
                                                        leaveCode +
                                                        "</strong> booking for <strong>" +
                                                        empName +
                                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                                    text:
                                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                                        booking_single_date +
                                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                                    buttons: [
                                                        "Cancel",
                                                        "One Day",
                                                        "All Rec.",
                                                        "Future Rec.",
                                                    ],
                                                    width: 600,
                                                })
                                                .then(function (result) {
                                                    switch (result) {
                                                        case "0":
                                                            break;
                                                        case "1":
                                                            sel_delete_period = "one";
                                                            editDeleteBookings.deleteBookingDayOnly(
                                                                bookingId,
                                                                globalSelectedDate,
                                                                status,
                                                                "",
                                                            );
                                                            callback("allow");
                                                            break;
                                                        case "2":
                                                            sel_delete_period = "all";
                                                            editDeleteBookings.deleteWholeBooking(
                                                                bookingId,
                                                                status,
                                                                recurring_link_id,
                                                                payId,
                                                            );
                                                            callback("allow");
                                                            break;
                                                        case "3":
                                                            sel_delete_period = "future";
                                                            editDeleteBookings.deleteWholeBooking(
                                                                bookingId,
                                                                status,
                                                                recurring_link_id,
                                                                payId,
                                                            );
                                                            callback("allow");
                                                            break;
                                                    }
                                                });
                                        }
                                    } else if (funcType === "leaveTab") {
                                        callback("allow");
                                    }
                                }
                            });
                        }
                    }
                }
            } else if (
                leaveCode === "LSL" ||
                leaveCode === "LSLH" ||
                leaveCode === "ULSL" ||
                leaveCode === "RRL"
            ) {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (
                            daySeqNumber == 1 ||
                            (curr_roster == "OTR" && daySeqNumber == 9) ||
                            (curr_roster == "Comms" &&
                                curr_shift == "CommCen E1" &&
                                daySeqNumber == 7) ||
                            (curr_roster == "Comms" &&
                                curr_shift == "CommCen E2" &&
                                daySeqNumber == 9)
                        ) {
                            if (leaveCode === "RRL") {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[Delete Tour]</strong> - Delete the selected tour from <strong>" +
                                            tour_start_date +
                                            "</strong> to <strong>" +
                                            tour_end_date +
                                            "</strong></br><strong>[Delete All]</strong> - Delete the whole RRL booking period</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "Delete Tour", "Delete All"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "tour";
                                                editDeleteBookings.deleteLSLBookingPeriod(
                                                    bookingId,
                                                    status,
                                                    single_selected_date,
                                                    8,
                                                    leaveCode,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br><i>Note: <strong>" +
                                            leaveCode +
                                            "</strong> bookings can only be deleted in <strong> 8 day / 1 tour </strong>blocks!</i></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[Delete Tour]</strong> - Delete the selected tour from <strong>" +
                                            tour_start_date +
                                            "</strong> to <strong>" +
                                            tour_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "Delete Tour"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "tour";
                                                editDeleteBookings.deleteLSLBookingPeriod(
                                                    bookingId,
                                                    status,
                                                    single_selected_date,
                                                    8,
                                                    leaveCode,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else {
                            webix.alert(
                                "To delete a " +
                                leaveCode +
                                " tour period you must select the first day of the tour!",
                            );
                            callback("deny");
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (result === 1 || result === 2) {
                        if (funcType === "funcToolbar") {
                            if (curr_roster != "Port Pirie") {
                                if (
                                    daySeqNumber == 1 ||
                                    (curr_roster == "OTR" && daySeqNumber == 9) ||
                                    (curr_roster == "Comms" &&
                                        curr_shift == "CommCen E1" &&
                                        daySeqNumber == 7) ||
                                    (curr_roster == "Comms" &&
                                        curr_shift == "CommCen E2" &&
                                        daySeqNumber == 9)
                                ) {
                                    if (leaveCode === "RRL") {
                                        webix
                                            .modalbox({
                                                title:
                                                    "You are about to delete a '" +
                                                    status +
                                                    "' <strong> " +
                                                    leaveCode +
                                                    "</strong> booking for <strong>" +
                                                    empName +
                                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                                text:
                                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[Delete Tour]</strong> - Delete the selected tour from <strong>" +
                                                    tour_start_date +
                                                    "</strong> to <strong>" +
                                                    tour_end_date +
                                                    "</strong></br><strong>[Delete All]</strong> - Delete the whole RRL booking period</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                                buttons: ["Cancel", "Delete Tour", "Delete All"],
                                                width: 600,
                                            })
                                            .then(function (result) {
                                                switch (result) {
                                                    case "0":
                                                        break;
                                                    case "1":
                                                        sel_delete_period = "tour";
                                                        editDeleteBookings.deleteLSLBookingPeriod(
                                                            bookingId,
                                                            status,
                                                            single_selected_date,
                                                            8,
                                                            leaveCode,
                                                            payId,
                                                        );
                                                        callback("allow");
                                                        break;
                                                    case "2":
                                                        sel_delete_period = "all";
                                                        editDeleteBookings.deleteWholeBooking(
                                                            bookingId,
                                                            status,
                                                            null,
                                                            payId,
                                                        );
                                                        callback("allow");
                                                        break;
                                                }
                                            });
                                    } else {
                                        webix
                                            .modalbox({
                                                title:
                                                    "You are about to delete a '" +
                                                    status +
                                                    "' <strong> " +
                                                    leaveCode +
                                                    "</strong> booking for <strong>" +
                                                    empName +
                                                    "</strong></br></br><i>Note: <strong>" +
                                                    leaveCode +
                                                    "</strong> bookings can only be deleted in <strong> 8 day / 1 tour </strong>blocks!</i></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                                text:
                                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[Delete Tour]</strong> - Delete the selected tour from <strong>" +
                                                    tour_start_date +
                                                    "</strong> to <strong>" +
                                                    tour_end_date +
                                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                                buttons: ["Cancel", "Delete Tour"],
                                                width: 600,
                                            })
                                            .then(function (result) {
                                                switch (result) {
                                                    case "0":
                                                        break;
                                                    case "1":
                                                        sel_delete_period = "tour";
                                                        editDeleteBookings.deleteLSLBookingPeriod(
                                                            bookingId,
                                                            status,
                                                            single_selected_date,
                                                            8,
                                                            leaveCode,
                                                            payId,
                                                        );
                                                        callback("allow");
                                                        break;
                                                }
                                            });
                                    }
                                } else {
                                    webix
                                        .modalbox({
                                            title:
                                                "You have not selected the first day of a tour so the only option is to delete the whole <strong> " +
                                                leaveCode +
                                                "</strong> booking for <strong>" +
                                                empName +
                                                "</strong>",
                                            text: "<div align='left'>---------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[Delete All]</strong> - Delete the whole RRL booking period</br>---------------------------------------------------------------------------------------------------------------------------------</div>",
                                            buttons: ["Cancel", "Delete All"],
                                            width: 500,
                                        })
                                        .then(function (result) {
                                            switch (result) {
                                                case "0":
                                                    break;
                                                case "1":
                                                    sel_delete_period = "all";
                                                    editDeleteBookings.deleteWholeBooking(
                                                        bookingId,
                                                        status,
                                                        null,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                            }
                                        });
                                }
                            } else {
                                if (
                                    (daySeqNumber == 1 &&
                                        moment(booking_date).isSame(single_selected_date)) ||
                                    (daySeqNumber == 1 &&
                                        moment(moment(booking_date).add(8, "days")).isSame(
                                            single_selected_date,
                                        )) ||
                                    (daySeqNumber == 1 &&
                                        moment(moment(booking_date).add(16, "days")).isSame(
                                            single_selected_date,
                                        )) ||
                                    (daySeqNumber == 1 &&
                                        moment(moment(booking_date).add(24, "days")).isSame(
                                            single_selected_date,
                                        ))
                                ) {
                                    webix
                                        .modalbox({
                                            title:
                                                "You are about to delete a '" +
                                                status +
                                                "' <strong> " +
                                                leaveCode +
                                                "</strong> booking for <strong>" +
                                                empName +
                                                "</strong></br></br><i>Note: <strong>" +
                                                leaveCode +
                                                "</strong> bookings can only be deleted in <strong> 8 day / 1 tour </strong>blocks!</i></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                            text:
                                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[Delete Tour]</strong> - Delete the selected tour from <strong>" +
                                                tour_start_date +
                                                "</strong> to <strong>" +
                                                tour_end_date +
                                                "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                            buttons: ["Cancel", "Delete Tour"],
                                            width: 600,
                                        })
                                        .then(function (result) {
                                            switch (result) {
                                                case "0":
                                                    break;
                                                case "1":
                                                    sel_delete_period = "tour";
                                                    editDeleteBookings.deleteLSLBookingPeriod(
                                                        bookingId,
                                                        status,
                                                        single_selected_date,
                                                        8,
                                                        leaveCode,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                            }
                                        });
                                } else {
                                    webix.alert(
                                        "To delete a " +
                                        leaveCode +
                                        " tour period you must select the first day of the tour!",
                                    );
                                    callback("deny");
                                }
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (
                        result === 3 ||
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        if (months_past < 2) {
                            webix.alert({
                                text:
                                    "You cannot delete this " +
                                    leaveCode +
                                    " booking because the selected tour starts in less than 2 months!",
                                width: 500,
                            });
                            callback("deny");
                        } else {
                            if (funcType === "funcToolbar") {
                                if (daySeqNumber !== 1) {
                                    webix.alert(
                                        "To delete a " +
                                        leaveCode +
                                        " tour period you must select the first day of the tour!",
                                    );
                                    callback("deny");
                                } else {
                                    webix
                                        .modalbox({
                                            title:
                                                "You are about to delete a '" +
                                                status +
                                                "' <strong> " +
                                                leaveCode +
                                                "</strong> booking for <strong>" +
                                                empName +
                                                "</strong></br></br><i>Note: <strong>" +
                                                leaveCode +
                                                "</strong> bookings can only be deleted in <strong> 8 day / 1 tour </strong>blocks!</i></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                            text:
                                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[Delete Tour]</strong> - Delete the selected tour from <strong>" +
                                                tour_start_date +
                                                "</strong> to <strong>" +
                                                tour_end_date +
                                                "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                            buttons: ["Cancel", "Delete Tour"],
                                            width: 600,
                                        })
                                        .then(function (result) {
                                            switch (result) {
                                                case "0":
                                                    break;
                                                case "1":
                                                    sel_delete_period = "tour";
                                                    editDeleteBookings.deleteLSLBookingPeriod(
                                                        bookingId,
                                                        status,
                                                        single_selected_date,
                                                        8,
                                                        leaveCode,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                            }
                                        });
                                }
                            } else if (funcType === "leaveTab") {
                                callback("allow");
                            }
                        }
                    }
                }
            } else if (leaveCode === "OL") {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (result === 1 || result === 2) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (result === 3 || result === 4) {
                        if (months_diff >= 1) {
                            webix.alert({
                                text:
                                    "You cannot delete this " +
                                    leaveCode +
                                    " booking because more than 1 month has elapsed since the booking!",
                                width: 550,
                            });
                            callback("deny");
                        } else {
                            if (funcType === "funcToolbar") {
                                if (recurring_link_id == null) {
                                    webix
                                        .modalbox({
                                            title:
                                                "You are about to delete a '" +
                                                status +
                                                "' <strong> " +
                                                leaveCode +
                                                "</strong> booking for <strong>" +
                                                empName +
                                                "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                            text:
                                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                                booking_single_date +
                                                "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                                booking_start_date +
                                                "</strong> to <strong>" +
                                                booking_end_date +
                                                "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                            buttons: ["Cancel", "One Day", "All Days"],
                                            width: 600,
                                        })
                                        .then(function (result) {
                                            switch (result) {
                                                case "0":
                                                    break;
                                                case "1":
                                                    sel_delete_period = "one";
                                                    editDeleteBookings.deleteBookingDayOnly(
                                                        bookingId,
                                                        globalSelectedDate,
                                                        status,
                                                        "",
                                                    );
                                                    callback("allow");
                                                    break;
                                                case "2":
                                                    sel_delete_period = "all";
                                                    editDeleteBookings.deleteWholeBooking(
                                                        bookingId,
                                                        status,
                                                        null,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                            }
                                        });
                                } else {
                                    webix
                                        .modalbox({
                                            title:
                                                "You are about to delete a '" +
                                                status +
                                                "' <strong> " +
                                                leaveCode +
                                                "</strong> booking for <strong>" +
                                                empName +
                                                "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                            text:
                                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                                booking_single_date +
                                                "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                            buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                            width: 600,
                                        })
                                        .then(function (result) {
                                            switch (result) {
                                                case "0":
                                                    break;
                                                case "1":
                                                    sel_delete_period = "one";
                                                    editDeleteBookings.deleteBookingDayOnly(
                                                        bookingId,
                                                        globalSelectedDate,
                                                        status,
                                                        "",
                                                    );
                                                    callback("allow");
                                                    break;
                                                case "2":
                                                    sel_delete_period = "all";
                                                    editDeleteBookings.deleteWholeBooking(
                                                        bookingId,
                                                        status,
                                                        recurring_link_id,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                                case "3":
                                                    sel_delete_period = "future";
                                                    editDeleteBookings.deleteWholeBooking(
                                                        bookingId,
                                                        status,
                                                        recurring_link_id,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                            }
                                        });
                                }
                            } else if (funcType === "leaveTab") {
                                callback("allow");
                            }
                        }
                    } else if (result === 5 || result === 6) {
                        webix.alert({
                            text: "You don't have permission to delete this booking!",
                            width: 500,
                        });
                        callback("deny");
                    }
                }
            } else if (
                leaveCode === "ULSL" ||
                leaveCode === "UPHL" ||
                leaveCode === "URET"
            ) {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (result === 1 || result === 2) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (result === 3) {
                        if (days_past >= 731) {
                            webix.alert({
                                text:
                                    "You cannot delete this " +
                                    leaveCode +
                                    " booking because it 731 or more days away!",
                                width: 550,
                            });
                            callback("deny");
                        } else {
                            if (funcType === "funcToolbar") {
                                if (recurring_link_id == null) {
                                    webix
                                        .modalbox({
                                            title:
                                                "You are about to delete a '" +
                                                status +
                                                "' <strong> " +
                                                leaveCode +
                                                "</strong> booking for <strong>" +
                                                empName +
                                                "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                            text:
                                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                                booking_single_date +
                                                "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                                booking_start_date +
                                                "</strong> to <strong>" +
                                                booking_end_date +
                                                "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                            buttons: ["Cancel", "One Day", "All Days"],
                                            width: 600,
                                        })
                                        .then(function (result) {
                                            switch (result) {
                                                case "0":
                                                    break;
                                                case "1":
                                                    sel_delete_period = "one";
                                                    editDeleteBookings.deleteBookingDayOnly(
                                                        bookingId,
                                                        globalSelectedDate,
                                                        status,
                                                        "",
                                                    );
                                                    callback("allow");
                                                    break;
                                                case "2":
                                                    sel_delete_period = "all";
                                                    editDeleteBookings.deleteWholeBooking(
                                                        bookingId,
                                                        status,
                                                        null,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                            }
                                        });
                                } else {
                                    webix
                                        .modalbox({
                                            title:
                                                "You are about to delete a '" +
                                                status +
                                                "' <strong> " +
                                                leaveCode +
                                                "</strong> booking for <strong>" +
                                                empName +
                                                "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                            text:
                                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                                booking_single_date +
                                                "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                            buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                            width: 600,
                                        })
                                        .then(function (result) {
                                            switch (result) {
                                                case "0":
                                                    break;
                                                case "1":
                                                    sel_delete_period = "one";
                                                    editDeleteBookings.deleteBookingDayOnly(
                                                        bookingId,
                                                        globalSelectedDate,
                                                        status,
                                                        "",
                                                    );
                                                    callback("allow");
                                                    break;
                                                case "2":
                                                    sel_delete_period = "all";
                                                    editDeleteBookings.deleteWholeBooking(
                                                        bookingId,
                                                        status,
                                                        recurring_link_id,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                                case "3":
                                                    sel_delete_period = "future";
                                                    editDeleteBookings.deleteWholeBooking(
                                                        bookingId,
                                                        status,
                                                        recurring_link_id,
                                                        payId,
                                                    );
                                                    callback("allow");
                                                    break;
                                            }
                                        });
                                }
                            } else if (funcType === "leaveTab") {
                                callback("allow");
                            }
                        }
                    } else if (
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        webix.alert({
                            text: "You don't have permission to delete this booking!",
                            width: 500,
                        });
                        callback("deny");
                    }
                }
            } else if (
                leaveCode === "XARL" ||
                leaveCode === "XLSL" ||
                leaveCode === "XLSS" ||
                leaveCode === "ULSS" ||
                leaveCode === "XPHL" ||
                leaveCode === "XRET" ||
                leaveCode === "XTOI" ||
                leaveCode === "SPEC" ||
                leaveCode === "LWOP" ||
                leaveCode === "MATH" ||
                leaveCode === "MATP" ||
                leaveCode === "PPLS" ||
                leaveCode === "PUR4" ||
                leaveCode === "PURA"
            ) {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (result === 1 || result === 2) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (
                        result === 3 ||
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        webix.alert({
                            text: "You don't have permission to delete this booking!",
                            width: 500,
                        });
                        callback("deny");
                    }
                }
            } else if (leaveCode === "VSBT") {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (result === 1 || result === 3) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (
                        result === 2 ||
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        webix.alert({
                            text: "You don't have permission to delete this booking!",
                            width: 500,
                        });
                        callback("deny");
                    }
                }
            } else if (leaveCode === "J" || leaveCode === "LD" || leaveCode === "E") {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (
                        result === 1 ||
                        result === 2 ||
                        result === 3 ||
                        result === 4
                    ) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (result === 5 || result === 6) {
                        webix.alert({
                            text: "You don't have permission to delete this booking!",
                            width: 500,
                        });
                        callback("deny");
                    }
                }
            } else if (leaveCode === "WC") {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (
                        result === 1 ||
                        result === 2 ||
                        result === 3
                    ) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        webix.alert({
                            text: "You don't have permission to delete this booking!",
                            width: 500,
                        });
                        callback("deny");
                    }
                }
            } else if (
                leaveCode === "AWOL" ||
                leaveCode === "UGAD" ||
                leaveCode === "RW" ||
                leaveCode === "NWD" ||
                leaveCode === "OTHE" ||
                leaveCode == "EXCH"
            ) {
                if (status === "Pending") {
                    if (funcType === "funcToolbar") {
                        if (recurring_link_id == null) {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a '" +
                                        status +
                                        "' <strong> " +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                        case "3":
                                            sel_delete_period = "future";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                recurring_link_id,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        }
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (status === "Approved") {
                    if (result === 1 || result === 2) {
                        if (funcType === "funcToolbar") {
                            if (recurring_link_id == null) {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a '" +
                                            status +
                                            "' <strong> " +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                            case "3":
                                                sel_delete_period = "future";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    recurring_link_id,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            }
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    } else if (
                        result === 3 ||
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        webix.alert({
                            text: "You don't have permission to delete this booking!",
                            width: 500,
                        });
                        callback("deny");
                    }
                }
            }
        } else if (bkType === "sick_leave") {
            if (
                leaveCode === "BERE" ||
                leaveCode === "FAML" ||
                leaveCode === "SIC" ||
                leaveCode === "SICM"
            ) {
                if (result === 1 || result === 2) {
                    if (funcType === "funcToolbar") {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a <strong>" +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (result === 3) {
                    if (
                        days_past < 0 &&
                        moment(single_selected_date).format("YYYYMMDD") !==
                        moment().format("YYYYMMDD")
                    ) {
                        webix.alert({
                            text:
                                "You cannot delete this " +
                                leaveCode +
                                " booking because it is in the past!",
                            width: 550,
                        });
                        callback("deny");
                    } else {
                        if (funcType === "funcToolbar") {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a <strong>" +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    }
                } else if (result === 4) {
                    if (
                        days_past < 0 &&
                        moment(single_selected_date).format("YYYYMMDD") !==
                        moment().format("YYYYMMDD")
                    ) {
                        webix.alert({
                            text:
                                "You cannot delete this " +
                                leaveCode +
                                " booking because it is in the past!",
                            width: 550,
                        });
                        callback("deny");
                    } else {
                        if (funcType === "funcToolbar") {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a <strong>" +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    }
                } else if (result === 5) {
                    if (
                        days_past < 0 &&
                        moment(single_selected_date).format("YYYYMMDD") !==
                        moment().format("YYYYMMDD")
                    ) {
                        webix.alert({
                            text:
                                "You cannot delete this " +
                                leaveCode +
                                " booking because it is in the past!",
                            width: 550,
                        });
                        callback("deny");
                    } else if (
                        moment().isAfter(moment(single_selected_date)) === true &&
                        moment(single_selected_date).format("YYYYMMDD") ===
                        moment().format("YYYYMMDD")
                    ) {
                        webix.alert({
                            text: "You do not have permission to delete this Sickness booking because the shift has started.</br>If a change needs to be made please contact your supervisor!",
                            width: 550,
                        });
                        callback("deny");
                    } else {
                        if (funcType === "funcToolbar") {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a <strong>" +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    }
                } else if (result === 6) {
                    if (
                        days_past < 0 &&
                        moment(single_selected_date).format("YYYYMMDD") !==
                        moment().format("YYYYMMDD")
                    ) {
                        webix.alert({
                            text:
                                "You cannot delete this " +
                                leaveCode +
                                " booking because it is in the past!",
                            width: 550,
                        });
                        callback("deny");
                    } else if (
                        moment().isAfter(moment(single_selected_date)) === true &&
                        moment(single_selected_date).format("YYYYMMDD") ===
                        moment().format("YYYYMMDD")
                    ) {
                        webix.alert({
                            text: "You do not have permission to delete this Sickness booking because the shift has started.</br>If a change needs to be made please contact your supervisor!",
                            width: 550,
                        });
                        callback("deny");
                    } else {
                        if (funcType === "funcToolbar") {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a <strong>" +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    }
                }
            } else if (
                leaveCode === "SLUP" ||
                leaveCode === "SLUW" ||
                leaveCode === "CVPL" ||
                leaveCode === "CVUL" ||
                leaveCode === "CVIL" ||
                leaveCode === "CVFL" ||
                leaveCode === "CVSL" ||
                leaveCode === "CVWR"
            ) {
                if (result === 1 || result === 2) {
                    if (funcType === "funcToolbar") {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a <strong>" +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (
                    result === 3 ||
                    result === 4 ||
                    result === 5 ||
                    result === 6
                ) {
                    webix.alert({
                        text: "You don't have permission to delete this booking!",
                        width: 500,
                    });
                    callback("deny");
                }
            } else if (leaveCode === "WRSL") {
                if (
                    result === 1 ||
                    result === 2 ||
                    result === 3
                ) {
                    if (funcType === "funcToolbar") {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a <strong>" +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else if (
                    result === 4 ||
                    result === 5 ||
                    result === 6
                ) {
                    webix.alert({
                        text: "You don't have permission to delete this booking!",
                        width: 500,
                    });
                    callback("deny");
                }
            }
        } else if (bkType === "standby" || bkType === "standby_link") {
            if (
                result === 1 ||
                result === 2 ||
                result === 3 ||
                result === 4
            ) {
                if (funcType === "funcToolbar") {
                    webix
                        .modalbox({
                            title:
                                "You are about to delete a <strong>" +
                                leaveCode +
                                "</strong> booking for <strong>" +
                                empName +
                                "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                            text:
                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                booking_single_date +
                                "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                booking_start_date +
                                "</strong> to <strong>" +
                                booking_end_date +
                                "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                            buttons: ["Cancel", "One Day", "All Days"],
                            width: 600,
                        })
                        .then(function (result) {
                            switch (result) {
                                case "0":
                                    break;
                                case "1":
                                    sel_delete_period = "one";
                                    resetSBlink(selected_employee_info.pay_id, globalSelectedDate);
                                    editDeleteBookings.deleteBookingDayOnly(
                                        bookingId,
                                        globalSelectedDate,
                                        status,
                                        "",
                                    );
                                    callback("allow");
                                    break;
                                case "2":
                                    sel_delete_period = "all";
                                    resetSBlink(selected_employee_info.pay_id, globalSelectedDate);
                                    editDeleteBookings.deleteWholeBooking(
                                        bookingId,
                                        status,
                                        null,
                                        payId,
                                    );
                                    callback("allow");
                                    break;
                            }
                        });
                } else if (funcType === "leaveTab") {
                    callback("allow");
                }
            } else if (result === 5) {
                if (days_past < 0) {
                    webix.alert({
                        text: "You can't delete 'Standby' bookings in the past!",
                        width: 500,
                    });
                    callback("deny");
                } else {
                    if (funcType === "funcToolbar") {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a <strong>" +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        resetSBlink(
                                            selected_employee_info.pay_id,
                                            globalSelectedDate,
                                        );
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        resetSBlink(
                                            selected_employee_info.pay_id,
                                            globalSelectedDate,
                                        );
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                }
            } else if (result === 6) {
                webix.alert({
                    text: "You don't have permission to delete 'Standby' bookings!",
                    width: 500,
                });
                callback("deny");
            }
        } else if (bkType === "act-up") {
            if (
                result === 1 ||
                result === 2 ||
                result === 3 ||
                result === 4
            ) {
                if (funcType === "funcToolbar") {
                    webix
                        .modalbox({
                            title:
                                "You are about to delete a <strong>" +
                                leaveCode +
                                "</strong> booking for <strong>" +
                                empName +
                                "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                            text:
                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                booking_single_date +
                                "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                booking_start_date +
                                "</strong> to <strong>" +
                                booking_end_date +
                                "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                            buttons: ["Cancel", "One Day", "All Days"],
                            width: 600,
                        })
                        .then(function (result) {
                            switch (result) {
                                case "0":
                                    break;
                                case "1":
                                    sel_delete_period = "one";
                                    editDeleteBookings.deleteBookingDayOnly(
                                        bookingId,
                                        globalSelectedDate,
                                        status,
                                        "",
                                    );
                                    callback("allow");
                                    break;
                                case "2":
                                    sel_delete_period = "all";
                                    editDeleteBookings.deleteWholeBooking(
                                        bookingId,
                                        status,
                                        null,
                                        payId,
                                    );
                                    callback("allow");
                                    break;
                            }
                        });
                } else if (funcType === "leaveTab") {
                    callback("allow");
                }
            } else if (result === 5) {
                if (days_past > 0) {
                    webix.alert({
                        text: "You can only delete today's 'Act-up' bookings!",
                        width: 500,
                    });
                    callback("deny");
                } else {
                    if (funcType === "funcToolbar") {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a <strong>" +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                }
            } else if (result === 6) {
                webix.alert({
                    text: "You don't have permission to delete 'Act-up' bookings!",
                    width: 500,
                });
                callback("deny");
            }
        } else if (bkType === "overtime") {
            if (
                result === 1 ||
                result === 2 ||
                result === 3
            ) {
                if (funcType === "funcToolbar") {
                    webix
                        .modalbox({
                            title:
                                "You are about to delete a <strong>" +
                                leaveCode +
                                "</strong> booking for <strong>" +
                                empName +
                                "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                            text:
                                "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                booking_single_date +
                                "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                booking_start_date +
                                "</strong> to <strong>" +
                                booking_end_date +
                                "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                            buttons: ["Cancel", "One Day", "All Days"],
                            width: 600,
                        })
                        .then(function (result) {
                            switch (result) {
                                case "0":
                                    break;
                                case "1":
                                    sel_delete_period = "one";
                                    editDeleteBookings.deleteBookingDayOnly(
                                        bookingId,
                                        globalSelectedDate,
                                        status,
                                        "",
                                    );
                                    callback("allow");
                                    break;
                                case "2":
                                    sel_delete_period = "all";
                                    editDeleteBookings.deleteWholeBooking(
                                        bookingId,
                                        status,
                                        null,
                                        payId,
                                    );
                                    callback("allow");
                                    break;
                            }
                        });
                } else if (funcType === "leaveTab") {
                    callback("allow");
                }
            } else if (result === 4) {
                if (days_past > 1) {
                    webix.alert({
                        text:
                            "You don't have permission to delete " +
                            leaveCode +
                            " bookings in the future!",
                        width: 500,
                    });
                    callback("deny");
                } else if (days_past < -8) {
                    webix.alert({
                        text:
                            "You don't have permission to delete " +
                            leaveCode +
                            " bookings more than 8 days in the past!",
                        width: 500,
                    });
                    callback("deny");
                } else {
                    if (funcType === "funcToolbar") {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a <strong>" +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                }
            } else if (result === 5) {
                if (
                    moment(single_selected_date).format("YYYYMMDD") ===
                    moment().format("YYYYMMDD")
                ) {
                    if (funcType === "funcToolbar") {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a <strong>" +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else if (funcType === "leaveTab") {
                        callback("allow");
                    }
                } else {
                    webix.alert({
                        text: "You can only delete 'Overtime' bookings for today's shift!",
                        width: 450,
                    });
                    callback("deny");
                }
            } else if (result === 6) {
                webix.alert({
                    text: "You don't have permission to delete 'Overtime' bookings!",
                    width: 500,
                });
                callback("deny");
            }
        } else if (bkType === "staff_movement") {
            let bookingsTitle = "";
            let sm_count = 0;
            let sm_booking_type = "";
            let last_sm = false;
            getUserBookings(
                selected_employee_info.pay_id,
                globalSelectedDate,
                function (bookingArray) {
                    bookingsTitle = "Prior Staff Movement booking found!";
                    for (let x = 0; x < bookingArray.length; x++) {
                        if (bookingArray[x].booking_type == "staff_movement") {
                            sm_count += 1;
                            if (
                                bookingArray[x].bk_period == bookingArray[x].shift_type ||
                                bookingArray[x].bk_period == "both"
                            ) {
                                if (sm_count == bookingArray.length) {
                                    sm_booking_type = bookingArray[x].leave_type_code;
                                    if (leaveCode == bookingArray[x].leave_type_code) {
                                        last_sm = true;
                                    }
                                }
                            }
                        }
                    }
                },
            );
            if (sm_count > 1 && last_sm === false) {
                webix.alert({
                    text:
                        "A subsequent 'Staff Movement' booking exists for this shift.</br>You must delete the " +
                        sm_booking_type +
                        " booking before you can delete this one!",
                    width: 550,
                });
            } else {
                if (
                    result === 1 ||
                    result === 2 ||
                    result === 3 ||
                    result === 4
                ) {
                    if (result !== 1 && travel_processed === true) {
                        webix.alert({
                            text: "You are trying to delete a 'Staff Movement' for which a travel claim has already been processed.</br>Please contact an Administrator for assistance!",
                            width: 600,
                        });
                        callback("deny");
                    } else {
                        if (funcType === "funcToolbar") {
                            webix
                                .modalbox({
                                    title:
                                        "You are about to delete a <strong>" +
                                        leaveCode +
                                        "</strong> booking for <strong>" +
                                        empName +
                                        "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                    text:
                                        "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                        booking_single_date +
                                        "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                        booking_start_date +
                                        "</strong> to <strong>" +
                                        booking_end_date +
                                        "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                    buttons: ["Cancel", "One Day", "All Days"],
                                    width: 600,
                                })
                                .then(function (result) {
                                    switch (result) {
                                        case "0":
                                            break;
                                        case "1":
                                            sel_delete_period = "one";
                                            editDeleteBookings.deleteBookingDayOnly(
                                                bookingId,
                                                globalSelectedDate,
                                                status,
                                                "",
                                            );
                                            callback("allow");
                                            break;
                                        case "2":
                                            sel_delete_period = "all";
                                            editDeleteBookings.deleteWholeBooking(
                                                bookingId,
                                                status,
                                                null,
                                                payId,
                                            );
                                            callback("allow");
                                            break;
                                    }
                                });
                        } else if (funcType === "leaveTab") {
                            callback("allow");
                        }
                    }
                } else if (result === 5) {
                    if (leaveCode === "OC" || leaveCode === "OD") {
                        webix.alert({
                            text: "You don't have permission to delete this 'Staff Movement' booking!",
                            width: 500,
                        });
                        callback("deny");
                    } else {
                        if (travel_processed === true) {
                            webix.alert({
                                text: "You are trying to delete a 'Staff Movement' for which a travel claim has already been processed.</br>Please contact an Administrator for assistance!",
                                width: 500,
                            });
                            callback("deny");
                        } else {
                            if (funcType === "funcToolbar") {
                                webix
                                    .modalbox({
                                        title:
                                            "You are about to delete a <strong>" +
                                            leaveCode +
                                            "</strong> booking for <strong>" +
                                            empName +
                                            "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                        text:
                                            "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br>[Cancel] - don't delete anything</br>[One Day] - this will delete the booking only for the selected date <strong>" +
                                            booking_single_date +
                                            "</strong></br>[All Days] - this will delete the whole booking from <strong>" +
                                            booking_start_date +
                                            "</strong> to <strong>" +
                                            booking_end_date +
                                            "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                        buttons: ["Cancel", "One Day", "All Days"],
                                        width: 600,
                                    })
                                    .then(function (result) {
                                        switch (result) {
                                            case "0":
                                                break;
                                            case "1":
                                                sel_delete_period = "one";
                                                editDeleteBookings.deleteBookingDayOnly(
                                                    bookingId,
                                                    globalSelectedDate,
                                                    status,
                                                    "",
                                                );
                                                callback("allow");
                                                break;
                                            case "2":
                                                sel_delete_period = "all";
                                                editDeleteBookings.deleteWholeBooking(
                                                    bookingId,
                                                    status,
                                                    null,
                                                    payId,
                                                );
                                                callback("allow");
                                                break;
                                        }
                                    });
                            } else if (funcType === "leaveTab") {
                                callback("allow");
                            }
                        }
                    }
                } else if (result === 6) {
                    webix.alert({
                        text: "You don't have permission to delete 'Staff Movement' bookings!",
                        width: 500,
                    });
                    callback("deny");
                }
            }
        } else if (bkType === "day_work") {
            if (funcType === "funcToolbar") {
                if (result === 1 || result === 2) {
                    if (recurring_link_id == null) {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a '" +
                                    status +
                                    "' <strong> " +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br><strong>[All Days]</strong> - this will delete the whole booking from <strong>" +
                                    booking_start_date +
                                    "</strong> to <strong>" +
                                    booking_end_date +
                                    "</strong></br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Days"],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            null,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    } else {
                        webix
                            .modalbox({
                                title:
                                    "You are about to delete a '" +
                                    status +
                                    "' <strong> " +
                                    leaveCode +
                                    "</strong> booking for <strong>" +
                                    empName +
                                    "</strong></br></br>Select an option below to confirm that you want to proceed with the deletion!",
                                text:
                                    "<div align='left'>-------------------------------------------------------------------------------------------------------------------------------------------</br><strong>[Cancel]</strong> - don't delete anything</br><strong>[One Day]</strong> - this will delete the booking only for the selected date <strong>" +
                                    booking_single_date +
                                    "</strong></br><strong>[All Rec.]</strong> - this will delete all the linked recurring days</br><strong>[Future Rec.]</strong> - this will only delete future linked recurring days</br>-------------------------------------------------------------------------------------------------------------------------------------------</div>",
                                buttons: ["Cancel", "One Day", "All Rec.", "Future Rec."],
                                width: 600,
                            })
                            .then(function (result) {
                                switch (result) {
                                    case "0":
                                        break;
                                    case "1":
                                        sel_delete_period = "one";
                                        editDeleteBookings.deleteBookingDayOnly(
                                            bookingId,
                                            globalSelectedDate,
                                            status,
                                            "",
                                        );
                                        callback("allow");
                                        break;
                                    case "2":
                                        sel_delete_period = "all";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            recurring_link_id,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                    case "3":
                                        sel_delete_period = "future";
                                        editDeleteBookings.deleteWholeBooking(
                                            bookingId,
                                            status,
                                            recurring_link_id,
                                            payId,
                                        );
                                        callback("allow");
                                        break;
                                }
                            });
                    }
                } else if (
                    result === 3 ||
                    result === 4 ||
                    result === 5 ||
                    result === 6
                ) {
                    webix.alert({
                        text: "You don't have permission to delete this booking!",
                        width: 500,
                    });
                    callback("deny");
                }
            }
        }
    })
}

function getPayIdfromSB(bookingId, callback) {
    webix
        .ajax()
        .headers({Authorization: "Bearer " + api_key})
        .sync()
        .get(
            server_url + "/bookings/get_pay_id_from_sb",
            {booking_id: bookingId},
            {
                error: function (err) {
                    callback("error");
                },
                success: function (result) {
                    if (result) {
                        let value = JSON.parse(result);
                        callback(value[0].pay_id);
                    } else {
                        callback("error");
                    }
                },
            },
        );
}

function resetSBlink(payId, dateString) {
    webix
        .ajax()
        .headers({Authorization: "Bearer " + api_key})
        .sync()
        .put(
            server_url + "/bookings/reset_sb_link",
            {pay_id: payId, date_string: dateString},
            {
                error: function (err) {
                }, success: function (result) {
                }
            },
        );
}


function checkCreatePermissions(funcType, bkType, callback) {
    let curr_roster = $$("schedule-page").$$("schedule_rosters").getText();
    let curr_shift = $$("schedule-page").$$("schedule_shifts").getText();
    let days_past = 0;
    let formValues = "";
    let startTime = "";
    let beginDateTime = "";
    let checkDateTime = "";
    let leaveCode = "";
    let currTime = moment();

    getUserPL(function (result) {
        if (funcType === "funcToolbar") {
            if (bkType === "leave_request") {
                formValues = $$("bookings_form").getValues();
                startTime = $("#bookings_start_time").mobiscroll("getVal");
                beginDateTime =
                    moment(formValues.bookings_start_date.slice(0, 10)).format(
                        "YYYY-MM-DD",
                    ) +
                    " " +
                    startTime;
                days_past = moment(beginDateTime).diff(moment(), "days", true);
                leaveCode = formValues.bookings_leave_type;
                if (
                    leaveCode == "ARL" ||
                    leaveCode == "PHOL" ||
                    leaveCode == "RET" ||
                    leaveCode == "SOIL" ||
                    leaveCode == "TOIL" ||
                    leaveCode == "ADOP" ||
                    leaveCode === "ANN"
                ) {
                    if (result === 1 || result === 2) {
                        callback("Approved");
                    } else if (
                        result === 3 ||
                        result === 4 ||
                        result === 5
                    ) {
                        checkWeekends(currTime, beginDateTime, function (days_diff) {
                            if (days_diff > 2 && days_past < 731) {
                                callback("Pending");
                            } else {
                                webix.alert({
                                    text:
                                        "You can't create " +
                                        leaveCode +
                                        " bookings that are less than 2 business days or more than 731 days away!",
                                    width: 550,
                                });
                                callback("deny");
                            }
                        });
                    } else if (result === 6) {
                        if (user_logged_in != selected_employee_info.pay_id) {
                            webix.alert({
                                text: "You can only create your own bookings!",
                                width: 350,
                            });
                        } else {
                            checkWeekends(currTime, beginDateTime, function (days_diff) {
                                if (days_diff > 2 && days_past < 731) {
                                    callback("Pending");
                                } else {
                                    webix.alert({
                                        text:
                                            "You can't create " +
                                            leaveCode +
                                            " bookings that are less than 2 business days or more than 731 days away!",
                                        width: 450,
                                    });
                                    callback("deny");
                                }
                            });
                        }
                    }
                } else if (
                    leaveCode == "LSL" ||
                    leaveCode == "XLSL" ||
                    leaveCode == "RRL" ||
                    leaveCode == "LSLH" ||
                    leaveCode == "ULSL"
                ) {
                    if (
                        daySeqNumber == 1 ||
                        (curr_roster == "OTR" && daySeqNumber == 9) ||
                        (curr_roster == "Comms" &&
                            curr_shift == "CommCen E1" &&
                            daySeqNumber == 7) ||
                        (curr_roster == "Comms" &&
                            curr_shift == "CommCen E2" &&
                            daySeqNumber == 9)
                    ) {
                        if (leaveCode == "XLSL" || leaveCode == "RRL") {
                            if (result === 1 || result === 2) {
                                callback("Approved");
                            } else {
                                webix.alert({
                                    text:
                                        "You don't have permission to create " +
                                        leaveCode +
                                        " bookings!",
                                    width: 400,
                                });
                            }
                        } else if (leaveCode == "LSL" || leaveCode == "ULSL") {
                            if (result === 1 || result === 2) {
                                callback("Approved");
                            } else if (
                                result === 3 ||
                                result === 4 ||
                                result === 5
                            ) {
                                checkWeekends(currTime, beginDateTime, function (days_diff) {
                                    if (days_diff > 2 && days_past < 731) {
                                        callback("Pending");
                                    } else {
                                        webix.alert({
                                            text:
                                                "You can't create " +
                                                leaveCode +
                                                " bookings that are less than 2 business days or more than 731 days away!",
                                            width: 450,
                                        });
                                        callback("deny");
                                    }
                                });
                            } else if (result === 6) {
                                if (user_logged_in != selected_employee_info.pay_id) {
                                    webix.alert({
                                        text: "You can only create your own bookings!",
                                        width: 350,
                                    });
                                } else {
                                    checkWeekends(currTime, beginDateTime, function (days_diff) {
                                        if (days_diff > 2 && days_past < 731) {
                                            callback("Pending");
                                        } else {
                                            webix.alert({
                                                text:
                                                    "You can't create " +
                                                    leaveCode +
                                                    " bookings that are less than 2 business days or more than 731 days away!",
                                                width: 450,
                                            });
                                            callback("deny");
                                        }
                                    });
                                }
                            }
                        } else if (leaveCode == "LSLH") {
                            if (result === 1 || result === 2) {
                                callback("Approved");
                            } else {
                                webix.alert({
                                    text:
                                        "You don't have permission to create " +
                                        leaveCode +
                                        " bookings!",
                                    width: 400,
                                });
                                callback("deny");
                            }
                        }
                    } else {
                        webix.alert({
                            text:
                                "You can only create a " +
                                leaveCode +
                                " booking when the 'Start Date' is the first work day of a tour!",
                            width: 550,
                        });
                        callback("deny");
                    }
                } else if (leaveCode == "LSLS") {
                    if (result === 1 || result === 2) {
                        callback("Approved");
                    } else if (
                        result === 3 ||
                        result === 4 ||
                        result === 5
                    ) {
                        checkWeekends(currTime, beginDateTime, function (days_diff) {
                            if (days_diff > 2 && days_past < 30) {
                                callback("Pending");
                            } else {
                                webix.alert({
                                    text:
                                        "You can't create " +
                                        leaveCode +
                                        " bookings that are less than 2 business days or more than 30 days away!",
                                    width: 450,
                                });
                                callback("deny");
                            }
                        });
                    } else if (result === 6) {
                        if (user_logged_in != selected_employee_info.pay_id) {
                            webix.alert({
                                text: "You can only create your own bookings!",
                                width: 350,
                            });
                        } else {
                            checkWeekends(currTime, beginDateTime, function (days_diff) {
                                if (days_diff > 2 && days_past < 30) {
                                    callback("Pending");
                                } else {
                                    webix.alert({
                                        text:
                                            "You can't create " +
                                            leaveCode +
                                            " bookings that are less than 2 business days or more than 30 days away!",
                                        width: 450,
                                    });
                                    callback("deny");
                                }
                            });
                        }
                    }
                } else if (
                    leaveCode == "XARL" ||
                    leaveCode == "XLSS" ||
                    leaveCode == "XPHL" ||
                    leaveCode == "XRET" ||
                    leaveCode == "XTOI" ||
                    leaveCode == "SPEC" ||
                    leaveCode == "LWOP" ||
                    leaveCode == "MATH" ||
                    leaveCode == "MATP" ||
                    leaveCode == "PPLS" ||
                    leaveCode == "PUR4" ||
                    leaveCode == "PURA" ||
                    leaveCode == "AWOL" ||
                    leaveCode == "NWD" ||
                    leaveCode == "OTHE"
                ) {
                    if (result === 1 || result === 2) {
                        callback("Approved");
                    } else {
                        if (result === 3 && leaveCode == "OTHE") {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text:
                                    "You don't have permission to create " +
                                    leaveCode +
                                    " bookings!",
                                width: 400,
                            });
                            callback("deny");
                        }
                    }
                } else if (leaveCode == "VSBT") {
                    if (result === 1 || result === 3) {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 400,
                        });
                        callback("deny");
                    }
                } else if (
                    leaveCode == "J" ||
                    leaveCode == "LD" ||
                    leaveCode == "E" ||
                    leaveCode == "WC" ||
                    leaveCode == "UGAD" ||
                    leaveCode == "RW" ||
                    leaveCode == "EXCH"
                ) {
                    if (
                        result === 1 ||
                        result === 2 ||
                        result === 3 ||
                        result === 4
                    ) {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 400,
                        });
                        callback("deny");
                    }
                } else if (
                    leaveCode == "ULSL" ||
                    leaveCode == "UPHL" ||
                    leaveCode == "URET" ||
                    leaveCode == "ULSS"
                ) {
                    if (result === 1 || result === 2) {
                        callback("Approved");
                    } else if (result === 3) {
                        if (days_past > -2) {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text:
                                    "You can't create " +
                                    leaveCode +
                                    " bookings that are more than 2 days in the past!",
                                width: 550,
                            });
                            callback("deny");
                        }
                    } else if (
                        result === 4 ||
                        result === 5 ||
                        result === 6
                    ) {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 400,
                        });
                        callback("deny");
                    }
                } else if (leaveCode == "OL") {
                    if (result === 1 || result === 2) {
                        callback("Approved");
                    } else if (result === 3 || result === 4) {
                        if (days_past > -30) {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text:
                                    "You can't create " +
                                    leaveCode +
                                    " bookings that are more than 30 days in the past!",
                                width: 550,
                            });
                            callback("deny");
                        }
                    } else if (result === 5 || result === 6) {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 400,
                        });
                        callback("deny");
                    }
                }
            } else if (bkType === "sick_leave") {
                formValues = $$("bookings_form").getValues();
                startTime = $("#bookings_start_time").mobiscroll("getVal");
                beginDateTime =
                    moment(formValues.bookings_start_date.slice(0, 10)).format(
                        "YYYY-MM-DD",
                    ) +
                    " " +
                    startTime;
                days_past = moment(beginDateTime).diff(moment(), "days", true);
                leaveCode = formValues.bookings_leave_type;
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    if (
                        leaveCode == "BERE" ||
                        leaveCode == "FAML" ||
                        leaveCode == "SIC" ||
                        leaveCode == "SICM" ||
                        leaveCode == "CVWR"
                    ) {
                        if (days_past >= -180) {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text: "You don't have permission to create 'Sickness' bookings more than 180 days in the past!",
                                width: 400,
                            });
                            callback("deny");
                        }
                    } else if (
                        leaveCode == "SLUP" ||
                        leaveCode == "SLUW" ||
                        leaveCode == "CVPL" ||
                        leaveCode == "CVUL" ||
                        leaveCode == "WRSL" ||
                        leaveCode == "CVIL" ||
                        leaveCode == "CVFL" ||
                        leaveCode == "CVSL"
                    ) {
                        webix.alert({
                            text:
                                "You don't have permission to create " +
                                leaveCode +
                                " sickness bookings!",
                            width: 400,
                        });
                        callback("deny");
                    } else {
                        callback("Approved");
                    }
                } else if (result === 4) {
                    if (
                        leaveCode == "BERE" ||
                        leaveCode == "FAML" ||
                        leaveCode == "SIC" ||
                        leaveCode == "SICM"
                    ) {
                        if (days_past >= -30) {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text: "You don't have permission to create 'Sickness' bookings more than 30 days in the past!",
                                width: 400,
                            });
                            callback("deny");
                        }
                    } else if (
                        leaveCode == "SLUP" ||
                        leaveCode == "SLUW" ||
                        leaveCode == "WRSL" ||
                        leaveCode == "CVPL" ||
                        leaveCode == "CVUL" ||
                        leaveCode == "CVIL" ||
                        leaveCode == "CVFL" ||
                        leaveCode == "CVSL" ||
                        leaveCode == "CVWR"
                    ) {
                        webix.alert({
                            text:
                                "You don't have permission to create " +
                                leaveCode +
                                " sickness bookings!",
                            width: 400,
                        });
                        callback("deny");
                    } else {
                        callback("Approved");
                    }
                } else if (result === 5) {
                    if (
                        leaveCode == "BERE" ||
                        leaveCode == "FAML" ||
                        leaveCode == "SIC" ||
                        leaveCode == "SICM"
                    ) {
                        if (days_past >= -2) {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text: "You don't have permission to create 'Sickness' bookings more than 2 days in the past!",
                                width: 400,
                            });
                            callback("deny");
                        }
                    } else if (
                        leaveCode == "SLUP" ||
                        leaveCode == "SLUW" ||
                        leaveCode == "CVPL" ||
                        leaveCode == "CVUL" ||
                        leaveCode == "WRSL" ||
                        leaveCode == "CVIL" ||
                        leaveCode == "CVFL" ||
                        leaveCode == "CVSL" ||
                        leaveCode == "CVWR"
                    ) {
                        webix.alert({
                            text:
                                "You don't have permission to create " +
                                leaveCode +
                                " sickness bookings!",
                            width: 400,
                        });
                        callback("deny");
                    }
                } else if (result === 6) {
                    if (user_logged_in != selected_employee_info.pay_id) {
                        webix.alert({
                            text: "You can only create your own sickness bookings!",
                            width: 400,
                        });
                        callback("deny");
                    } else {
                        if (
                            leaveCode == "BERE" ||
                            leaveCode == "FAML" ||
                            leaveCode == "SIC" ||
                            leaveCode == "SICM"
                        ) {
                            if (days_past >= 0) {
                                callback("Approved");
                            } else {
                                webix.alert({
                                    text: "You don't have permission to create 'Sickness' bookings in the past!",
                                    width: 400,
                                });
                                callback("deny");
                            }
                        } else if (
                            leaveCode == "SLUP" ||
                            leaveCode == "SLUW" ||
                            leaveCode == "CVPL" ||
                            leaveCode == "CVUL" ||
                            leaveCode == "WRSL" ||
                            leaveCode == "CVIL" ||
                            leaveCode == "CVFL" ||
                            leaveCode == "CVSL" ||
                            leaveCode == "CVWR"
                        ) {
                            webix.alert({
                                text:
                                    "You don't have permission to create " +
                                    leaveCode +
                                    " sickness bookings!",
                                width: 400,
                            });
                            callback("deny");
                        }
                    }
                }
            } else if (bkType === "standby" || bkType === "standby_link") {
                formValues = $$("standbys_form").getValues();
                startTime = $("#standbys_start_time").mobiscroll("getVal");
                beginDateTime =
                    moment(formValues.standbys_start_date.slice(0, 10)).format(
                        "YYYY-MM-DD",
                    ) +
                    " " +
                    startTime;
                days_past = moment(beginDateTime).diff(moment(), "days", true);
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    callback("Approved");
                } else if (result === 4) {
                    callback("Approved");
                } else if (result === 5) {
                    if (days_past >= 0) {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text: "You don't have permission to create 'Standby' bookings for past shifts!",
                            width: 500,
                        });
                        callback("deny");
                    }
                } else if (result === 6) {
                    webix.alert({
                        text: "You don't have permission to create 'Standby' bookings!",
                        width: 500,
                    });
                    callback("deny");
                }
            } else if (bkType === "act-up") {
                formValues = $$("actups_form").getValues();
                beginDateTime = moment(formValues.actups_start_date.slice(0, 10)).format(
                    "YYYYMMDD",
                );
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    callback("Approved");
                } else if (result === 4) {
                    callback("Approved");
                } else if (result === 5) {
                    if (beginDateTime === moment().format("YYYYMMDD")) {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text: "You can only create an 'Act-Up' for today's shift!",
                            width: 450,
                        });
                        callback("deny");
                    }
                } else if (result === 6) {
                    webix.alert({
                        text: "You don't have permission to create 'Act-up' bookings!",
                        width: 500,
                    });
                    callback("deny");
                }
            } else if (bkType === "overtime") {
                formValues = $$("overtime_form").getValues();
                startTime = $("#overtime_start_time").mobiscroll("getVal");
                beginDateTime =
                    moment(formValues.overtime_start_date.slice(0, 10)).format(
                        "YYYY-MM-DD",
                    ) +
                    " " +
                    startTime;
                checkDateTime = moment(
                    formValues.overtime_start_date.slice(0, 10),
                ).format("YYYYMMDD");
                days_past = moment(beginDateTime).diff(moment(), "days", true);
                leaveCode = formValues.overtime_activity;
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    callback("Approved");
                } else if (result === 4) {
                    if (
                        leaveCode == "HB" ||
                        leaveCode == "OTFC" ||
                        leaveCode == "RC" ||
                        leaveCode == "IA"
                    ) {
                        if (days_past > 8) {
                            webix.alert({
                                text:
                                    "You don't have permission to create " +
                                    leaveCode +
                                    " bookings more than 8 days away!",
                                width: 500,
                            });
                            callback("deny");
                        } else {
                            callback("Approved");
                        }
                    } else if (
                        leaveCode == "REL" ||
                        leaveCode == "DRILL" ||
                        leaveCode == "VSBW"
                    ) {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 500,
                        });
                        callback("deny");
                    }
                } else if (result === 5) {
                    if (leaveCode == "HB" || leaveCode == "OTFC" || leaveCode == "RC") {
                        if (checkDateTime === moment().format("YYYYMMDD")) {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text:
                                    "You can only create " +
                                    leaveCode +
                                    " bookings for today's shift!",
                                width: 500,
                            });
                            callback("deny");
                        }
                    } else if (leaveCode == "VSBW") {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 500,
                        });
                        callback("deny");
                    }
                } else if (result === 6) {
                    webix.alert({
                        text: "You don't have permission to create 'Overtime' bookings!",
                        width: 500,
                    });
                    callback("deny");
                }
            } else if (bkType === "staff_movement") {
                formValues = $$("staff_movement_form").getValues();
                leaveCode = formValues.shift_adjustment_type;
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    callback("Approved");
                } else if (result === 4) {
                    callback("Approved");
                } else if (result === 5) {
                    if (leaveCode == "OC" || leaveCode == "OD" || leaveCode == "Depl") {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 500,
                        });
                        callback("deny");
                    } else {
                        callback("Approved");
                    }
                } else if (result === 6) {
                    webix.alert({
                        text: "You don't have permission to create 'Staff Movement' bookings!",
                        width: 500,
                    });
                    callback("deny");
                }
            } else if (bkType === "day_work") {
                callback("Approved");
            }
        } else if (funcType === "leaveTab") {
            formValues = $$("bookings-page")
                .$$("booking_create")
                .$$("create_booking")
                .getValues();
            beginDateTime =
                moment(formValues.start_date.slice(0, 10)).format("YYYY-MM-DD") +
                " " +
                formValues.start_time.slice(0, 5);
            days_past = moment(beginDateTime).diff(moment(), "days", true);
            leaveCode = formValues.leave_type;
            if (
                leaveCode == "ARL" ||
                leaveCode == "PHOL" ||
                leaveCode == "RET" ||
                leaveCode == "SOIL" ||
                leaveCode == "TOIL" ||
                leaveCode == "ADOP" ||
                leaveCode === "ANN"
            ) {
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    checkWeekends(currTime, beginDateTime, function (days_diff) {
                        if (days_diff > 2 && days_past < 731) {
                            callback("Pending");
                        } else {
                            webix.alert({
                                text:
                                    "You can't create " +
                                    leaveCode +
                                    " bookings that are less than 2 business days or more than 731 days away!",
                                width: 450,
                            });
                            callback("deny");
                        }
                    });
                }
            } else if (
                leaveCode == "LSL" ||
                leaveCode == "XLSL" ||
                leaveCode == "RRL" ||
                leaveCode == "LSLH" ||
                leaveCode == "ULSL"
            ) {
                if (daySeqNumber !== 1) {
                    webix.alert({
                        text:
                            "You can only create an " +
                            leaveCode +
                            " booking when the 'Start Date' is the first work day of a tour!",
                        width: 550,
                    });
                    callback("deny");
                } else {
                    if (leaveCode == "XLSL" || leaveCode == "RRL") {
                        if (result === 1 || result === 2) {
                            callback("Approved");
                        } else {
                            webix.alert({
                                text:
                                    "You don't have permission to create " +
                                    leaveCode +
                                    " bookings!",
                                width: 400,
                            });
                            callback("deny");
                        }
                    } else if (leaveCode == "LSL" || leaveCode == "ULSL") {
                        if (result === 1 || result === 2) {
                            callback("Approved");
                        } else if (result === 3) {
                            checkWeekends(currTime, beginDateTime, function (days_diff) {
                                if (days_diff > 2 && days_past < 731) {
                                    callback("Pending");
                                } else {
                                    webix.alert({
                                        text:
                                            "You can't create " +
                                            leaveCode +
                                            " bookings that are less than 2 business days or more than 731 days away!",
                                        width: 450,
                                    });
                                    callback("deny");
                                }
                            });
                        }
                    }
                }
            } else if (leaveCode == "LSLS") {
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    checkWeekends(currTime, beginDateTime, function (days_diff) {
                        if (days_diff > 2 && days_past < 30) {
                            callback("Pending");
                        } else {
                            webix.alert({
                                text:
                                    "You can't create " +
                                    leaveCode +
                                    " bookings that are less than 2 business days or more than 30 days away!",
                                width: 450,
                            });
                            callback("deny");
                        }
                    });
                }
            } else if (
                leaveCode == "XARL" ||
                leaveCode == "XLSS" ||
                leaveCode == "XPHL" ||
                leaveCode == "XRET" ||
                leaveCode == "XTOI" ||
                leaveCode == "SPEC" ||
                leaveCode == "LWOP" ||
                leaveCode == "MATH" ||
                leaveCode == "MATP" ||
                leaveCode == "PPLS" ||
                leaveCode == "PUR4" ||
                leaveCode == "PURA" ||
                leaveCode == "AWOL" ||
                leaveCode == "NWD" ||
                leaveCode == "OTHE"
            ) {
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else {
                    if (result === 3 && leaveCode == "OTHE") {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text:
                                "You don't have permission to create " + leaveCode + " bookings!",
                            width: 400,
                        });
                        callback("deny");
                    }
                }
            } else if (leaveCode == "VSBT") {
                if (result === 1 || result === 3) {
                    callback("Approved");
                } else {
                    webix.alert({
                        text:
                            "You don't have permission to create " + leaveCode + " bookings!",
                        width: 400,
                    });
                    callback("deny");
                }
            } else if (
                leaveCode == "J" ||
                leaveCode == "LD" ||
                leaveCode == "E" ||
                leaveCode == "WC" ||
                leaveCode == "UGAD" ||
                leaveCode == "RW" ||
                leaveCode == "EXCH"
            ) {
                if (
                    result === 1 ||
                    result === 2 ||
                    result === 3
                ) {
                    callback("Approved");
                } else {
                    webix.alert({
                        text:
                            "You don't have permission to create " + leaveCode + " bookings!",
                        width: 400,
                    });
                    callback("deny");
                }
            } else if (
                leaveCode == "ULSL" ||
                leaveCode == "UPHL" ||
                leaveCode == "URET" ||
                leaveCode == "ULSS"
            ) {
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    if (days_past > -2) {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text:
                                "You can't create " +
                                leaveCode +
                                " bookings that are more than 2 days in the past!",
                            width: 550,
                        });
                        callback("deny");
                    }
                }
            } else if (leaveCode == "OL") {
                if (result === 1 || result === 2) {
                    callback("Approved");
                } else if (result === 3) {
                    if (days_past > -30) {
                        callback("Approved");
                    } else {
                        webix.alert({
                            text:
                                "You can't create " +
                                leaveCode +
                                " bookings that are more than 30 days in the past!",
                            width: 550,
                        });
                        callback("deny");
                    }
                }
            }
        }
    })
}

function setDefaultRosterView(payId, callback) {
    let p_level = 6;
    webix
        .ajax()
        .headers({Authorization: "Bearer " + api_key})
        .sync()
        .get(
            server_url + "/login/current_roster",
            {pay_id: payId},
            {
                error: function (err) {
                    $$("schedule-page").$$("schedule_rosters").setValue("Metro");
                },
                success: function (results) {
                    let values = JSON.parse(results);
                    if (values.length > 0) {
                        curr_user_roster = values[0].roster;
                        curr_user_shift = values[0].shift;
                        curr_user_location = values[0].location;

                        if (values[0].rank == "MOFF") {
                            webix
                                .ajax()
                                .headers({Authorization: "Bearer " + api_key})
                                .sync()
                                .put(
                                    server_url + "/admin/permission",
                                    {pay_id: payId, permission_level: 5},
                                    {
                                        error: function (err) {
                                        },
                                        success: function () {
                                            if (user_permission_level > 5) {
                                                user_permission_level = 5;
                                            }
                                            $$("logged_in_label").define(
                                                "label",
                                                "<div class='fas fa-user' style='color: white; margin-right: 5px'></div><div class='header_font_small' style='display:inline-block'</div>" +
                                                user_logged_in_name +
                                                " | " +
                                                user_logged_in +
                                                " | " +
                                                user_logged_in_email +
                                                " | " +
                                                user_permission_level +
                                                "</div>",
                                            );
                                            $$("logged_in_label").refresh();
                                        },
                                    },
                                );
                        } else if (values[0].rank == "COFF") {
                            webix
                                .ajax()
                                .headers({Authorization: "Bearer " + api_key})
                                .sync()
                                .put(
                                    server_url + "/admin/permission",
                                    {pay_id: payId, permission_level: 4},
                                    {
                                        error: function (err) {
                                        },
                                        success: function () {
                                            if (user_permission_level > 4) {
                                                user_permission_level = 4;
                                            }
                                            $$("logged_in_label").define(
                                                "label",
                                                "<div class='fas fa-user' style='color: white; margin-right: 5px'></div><div class='header_font_small' style='display:inline-block'</div>" +
                                                user_logged_in_name +
                                                " | " +
                                                user_logged_in +
                                                " | " +
                                                user_logged_in_email +
                                                " | " +
                                                user_permission_level +
                                                "</div>",
                                            );
                                            $$("logged_in_label").refresh();
                                        },
                                    },
                                );
                        } else if (values[0].rank == "SFF") {
                            getSkillCodes(payId, function (response) {
                                if (response.some((response) => response.code == "SFQ")) {
                                    p_level = 5;
                                } else if (
                                    response.some((response) => response.code == "SFC")
                                ) {
                                    p_level = 5;
                                } else if (
                                    response.some((response) => response.code == "SFRQ")
                                ) {
                                    p_level = 5;
                                } else if (
                                    response.some((response) => response.code == "MOQ")
                                ) {
                                    p_level = 5;
                                } else if (
                                    response.some((response) => response.code == "SCQ")
                                ) {
                                    p_level = 5;
                                } else if (
                                    response.some((response) => response.code == "MVE")
                                ) {
                                    p_level = 5;
                                } else if (
                                    response.some((response) => response.code == "ESC")
                                ) {
                                    p_level = 3;
                                } else {
                                    p_level = 6;
                                }
                                webix
                                    .ajax()
                                    .headers({Authorization: "Bearer " + api_key})
                                    .sync()
                                    .put(
                                        server_url + "/admin/permission",
                                        {pay_id: payId, permission_level: p_level},
                                        {
                                            error: function (err) {
                                            },
                                            success: function () {
                                                if (user_permission_level > p_level) {
                                                    user_permission_level = p_level;
                                                }
                                                $$("logged_in_label").define(
                                                    "label",
                                                    "<div class='fas fa-user' style='color: white; margin-right: 5px'></div><div class='header_font_small' style='display:inline-block'</div>" +
                                                    user_logged_in_name +
                                                    " | " +
                                                    user_logged_in +
                                                    " | " +
                                                    user_logged_in_email +
                                                    " | " +
                                                    user_permission_level +
                                                    "</div>",
                                                );
                                                $$("logged_in_label").refresh();
                                            },
                                        },
                                    );
                            });
                        } else if (values[0].rank == "SO") {
                            getSkillCodes(payId, function (response) {
                                if (response.some((response) => response.code == "SOQ")) {
                                    p_level = 3;
                                } else if (
                                    response.some((response) => response.code == "SOC")
                                ) {
                                    p_level = 3;
                                } else if (
                                    response.some((response) => response.code == "DRO")
                                ) {
                                    p_level = 4;
                                } else if (
                                    response.some((response) => response.code == "EMO")
                                ) {
                                    p_level = 4;
                                } else if (
                                    response.some((response) => response.code == "ESC")
                                ) {
                                    p_level = 3;
                                } else {
                                    p_level = 5;
                                }
                                webix
                                    .ajax()
                                    .headers({Authorization: "Bearer " + api_key})
                                    .sync()
                                    .put(
                                        server_url + "/admin/permission",
                                        {pay_id: payId, permission_level: p_level},
                                        {
                                            error: function (err) {
                                            },
                                            success: function () {
                                                if (user_permission_level > p_level) {
                                                    user_permission_level = p_level;
                                                }
                                                $$("logged_in_label").define(
                                                    "label",
                                                    "<div class='fas fa-user' style='color: white; margin-right: 5px'></div><div class='header_font_small' style='display:inline-block'</div>" +
                                                    user_logged_in_name +
                                                    " | " +
                                                    user_logged_in +
                                                    " | " +
                                                    user_logged_in_email +
                                                    " | " +
                                                    user_permission_level +
                                                    "</div>",
                                                );
                                                $$("logged_in_label").refresh();
                                            },
                                        },
                                    );
                            });
                        } else if (values[0].rank == "RSO") {
                            p_level = 5;
                            webix
                                .ajax()
                                .headers({Authorization: "Bearer " + api_key})
                                .sync()
                                .put(
                                    server_url + "/admin/permission",
                                    {pay_id: payId, permission_level: p_level},
                                    {
                                        error: function (err) {
                                        },
                                        success: function () {
                                            if (user_permission_level > p_level) {
                                                user_permission_level = p_level;
                                            }
                                            $$("logged_in_label").define(
                                                "label",
                                                "<div class='fas fa-user' style='color: white; margin-right: 5px'></div><div class='header_font_small' style='display:inline-block'</div>" +
                                                user_logged_in_name +
                                                " | " +
                                                user_logged_in +
                                                " | " +
                                                user_logged_in_email +
                                                " | " +
                                                user_permission_level +
                                                "</div>",
                                            );
                                            $$("logged_in_label").refresh();
                                        },
                                    },
                                );
                        } else if (
                            values[0].rank == "MAN" &&
                            curr_user_roster == "Community Safety & Resilience" &&
                            curr_user_shift == "CRD"
                        ) {
                            p_level = 3;
                            webix
                                .ajax()
                                .headers({Authorization: "Bearer " + api_key})
                                .sync()
                                .put(
                                    server_url + "/admin/permission",
                                    {pay_id: payId, permission_level: p_level},
                                    {
                                        error: function (err) {
                                        },
                                        success: function () {
                                            if (user_permission_level > p_level) {
                                                user_permission_level = p_level;
                                            }
                                            $$("logged_in_label").define(
                                                "label",
                                                "<div class='fas fa-user' style='color: white; margin-right: 5px'></div><div class='header_font_small' style='display:inline-block'</div>" +
                                                user_logged_in_name +
                                                " | " +
                                                user_logged_in +
                                                " | " +
                                                user_logged_in_email +
                                                " | " +
                                                user_permission_level +
                                                "</div>",
                                            );
                                            $$("logged_in_label").refresh();
                                        },
                                    },
                                );
                        }
                        updateEmployeeHomeStationInfo(
                            payId,
                            values[0].perm_home_station,
                            values[0].perm_home_station_id,
                        );

                        $$("loader-window").hide();
                        if (user_permission_level == 1) {
                            $$("bookings-page")
                                .$$("travel_create")
                                .$$("btn_find_travel_bookings")
                                .enable();
                            $$("bookings-page")
                                .$$("travel_create")
                                .$$("btn_generate_travel_claims")
                                .enable();
                        }
                        callback("loaded");
                    } else {
                        $$("schedule-page").$$("schedule_rosters").setValue("Metro");
                        updateEmployeeHomeStationInfo(payId, "", 0);
                        if (user_permission_level == 1) {
                            $$("bookings-page")
                                .$$("travel_create")
                                .$$("btn_find_travel_bookings")
                                .enable();
                            $$("bookings-page")
                                .$$("travel_create")
                                .$$("btn_generate_travel_claims")
                                .enable();
                        }
                        callback("loaded");
                    }
                },
            },
        );
}

function updateEmployeeHomeStationInfo(pay_id, location, location_id) {
    let locationId = location_id;
    webix
        .ajax()
        .headers({Authorization: "Bearer " + api_key})
        .put(
            server_url + "/login/update_home_station",
            {pay_id: pay_id, home_station: location, home_station_id: location_id},
            {
                error: function (err) {
                }, success: function (result) {
                }
            },
        );
}

function updateResDistance(pay_id) {
    webix
        .ajax()
        .headers({Authorization: "Bearer " + api_key})
        .post(
            server_url + "/admin/update_employee_distance",
            {pay_id: pay_id},
            {
                error: function (err) {
                }, success: function (result) {
                }
            },
        );
}
