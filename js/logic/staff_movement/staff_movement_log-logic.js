let staffMovementLogs = (function () {
  let shiftArray = [];
  let locationsArray = [];
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("bookings-page")
      .$$("staff_movement_log")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("bookings-page")
      .$$("staff_movement_log")
      .$$("to_date")
      .setValue(endOfMonth);
    $$("bookings-page")
      .$$("staff_movement_log")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page")
      .$$("staff_movement_log")
      .$$("status_filter")
      .setValue(1);
    $$("bookings-page")
      .$$("staff_movement_log")
      .$$("code_filter")
      .setValue("ALL");
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("staff_movement_log")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("bookings-page")
          .$$("staff_movement_log")
          .$$("shift_filter")
          .setValue("");
        $$("bookings-page")
          .$$("staff_movement_log")
          .$$("location_filter")
          .setValue("");
        if (newv == "-- All Rosters --") {
          $$("bookings-page")
            .$$("staff_movement_log")
            .$$("shift_filter")
            .hide();
          $$("bookings-page")
            .$$("staff_movement_log")
            .$$("location_filter")
            .hide();
        } else {
          $$("bookings-page")
            .$$("staff_movement_log")
            .$$("shift_filter")
            .show();
          $$("bookings-page")
            .$$("staff_movement_log")
            .$$("location_filter")
            .show();
          $$("bookings-page")
            .$$("staff_movement_log")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("bookings-page")
            .$$("staff_movement_log")
            .$$("location_filter")
            .setValue("-- All Stations --");
        }
        load_shifts(newv);
      });
    $$("bookings-page")
      .$$("staff_movement_log")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        getStaffMovements();
      });
  }
  shift_types_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("staff_movement_log").$$("code_filter");
    if (data) {
      let options = [];
      let leave_types = JSON.parse(data);
      options.push({ id: "ALL", value: "-- All Relieving Types --" });
      leave_types.forEach(function (value) {
        options.push({ id: value.code, value: value.description });
      });
      select.define("options", options);
      select.refresh();
    }
  });
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                shiftArray.unshift("-- All Shifts --");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                locationsArray.unshift("-- All Stations --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                $$("bookings-page")
                  .$$("staff_movement_log")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("bookings-page")
                  .$$("staff_movement_log")
                  .$$("shift_filter")
                  .refresh();
                $$("bookings-page")
                  .$$("staff_movement_log")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("bookings-page")
                  .$$("staff_movement_log")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function getStaffMovements() {
    let fromDate = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("from_date")
      .getValue();
    let toDate = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("to_date")
      .getValue();
    let roster = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("roster_filter")
      .getText();
    let shift = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("shift_filter")
      .getText();
    let location = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("location_filter")
      .getText();
    let leave_type_code = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("code_filter")
      .getValue();
    let status = $$("bookings-page")
      .$$("staff_movement_log")
      .$$("status_filter")
      .getText();
    $$("loader-window").show();
    setTimeout(function () {
      $$("bookings-page")
        .$$("staff_movement_log")
        .$$("staff_movement_logs_grid")
        .clearAll();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/bookings/staff_movement_logs",
          {
            start_date: moment(fromDate).format("YYYY-MM-DD 00:00:01"),
            end_date: moment(toDate).format("YYYY-MM-DD 23:59:59"),
            roster: roster,
            shift: shift,
            location: location,
            leave_type_code: leave_type_code,
            status: status,
          },
          {
            error: function (err) {
              $$("loader-window").hide();
            },
            success: function (results) {
              let result = JSON.parse(results);
              let search_results = [];
              let location = "";
              let middleName = "";
              let delete_icon = "";
              if (result.length > 0) {
                for (let x = 0; x < result.length; x++) {
                  if (result[x].location === null) {
                    location = "";
                  } else {
                    location = result[x].location;
                  }
                  if (result[x].middle_name === null) {
                    middleName = "";
                  } else {
                    middleName = result[x].middle_name;
                  }
                  if (result[x].deleted == true) {
                    delete_icon =
                      "<span class = 'delete_request_true webix_icon fas fa-minus-circle'></span>";
                  } else {
                    delete_icon =
                      "<span class = 'delete_request_false webix_icon fas fa-minus-circle'></span>";
                  }
                  search_results.push({
                    booking_id: result[x].booking_id,
                    service_no: result[x].pay_id,
                    name:
                      result[x].surname +
                      ", " +
                      result[x].first_name +
                      " " +
                      middleName,
                    rank: result[x].rank,
                    roster: result[x].roster,
                    shift: result[x].shift,
                    location: location,
                    booking_first_date: result[x].booking_first_date,
                    booking_last_date: result[x].booking_last_date,
                    type: result[x].leave_type_code,
                    from: result[x].shift + " - " + result[x].location,
                    to:
                      result[x].moved_shift + " - " + result[x].moved_location,
                    deleted: delete_icon,
                  });
                }
                $$("bookings-page")
                  .$$("staff_movement_log")
                  .$$("staff_movement_logs_grid")
                  .define("data", search_results);
                $$("bookings-page")
                  .$$("staff_movement_log")
                  .$$("staff_movement_logs_grid")
                  .refresh();
                $$("loader-window").hide();
              } else {
                $$("loader-window").hide();
              }
            },
          },
        );
    }, 250);
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
