let overtimeLogs = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster;
  let exp_shift;
  let exp_location;
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("bookings-page")
      .$$("overtime_log")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("bookings-page").$$("overtime_log").$$("to_date").setValue(endOfMonth);
    $$("bookings-page")
      .$$("overtime_log")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page").$$("overtime_log").$$("status_filter").setValue(1);
    $$("bookings-page").$$("overtime_log").$$("code_filter").setValue("ALL");
    exp_grid = $$("bookings-page").$$("overtime_log").$$("overtime_logs_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report period " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Overtime Logs Report for " +
            exp_roster +
            " | " +
            exp_shift +
            " | " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("overtime_log")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("bookings-page")
          .$$("booking_log")
          .$$("from_date")
          .getValue();
        exp_toDate = $$("bookings-page")
          .$$("booking_log")
          .$$("to_date")
          .getValue();
        exp_roster = $$("bookings-page")
          .$$("booking_log")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("bookings-page")
          .$$("booking_log")
          .$$("shift_filter")
          .getText();
        exp_location = $$("bookings-page")
          .$$("booking_log")
          .$$("location_filter")
          .getText();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Overtime Logs Report (" + moment().format("DD-MM-YYYY") + ")",
            styles: true,
            heights: true,
            columns: {
              service_no: true,
              name: true,
              rank: true,
              roster: true,
              shift: true,
              location: true,
              booking_first_date: true,
              booking_last_date: true,
              type: true,
              hours: true,
              comments: true,
              from: true,
              to: true,
              deleted: {
                template: function (obj) {
                  if (
                    obj.deleted ==
                    "<span class = 'delete_request_true webix_icon fas fa-minus-circle'></span>"
                  ) {
                    return "Yes";
                  } else {
                    return "No";
                  }
                },
              },
            },
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("bookings-page")
      .$$("overtime_log")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("bookings-page").$$("overtime_log").$$("shift_filter").setValue("");
        $$("bookings-page")
          .$$("overtime_log")
          .$$("location_filter")
          .setValue("");
        if (newv == "-- All Rosters --") {
          $$("bookings-page").$$("overtime_log").$$("shift_filter").hide();
          $$("bookings-page").$$("overtime_log").$$("location_filter").hide();
        } else {
          $$("bookings-page").$$("overtime_log").$$("shift_filter").show();
          $$("bookings-page").$$("overtime_log").$$("location_filter").show();
          $$("bookings-page")
            .$$("overtime_log")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("bookings-page")
            .$$("overtime_log")
            .$$("location_filter")
            .setValue("-- All Stations --");
        }
        load_shifts(newv);
      });
    $$("bookings-page")
      .$$("overtime_log")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        getOvertimeLogs();
      });
  }
  overtime_types_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("overtime_log").$$("code_filter");
    if (data) {
      let options = [];
      let leave_types = JSON.parse(data);
      options.push({ id: "ALL", value: "-- All Overtime Types --" });
      leave_types.forEach(function (value) {
        options.push({ id: value.code, value: value.overtime_name });
      });
      select.define("options", options);
      select.refresh();
    }
  });
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("overtime_log").$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                shiftArray.unshift("-- All Shifts --");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("bookings-page")
                  .$$("overtime_log")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("bookings-page")
                  .$$("overtime_log")
                  .$$("shift_filter")
                  .refresh();
                $$("bookings-page")
                  .$$("overtime_log")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("bookings-page")
                  .$$("overtime_log")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function getOvertimeLogs() {
    let fromDate = $$("bookings-page")
      .$$("overtime_log")
      .$$("from_date")
      .getValue();
    let toDate = $$("bookings-page")
      .$$("overtime_log")
      .$$("to_date")
      .getValue();
    let roster = $$("bookings-page")
      .$$("overtime_log")
      .$$("roster_filter")
      .getText();
    let shift = $$("bookings-page")
      .$$("overtime_log")
      .$$("shift_filter")
      .getText();
    let location = $$("bookings-page")
      .$$("overtime_log")
      .$$("location_filter")
      .getText();
    let leave_type_code = $$("bookings-page")
      .$$("overtime_log")
      .$$("code_filter")
      .getValue();
    let status = $$("bookings-page")
      .$$("overtime_log")
      .$$("status_filter")
      .getText();
    exp_grid.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/overtime_logs",
        {
          start_date: moment(fromDate).format("YYYY-MM-DD 00:00:01"),
          end_date: moment(toDate).format("YYYY-MM-DD 23:59:59"),
          roster: roster,
          shift: shift,
          location: location,
          leave_type_code: leave_type_code,
          status: status,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let result = JSON.parse(results);
            let search_results = [];
            let location = "";
            let middleName = "";
            let delete_icon = "";
            let reason = "";
            if (result.length > 0) {
              for (let x = 0; x < result.length; x++) {
                if (result[x].location === null) {
                  location = "";
                } else {
                  location = result[x].location;
                }
                if (result[x].middle_name === null) {
                  middleName = "";
                } else {
                  middleName = result[x].middle_name;
                }
                if (result[x].deleted == true) {
                  delete_icon =
                    "<span class = 'delete_request_true webix_icon fas fa-minus-circle'></span>";
                } else {
                  delete_icon =
                    "<span class = 'delete_request_false webix_icon fas fa-minus-circle'></span>";
                }
                reason = result[x].comments.slice(
                  0,
                  result[x].comments.indexOf("-") - 1,
                );
                reason = reason.replace("Reason: ", "");
                search_results.push({
                  booking_id: result[x].booking_id,
                  service_no: result[x].pay_id,
                  name:
                    result[x].surname +
                    ", " +
                    result[x].first_name +
                    " " +
                    middleName,
                  rank: result[x].rank,
                  roster: result[x].roster,
                  shift: result[x].shift,
                  location: location,
                  booking_first_date: result[x].booking_first_date,
                  booking_last_date: result[x].booking_last_date,
                  type: result[x].leave_type_code,
                  hours: result[x].total_hours,
                  comments: reason,
                  from:
                    result[x].moved_shift + " - " + result[x].moved_location,
                  to: result[x].shift + " - " + result[x].location,
                  deleted: delete_icon,
                });
              }
            }
            exp_grid.define("data", search_results);
            exp_grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
