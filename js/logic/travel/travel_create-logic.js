let createTravel = (function () {
    let locations_list = [];

    function initApplication() {
        //eventHandlers();
        $$("bookings-page")
            .$$("travel_create")
            .$$("travel_date")
            .setValue(new Date());

    }

    function eventHandlers() {
        $$("bookings-page")
            .$$("travel_create")
            .$$("calc_distance")
            .attachEvent("onItemClick", function (id, e) {
                let form = $$("bookings-page")
                    .$$("travel_create")
                    .$$("formTravel")
                    .getValues();
                if (form.route_to == "" || form.route_from == "") {
                    webix.alert({
                        text: "You must specify both a 'Route From' & 'Route To' location!",
                        width: 500,
                    });
                } else if (form.route_to == form.route_from) {
                    webix.alert({
                        text: "The 'Route From' & 'Route To' locations can't be the same!",
                        width: 500,
                    });
                } else {
                    getDistance(
                        form.route_from,
                        form.route_to,
                        form.employees,
                        function (response) {
                            $$("bookings-page")
                                .$$("travel_create")
                                .$$("distance_kms")
                                .setValue(response);
                        },
                    );
                }
            });
        $$("bookings-page")
            .$$("travel_create")
            .$$("travel_confirm")
            .attachEvent("onChange", function (newv, oldv) {
                if (newv == 1) {
                    $$("bookings-page").$$("travel_create").$$("btn_save").enable();
                } else {
                    $$("bookings-page").$$("travel_create").$$("btn_save").disable();
                }
            });
        $$("bookings-page")
            .$$("travel_create")
            .$$("btn_save")
            .attachEvent("onItemClick", function (id, e) {
                if (
                    $$("bookings-page").$$("travel_create").$$("formTravel").validate()
                ) {
                    saveTravel();
                }
            });
        $$("bookings-page")
            .$$("travel_create")
            .$$("btn_find_travel_bookings")
            .attachEvent("onItemClick", function (id, e) {
                getTravelBookingsData();
            });
        $$("bookings-page")
            .$$("travel_create")
            .$$("btn_generate_travel_claims")
            .attachEvent("onItemClick", function (id, e) {
                createTravelLogs();
            });
        $$("bookings-page")
            .$$("travel_create")
            .$$("route_from")
            .attachEvent("onChange", function (newv, oldv) {
                $$("bookings-page").$$("travel_create").$$("distance_kms").setValue("");
            });
        $$("bookings-page")
            .$$("travel_create")
            .$$("route_to")
            .attachEvent("onChange", function (newv, oldv) {
                $$("bookings-page").$$("travel_create").$$("distance_kms").setValue("");
            });
        $$("bookings-page")
            .$$("travel_create")
            .$$("travel_port_pirie")
            .attachEvent("onChange", function (newv, oldv) {
                if (newv == 1) {
                    $$("bookings-page")
                        .$$("travel_create")
                        .$$("travel_mt_gambier")
                        .setValue(0);
                    $$("bookings-page")
                        .$$("travel_create")
                        .$$("travel_other")
                        .setValue(0);
                    $$("bookings-page").$$("travel_create").$$("route_from").setValue("");
                    $$("bookings-page").$$("travel_create").$$("route_to").setValue("");
                    let route_to = $$("bookings-page").$$("travel_create").$$("route_to");
                    let route_from = $$("bookings-page")
                        .$$("travel_create")
                        .$$("route_from");
                    let locations_data = [];
                    locations_data.push({id: "Res", value: "Residence"});
                    locations_data.push({id: 50, value: "Port Pirie"});
                    route_to.define("options", locations_data);
                    route_to.refresh();
                    route_from.define("options", locations_data);
                    route_from.refresh();
                    $$("bookings-page").$$("travel_create").$$("route_from").enable();
                    $$("bookings-page").$$("travel_create").$$("route_to").enable();
                    $$("bookings-page").$$("travel_create").$$("travel_type").enable();
                } else {
                    $$("bookings-page").$$("travel_create").$$("route_from").setValue("");
                    $$("bookings-page").$$("travel_create").$$("route_to").setValue("");
                    $$("bookings-page").$$("travel_create").$$("route_from").disable();
                    $$("bookings-page").$$("travel_create").$$("route_to").disable();
                    $$("bookings-page").$$("travel_create").$$("travel_type").disable();
                }
            });

        $$("bookings-page")
            .$$("travel_create")
            .$$("roster_filter")
            .attachEvent("onChange", function (newv, oldv) {
                load_shifts(newv);
            });
    }

    rosters_subject.subscribe(function (data) {
        let select = $$("bookings-page").$$("travel_create").$$("roster_filter");
        if (data) {
            let options = [];
            let roster_names = JSON.parse(data);
            options.push("-- All Rosters --");
            roster_names.forEach(function (value) {
                options.push(value.roster_name);
            });
            select.define("options", options);
            select.refresh();
        }
    });

    function load_shifts(rosterName) {
        if (rosterName == undefined) {
            rosterName = "Metro";
        }
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/schedule/shifts",
                {roster_name: rosterName},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        if (results) {
                            let shiftList = JSON.parse(results);
                            if (shiftList.data.length > 0) {
                                let shiftArray = shiftList.data[0].shifts.split(",");
                                let shiftOptions = [];
                                shiftArray.forEach(function (value) {
                                    shiftOptions.push(value);
                                });
                                shiftOptions.unshift("-- All Shifts --");
                                $$("bookings-page")
                                    .$$("travel_create")
                                    .$$("shift_filter")
                                    .define("options", shiftOptions);
                                $$("bookings-page")
                                    .$$("travel_create")
                                    .$$("shift_filter")
                                    .refresh();
                            }
                        }
                    },
                },
            );
    }

    function saveTravel() {
        const form = $$("bookings-page")
            .$$("travel_create")
            .$$("formTravel")
            .getValues();
        const employeeName = $$("bookings-page")
            .$$("travel_create")
            .$$("employees")
            .getText();
        const travelDate = moment(form.travel_date);
        const dateString = travelDate.format("YYYYMMDD");
        const daysDiff = travelDate.diff(moment(), "days");
        if (daysDiff > 0) {
            return webix.alert({
                text: "You can't create Travel Claims for future dates!",
                width: 500,
            });
        }
        checkExistingTravelClaim(
            form.employees,
            dateString,
            form.route_from,
            form.route_to,
            function (response) {
                const existingClaim = response.length > 0;
                const proceedIfValid = () => {
                    $$("loader-window").show();
                    if (form.route_from === form.route_to) {
                        $$("loader-window").hide();
                        return webix.alert({
                            text: "The 'Route From' and 'Route To' locations can't be the same!",
                            width: 500,
                        });
                    }
                    if (form.travel_port_pirie == 1 && form.travel_type === "") {
                        $$("loader-window").hide();
                        return webix.alert({
                            text: "With a 'Port Pirie' travel claim you must specify a Type!",
                            width: 500,
                        });
                    }
                    getRosterForTravel(form.employees, dateString, function (callback) {
                        const rosterInfo =
                            callback.length > 0
                                ? callback[0]
                                : {roster: "Metro", shift: "", location: ""};
                        const bookingId = formatUuid(getRandomValuesFunc());
                        const travelPayload = {
                            travel_id: bookingId,
                            pay_id: form.employees,
                            travel_date: travelDate.format("YYYY-MM-DD") + " 12:00:00",
                            route_from: form.route_from,
                            route_to: form.route_to,
                            date_string: dateString,
                            comments: form.comments,
                            created_by: user_logged_in,
                            linked_booking_id: "",
                            status: "Pending",
                            travel_with_ppe: form.travel_with_ppe,
                            prior_notice: form.prior_notice,
                            roster: rosterInfo.roster,
                            shift: rosterInfo.shift,
                            location: rosterInfo.location,
                            type: "Manual",
                            pp_travel_type: form.travel_type,
                            res_to_hs_distance: form.res_to_hs_dist,
                        };
                        webix
                            .ajax()
                            .headers({Authorization: "Bearer " + api_key})
                            .post(server_url + "/bookings/travel", travelPayload, {
                                error: function () {
                                    $$("loader-window").hide();
                                },
                                success: function () {
                                    webix.alert("Travel Entry Saved!");
                                    $$("loader-window").hide();
                                    clearTravelForm();
                                },
                            });
                    });
                };
                if (existingClaim) {
                    webix.confirm({
                        title: "Travel Claim Exists",
                        text: `There is already a Travel Claim entry for ${employeeName} for this travel date and route!</br></br>Would you like to proceed anyway?`,
                        width: 650,
                        type: "confirm-warning",
                        cancel: "No",
                        ok: "Yes",
                        callback: function (result) {
                            if (!result) return;
                            if (user_permission_level === 1) {
                                proceedIfValid();
                            } else {
                                webix.alert({
                                    text: "You don't have permission to perform this function!<br>Note: Level 1 users only!",
                                    width: 460,
                                });
                            }
                        },
                    });
                } else {
                    proceedIfValid();
                }
            },
        );
    }

    function clearTravelForm() {
        const travelCreate = $$("bookings-page").$$("travel_create");
        travelCreate.$$("formTravel").clear();
        travelCreate.$$("travel_date").setValue(new Date());
        travelCreate.$$("travel_confirm").setValue(0);
    }

    employees_subject.subscribe(function (data) {
        let select = $$("bookings-page").$$("travel_create").$$("employees");
        let select2 = $$("bookings-page")
            .$$("travel_create")
            .$$("travel_employee_filter");
        select.define("options", data);
        select.refresh();
        select2.define("options", data);
        select2.refresh();

        setUserFieldValues();

    });
    locations_subject.subscribe(function (data) {
        let results = JSON.parse(data);
        for (let x = 0; x < results.length; x++) {
            if (
                results[x].station_id <= 20 ||
                results[x].station_id > 70 ||
                (results[x].station_id > 50 && results[x].station_id < 70)
            ) {
            } else {
                locations_list.push({
                    id: results[x].station_id,
                    value: results[x].name,
                });
            }
        }
        locations_list.unshift({id: "20", value: "Adelaide"});
        locations_list.unshift({id: "Res", value: "Residence"});

        $$("bookings-page").$$("travel_create").$$("travel_rel_stat").define("options", locations_list);
        $$("bookings-page").$$("travel_create").$$("travel_rel_stat").refresh();
    });

    function checkExistingTravelClaim(
        pay_id,
        date_string,
        route_from,
        route_to,
        callback,
    ) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/bookings/check_existing_travel_claim",
                {
                    pay_id: pay_id,
                    date_string: date_string,
                    route_from: route_from,
                    route_to: route_to,
                },
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function getRosterForTravel(pay_id, date_stamp, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_employee_roster",
                {pay_id: pay_id, date_string: date_stamp},
                {
                    error: function (err) {
                        callback("");
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function getResToHSDistance(pay_id) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_res_to_hs_distance",
                {pay_id: pay_id},
                {
                    error: function (err) {
                        $$("bookings-page")
                            .$$("travel_create")
                            .$$("res_to_hs_dist")
                            .setValue("0");
                    },
                    success: function (results) {
                        let distance = JSON.parse(results);
                        $$("bookings-page")
                            .$$("travel_create")
                            .$$("res_to_hs_dist")
                            .setValue(distance[0].res_to_hs_kms);
                    },
                },
            );
    }

    function getTravelBookingsData() {
        $$("loader-window").show();
        let grid = $$("bookings-page")
            .$$("travel_create")
            .$$("travel_bookings_grid");
        let payId = $$("bookings-page")
            .$$("travel_create")
            .$$("travel_employee_filter")
            .getValue();
        let travel_date_filter = $$("bookings-page")
            .$$("travel_create")
            .$$("travel_date_filter")
            .getValue();
        let travel_roster_filter = $$("bookings-page")
            .$$("travel_create")
            .$$("roster_filter")
            .getValue();
        let travel_shift_filter = $$("bookings-page")
            .$$("travel_create")
            .$$("shift_filter")
            .getValue();
        let type_filter = $$("bookings-page")
            .$$("travel_create")
            .$$("type_filter")
            .getValue();
        let pay_id = 0;
        grid.clearAll();
        if (payId == "") {
            pay_id = 0;
        } else {
            pay_id = payId;
        }
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/admin/get_data_for_travel",
                {
                    pay_id: pay_id,
                    travel_date: travel_date_filter,
                    roster: travel_roster_filter,
                    shift: travel_shift_filter,
                    type_filter: type_filter,
                },
                {
                    error: function (err) {
                        $$("loader-window").hide();
                    },
                    success: function (results) {
                        if (results) {
                            let bookings = [];
                            let employeeName = "";
                            let records = JSON.parse(results);
                            records.forEach(function (values) {
                                if (values.middle_name == null) {
                                    employeeName = values.surname + ", " + values.first_name;
                                } else {
                                    employeeName =
                                        values.surname +
                                        ", " +
                                        values.first_name +
                                        " " +
                                        values.middle_name;
                                }
                                if (type_filter === "19RC") {
                                    if (
                                        moment(values.start_date, "DD/MM/YYYY HH:mm").isAfter(
                                            moment("2023-08-20 06:00"),
                                        )
                                    ) {
                                        if (values.from_station == values.to_station) {
                                        } else if (
                                            values.from_station == 19 &&
                                            (values.to_station == 20 ||
                                                values.to_station == 29 ||
                                                values.to_station == 100 ||
                                                values.to_station == "04")
                                        ) {
                                        } else if (
                                            values.from_station == 20 &&
                                            (values.to_station == 19 ||
                                                values.to_station == 29 ||
                                                values.to_station == 100 ||
                                                values.to_station == "04")
                                        ) {
                                        } else if (
                                            values.from_station == 29 &&
                                            (values.to_station == 19 ||
                                                values.to_station == 20 ||
                                                values.to_station == 100 ||
                                                values.to_station == "04")
                                        ) {
                                        } else if (
                                            values.from_station == "04" &&
                                            (values.to_station == 19 ||
                                                values.to_station == 20 ||
                                                values.to_station == 100 ||
                                                values.to_station == 29)
                                        ) {
                                        } else if (
                                            values.from_station == 100 &&
                                            (values.to_station == 19 ||
                                                values.to_station == 20 ||
                                                values.to_station == 29 ||
                                                values.to_station == "04")
                                        ) {
                                        } else if (
                                            values.from_station == 32 &&
                                            values.to_station == 39
                                        ) {
                                        } else if (
                                            values.from_station == 39 &&
                                            values.to_station == 32
                                        ) {
                                        } else if (
                                            values.from_station == 46 &&
                                            values.to_station == 49
                                        ) {
                                        } else if (
                                            values.from_station == 49 &&
                                            values.to_station == 46
                                        ) {
                                        } else if (
                                            values.from_station == 25 &&
                                            values.to_station == 27
                                        ) {
                                        } else if (
                                            values.from_station == 27 &&
                                            values.to_station == 25
                                        ) {
                                        } else if (
                                            values.leave_type_code == "OD" ||
                                            values.leave_type_code == "OC" ||
                                            values.leave_type_code == "Depl"
                                        ) {
                                        } else if (values.roster == "Day Work") {
                                        } else if (
                                            values.leave_type_code == "RC" &&
                                            values.from_station == values.home_station_id
                                        ) {
                                        } else if (
                                            values.from_station == "00" ||
                                            values.to_station == "00"
                                        ) {
                                        } else if (
                                            values.leave_type_code == "RC" &&
                                            values.home_station_id == 19
                                        ) {
                                            bookings.push({
                                                booking_id: values.booking_id,
                                                pay_id: values.pay_id,
                                                employee: employeeName,
                                                start_date: values.start_date,
                                                end_date: values.end_date,
                                                leave_type_code: values.leave_type_code,
                                                leave_type_description: values.leave_type_description,
                                                roster: values.roster,
                                                shift: values.shift,
                                                location: values.location,
                                                date_string: values.date_string,
                                                hours: values.hours,
                                                booking_type: values.booking_type,
                                                from_station: values.from_station,
                                                to_station: values.to_station,
                                                sm_travel_notice: values.sm_travel_notice,
                                                sm_travel_ppe: values.sm_travel_ppe,
                                                period: values.bk_period,
                                                comments: values.comments,
                                                home_station_id: values.home_station_id,
                                                res_to_hs_kms: values.res_to_hs_kms,
                                            });
                                        }
                                    }
                                } else {
                                    if (values.from_station == values.to_station) {
                                    } else if (
                                        values.from_station == 19 &&
                                        (values.to_station == 20 ||
                                            values.to_station == 29 ||
                                            values.to_station == 100 ||
                                            values.to_station == "04")
                                    ) {
                                    } else if (
                                        values.from_station == 20 &&
                                        (values.to_station == 19 ||
                                            values.to_station == 29 ||
                                            values.to_station == 100 ||
                                            values.to_station == "04")
                                    ) {
                                    } else if (
                                        values.from_station == 29 &&
                                        (values.to_station == 19 ||
                                            values.to_station == 20 ||
                                            values.to_station == 100 ||
                                            values.to_station == "04")
                                    ) {
                                    } else if (
                                        values.from_station == "04" &&
                                        (values.to_station == 19 ||
                                            values.to_station == 20 ||
                                            values.to_station == 100 ||
                                            values.to_station == 29)
                                    ) {
                                    } else if (
                                        values.from_station == 100 &&
                                        (values.to_station == 19 ||
                                            values.to_station == 20 ||
                                            values.to_station == 29 ||
                                            values.to_station == "04")
                                    ) {
                                    } else if (
                                        values.from_station == 32 &&
                                        values.to_station == 39
                                    ) {
                                    } else if (
                                        values.from_station == 39 &&
                                        values.to_station == 32
                                    ) {
                                    } else if (
                                        values.from_station == 46 &&
                                        values.to_station == 49
                                    ) {
                                    } else if (
                                        values.from_station == 49 &&
                                        values.to_station == 46
                                    ) {
                                    } else if (
                                        values.from_station == 25 &&
                                        values.to_station == 27
                                    ) {
                                    } else if (
                                        values.from_station == 27 &&
                                        values.to_station == 25
                                    ) {
                                    } else if (
                                        values.leave_type_code == "OD" ||
                                        values.leave_type_code == "OC" ||
                                        values.leave_type_code == "Depl"
                                    ) {
                                    } else if (values.roster == "Day Work") {
                                    } else if (
                                        values.leave_type_code == "RC" &&
                                        values.from_station == values.home_station_id
                                    ) {
                                    } else if (
                                        values.from_station == "00" ||
                                        values.to_station == "00"
                                    ) {
                                    } else if (
                                        values.leave_type_code == "RC" &&
                                        values.home_station_id == 19
                                    ) {
                                    } else {
                                        bookings.push({
                                            booking_id: values.booking_id,
                                            pay_id: values.pay_id,
                                            employee: employeeName,
                                            start_date: values.start_date,
                                            end_date: values.end_date,
                                            leave_type_code: values.leave_type_code,
                                            leave_type_description: values.leave_type_description,
                                            roster: values.roster,
                                            shift: values.shift,
                                            location: values.location,
                                            date_string: values.date_string,
                                            hours: values.hours,
                                            booking_type: values.booking_type,
                                            from_station: values.from_station,
                                            to_station: values.to_station,
                                            sm_travel_notice: values.sm_travel_notice,
                                            sm_travel_ppe: values.sm_travel_ppe,
                                            period: values.bk_period,
                                            comments: values.comments,
                                            home_station_id: values.home_station_id,
                                            res_to_hs_kms: values.res_to_hs_kms,
                                        });
                                    }
                                }
                            });
                            grid.define("data", bookings);
                            grid.refresh();
                            $$("bookings-page")
                                .$$("travel_create")
                                .$$("records_count")
                                .define(
                                    "template",
                                    grid.count() + " staff movement records found!",
                                );
                            $$("bookings-page")
                                .$$("travel_create")
                                .$$("records_count")
                                .refresh();
                        }
                        $$("loader-window").hide();
                    },
                },
            );
    }

    function getLastWorkStation(
        pay_id,
        date_stamp,
        incToday,
        only1Day,
        callback,
    ) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_past_work_locations",
                {
                    pay_id: pay_id,
                    date_string: date_stamp,
                    incToday: incToday,
                    only1Day: only1Day,
                },
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function getPastDayShiftInfo(pay_id, date_stamp, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_past_day_shift_info",
                {pay_id: pay_id, date_string: date_stamp},
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function createTravelLogs() {
        let grid = $$("bookings-page")
            .$$("travel_create")
            .$$("travel_bookings_grid");
        let total_entries = grid.count();
        if (total_entries > 0) {
            webix.confirm({
                title: "Confirm Processing Travel Bookings",
                text:
                    total_entries +
                    " bookings were found that qualify for a Travel Claim</br>Select 'Process' to generate Travel Logs",
                ok: "Process",
                cancel: "Cancel",
                width: 550,
                callback: function (result) {
                    if (result == true) {
                        $$("loader-window").show();
                        webix.message({
                            id: "travel_counter",
                            text:
                                "Processing " + total_entries + " staff movement entries...",
                            type: "debug",
                            expire: -1,
                        });
                        setTimeout(function () {
                            let travel_array = [];
                            let travel_object = {};
                            let travel_object_to_res = {};
                            let travel_object_from_res = {};
                            let travel_object_to_next_rs = {};
                            let travel_object_from_last_rs = {};
                            let travel_object_res_to_next_rs = {};
                            let travel_object_rs_to_next_rs = {};
                            let travel_object_res_to_hs = {};
                            let travel_object_20_to_res = {};
                            let sm_id_array = [];
                            let array_count = 0;
                            let last_row_id = grid.getLastId();
                            let station_to_station = false;
                            let last_to_station = "";
                            let last_claim_rs_to_rs = false;
                            let changeFromRes = false;
                            grid.eachRow(function (row) {
                                let record = grid.getItem(row);
                                let next_row = grid.getNextId(row, 1);
                                let next_record = grid.getItem(next_row);
                                let travel_id = formatUuid(getRandomValuesFunc());
                                let with_ppe = record.sm_travel_ppe;
                                let prior_notice = record.sm_travel_notice;
                                let res_to_hs_kms = record.res_to_hs_kms;
                                let route_from = record.from_station;
                                let route_to = record.to_station;
                                let pay_id = record.pay_id;
                                let travel_date_string = record.date_string;
                                let travel_date = record.start_date;
                                let linked_booking_id = record.booking_id;
                                let roster = record.roster;
                                let shift = record.shift;
                                let location = record.location;
                                let home_station_id = record.home_station_id;
                                let sm_period = record.period;
                                let reliever_to_rs = false;
                                let last_rs_different = false;
                                let skip_rest_claims = false;
                                let no_res_to_rs_required = false;
                                let res_to_rs_with_prior = false;
                                let res_back_to_hs_required = false;
                                let otr_to_res_required = false;
                                let otr_skip_travel = false;
                                travel_object = {};
                                travel_object_to_res = {};
                                travel_object_from_res = {};
                                travel_object_to_next_rs = {};
                                travel_object_from_last_rs = {};
                                travel_object_res_to_next_rs = {};
                                travel_object_rs_to_next_rs = {};
                                travel_object_res_to_hs = {};
                                travel_object_20_to_res = {};
                                if (record.leave_type_code !== "RC") {
                                    if (travel_array.length > 0) {
                                        if (
                                            travel_array[travel_array.length - 1].pay_id != pay_id
                                        ) {
                                            last_claim_rs_to_rs = false;
                                        }
                                    }
                                    if (station_to_station === false) {
                                        if (route_from == 19) {
                                            reliever_to_rs = true;
                                        } else if (route_from == home_station_id) {
                                            if (prior_notice === false) {
                                                no_res_to_rs_required = true;
                                            } else {
                                                res_to_rs_with_prior = true;
                                            }
                                        }
                                        if (no_res_to_rs_required === false) {
                                            if (last_claim_rs_to_rs === false) {
                                                getLastWorkStation(
                                                    pay_id,
                                                    travel_date_string,
                                                    false,
                                                    false,
                                                    function (results) {
                                                        if (results.length > 0) {
                                                            for (let x = 0; x < results.length; x++) {
                                                                if (
                                                                    results[x].booking_type == "staff_movement"
                                                                ) {
                                                                    if (
                                                                        results[x].leave_type_code != "OC" &&
                                                                        results[x].leave_type_code != "OD" &&
                                                                        results[x].leave_type_code != "Depl"
                                                                    ) {
                                                                        if (
                                                                            results[x].with_standby === false &&
                                                                            results[x].with_recall === false
                                                                        ) {
                                                                            if (
                                                                                results[x].leave_type_code == "OTRRN"
                                                                            ) {
                                                                                route_to = "103";
                                                                            } else if (
                                                                                results[x].leave_type_code == "OTRRS"
                                                                            ) {
                                                                                route_to = "104";
                                                                            } else if (
                                                                                results[x].leave_type_code == "OTRRC"
                                                                            ) {
                                                                                route_to = "100";
                                                                            } else {
                                                                                route_to = results[
                                                                                    x
                                                                                    ].leave_type_code.slice(0, -1);
                                                                            }
                                                                            if (route_to == record.to_station) {
                                                                                with_ppe = false;
                                                                            } else {
                                                                                with_ppe = true;
                                                                                last_rs_different = true;
                                                                            }
                                                                            break;
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                );
                                            } else {
                                                if (last_to_station == record.to_station) {
                                                    with_ppe = false;
                                                } else {
                                                    with_ppe = true;
                                                }
                                                last_claim_rs_to_rs = false;
                                            }
                                            route_to = record.to_station;
                                            if (route_to == "OTR") {
                                                route_to = "100";
                                            } else if (route_to == "OTRC") {
                                                route_to = "100";
                                            } else if (route_to == "OTRS") {
                                                route_to = "104";
                                            } else if (route_to == "OTRN") {
                                                route_to = "103";
                                            }
                                            if (travel_array.length > 0) {
                                                if (
                                                    travel_array[travel_array.length - 1].pay_id == pay_id
                                                ) {
                                                    if (
                                                        travel_array[travel_array.length - 1].equipment ===
                                                        true
                                                    ) {
                                                        with_ppe = true;
                                                    } else {
                                                        with_ppe = false;
                                                    }
                                                }
                                            }
                                            if (
                                                route_from == 100 &&
                                                route_to != 100 &&
                                                prior_notice == true
                                            ) {
                                                let otr_travel_date_string = "";
                                                let otr_travel_date = "";
                                                getLastWorkStation(
                                                    pay_id,
                                                    travel_date_string,
                                                    false,
                                                    false,
                                                    function (results) {
                                                        if (results.length > 0) {
                                                            for (let x = 0; x < results.length; x++) {
                                                                if (
                                                                    results[x].date_string ==
                                                                    travel_date_string - 1
                                                                ) {
                                                                    if (
                                                                        results[x].booking_type == "staff_movement"
                                                                    ) {
                                                                        if (
                                                                            results[x].leave_type_code != "OC" &&
                                                                            results[x].leave_type_code != "OD" &&
                                                                            results[x].leave_type_code != "Depl"
                                                                        ) {
                                                                            if (
                                                                                results[x].with_standby === false &&
                                                                                results[x].with_recall === false
                                                                            ) {
                                                                                otr_travel_date_string =
                                                                                    results[x].date_string;
                                                                                otr_travel_date = moment(
                                                                                    results[x].start_date,
                                                                                    "DD/MM/YYYY HH:mm",
                                                                                ).format("DD/MM/YYYY 12:00");
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                } else if (
                                                                    results[x].date_string ==
                                                                    travel_date_string - 2
                                                                ) {
                                                                    if (
                                                                        results[x].booking_type == "staff_movement"
                                                                    ) {
                                                                        if (
                                                                            results[x].leave_type_code != "OC" &&
                                                                            results[x].leave_type_code != "OD" &&
                                                                            results[x].leave_type_code != "Depl"
                                                                        ) {
                                                                            if (
                                                                                results[x].with_standby === false &&
                                                                                results[x].with_recall === false
                                                                            ) {
                                                                                otr_travel_date_string =
                                                                                    results[x].date_string;
                                                                                otr_travel_date = moment(
                                                                                    results[x].start_date,
                                                                                    "DD/MM/YYYY HH:mm",
                                                                                ).format("DD/MM/YYYY 12:00");
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                } else if (
                                                                    results[x].date_string ==
                                                                    travel_date_string - 3
                                                                ) {
                                                                    if (
                                                                        results[x].booking_type == "staff_movement"
                                                                    ) {
                                                                        if (
                                                                            results[x].leave_type_code != "OC" &&
                                                                            results[x].leave_type_code != "OD" &&
                                                                            results[x].leave_type_code != "Depl"
                                                                        ) {
                                                                            if (
                                                                                results[x].with_standby === false &&
                                                                                results[x].with_recall === false
                                                                            ) {
                                                                                otr_travel_date_string =
                                                                                    results[x].date_string;
                                                                                otr_travel_date = moment(
                                                                                    results[x].start_date,
                                                                                    "DD/MM/YYYY HH:mm",
                                                                                ).format("DD/MM/YYYY 12:00");
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                } else if (
                                                                    results[x].date_string ==
                                                                    travel_date_string - 4
                                                                ) {
                                                                    if (
                                                                        results[x].booking_type == "staff_movement"
                                                                    ) {
                                                                        if (
                                                                            results[x].leave_type_code != "OC" &&
                                                                            results[x].leave_type_code != "OD" &&
                                                                            results[x].leave_type_code != "Depl"
                                                                        ) {
                                                                            if (
                                                                                results[x].with_standby === false &&
                                                                                results[x].with_recall === false
                                                                            ) {
                                                                                otr_travel_date_string =
                                                                                    results[x].date_string;
                                                                                otr_travel_date = moment(
                                                                                    results[x].start_date,
                                                                                    "DD/MM/YYYY HH:mm",
                                                                                ).format("DD/MM/YYYY 12:00");
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                } else {
                                                                    getPastDayShiftInfo(
                                                                        pay_id,
                                                                        travel_date_string,
                                                                        function (results) {
                                                                            if (results.length > 0) {
                                                                                for (
                                                                                    let x = 0;
                                                                                    x < results.length;
                                                                                    x++
                                                                                ) {
                                                                                    if (results[x].shift_type != "off") {
                                                                                        otr_travel_date_string =
                                                                                            results[x].date_string;
                                                                                        otr_travel_date =
                                                                                            results[x].roster_date;
                                                                                        otr_to_res_required = true;
                                                                                        break;
                                                                                    }
                                                                                }
                                                                            }
                                                                        },
                                                                    );
                                                                    break;
                                                                }
                                                            }
                                                        } else {
                                                            getPastDayShiftInfo(
                                                                pay_id,
                                                                travel_date_string,
                                                                function (results) {
                                                                    if (results.length > 0) {
                                                                        for (let x = 0; x < results.length; x++) {
                                                                            if (results[x].shift_type != "off") {
                                                                                otr_travel_date_string =
                                                                                    results[x].date_string;
                                                                                otr_travel_date =
                                                                                    results[x].roster_date;
                                                                                otr_to_res_required = true;
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                },
                                                            );
                                                        }
                                                    },
                                                );
                                                if (otr_to_res_required === true) {
                                                    travel_object_20_to_res.travel_id = travel_id;
                                                    travel_object_20_to_res.pay_id = pay_id;
                                                    travel_object_20_to_res.date_string =
                                                        otr_travel_date_string;
                                                    travel_object_20_to_res.travel_date = otr_travel_date;
                                                    travel_object_20_to_res.route_from = "20";
                                                    travel_object_20_to_res.route_to = "Res";
                                                    travel_object_20_to_res.home_station =
                                                        home_station_id;
                                                    travel_object_20_to_res.created_by = user_logged_in;
                                                    travel_object_20_to_res.equipment = with_ppe;
                                                    travel_object_20_to_res.notice = prior_notice;
                                                    travel_object_20_to_res.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_20_to_res.roster = roster;
                                                    travel_object_20_to_res.shift = shift;
                                                    travel_object_20_to_res.location = location;
                                                    travel_object_20_to_res.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object_20_to_res.type = "Automatic";
                                                    travel_object_20_to_res.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object_20_to_res.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_20_to_res);
                                                    array_count += 1;
                                                }
                                            }
                                            if (changeFromRes === true) {
                                                travel_object_res_to_next_rs.route_from = route_from;
                                                changeFromRes = false;
                                            } else {
                                                travel_object_res_to_next_rs.route_from = "Res";
                                            }
                                            travel_object_res_to_next_rs.travel_id = travel_id;
                                            travel_object_res_to_next_rs.pay_id = pay_id;
                                            travel_object_res_to_next_rs.date_string =
                                                travel_date_string;
                                            travel_object_res_to_next_rs.travel_date = travel_date;
                                            travel_object_res_to_next_rs.route_to = route_to;
                                            travel_object_res_to_next_rs.home_station =
                                                home_station_id;
                                            travel_object_res_to_next_rs.created_by = user_logged_in;
                                            travel_object_res_to_next_rs.equipment = with_ppe;
                                            travel_object_res_to_next_rs.notice = prior_notice;
                                            travel_object_res_to_next_rs.linked_booking_id =
                                                linked_booking_id;
                                            travel_object_res_to_next_rs.roster = roster;
                                            travel_object_res_to_next_rs.shift = shift;
                                            travel_object_res_to_next_rs.location = location;
                                            travel_object_res_to_next_rs.created_date = moment().add(
                                                array_count,
                                                "milliseconds",
                                            );
                                            travel_object_res_to_next_rs.type = "Automatic";
                                            travel_object_res_to_next_rs.res_to_hs_kms =
                                                res_to_hs_kms;
                                            travel_object_res_to_next_rs.movement_type =
                                                record.leave_type_code;
                                            travel_array.push(travel_object_res_to_next_rs);
                                            array_count += 1;
                                        }
                                    } else {
                                        last_claim_rs_to_rs = true;
                                        last_to_station = route_to;
                                    }
                                    station_to_station = false;
                                    if (last_row_id != row) {
                                        if (
                                            next_record.date_string == record.date_string &&
                                            next_record.pay_id == record.pay_id
                                        ) {
                                            skip_rest_claims = true;
                                            station_to_station = true;
                                        }
                                    }
                                    if (skip_rest_claims === false) {
                                        with_ppe = record.sm_travel_ppe;
                                        if (reliever_to_rs === false) {
                                            if (res_to_rs_with_prior === false) {
                                                travel_object.travel_id = travel_id;
                                                travel_object.pay_id = pay_id;
                                                travel_object.date_string = travel_date_string;
                                                travel_object.travel_date = travel_date;
                                                travel_object.route_from = route_from;
                                                travel_object.route_to = route_to;
                                                travel_object.home_station = home_station_id;
                                                travel_object.created_by = user_logged_in;
                                                travel_object.equipment = with_ppe;
                                                travel_object.notice = prior_notice;
                                                travel_object.linked_booking_id = linked_booking_id;
                                                travel_object.roster = roster;
                                                travel_object.shift = shift;
                                                travel_object.location = location;
                                                travel_object.created_date = moment().add(
                                                    array_count,
                                                    "milliseconds",
                                                );
                                                travel_object.type = "Automatic";
                                                travel_object.res_to_hs_kms = res_to_hs_kms;
                                                travel_object.movement_type = record.leave_type_code;
                                                travel_array.push(travel_object);
                                                array_count += 1;
                                            }
                                        }
                                        if (record.to_station != home_station_id) {
                                            getNextWorkDay(
                                                pay_id,
                                                travel_date_string,
                                                function (results) {
                                                    if (results.length > 0) {
                                                        for (let x = 0; x < results.length; x++) {
                                                            if (results[x].booking_type == "staff_movement") {
                                                                if (
                                                                    results[x].leave_type_code != "OC" &&
                                                                    results[x].leave_type_code != "OD" &&
                                                                    results[x].leave_type_code != "Depl"
                                                                ) {
                                                                    if (
                                                                        results[x].with_standby === false &&
                                                                        results[x].with_recall === false
                                                                    ) {
                                                                        if (results[x].leave_type_code == "OTRRN") {
                                                                            route_to = "103";
                                                                        } else if (
                                                                            results[x].leave_type_code == "OTRRS"
                                                                        ) {
                                                                            route_to = "104";
                                                                        } else if (
                                                                            results[x].leave_type_code == "OTRRC"
                                                                        ) {
                                                                            route_to = "100";
                                                                        } else {
                                                                            route_to = results[
                                                                                x
                                                                                ].leave_type_code.slice(0, -1);
                                                                        }
                                                                        if (route_to == record.to_station) {
                                                                            with_ppe = false;
                                                                        } else {
                                                                            with_ppe = true;
                                                                        }
                                                                        res_back_to_hs_required = false;
                                                                        break;
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        route_to = home_station_id;
                                                        with_ppe = true;
                                                        res_back_to_hs_required = true;
                                                    }
                                                },
                                            );
                                            if (sm_period == "night") {
                                                travel_date_string = record.date_string + 1;
                                                travel_date = moment(
                                                    record.start_date,
                                                    "DD/MM/YYYY HH:mm",
                                                ).add(1, "days");
                                                travel_date =
                                                    moment(travel_date).format("DD/MM/YYYY HH:mm");
                                            }
                                            route_to = record.to_station;
                                            if (
                                                home_station_id == 100 &&
                                                route_to == 20 &&
                                                travel_array[array_count - 1].route_to == 20
                                            ) {
                                                otr_skip_travel = true;
                                            }
                                            if (otr_skip_travel === false) {
                                                if (
                                                    home_station_id == 19 &&
                                                    route_to == 20 &&
                                                    with_ppe == false &&
                                                    prior_notice == false
                                                ) {
                                                    changeFromRes = true;
                                                } else {
                                                    travel_object_to_res.travel_id = travel_id;
                                                    travel_object_to_res.pay_id = pay_id;
                                                    travel_object_to_res.date_string = travel_date_string;
                                                    travel_object_to_res.travel_date = travel_date;
                                                    travel_object_to_res.route_from = route_to;
                                                    travel_object_to_res.route_to = "Res";
                                                    travel_object_to_res.home_station = home_station_id;
                                                    travel_object_to_res.created_by = user_logged_in;
                                                    travel_object_to_res.equipment = with_ppe;
                                                    travel_object_to_res.notice = prior_notice;
                                                    travel_object_to_res.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_to_res.roster = roster;
                                                    travel_object_to_res.shift = shift;
                                                    travel_object_to_res.location = location;
                                                    travel_object_to_res.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object_to_res.type = "Automatic";
                                                    travel_object_to_res.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object_to_res.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_to_res);
                                                    array_count += 1;
                                                }
                                            }
                                        }
                                    }
                                    if (otr_skip_travel === false) {
                                        if (record.to_station != home_station_id) {
                                            if (res_back_to_hs_required === false) {
                                                if (
                                                    no_res_to_rs_required === true &&
                                                    prior_notice === false
                                                ) {
                                                    travel_object_res_to_next_rs.travel_id = travel_id;
                                                    travel_object_res_to_next_rs.pay_id = pay_id;
                                                    travel_object_res_to_next_rs.date_string =
                                                        travel_date_string;
                                                    travel_object_res_to_next_rs.travel_date =
                                                        travel_date;
                                                    travel_object_res_to_next_rs.route_from = "Res";
                                                    travel_object_res_to_next_rs.route_to =
                                                        home_station_id;
                                                    travel_object_res_to_next_rs.home_station =
                                                        home_station_id;
                                                    travel_object_res_to_next_rs.created_by =
                                                        user_logged_in;
                                                    travel_object_res_to_next_rs.equipment = with_ppe;
                                                    travel_object_res_to_next_rs.notice = prior_notice;
                                                    travel_object_res_to_next_rs.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_res_to_next_rs.roster = roster;
                                                    travel_object_res_to_next_rs.shift = shift;
                                                    travel_object_res_to_next_rs.location = location;
                                                    travel_object_res_to_next_rs.created_date =
                                                        moment().add(array_count, "milliseconds");
                                                    travel_object_res_to_next_rs.type = "Automatic";
                                                    travel_object_res_to_next_rs.res_to_hs_kms =
                                                        res_to_hs_kms;
                                                    travel_object_res_to_next_rs.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_res_to_next_rs);
                                                    array_count += 1;
                                                }
                                            }
                                        }
                                    }
                                    if (record.to_station != home_station_id) {
                                        if (
                                            res_back_to_hs_required === true ||
                                            home_station_id == 100
                                        ) {
                                            let no_upcoming_ra = false;
                                            getNextShiftDate(
                                                pay_id,
                                                travel_date_string,
                                                function (values) {
                                                    if (values.length > 0) {
                                                        for (let x = 0; x < values.length; x++) {
                                                            if (
                                                                values[x].booking_type == "sick_leave" ||
                                                                values[x].booking_type == "leave_request"
                                                            ) {
                                                            } else if (values[x].booking_type == null) {
                                                                travel_date_string = values[x].date_string;
                                                                travel_date = values[x].roster_date;
                                                                route_to = "20";
                                                                break;
                                                            } else if (
                                                                values[x].booking_type == "staff_movement" &&
                                                                values[x].leave_type_code != "OC" &&
                                                                values[x].leave_type_code != "OD" &&
                                                                values[x].leave_type_code != "Depl"
                                                            ) {
                                                                travel_date_string = values[x].date_string;
                                                                travel_date = values[x].roster_date;
                                                                if (values[x].leave_type_code == "OTRRN") {
                                                                    route_to = "103";
                                                                } else if (
                                                                    values[x].leave_type_code == "OTRRS"
                                                                ) {
                                                                    route_to = "104";
                                                                } else if (
                                                                    values[x].leave_type_code == "OTRRC"
                                                                ) {
                                                                    route_to = "100";
                                                                } else {
                                                                    route_to = values[x].leave_type_code.slice(
                                                                        0,
                                                                        -1,
                                                                    );
                                                                }
                                                                break;
                                                            }
                                                        }
                                                    } else {
                                                        no_upcoming_ra = true;
                                                    }
                                                },
                                            );
                                            if (no_upcoming_ra === false) {
                                                if (record.roster.includes("OTR")) {
                                                    if (route_to == "OTR") {
                                                        route_to = "100";
                                                    } else if (route_to == "OTRC") {
                                                        route_to = "100";
                                                    } else if (route_to == "OTRS") {
                                                        route_to = "104";
                                                    } else if (route_to == "OTRN") {
                                                        route_to = "103";
                                                    }
                                                    travel_object_res_to_hs.travel_id = travel_id;
                                                    travel_object_res_to_hs.pay_id = pay_id;
                                                    travel_object_res_to_hs.date_string =
                                                        travel_date_string;
                                                    travel_object_res_to_hs.travel_date = travel_date;
                                                    travel_object_res_to_hs.route_from = "Res";
                                                    travel_object_res_to_hs.route_to = route_to;
                                                    travel_object_res_to_hs.home_station =
                                                        home_station_id;
                                                    travel_object_res_to_hs.created_by = user_logged_in;
                                                    travel_object_res_to_hs.equipment = with_ppe;
                                                    travel_object_res_to_hs.notice = prior_notice;
                                                    travel_object_res_to_hs.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_res_to_hs.roster = roster;
                                                    travel_object_res_to_hs.shift = shift;
                                                    travel_object_res_to_hs.location = location;
                                                    travel_object_res_to_hs.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object_res_to_hs.type = "Automatic";
                                                    travel_object_res_to_hs.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object_res_to_hs.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_res_to_hs);
                                                    array_count += 1;
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    route_to = record.from_station;
                                    route_from = record.to_station;
                                    record.from_station = route_from;
                                    record.to_station = route_to;
                                    let back_to_hs = false;
                                    if (home_station_id != "19") {
                                        if (station_to_station === false) {
                                            if (route_from == 19) {
                                                reliever_to_rs = true;
                                            } else if (route_from == home_station_id) {
                                                if (prior_notice === false) {
                                                    no_res_to_rs_required = true;
                                                } else {
                                                    res_to_rs_with_prior = true;
                                                }
                                            }
                                            if (no_res_to_rs_required === false) {
                                                if (last_claim_rs_to_rs === false) {
                                                    getLastWorkStation(
                                                        pay_id,
                                                        travel_date_string,
                                                        false,
                                                        false,
                                                        function (results) {
                                                            if (results.length > 0) {
                                                                for (let x = 0; x < results.length; x++) {
                                                                    if (
                                                                        results[x].booking_type == "staff_movement"
                                                                    ) {
                                                                        if (
                                                                            results[x].leave_type_code != "OC" &&
                                                                            results[x].leave_type_code != "OD" &&
                                                                            results[x].leave_type_code != "Depl"
                                                                        ) {
                                                                            if (
                                                                                results[x].with_standby === false &&
                                                                                results[x].with_recall === false
                                                                            ) {
                                                                                if (
                                                                                    results[x].leave_type_code == "OTRRN"
                                                                                ) {
                                                                                    route_to = "103";
                                                                                } else if (
                                                                                    results[x].leave_type_code == "OTRRS"
                                                                                ) {
                                                                                    route_to = "104";
                                                                                } else if (
                                                                                    results[x].leave_type_code == "OTRRC"
                                                                                ) {
                                                                                    route_to = "100";
                                                                                } else {
                                                                                    route_to = results[
                                                                                        x
                                                                                        ].leave_type_code.slice(0, -1);
                                                                                }
                                                                                if (route_to == record.to_station) {
                                                                                    with_ppe = false;
                                                                                } else {
                                                                                    with_ppe = true;
                                                                                    last_rs_different = true;
                                                                                }
                                                                                break;
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        },
                                                    );
                                                } else {
                                                    if (last_to_station == record.to_station) {
                                                        with_ppe = false;
                                                    } else {
                                                        with_ppe = true;
                                                    }
                                                    last_claim_rs_to_rs = false;
                                                }
                                                route_to = record.to_station;
                                                if (route_to == "OTR") {
                                                    route_to = "100";
                                                } else if (route_to == "OTRC") {
                                                    route_to = "100";
                                                } else if (route_to == "OTRS") {
                                                    route_to = "104";
                                                } else if (route_to == "OTRN") {
                                                    route_to = "103";
                                                }
                                                if (travel_array.length > 0) {
                                                    if (
                                                        travel_array[travel_array.length - 1].pay_id ==
                                                        pay_id
                                                    ) {
                                                        if (
                                                            travel_array[travel_array.length - 1]
                                                                .equipment === true
                                                        ) {
                                                            with_ppe = true;
                                                        } else {
                                                            with_ppe = false;
                                                        }
                                                    }
                                                }
                                                if (
                                                    route_from == 100 &&
                                                    route_to != 100 &&
                                                    prior_notice == true
                                                ) {
                                                    let otr_travel_date_string = "";
                                                    let otr_travel_date = "";
                                                    getLastWorkStation(
                                                        pay_id,
                                                        travel_date_string,
                                                        false,
                                                        false,
                                                        function (results) {
                                                            if (results.length > 0) {
                                                                for (let x = 0; x < results.length; x++) {
                                                                    if (
                                                                        results[x].date_string ==
                                                                        travel_date_string - 1
                                                                    ) {
                                                                        if (
                                                                            results[x].booking_type ==
                                                                            "staff_movement"
                                                                        ) {
                                                                            if (
                                                                                results[x].leave_type_code != "OC" &&
                                                                                results[x].leave_type_code != "OD" &&
                                                                                results[x].leave_type_code != "Depl"
                                                                            ) {
                                                                                if (
                                                                                    results[x].with_standby === false &&
                                                                                    results[x].with_recall === false
                                                                                ) {
                                                                                    otr_travel_date_string =
                                                                                        results[x].date_string;
                                                                                    otr_travel_date = moment(
                                                                                        results[x].start_date,
                                                                                        "DD/MM/YYYY HH:mm",
                                                                                    ).format("DD/MM/YYYY 12:00");
                                                                                    break;
                                                                                }
                                                                            }
                                                                        }
                                                                    } else if (
                                                                        results[x].date_string ==
                                                                        travel_date_string - 2
                                                                    ) {
                                                                        if (
                                                                            results[x].booking_type ==
                                                                            "staff_movement"
                                                                        ) {
                                                                            if (
                                                                                results[x].leave_type_code != "OC" &&
                                                                                results[x].leave_type_code != "OD" &&
                                                                                results[x].leave_type_code != "Depl"
                                                                            ) {
                                                                                if (
                                                                                    results[x].with_standby === false &&
                                                                                    results[x].with_recall === false
                                                                                ) {
                                                                                    otr_travel_date_string =
                                                                                        results[x].date_string;
                                                                                    otr_travel_date = moment(
                                                                                        results[x].start_date,
                                                                                        "DD/MM/YYYY HH:mm",
                                                                                    ).format("DD/MM/YYYY 12:00");
                                                                                    break;
                                                                                }
                                                                            }
                                                                        }
                                                                    } else if (
                                                                        results[x].date_string ==
                                                                        travel_date_string - 3
                                                                    ) {
                                                                        if (
                                                                            results[x].booking_type ==
                                                                            "staff_movement"
                                                                        ) {
                                                                            if (
                                                                                results[x].leave_type_code != "OC" &&
                                                                                results[x].leave_type_code != "OD" &&
                                                                                results[x].leave_type_code != "Depl"
                                                                            ) {
                                                                                if (
                                                                                    results[x].with_standby === false &&
                                                                                    results[x].with_recall === false
                                                                                ) {
                                                                                    otr_travel_date_string =
                                                                                        results[x].date_string;
                                                                                    otr_travel_date = moment(
                                                                                        results[x].start_date,
                                                                                        "DD/MM/YYYY HH:mm",
                                                                                    ).format("DD/MM/YYYY 12:00");
                                                                                    break;
                                                                                }
                                                                            }
                                                                        }
                                                                    } else if (
                                                                        results[x].date_string ==
                                                                        travel_date_string - 4
                                                                    ) {
                                                                        if (
                                                                            results[x].booking_type ==
                                                                            "staff_movement"
                                                                        ) {
                                                                            if (
                                                                                results[x].leave_type_code != "OC" &&
                                                                                results[x].leave_type_code != "OD" &&
                                                                                results[x].leave_type_code != "Depl"
                                                                            ) {
                                                                                if (
                                                                                    results[x].with_standby === false &&
                                                                                    results[x].with_recall === false
                                                                                ) {
                                                                                    otr_travel_date_string =
                                                                                        results[x].date_string;
                                                                                    otr_travel_date = moment(
                                                                                        results[x].start_date,
                                                                                        "DD/MM/YYYY HH:mm",
                                                                                    ).format("DD/MM/YYYY 12:00");
                                                                                    break;
                                                                                }
                                                                            }
                                                                        }
                                                                    } else {
                                                                        getPastDayShiftInfo(
                                                                            pay_id,
                                                                            travel_date_string,
                                                                            function (results) {
                                                                                if (results.length > 0) {
                                                                                    for (
                                                                                        let x = 0;
                                                                                        x < results.length;
                                                                                        x++
                                                                                    ) {
                                                                                        if (
                                                                                            results[x].shift_type != "off"
                                                                                        ) {
                                                                                            otr_travel_date_string =
                                                                                                results[x].date_string;
                                                                                            otr_travel_date =
                                                                                                results[x].roster_date;
                                                                                            otr_to_res_required = true;
                                                                                            break;
                                                                                        }
                                                                                    }
                                                                                }
                                                                            },
                                                                        );
                                                                        break;
                                                                    }
                                                                }
                                                            } else {
                                                                getPastDayShiftInfo(
                                                                    pay_id,
                                                                    travel_date_string,
                                                                    function (results) {
                                                                        if (results.length > 0) {
                                                                            for (let x = 0; x < results.length; x++) {
                                                                                if (results[x].shift_type != "off") {
                                                                                    otr_travel_date_string =
                                                                                        results[x].date_string;
                                                                                    otr_travel_date =
                                                                                        results[x].roster_date;
                                                                                    otr_to_res_required = true;
                                                                                    break;
                                                                                }
                                                                            }
                                                                        }
                                                                    },
                                                                );
                                                            }
                                                        },
                                                    );
                                                    if (otr_to_res_required === true) {
                                                        travel_object_20_to_res.travel_id = travel_id;
                                                        travel_object_20_to_res.pay_id = pay_id;
                                                        travel_object_20_to_res.date_string =
                                                            otr_travel_date_string;
                                                        travel_object_20_to_res.travel_date =
                                                            otr_travel_date;
                                                        travel_object_20_to_res.route_from = "20";
                                                        travel_object_20_to_res.route_to = "Res";
                                                        travel_object_20_to_res.home_station =
                                                            home_station_id;
                                                        travel_object_20_to_res.created_by = user_logged_in;
                                                        travel_object_20_to_res.equipment = with_ppe;
                                                        travel_object_20_to_res.notice = prior_notice;
                                                        travel_object_20_to_res.linked_booking_id =
                                                            linked_booking_id;
                                                        travel_object_20_to_res.roster = roster;
                                                        travel_object_20_to_res.shift = shift;
                                                        travel_object_20_to_res.location = location;
                                                        travel_object_20_to_res.created_date = moment().add(
                                                            array_count,
                                                            "milliseconds",
                                                        );
                                                        travel_object_20_to_res.type = "Automatic";
                                                        travel_object_20_to_res.res_to_hs_kms =
                                                            res_to_hs_kms;
                                                        travel_object_20_to_res.movement_type =
                                                            record.leave_type_code;
                                                        travel_array.push(travel_object_20_to_res);
                                                        array_count += 1;
                                                    }
                                                }
                                                travel_object_res_to_next_rs.travel_id = travel_id;
                                                travel_object_res_to_next_rs.pay_id = pay_id;
                                                travel_object_res_to_next_rs.date_string =
                                                    travel_date_string;
                                                travel_object_res_to_next_rs.travel_date = travel_date;
                                                travel_object_res_to_next_rs.route_from = "Res";
                                                travel_object_res_to_next_rs.route_to = route_to;
                                                travel_object_res_to_next_rs.home_station =
                                                    home_station_id;
                                                travel_object_res_to_next_rs.created_by =
                                                    user_logged_in;
                                                travel_object_res_to_next_rs.equipment = with_ppe;
                                                travel_object_res_to_next_rs.notice = prior_notice;
                                                travel_object_res_to_next_rs.linked_booking_id =
                                                    linked_booking_id;
                                                travel_object_res_to_next_rs.roster = roster;
                                                travel_object_res_to_next_rs.shift = shift;
                                                travel_object_res_to_next_rs.location = location;
                                                travel_object_res_to_next_rs.created_date =
                                                    moment().add(array_count, "milliseconds");
                                                travel_object_res_to_next_rs.type = "Automatic";
                                                travel_object_res_to_next_rs.res_to_hs_kms =
                                                    res_to_hs_kms;
                                                travel_object_res_to_next_rs.movement_type =
                                                    record.leave_type_code;
                                                travel_array.push(travel_object_res_to_next_rs);
                                                array_count += 1;
                                            }
                                        } else {
                                            last_claim_rs_to_rs = true;
                                            last_to_station = route_to;
                                        }
                                        station_to_station = false;
                                        if (last_row_id != row) {
                                            if (
                                                next_record.date_string == record.date_string &&
                                                next_record.pay_id == record.pay_id
                                            ) {
                                                skip_rest_claims = true;
                                                station_to_station = true;
                                            }
                                        }
                                        if (skip_rest_claims === false) {
                                            with_ppe = record.sm_travel_ppe;
                                            if (reliever_to_rs === false) {
                                                if (res_to_rs_with_prior === false) {
                                                    travel_object.travel_id = travel_id;
                                                    travel_object.pay_id = pay_id;
                                                    travel_object.date_string = travel_date_string;
                                                    travel_object.travel_date = travel_date;
                                                    travel_object.route_from = route_from;
                                                    travel_object.route_to = route_to;
                                                    travel_object.home_station = home_station_id;
                                                    travel_object.created_by = user_logged_in;
                                                    travel_object.equipment = with_ppe;
                                                    travel_object.notice = prior_notice;
                                                    travel_object.linked_booking_id = linked_booking_id;
                                                    travel_object.roster = roster;
                                                    travel_object.shift = shift;
                                                    travel_object.location = location;
                                                    travel_object.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object.type = "Automatic";
                                                    travel_object.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object.movement_type = record.leave_type_code;
                                                    travel_array.push(travel_object);
                                                    array_count += 1;
                                                }
                                            }
                                            if (record.to_station != home_station_id) {
                                                getNextWorkDay(
                                                    pay_id,
                                                    travel_date_string,
                                                    function (results) {
                                                        if (results.length > 0) {
                                                            for (let x = 0; x < results.length; x++) {
                                                                if (
                                                                    results[x].booking_type == "staff_movement"
                                                                ) {
                                                                    if (
                                                                        results[x].leave_type_code != "OC" &&
                                                                        results[x].leave_type_code != "OD" &&
                                                                        results[x].leave_type_code != "Depl"
                                                                    ) {
                                                                        if (
                                                                            results[x].with_standby === false &&
                                                                            results[x].with_recall === false
                                                                        ) {
                                                                            if (
                                                                                results[x].leave_type_code == "OTRRN"
                                                                            ) {
                                                                                route_to = "103";
                                                                            } else if (
                                                                                results[x].leave_type_code == "OTRRS"
                                                                            ) {
                                                                                route_to = "104";
                                                                            } else if (
                                                                                results[x].leave_type_code == "OTRRC"
                                                                            ) {
                                                                                route_to = "100";
                                                                            } else {
                                                                                route_to = results[
                                                                                    x
                                                                                    ].leave_type_code.slice(0, -1);
                                                                            }
                                                                            if (route_to == record.to_station) {
                                                                                with_ppe = false;
                                                                            } else {
                                                                                with_ppe = true;
                                                                            }
                                                                            res_back_to_hs_required = false;
                                                                            getSameDayShifts(
                                                                                pay_id,
                                                                                travel_date_string,
                                                                                function (values) {
                                                                                    if (values.length > 0) {
                                                                                        if (
                                                                                            values[0].date_string ==
                                                                                            travel_date_string + 1
                                                                                        ) {
                                                                                            if (
                                                                                                record.period !=
                                                                                                values[0].shift_type
                                                                                            ) {
                                                                                                if (
                                                                                                    values[0].leave_type_code ==
                                                                                                    null
                                                                                                ) {
                                                                                                    otr_skip_travel = true;
                                                                                                    back_to_hs = true;
                                                                                                    if (
                                                                                                        values[0].shift_type ==
                                                                                                        "day"
                                                                                                    ) {
                                                                                                        travel_date_string =
                                                                                                            values[0].date_string;
                                                                                                        travel_date =
                                                                                                            values[0].roster_date;
                                                                                                    }
                                                                                                }
                                                                                            }
                                                                                        }
                                                                                    }
                                                                                },
                                                                            );
                                                                            break;
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        } else {
                                                            route_to = home_station_id;
                                                            with_ppe = true;
                                                            res_back_to_hs_required = true;
                                                        }
                                                    },
                                                );
                                                route_to = record.to_station;
                                                if (
                                                    home_station_id == 100 &&
                                                    route_to == 20 &&
                                                    travel_array[array_count - 1].route_to == 20
                                                ) {
                                                    otr_skip_travel = true;
                                                }
                                                if (back_to_hs === true) {
                                                    travel_object_to_res.travel_id = travel_id;
                                                    travel_object_to_res.pay_id = pay_id;
                                                    travel_object_to_res.date_string = travel_date_string;
                                                    travel_object_to_res.travel_date = travel_date;
                                                    travel_object_to_res.route_from = route_to;
                                                    travel_object_to_res.route_to = home_station_id;
                                                    travel_object_to_res.home_station = home_station_id;
                                                    travel_object_to_res.created_by = user_logged_in;
                                                    travel_object_to_res.equipment = with_ppe;
                                                    travel_object_to_res.notice = prior_notice;
                                                    travel_object_to_res.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_to_res.roster = roster;
                                                    travel_object_to_res.shift = shift;
                                                    travel_object_to_res.location = location;
                                                    travel_object_to_res.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object_to_res.type = "Automatic";
                                                    travel_object_to_res.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object_to_res.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_to_res);
                                                    array_count += 1;
                                                }
                                                if (otr_skip_travel === false) {
                                                    travel_object_to_res.travel_id = travel_id;
                                                    travel_object_to_res.pay_id = pay_id;
                                                    travel_object_to_res.date_string = travel_date_string;
                                                    travel_object_to_res.travel_date = travel_date;
                                                    travel_object_to_res.route_from = route_to;
                                                    travel_object_to_res.route_to = "Res";
                                                    travel_object_to_res.home_station = home_station_id;
                                                    travel_object_to_res.created_by = user_logged_in;
                                                    travel_object_to_res.equipment = with_ppe;
                                                    travel_object_to_res.notice = prior_notice;
                                                    travel_object_to_res.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_to_res.roster = roster;
                                                    travel_object_to_res.shift = shift;
                                                    travel_object_to_res.location = location;
                                                    travel_object_to_res.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object_to_res.type = "Automatic";
                                                    travel_object_to_res.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object_to_res.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_to_res);
                                                    array_count += 1;
                                                }
                                            }
                                        }
                                        if (record.to_station != home_station_id) {
                                            let no_upcoming_ra = false;
                                            getNextShiftDate(
                                                pay_id,
                                                travel_date_string,
                                                function (values) {
                                                    if (values.length > 0) {
                                                        for (let x = 0; x < values.length; x++) {
                                                            if (
                                                                values[x].booking_type == "sick_leave" ||
                                                                values[x].booking_type == "leave_request"
                                                            ) {
                                                            } else if (values[x].booking_type == null) {
                                                                travel_date_string = values[x].date_string;
                                                                travel_date = values[x].roster_date;
                                                                route_to = record.home_station_id;
                                                                break;
                                                            } else if (
                                                                values[x].booking_type == "staff_movement" &&
                                                                values[x].leave_type_code != "OC" &&
                                                                values[x].leave_type_code != "OD" &&
                                                                values[x].leave_type_code != "Depl"
                                                            ) {
                                                                travel_date_string = values[x].date_string;
                                                                travel_date = values[x].roster_date;
                                                                if (values[x].leave_type_code == "OTRRN") {
                                                                    route_to = "103";
                                                                } else if (
                                                                    values[x].leave_type_code == "OTRRS"
                                                                ) {
                                                                    route_to = "104";
                                                                } else if (
                                                                    values[x].leave_type_code == "OTRRC"
                                                                ) {
                                                                    route_to = "100";
                                                                } else {
                                                                    route_to = values[x].leave_type_code.slice(
                                                                        0,
                                                                        -1,
                                                                    );
                                                                }
                                                                break;
                                                            }
                                                        }
                                                    } else {
                                                        no_upcoming_ra = true;
                                                    }
                                                },
                                            );
                                            if (roster != "Port Pirie") {
                                                if (no_upcoming_ra === false && back_to_hs === false) {
                                                    travel_object_res_to_hs.travel_id = travel_id;
                                                    travel_object_res_to_hs.pay_id = pay_id;
                                                    travel_object_res_to_hs.date_string =
                                                        travel_date_string;
                                                    travel_object_res_to_hs.travel_date = travel_date;
                                                    travel_object_res_to_hs.route_from = "Res";
                                                    travel_object_res_to_hs.route_to = route_to;
                                                    travel_object_res_to_hs.home_station =
                                                        home_station_id;
                                                    travel_object_res_to_hs.created_by = user_logged_in;
                                                    travel_object_res_to_hs.equipment = with_ppe;
                                                    travel_object_res_to_hs.notice = prior_notice;
                                                    travel_object_res_to_hs.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_res_to_hs.roster = roster;
                                                    travel_object_res_to_hs.shift = shift;
                                                    travel_object_res_to_hs.location = location;
                                                    travel_object_res_to_hs.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object_res_to_hs.type = "Automatic";
                                                    travel_object_res_to_hs.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object_res_to_hs.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_res_to_hs);
                                                    array_count += 1;
                                                }
                                            }
                                        }
                                    } else {
                                        let last_day_shift_same = false;
                                        let next_day_shift_same = false;
                                        let next_work_date = "";
                                        let next_work_date_string = "";
                                        let two_sm_same_day = false;
                                        let next_day_rc = false;
                                        getLastWorkStation(
                                            pay_id,
                                            travel_date_string,
                                            false,
                                            true,
                                            function (results) {
                                                if (results.length > 0) {
                                                    if (
                                                        (results[0].leave_type_code == "43R" &&
                                                            route_to == "104") ||
                                                        (results[0].leave_type_code == "32R" &&
                                                            route_to == "103") ||
                                                        (results[0].leave_type_code == "20R" &&
                                                            route_to == "100") ||
                                                        results[0].leave_type_code.slice(0, -1) == route_to
                                                    ) {
                                                        last_day_shift_same = true;
                                                    } else {
                                                        route_from = results[0].leave_type_code.slice(
                                                            0,
                                                            -1,
                                                        );
                                                    }
                                                }
                                            },
                                        );
                                        if (route_from == "104") {
                                            route_from = "43";
                                        } else if (route_from == "103") {
                                            route_from = "32";
                                        } else if (route_from == "100") {
                                            route_from = "20";
                                        }
                                        checkForSMSameDay(
                                            pay_id,
                                            travel_date_string,
                                            function (results) {
                                                if (results.length > 0) {
                                                    if (results[0].sm_count > 1) {
                                                        two_sm_same_day = true;
                                                        route_from = results[0].leave_type_code.slice(
                                                            0,
                                                            -1,
                                                        );
                                                        travel_object_from_last_rs.travel_id = travel_id;
                                                        travel_object_from_last_rs.pay_id = pay_id;
                                                        travel_object_from_last_rs.date_string =
                                                            travel_date_string;
                                                        travel_object_from_last_rs.travel_date =
                                                            travel_date;
                                                        travel_object_from_last_rs.route_from = route_from;
                                                        travel_object_from_last_rs.route_to = route_to;
                                                        travel_object_from_last_rs.home_station =
                                                            home_station_id;
                                                        travel_object_from_last_rs.created_by =
                                                            user_logged_in;
                                                        travel_object_from_last_rs.equipment = true;
                                                        travel_object_from_last_rs.notice = false;
                                                        travel_object_from_last_rs.linked_booking_id = null;
                                                        travel_object_from_last_rs.roster = roster;
                                                        travel_object_from_last_rs.shift = shift;
                                                        travel_object_from_last_rs.location = location;
                                                        travel_object_from_last_rs.created_date =
                                                            moment().add(array_count, "milliseconds");
                                                        travel_object_from_last_rs.type = "Automatic";
                                                        travel_object_from_last_rs.res_to_hs_kms =
                                                            res_to_hs_kms;
                                                        travel_object_from_last_rs.movement_type =
                                                            record.leave_type_code;
                                                        travel_array.push(travel_object_from_last_rs);
                                                        array_count += 1;
                                                        travel_object_rs_to_next_rs.travel_id = travel_id;
                                                        travel_object_rs_to_next_rs.pay_id = pay_id;
                                                        travel_object_rs_to_next_rs.date_string =
                                                            travel_date_string;
                                                        travel_object_rs_to_next_rs.travel_date =
                                                            travel_date;
                                                        travel_object_rs_to_next_rs.route_from = route_to;
                                                        travel_object_rs_to_next_rs.route_to = route_from;
                                                        travel_object_rs_to_next_rs.home_station =
                                                            home_station_id;
                                                        travel_object_rs_to_next_rs.created_by =
                                                            user_logged_in;
                                                        travel_object_rs_to_next_rs.equipment = true;
                                                        travel_object_rs_to_next_rs.notice = false;
                                                        travel_object_rs_to_next_rs.linked_booking_id =
                                                            null;
                                                        travel_object_rs_to_next_rs.roster = roster;
                                                        travel_object_rs_to_next_rs.shift = shift;
                                                        travel_object_rs_to_next_rs.location = location;
                                                        travel_object_rs_to_next_rs.created_date =
                                                            moment().add(array_count, "milliseconds");
                                                        travel_object_rs_to_next_rs.type = "Automatic";
                                                        travel_object_rs_to_next_rs.res_to_hs_kms =
                                                            res_to_hs_kms;
                                                        travel_object_rs_to_next_rs.movement_type =
                                                            record.leave_type_code;
                                                        travel_array.push(travel_object_rs_to_next_rs);
                                                        array_count += 1;
                                                    }
                                                }
                                            },
                                        );
                                        if (two_sm_same_day === false) {
                                            if (last_day_shift_same === false) {
                                                travel_object.travel_id = travel_id;
                                                travel_object.pay_id = pay_id;
                                                travel_object.date_string = travel_date_string;
                                                travel_object.travel_date = travel_date;
                                                travel_object.route_from = route_from;
                                                travel_object.route_to = route_to;
                                                travel_object.home_station = home_station_id;
                                                travel_object.created_by = user_logged_in;
                                                travel_object.equipment = with_ppe;
                                                travel_object.notice = prior_notice;
                                                travel_object.linked_booking_id = linked_booking_id;
                                                travel_object.roster = roster;
                                                travel_object.shift = shift;
                                                travel_object.location = location;
                                                travel_object.created_date = moment().add(
                                                    array_count,
                                                    "milliseconds",
                                                );
                                                travel_object.type = "Automatic";
                                                travel_object.res_to_hs_kms = res_to_hs_kms;
                                                travel_object.movement_type = record.leave_type_code;
                                                travel_array.push(travel_object);
                                                array_count += 1;
                                                getNextWorkDay(
                                                    pay_id,
                                                    travel_date_string,
                                                    function (results) {
                                                        if (results.length > 0) {
                                                            if (results[0].leave_type_code == "RC") {
                                                                next_day_rc = true;
                                                            } else {
                                                                if (
                                                                    results[0].leave_type_code.slice(0, -1) ==
                                                                    route_to
                                                                ) {
                                                                    next_day_shift_same = true;
                                                                    next_work_date_string =
                                                                        results[0].date_string;
                                                                    next_work_date = moment(
                                                                        results[0].start_date,
                                                                    ).format("DD/MM/YYYY 12:00");
                                                                } else {
                                                                    next_work_date_string =
                                                                        results[0].date_string;
                                                                    next_work_date = moment(
                                                                        results[0].start_date,
                                                                    ).format("DD/MM/YYYY 12:00");
                                                                }
                                                            }
                                                        }
                                                    },
                                                );
                                                if (next_day_shift_same === false) {
                                                    if (sm_period == "night") {
                                                        travel_date_string = record.date_string + 1;
                                                        travel_date = moment(
                                                            record.start_date,
                                                            "DD/MM/YYYY HH:mm",
                                                        ).add(1, "days");
                                                        travel_date =
                                                            moment(travel_date).format("DD/MM/YYYY HH:mm");
                                                    }
                                                    travel_object_to_res.travel_id = travel_id;
                                                    travel_object_to_res.pay_id = pay_id;
                                                    travel_object_to_res.date_string = travel_date_string;
                                                    travel_object_to_res.travel_date = travel_date;
                                                    travel_object_to_res.route_from = route_to;
                                                    travel_object_to_res.route_to = "Res";
                                                    travel_object_to_res.home_station = home_station_id;
                                                    travel_object_to_res.created_by = user_logged_in;
                                                    travel_object_to_res.equipment = with_ppe;
                                                    travel_object_to_res.notice = prior_notice;
                                                    travel_object_to_res.linked_booking_id =
                                                        linked_booking_id;
                                                    travel_object_to_res.roster = roster;
                                                    travel_object_to_res.shift = shift;
                                                    travel_object_to_res.location = location;
                                                    travel_object_to_res.created_date = moment().add(
                                                        array_count,
                                                        "milliseconds",
                                                    );
                                                    travel_object_to_res.type = "Automatic";
                                                    travel_object_to_res.res_to_hs_kms = res_to_hs_kms;
                                                    travel_object_to_res.movement_type =
                                                        record.leave_type_code;
                                                    travel_array.push(travel_object_to_res);
                                                    array_count += 1;
                                                }
                                                if (next_day_rc === false) {
                                                    {
                                                        travel_object_res_to_next_rs.travel_id = travel_id;
                                                        travel_object_res_to_next_rs.pay_id = pay_id;
                                                        travel_object_res_to_next_rs.date_string =
                                                            next_work_date_string;
                                                        travel_object_res_to_next_rs.travel_date =
                                                            next_work_date;
                                                        travel_object_res_to_next_rs.route_from = "Res";
                                                        travel_object_res_to_next_rs.route_to = route_from;
                                                        travel_object_res_to_next_rs.home_station =
                                                            home_station_id;
                                                        travel_object_res_to_next_rs.created_by =
                                                            user_logged_in;
                                                        travel_object_res_to_next_rs.equipment = with_ppe;
                                                        travel_object_res_to_next_rs.notice = prior_notice;
                                                        travel_object_res_to_next_rs.linked_booking_id =
                                                            linked_booking_id;
                                                        travel_object_res_to_next_rs.roster = roster;
                                                        travel_object_res_to_next_rs.shift = shift;
                                                        travel_object_res_to_next_rs.location = location;
                                                        travel_object_res_to_next_rs.created_date =
                                                            moment().add(array_count, "milliseconds");
                                                        travel_object_res_to_next_rs.type = "Automatic";
                                                        travel_object_res_to_next_rs.res_to_hs_kms =
                                                            res_to_hs_kms;
                                                        travel_object_res_to_next_rs.movement_type =
                                                            record.leave_type_code;
                                                        travel_array.push(travel_object_res_to_next_rs);
                                                        array_count += 1;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                                sm_id_array.push({
                                    bk_id: record.booking_id,
                                    sm_date_string: record.date_string,
                                });
                            });
                            const filtered_travel_array = travel_array.filter(
                                (v, i, a) =>
                                    a.findIndex(
                                        (t) =>
                                            t.pay_id === v.pay_id &&
                                            t.date_string === v.date_string &&
                                            t.route_to === v.route_to,
                                    ) === i,
                            );
                            webix.message.pull["travel_counter"].firstChild.innerHTML =
                                "Now calculating travel distances...";
                            webix
                                .ajax()
                                .headers({Authorization: "Bearer " + api_key})
                                .post(
                                    server_url + "/admin/travel_logs",
                                    {
                                        travel_array: filtered_travel_array,
                                        sm_id_array: sm_id_array,
                                    },
                                    {
                                        error: function (err) {
                                            webix.message.hide("travel_counter");
                                            $$("loader-window").hide();
                                            webix.alert(
                                                "One or more errors occurred while processing the Travel Claims!",
                                            );
                                            grid.clearAll();
                                        },
                                        success: function (results) {
                                            webix.message.hide("travel_counter");
                                            $$("loader-window").hide();
                                            grid.clearAll();
                                            webix.confirm({
                                                title: "Travel Claims",
                                                ok: "Yes",
                                                cancel: "No",
                                                width: 500,
                                                text:
                                                    array_count +
                                                    " Travel Claims were processed successfully!<br><br>Do you want to search for duplicate claims?",
                                                callback: function (result) {
                                                    if (result == true) {
                                                        travelRequests.loadDuplicateTCs();
                                                    }
                                                },
                                            });
                                        },
                                    },
                                );
                        }, 250);
                    }
                },
            });
        } else {
            $$("loader-window").hide();
            webix.alert(
                "There were no bookings found for processing a travel claim!",
            );
        }
    }

    function checkForSMSameDay(pay_id, date_string, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/check_for_SM_same_day",
                {pay_id: pay_id, date_string: date_string},
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function getNextShiftDate(pay_id, date_stamp, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_next_shift_date",
                {pay_id: pay_id, date_string: date_stamp},
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function getSameDayShifts(pay_id, date_stamp, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_same_day_shifts",
                {pay_id: pay_id, date_string: date_stamp},
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function getNextWorkDay(pay_id, date_stamp, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_next_work_day",
                {pay_id: pay_id, date_string: date_stamp},
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            callback(values);
                        }
                    },
                },
            );
    }

    function getDistance(fromAddress, toAddress, pay_id, callback) {
        $$("loader-window").show();
        setTimeout(function () {
            webix
                .ajax()
                .headers({Authorization: "Bearer " + api_key})
                .sync()
                .get(
                    server_url + "/admin/get_distance",
                    {route_from: fromAddress, route_to: toAddress, pay_id: pay_id},
                    {
                        error: function (err) {
                            $$("loader-window").hide();
                            callback("?");
                        },
                        success: function (results) {
                            $$("loader-window").hide();
                            if (results) {
                                let values = JSON.parse(results);
                                callback(values.response);
                            } else {
                                callback("?");
                            }
                        },
                    },
                );
        }, 250);
    }

    function setUserFieldValues() {

        $$("bookings-page").$$("travel_create").$$("employees").setValue(user_logged_in);
        $$("bookings-page").$$("travel_create").$$("employees_id").setValue(user_logged_in);

        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_current_home_address",
                {pay_id: user_logged_in},
                {
                    error: function (err) {
                        $$("bookings-page").$$("travel_create").$$("employees_address").setValue("?");
                        $$("bookings-page").$$("travel_create").$$("res_to_hs_dist").setValue("?");

                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            $$("bookings-page").$$("travel_create").$$("employees_address").setValue(values[0].street + ", " + values[0].suburb + ", " + values[0].state + " " + values[0].post_code);
                            $$("bookings-page").$$("travel_create").$$("res_to_hs_dist").setValue(values[0].res_to_hs_dist);
                        } else {
                            $$("bookings-page").$$("travel_create").$$("employees_address").setValue("?");
                            $$("bookings-page").$$("travel_create").$$("res_to_hs_dist").setValue("?");
                        }
                    },
                },
            );
    }

    return {
        initialise: function () {
            initApplication();
        },
    };
})();
