let travelRequests = (function () {
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("bookings-page")
      .$$("travel_requests")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("bookings-page")
      .$$("travel_requests")
      .$$("to_date")
      .setValue(endOfMonth);
    $$("bookings-page")
      .$$("travel_requests")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page").$$("travel_requests").$$("status_filter").setValue(2);
  }
  function eventHandlers() {
    $$("travel-duplicates_grid").attachEvent(
      "onCheck",
      function (row, column, state) {
        let grid = $$("travel-duplicates_grid");
        let selectArray = [];
        if (column === "select") {
          grid.eachRow(function (rowId) {
            if (grid.getItem(rowId).select == 1) {
              selectArray.push(grid.getItem(rowId).group_id);
            }
          });
          let id_selected = grid.getItem(row).group_id;
          let id_found = selectArray.filter((x) => x === id_selected).length;
          if (id_found > 1) {
            grid.eachRow(function (rowId) {
              if (grid.getItem(rowId).group_id === id_selected) {
                grid.updateItem(rowId, { select: 0 });
              }
            });
          }
        }
      },
    );
    $$("btn_delete_duplicates").attachEvent("onItemClick", function (id, e) {
      removeTCduplicates();
    });
    $$("btn_travel-duplicates_close").attachEvent(
      "onItemClick",
      function (id, e) {
        $$("travel-duplicates-window").hide();
      },
    );
    $$("bookings-page")
      .$$("travel_requests")
      .$$("btn_show_duplicates")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1) {
          loadDuplicateTCs();
        } else {
          webix.alert("You don't have permission to use this function!");
        }
      });
    $$("bookings-page")
      .$$("travel_requests")
      .$$("status_filter")
      .attachEvent("onChange", function (newv) {
        if (newv == 4) {
          $$("bookings-page")
            .$$("travel_requests")
            .$$("btn_approve_claims")
            .disable();
        } else {
          $$("bookings-page")
            .$$("travel_requests")
            .$$("btn_approve_claims")
            .enable();
        }
      });
    $$("bookings-page")
      .$$("travel_requests")
      .$$("btn_bulk_delete")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1) {
          let grid = $$("bookings-page")
            .$$("travel_requests")
            .$$("travel-requests_grid")
            .serialize();
          let selectedCount = [];
          for (let i = 0; i < grid.length; i++) {
            if (grid[i].select == 1) {
              selectedCount.push(grid[i]);
            }
          }
          if (selectedCount.length > 0) {
            webix.confirm({
              title: "Delete Selected Claims",
              ok: "Yes",
              cancel: "No",
              width: 550,
              text:
                selectedCount.length +
                " records are selected</br>Are you sure you want to delete these?</br></br>Note: Any selected claim that has already been Approved will be ignored!",
              callback: function (result) {
                switch (result) {
                  case true:
                    let selectedIds = [];
                    for (let i = 0; i < grid.length; i++) {
                      if (grid[i].select == 1) {
                        if (
                          grid[i].status !=
                          "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>"
                        ) {
                          selectedIds.push(grid[i].db_id);
                        }
                      }
                    }
                    if (selectedIds.length > 0) {
                      bulkDeleteClaims(selectedIds);
                    } else {
                      webix.alert("No valid travel claims selected!");
                    }
                }
              },
            });
          } else {
            webix.alert("No records selected!");
          }
        } else {
          webix.alert({
            text: "You don't have permission to Approve claims!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("travel_requests")
      .$$("btn_approve_claims")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1) {
          let grid = $$("bookings-page")
            .$$("travel_requests")
            .$$("travel-requests_grid")
            .serialize();
          let selectedCount = [];
          for (let i = 0; i < grid.length; i++) {
            if (grid[i].select == 1) {
              selectedCount.push(grid[i]);
            }
          }
          if (selectedCount.length > 0) {
            webix.confirm({
              title: "Approved Selected Claims",
              ok: "Yes",
              cancel: "No",
              width: 550,
              text:
                selectedCount.length +
                " records are selected</br>Are you sure you want to change the status of these to 'Approved'?</br></br>Note: Any selected claim that has already been Approved will be ignored!",
              callback: function (result) {
                switch (result) {
                  case true:
                    let selectedIds = [];
                    for (let i = 0; i < grid.length; i++) {
                      if (grid[i].select == 1) {
                        if (
                          grid[i].status !=
                          "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>"
                        ) {
                          selectedIds.push(grid[i].db_id);
                        }
                      }
                    }
                    if (selectedIds.length > 0) {
                      bulkApproveClaims(selectedIds);
                    } else {
                      webix.alert("No valid travel claims selected!");
                    }
                }
              },
            });
          } else {
            webix.alert("No records selected!");
          }
        } else {
          webix.alert({
            text: "You don't have permission to Approve claims!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("travel_requests")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("bookings-page")
          .$$("travel_requests")
          .$$("roster_filter")
          .getText();
        let status = $$("bookings-page")
          .$$("travel_requests")
          .$$("status_filter")
          .getText();
        let startDate = $$("bookings-page")
          .$$("travel_requests")
          .$$("from_date")
          .getValue();
        let endDate = $$("bookings-page")
          .$$("travel_requests")
          .$$("to_date")
          .getValue();
        $$("bookings-page")
          .$$("travel_requests")
          .$$("selected_request")
          .define("template", "Selected Request: None");
        $$("bookings-page")
          .$$("travel_requests")
          .$$("selected_request")
          .refresh();
        $$("bookings-page").$$("travel_requests").$$("request_id").setValue("");
        loadTravelRequests(roster, status, startDate, endDate);
      });
    $$("bookings-page")
      .$$("travel_requests")
      .$$("btn_approve")
      .attachEvent("onItemClick", function (id, e) {
        let travel_id = $$("bookings-page")
          .$$("travel_requests")
          .$$("request_id")
          .getValue();
        let newStatus = "Approved";
        let comments = $$("bookings-page")
          .$$("travel_requests")
          .$$("request_comments")
          .getValue();
        updateRequestStatus(travel_id, newStatus, comments);
      });
    $$("bookings-page")
      .$$("travel_requests")
      .$$("btn_deny")
      .attachEvent("onItemClick", function (id, e) {
        let travel_id = $$("bookings-page")
          .$$("travel_requests")
          .$$("request_id")
          .getValue();
        let newStatus = "Denied";
        let comments = $$("bookings-page")
          .$$("travel_requests")
          .$$("request_comments")
          .getValue();
        updateRequestStatus(travel_id, newStatus, comments);
      });
    $$("btn_travel-logs_close").attachEvent("onItemClick", function (id, e) {
      $$("travel-logs-window").hide();
    });
    $$("travel_log_btn_save").attachEvent("onItemClick", function (id, e) {
      updateTravel();
    });
    $$("travel_log_btn_calculate").attachEvent("onItemClick", function (id, e) {
      let route_from = $$("travel_log_route_from").getValue();
      let route_to = $$("travel_log_route_to").getValue();
      let with_ppe = $$("travel_log_with_ppe").getValue();
      if (route_from != "" && route_to != "") {
        $$("loader-window").show();
        getTravelDistance(route_from, route_to, with_ppe);
      } else {
        webix.alert("You must specify valid 'From' and 'To' routes!");
      }
    });
    $$("bookings-page")
      .$$("travel_requests")
      .$$("travel-requests_grid")
      .attachEvent("onItemClick", function (id, e, node) {
        let selectedRow = $$("bookings-page")
          .$$("travel_requests")
          .$$("travel-requests_grid")
          .getSelectedItem(id);
        let form = $$("travel_log_entries");
        let route = selectedRow[0].route;
        let route_from = "";
        let route_to = "";
        let status = "";
        let with_ppe = "";
        let prior_notice = "";
        if (id.column == "delete") {
          webix.confirm({
            title: "Delete Travel Claim",
            ok: "Yes",
            cancel: "No",
            text: "Are you sure you want to delete the selected Travel claim?",
            callback: function (result) {
              switch (result) {
                case true:
                  deleteTravelClaim(selectedRow[0].db_id);
              }
            },
          });
        } else if (id.column == "edit") {
          route_from = route.split("-")[0];
          route_to = route.split("-")[1];
          if (selectedRow[0].with_ppe == "Yes") {
            with_ppe = 1;
          } else {
            with_ppe = 0;
          }
          if (selectedRow[0].prior_notice == "Yes") {
            prior_notice = 1;
          } else {
            prior_notice = 0;
          }
          if (
            selectedRow[0].status ==
            "<span class = 'approved_pending webix_icon fas fa-clock'></span>"
          ) {
            status = "Pending";
          } else if (
            selectedRow[0].status ==
            "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>"
          ) {
            status = "Approved";
          } else if (
            selectedRow[0].status ==
            "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>"
          ) {
            status = "Denied";
          }
          if (status == "Approved") {
            webix.alert({
              text: "You can't Edit an already 'Approved' travel claim!",
              width: 500,
            });
          } else {
            form.setValues({
              travel_log_travel_id: selectedRow[0].db_id,
              travel_log_pay_id: selectedRow[0].pay_id,
              travel_log_employee: selectedRow[0].employee,
              travel_log_datepicker: selectedRow[0].travel_date,
              travel_log_route_from: route_from,
              travel_log_route_to: route_to,
              travel_log_distance: selectedRow[0].distance,
              travel_log_with_ppe: with_ppe,
              travel_log_prior_notice: prior_notice,
              travel_log_status: status,
              travel_log_comments: selectedRow[0].request_comments,
            });
            $$("travel-logs-window").show();
          }
        } else {
          selectedRow = this.getItem(id);
          $$("bookings-page")
            .$$("travel_requests")
            .$$("selected_request")
            .define(
              "template",
              "Selected Request: " +
                selectedRow.route +
                " on " +
                moment(selectedRow.travel_date).format("DD/MM/YYYY") +
                " for " +
                selectedRow.employee,
            );
          $$("bookings-page")
            .$$("travel_requests")
            .$$("selected_request")
            .refresh();
          $$("bookings-page")
            .$$("travel_requests")
            .$$("request_id")
            .setValue(selectedRow.db_id);
          $$("bookings-page").$$("travel_requests").$$("btn_approve").enable();
          $$("bookings-page").$$("travel_requests").$$("btn_deny").enable();
        }
      });
  }
  function loadDuplicateTCs() {
    let grid = $$("travel-duplicates_grid");
    $$("loader-window").show();
    setTimeout(function () {
      grid.clearAll();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/admin/get_travel_duplicates",
          {},
          {
            error: function (err) {
              $$("loader-window").hide();
              webix.alert(
                "There was an error loading duplicate travel claims!",
              );
            },
            success: function (results) {
              if (results) {
                let records = JSON.parse(results);
                let duplicates = [];
                let with_ppe = "";
                let prior_notice = "";
                if (records.length > 0) {
                  for (let x = 0; x < records.length; x++) {
                    if (
                      records[x].with_ppe === true ||
                      records[x].with_ppe == 1
                    ) {
                      with_ppe = "Yes";
                    } else {
                      with_ppe = "No";
                    }
                    if (
                      records[x].prior_notice === true ||
                      records[x].prior_notice == 1
                    ) {
                      prior_notice = "Yes";
                    } else {
                      prior_notice = "No";
                    }
                    duplicates.push({
                      db_id: records[x].id,
                      pay_id: records[x].pay_id,
                      employee:
                        records[x].surname + ", " + records[x].first_name,
                      travel_date: moment(
                        records[x].date_string,
                        "YYYYMMDD",
                      ).format("DD/MM/YYYY"),
                      route_from: records[x].route_from,
                      distance: records[x].distance,
                      route_to: records[x].route_to,
                      with_ppe: with_ppe,
                      prior_notice: prior_notice,
                      type: records[x].type,
                      status: records[x].status,
                      comments: records[x].request_comments,
                    });
                  }
                  grid.define("data", duplicates);
                  grid.refresh();
                  let x = 0;
                  let y = 0;
                  grid.eachRow(function (row) {
                    x += 1;
                    y += 1;
                    if (x === 3 || x === 4) {
                      grid.addRowCss(row, "duplicate_group");
                    }
                    if (x === 1 || x === 3) {
                      grid.updateItem(row, { group_id: y });
                    } else {
                      grid.updateItem(row, { group_id: y - 1 });
                    }
                    if (x === 4) {
                      x = 0;
                    }
                  });
                  $$("loader-window").hide();
                  $$("travel-duplicates-window").show();
                  grid.scrollTo(0);
                } else {
                  $$("loader-window").hide();
                  webix.alert("No duplicates found!");
                }
              } else {
                $$("loader-window").hide();
                webix.alert("No duplicates found!");
              }
            },
          },
        );
    }, 250);
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page").$$("travel_requests").$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  locations_subject.subscribe(function (data) {
    let route_to = $$("travel_log_route_to");
    let route_from = $$("travel_log_route_from");
    let results = JSON.parse(data);
    let locations_list = [];
    for (let x = 0; x < results.length; x++) {
      if (
        results[x].station_id <= 20 ||
        results[x].station_id > 70 ||
        (results[x].station_id > 50 && results[x].station_id < 70)
      ) {
      } else {
        locations_list.push({
          id: results[x].station_id,
          value: results[x].name,
        });
      }
    }
    locations_list.unshift({ id: "20", value: "Adelaide" });
    locations_list.unshift({ id: "Res", value: "Residence" });
    locations_list.push({ id: "100", value: "OTR Central" });
    locations_list.push({ id: "103", value: "OTR North" });
    locations_list.push({ id: "104", value: "OTR South" });
    route_to.define("options", locations_list);
    route_to.refresh();
    route_from.define("options", locations_list);
    route_from.refresh();
  });
  function removeTCduplicates() {
    let grid = $$("travel-duplicates_grid");
    let deleteArray = [];
    grid.eachRow(function (row) {
      let record = grid.getItem(row);
      if (record.select == 1) {
        deleteArray.push(record.db_id);
      }
    });
    if (deleteArray.length > 0) {
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .del(
          server_url + "/admin/remove_tc_duplicates",
          { deleteArray: deleteArray },
          {
            error: function (err) {
              webix.alert({
                text: "There was an error deleting the selected duplicate travel claims!",
                width: 450,
              });
            },
            success: function (results) {
              $$("travel-duplicates-window").hide();
              webix.message({
                text: "Selected travel duplicates deleted!",
                type: "success",
                expire: 2e3,
              });
            },
          },
        );
    } else {
      webix.alert({ text: "Nothing to delete!", width: 400 });
    }
  }
  function deleteTravelClaim(id) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/delete_travel",
        { id: id, deleted_by: user_logged_in },
        {
          error: function (err) {},
          success: function () {
            webix.message({
              text: "Travel claim deleted successfully!",
              type: "success",
              expire: 1500,
            });
            $$("bookings-page")
              .$$("travel_requests")
              .$$("btn_search")
              .callEvent("onItemClick");
          },
        },
      );
  }
  function bulkDeleteClaims(claimsArray) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/bookings/delete_travel_requests",
        { id_array: claimsArray, deleted_by: user_logged_in },
        {
          error: function (err) {},
          success: function (results) {
            webix.alert({
              text: "Selected travel claims deleted successfully!",
              width: 450,
            });
            $$("bookings-page")
              .$$("travel_requests")
              .$$("btn_search")
              .callEvent("onItemClick");
          },
        },
      );
  }
  function bulkApproveClaims(claimsArray) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/bookings/approve_travel_requests",
        { id_array: claimsArray, approved_denied_by: user_logged_in },
        {
          error: function (err) {},
          success: function (results) {
            webix.alert({
              text: "Selected travel claims updated successfully!",
              width: 450,
            });
            $$("bookings-page")
              .$$("travel_requests")
              .$$("btn_search")
              .callEvent("onItemClick");
          },
        },
      );
  }
  function updateRequestStatus(id, newStatus, comments) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/bookings/travel_requests",
        {
          id: id,
          status: newStatus,
          approved_denied_by: user_logged_in,
          request_comments: comments,
          update_method: "update_status",
        },
        {
          error: function (err) {},
          success: function (results) {
            webix.alert("Travel request status changed successfully!");
            $$("bookings-page")
              .$$("travel_requests")
              .$$("btn_search")
              .callEvent("onItemClick");
          },
        },
      );
  }
  function loadTravelRequests(roster, status, startDate, endDate) {
    let grid = $$("bookings-page")
      .$$("travel_requests")
      .$$("travel-requests_grid");
    let start_date = startDate.slice(0, 10) + " " + "00:01";
    let end_date = endDate.slice(0, 10) + " " + "23:59";
    let travel_date_format = "";
    let created_date_format = "";
    let withPPE = "";
    let priorNotice = "";
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/travel_requests",
        {
          roster: roster,
          status: status,
          start_date: start_date,
          end_date: end_date,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let values = JSON.parse(results);
            let status_icon = "";
            let middleName = "";
            $$("bookings-page")
              .$$("travel_requests")
              .$$("search_count")
              .define("template", values.length + " requests found");
            $$("bookings-page")
              .$$("travel_requests")
              .$$("search_count")
              .refresh();
            values.forEach(function (result) {
              if (result.status == "Pending") {
                status_icon =
                  "<span class = 'approved_pending webix_icon fas fa-clock'></span>";
              } else if (result.status == "Approved") {
                status_icon =
                  "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>";
              } else if (result.status == "Denied") {
                status_icon =
                  "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>";
              }
              if (result.middle_name === null) {
                middleName = "";
              } else {
                middleName = result.middle_name;
              }
              if (result.prior_notice == true) {
                priorNotice = "Yes";
              } else {
                priorNotice = "No";
              }
              if (result.with_ppe == true) {
                withPPE = "Yes";
              } else {
                withPPE = "No";
              }
              travel_date_format = moment(
                result.travel_date,
                "DD/MM/YYYY",
              ).toDate();
              created_date_format = moment(
                result.created_date,
                "DD/MM/YYYY H:mm",
              ).toDate();
              grid.add({
                db_id: result.id,
                pay_id: result.pay_id,
                employee:
                  result.surname + ", " + result.first_name + " " + middleName,
                rank: result.rank,
                shift: result.shift,
                route: result.route_from + "-" + result.route_to,
                distance: result.distance,
                travel_date: travel_date_format,
                prior_notice: priorNotice,
                with_ppe: withPPE,
                created_by: result.created_by,
                created_date: created_date_format,
                request_comments: result.request_comments,
                status: status_icon,
              });
            });
            grid.adjustColumn("shift");
            $$("loader-window").hide();
          },
        },
      );
  }
  function updateTravel() {
    let form = $$("travel_log_entries").getValues();
    let travel_date = form.travel_log_datepicker;
    let dateString = moment(travel_date).format("YYYYMMDD");
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/bookings/travel_request",
        {
          id: form.travel_log_travel_id,
          travel_date: moment(travel_date).format("YYYY-MM-DD") + " 12:00:00",
          route_from: form.travel_log_route_from,
          route_to: form.travel_log_route_to,
          distance: form.travel_log_distance,
          date_string: dateString,
          request_comments: form.travel_log_comments,
          status: form.travel_log_status,
          with_ppe: form.travel_log_with_ppe,
          prior_notice: form.travel_log_prior_notice,
          type: "Manual",
          update_method: "update_claim",
        },
        {
          error: function (err) {},
          success: function () {
            $$("travel-logs-window").hide();
            webix.message({
              text: "Travel Claim Updated!",
              type: "success",
              expire: 1500,
            });
            $$("bookings-page")
              .$$("travel_requests")
              .$$("btn_search")
              .callEvent("onItemClick");
          },
        },
      );
  }
  function getTravelDistance(route_from, route_to, with_ppe) {
    let pay_id = $$("travel_log_pay_id").getValue();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/travel_distance",
        { pay_id: pay_id, route_from: route_from, route_to: route_to },
        {
          error: function (err) {
            $$("travel_log_distance").setValue(0);
            $$("loader-window").hide();
          },
          success: function (result) {
            let values = JSON.parse(result);
            webix
              .ajax()
              .headers({ Authorization: "Bearer " + api_key })
              .sync()
              .get(
                server_url + "/admin/get_res_to_hs_distance",
                { pay_id: pay_id },
                {
                  error: function (err) {
                    $$("loader-window").hide();
                  },
                  success: function (results) {
                    let distance = JSON.parse(results);
                    if (with_ppe == 0) {
                      $$("travel_log_distance").setValue(
                        values.distance - distance[0].res_to_hs_kms,
                      );
                    } else {
                      $$("travel_log_distance").setValue(values.distance);
                    }
                    $$("loader-window").hide();
                  },
                },
              );
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
    loadDuplicateTCs: function () {
      loadDuplicateTCs();
    },
  };
})();
