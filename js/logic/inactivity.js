let timeoutPeriod = 600;
function IdleTimeout(timeoutSeconds, options) {
  let defaultOptions = { callback: function () {}, alertTimeoutSeconds: 10 };
  this.timeoutSeconds = timeoutSeconds;
  this.options = Object.assign({}, defaultOptions, options);
  this.timeoutRef = null;
  this.activityListenerRef = this.activityListener.bind(this);
  window.addEventListener("mousemove", this.activityListenerRef);
  this.startTimer();
}
IdleTimeout.prototype.startTimer = function () {
  this.timeoutRef = setTimeout(
    this.onTimerDone.bind(this),
    this.timeoutSeconds * 1e3,
  );
};
IdleTimeout.prototype.onTimerDone = function () {
  this.options.callback();
  window.removeEventListener("mousemove", this.activityListenerRef);
};
IdleTimeout.prototype.clearTimer = function () {
  clearTimeout(this.timeoutRef);
};
IdleTimeout.prototype.resetTimer = function () {
  this.clearTimer();
  this.startTimer();
};
IdleTimeout.prototype.activityListener = function () {
  this.resetTimer();
};
