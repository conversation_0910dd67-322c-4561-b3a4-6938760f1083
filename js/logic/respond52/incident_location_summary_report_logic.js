let IncidentLocationSummaryReport = (function () {
  let stationLookup = [];
  function initApplication() {
    eventHandlers();
    getLocationsList();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("from")
      .setValue(startOfMonth);
    $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("to")
      .setValue(endOfMonth);
    $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("location_filter")
      .setValue("1");
    let cal1Popup = $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("from")
      .getPopup()
      .getBody();
    cal1Popup.define("minDate", "2021-11-01");
    cal1Popup.refresh();
    let cal2Popup = $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("to")
      .getPopup()
      .getBody();
    cal2Popup.define("minDate", "2021-11-01");
    cal2Popup.refresh();
  }
  function eventHandlers() {
    $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let station_id = $$("respond52-page")
          .$$("respond52_incident_location_summary")
          .$$("location_filter")
          .getValue();
        let fromDate = $$("respond52-page")
          .$$("respond52_incident_location_summary")
          .$$("from")
          .getValue();
        let toDate = $$("respond52-page")
          .$$("respond52_incident_location_summary")
          .$$("to")
          .getValue();
        generateIncidentLocationGrid(station_id, fromDate, toDate);
      });
  }
  function generateIncidentLocationGrid(station_id, fromDate, toDate) {
    let grid = $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("incident_location_type_grid");
    $$("loader-window").show();
    grid.clearAll();
    grid.config.columns = [];
    grid.refreshColumns();
    $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("incident_records")
      .define(
        "template",
        "<div style='font-size: 17px; text-align: center'>Total Incidents: 0</div>",
      );
    $$("respond52-page")
      .$$("respond52_incident_location_summary")
      .$$("incident_records")
      .refresh();
    if (station_id == 1) {
      stationLookup.forEach(function (values) {
        if (values.id != "1") {
          grid.config.columns.push({
            id: values.id,
            header: {
              text: values.value,
              rotate: true,
              height: 130,
              css: "r52_grid_header",
            },
            width: 50,
            css: { "text-align": "center" },
          });
        }
      });
      grid.config.columns.unshift({
        id: "type",
        header: { text: "Incident Type", css: "r52_grid_header" },
        adjust: true,
        css: { "font-weight": "bold" },
      });
      grid.refreshColumns();
    } else {
      let station_name = $$("respond52-page")
        .$$("respond52_incident_location_summary")
        .$$("location_filter")
        .getText();
      grid.config.columns.push({
        id: station_id,
        header: {
          text: station_name,
          css: { "font-size": "16px" },
          height: 50,
        },
        width: 150,
        css: { "text-align": "center" },
      });
      grid.config.columns.unshift({
        id: "type",
        header: { text: "Incident Type", css: { "font-size": "16px" } },
        adjust: true,
        css: { "font-weight": "bold" },
      });
      grid.refreshColumns();
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/respond52/incident_location_summary_report",
        { station_id: station_id, from_date: fromDate, to_date: toDate },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let dataArray = [];
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                let all_records_qty = 0;
                let thisType = "";
                let lastType = "";
                let y = -1;
                for (let x = 0; x < data.length; x++) {
                  all_records_qty = all_records_qty + data[x].qty;
                  thisType = data[x].incident_type;
                  if (thisType == lastType) {
                    dataArray[y][data[x].station_id] = data[x].qty;
                  } else {
                    dataArray.push({
                      type: data[x].incident_type,
                      [data[x].station_id]: data[x].qty,
                    });
                    y += 1;
                  }
                  lastType = data[x].incident_type;
                }
                grid.define("data", dataArray);
                grid.refresh();
                grid.resize();
                $$("respond52-page")
                  .$$("respond52_incident_location_summary")
                  .$$("incident_records")
                  .define(
                    "template",
                    "<div style='font-size: 17px; text-align: center'>Total Incidents: " +
                      all_records_qty +
                      "</div>",
                  );
                $$("respond52-page")
                  .$$("respond52_incident_location_summary")
                  .$$("incident_records")
                  .refresh();
                $$("loader-window").hide();
              } else {
                $$("loader-window").hide();
                webix.alert("No data to report!");
              }
            }
          },
        },
      );
  }
  function getLocationsList() {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/respond52/get_locations_list",
        {},
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let locationsArray = [];
            if (results) {
              let locations = JSON.parse(results);
              locations.forEach(function (values) {
                if (
                  values.station_id != "29" &&
                  values.station_id != "39" &&
                  values.station_id != "49"
                ) {
                  locationsArray.push({
                    id: values.station_id,
                    value: values.name,
                  });
                }
              });
              locationsArray.unshift({ id: "20", value: "Adelaide" });
              locationsArray.unshift({ id: "1", value: "-- All Stations --" });
              stationLookup = locationsArray;
              $$("respond52-page")
                .$$("respond52_incident_location_summary")
                .$$("location_filter")
                .define("options", locationsArray);
              $$("respond52-page")
                .$$("respond52_incident_location_summary")
                .$$("location_filter")
                .refresh();
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
