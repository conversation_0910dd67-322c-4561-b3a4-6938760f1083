let IncidentInfoSearchReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  function initApplication() {
    eventHandlers();
    $$("respond52-page")
      .$$("respond52_incident_info_search")
      .$$("date_filter")
      .setValue(new Date());
    let cal1Popup = $$("respond52-page")
      .$$("respond52_incident_info_search")
      .$$("date_filter")
      .getPopup()
      .getBody();
    cal1Popup.define("minDate", "2021-11-01");
    cal1Popup.refresh();
    exp_grid = $$("respond52-page")
      .$$("respond52_incident_info_search")
      .$$("incident_info_search_report_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Respond52 Incident Info Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("respond52-page")
      .$$("respond52_incident_info_search")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let incident_date = $$("respond52-page")
          .$$("respond52_incident_info_search")
          .$$("date_filter")
          .getValue();
        let incident_no = $$("respond52-page")
          .$$("respond52_incident_info_search")
          .$$("incident_no_filter")
          .getValue();
        generateIncidentInfoReport(incident_date, incident_no);
      });
    $$("respond52-page")
      .$$("respond52_incident_info_search")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        $$("loader-window").show();
        setTimeout(function () {
          if (exp_grid.count() > 0) {
            webix
              .toExcel(exp_grid, {
                filename:
                  "Respond52 Incident Info Report - (" +
                  moment().format("DD-MM-YYYY") +
                  ")",
                styles: true,
                heights: true,
              })
              .then(function () {
                $$("loader-window").hide();
              });
          } else {
            webix.alert("No data to Export!");
          }
        }, 300);
      });
  }
  function generateIncidentInfoReport(incidentDate, incidentNo) {
    let grid = $$("respond52-page")
      .$$("respond52_incident_info_search")
      .$$("incident_info_search_report_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/respond52/incident_info_search_report",
        { incident_time: incidentDate, din: incidentNo },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let position = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " (" +
                      data[x].user_id +
                      ")";
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name +
                      " (" +
                      data[x].user_id +
                      ")";
                  }
                  if (data[x].position == 1) {
                    position = "Officer";
                  } else if (data[x].position == 2) {
                    position = "Driver";
                  } else if (
                    data[x].position == 3 ||
                    data[x].position == 4 ||
                    data[x].position == 5
                  ) {
                    position = "Crew";
                  }
                  reportData.push({
                    incident_date: data[x].incident_time,
                    incident_no: data[x].din,
                    incident_type: data[x].incident_type,
                    employee: empName,
                    station_id: data[x].station_id,
                    truck_id: data[x].callsign,
                    position: position,
                  });
                }
              }
            }
            grid.define("data", reportData);
            grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
