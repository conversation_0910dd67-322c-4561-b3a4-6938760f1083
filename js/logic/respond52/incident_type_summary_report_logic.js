let IncidentTypeSummaryReport = (function () {
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("from")
      .setValue(startOfMonth);
    $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("to")
      .setValue(endOfMonth);
    $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("employee_filter")
      .setValue(1);
    let cal1Popup = $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("from")
      .getPopup()
      .getBody();
    cal1Popup.define("minDate", "2021-11-01");
    cal1Popup.refresh();
    let cal2Popup = $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("to")
      .getPopup()
      .getBody();
    cal2Popup.define("minDate", "2021-11-01");
    cal2Popup.refresh();
  }
  function eventHandlers() {
    $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let payId = $$("respond52-page")
          .$$("respond52_incident_type_summary")
          .$$("employee_filter")
          .getValue();
        let fromDate = $$("respond52-page")
          .$$("respond52_incident_type_summary")
          .$$("from")
          .getValue();
        let toDate = $$("respond52-page")
          .$$("respond52_incident_type_summary")
          .$$("to")
          .getValue();
        generateIncidentTypeChart(payId, fromDate, toDate);
      });
  }
  employees_subject.subscribe(function (data) {
    let select = $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      if (user_permission_level == 6) {
        employee_list.push({ id: user_logged_in, value: user_logged_in_name });
        $$("respond52-page")
          .$$("respond52_incident_type_summary")
          .$$("employee_filter")
          .setValue(user_logged_in);
      } else {
        employee_list.push({ id: 1, value: "-- All Employees --" });
        data.forEach(function (value) {
          employee_list.push({ id: value.id, value: value.value });
        });
      }
      select.define("options", employee_list);
      select.refresh();
    }
  });
  function generateIncidentTypeChart(payId, fromDate, toDate) {
    let grid = $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("incident_type_summary_chart");
    $$("loader-window").show();
    grid.clearAll();
    $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("incident_records")
      .define(
        "template",
        "<div style='font-size: 17px; text-align: center'>Total Incidents: 0</div>",
      );
    $$("respond52-page")
      .$$("respond52_incident_type_summary")
      .$$("incident_records")
      .refresh();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/respond52/incident_type_summary_report",
        { pay_id: payId, from_date: fromDate, to_date: toDate },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                let all_records_qty = 0;
                for (let x = 0; x < data.length; x++) {
                  all_records_qty = all_records_qty + data[x].qty;
                  reportData.push({
                    type: data[x].incident_type,
                    qty: data[x].qty,
                  });
                }
                grid.define("data", reportData);
                grid.refresh();
                $$("respond52-page")
                  .$$("respond52_incident_type_summary")
                  .$$("incident_records")
                  .define(
                    "template",
                    "<div style='font-size: 17px; text-align: center'>Total Incidents: " +
                      all_records_qty +
                      "</div>",
                  );
                $$("respond52-page")
                  .$$("respond52_incident_type_summary")
                  .$$("incident_records")
                  .refresh();
                $$("loader-window").hide();
              } else {
                $$("loader-window").hide();
                webix.alert("No data to report!");
              }
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
