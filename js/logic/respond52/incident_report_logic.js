let IncidentReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("respond52-page")
      .$$("respond52_incident")
      .$$("from")
      .setValue(startOfMonth);
    $$("respond52-page").$$("respond52_incident").$$("to").setValue(endOfMonth);
    $$("respond52-page")
      .$$("respond52_incident")
      .$$("employee_filter")
      .setValue(1);
    $$("respond52-page")
      .$$("respond52_incident")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    let cal1Popup = $$("respond52-page")
      .$$("respond52_incident")
      .$$("from")
      .getPopup()
      .getBody();
    cal1Popup.define("minDate", "2021-11-01");
    cal1Popup.refresh();
    let cal2Popup = $$("respond52-page")
      .$$("respond52_incident")
      .$$("to")
      .getPopup()
      .getBody();
    cal2Popup.define("minDate", "2021-11-01");
    cal2Popup.refresh();
    exp_grid = $$("respond52-page")
      .$$("respond52_incident")
      .$$("incident_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results filtered by Roster: " +
            exp_roster +
            " | " +
            exp_shift +
            " | " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Respond52 Incident Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("respond52-page")
      .$$("respond52_incident")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("respond52-page")
          .$$("respond52_incident")
          .$$("roster_filter")
          .getText();
        let shift = $$("respond52-page")
          .$$("respond52_incident")
          .$$("shift_filter")
          .getText();
        let location = $$("respond52-page")
          .$$("respond52_incident")
          .$$("location_filter")
          .getText();
        let payId = $$("respond52-page")
          .$$("respond52_incident")
          .$$("employee_filter")
          .getValue();
        let fromDate = $$("respond52-page")
          .$$("respond52_incident")
          .$$("from")
          .getValue();
        let toDate = $$("respond52-page")
          .$$("respond52_incident")
          .$$("to")
          .getValue();
        generateIncidentReport(
          roster,
          shift,
          location,
          payId,
          fromDate,
          toDate,
        );
      });
    $$("respond52-page")
      .$$("respond52_incident")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("respond52-page")
          .$$("respond52_incident")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("respond52-page")
          .$$("respond52_incident")
          .$$("shift_filter")
          .getText();
        exp_location = $$("respond52-page")
          .$$("respond52_incident")
          .$$("location_filter")
          .getText();
        exp_roster = exp_roster.replaceAll("--", "");
        exp_shift = exp_shift.replaceAll("--", "");
        exp_location = exp_location.replaceAll("--", "");
        $$("loader-window").show();
        setTimeout(function () {
          if (exp_grid.count() > 0) {
            webix
              .toExcel(exp_grid, {
                filename:
                  "Respond52 Incident Report - (" +
                  moment().format("DD-MM-YYYY") +
                  ")",
                styles: true,
                heights: true,
              })
              .then(function () {
                $$("loader-window").hide();
              });
          } else {
            webix.alert("No data to Export!");
          }
        }, 300);
      });
    $$("respond52-page")
      .$$("respond52_incident")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("respond52-page")
            .$$("respond52_incident")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("respond52-page")
            .$$("respond52_incident")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("respond52-page")
            .$$("respond52_incident")
            .$$("shift_filter")
            .disable();
          $$("respond52-page")
            .$$("respond52_incident")
            .$$("location_filter")
            .disable();
        } else {
          $$("respond52-page")
            .$$("respond52_incident")
            .$$("shift_filter")
            .enable();
          $$("respond52-page")
            .$$("respond52_incident")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("respond52-page")
      .$$("respond52_incident")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("respond52-page")
          .$$("respond52_incident")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  employees_subject.subscribe(function (data) {
    let select = $$("respond52-page")
      .$$("respond52_incident")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      if (user_permission_level == 6) {
        employee_list.push({ id: user_logged_in, value: user_logged_in_name });
        $$("respond52-page")
          .$$("respond52_incident")
          .$$("employee_filter")
          .setValue(user_logged_in);
      } else {
        employee_list.push({ id: 1, value: "-- All Employees --" });
        data.forEach(function (value) {
          employee_list.push({ id: value.id, value: value.value });
        });
      }
      select.define("options", employee_list);
      select.refresh();
    }
  });
  rosters_subject.subscribe(function (data) {
    let select = $$("respond52-page")
      .$$("respond52_incident")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("respond52-page")
                  .$$("respond52_incident")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("respond52-page")
                  .$$("respond52_incident")
                  .$$("shift_filter")
                  .refresh();
                $$("respond52-page")
                  .$$("respond52_incident")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("respond52-page")
                  .$$("respond52_incident")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateIncidentReport(
    roster,
    shift,
    location,
    payId,
    fromDate,
    toDate,
  ) {
    let grid = $$("respond52-page")
      .$$("respond52_incident")
      .$$("incident_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/respond52/incident_report",
        {
          roster: roster,
          shift: shift,
          location: location,
          pay_id: payId,
          from_date: fromDate,
          to_date: toDate,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let position = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].position == 1) {
                    position = "Officer";
                  } else if (data[x].position == 2) {
                    position = "Driver";
                  } else if (
                    data[x].position == 3 ||
                    data[x].position == 4 ||
                    data[x].position == 5
                  ) {
                    position = "Crew";
                  }
                  reportData.push({
                    pay_id: data[x].user_id,
                    employee: empName.toUpperCase(),
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    incident_date: data[x].incident_time,
                    incident_no: data[x].din,
                    incident_type: data[x].incident_type,
                    station_id: data[x].station_id,
                    truck_id: data[x].callsign,
                    position: position,
                  });
                }
              }
            }
            grid.define("data", reportData);
            grid.refresh();
            $$("respond52-page")
              .$$("respond52_incident")
              .$$("records_count")
              .define("template", reportData.length + " incidents found!");
            $$("respond52-page")
              .$$("respond52_incident")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
