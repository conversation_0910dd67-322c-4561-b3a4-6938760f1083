let RidingPositionsLocationReport = (function () {
  let auto_refresh;
  function initApplication() {
    eventHandlers();
    $$("respond52-page")
      .$$("respond52_riding_position_location")
      .$$("r52_station_list")
      .disableItem("34");
  }
  function eventHandlers() {
    $$("respond52-page")
      .$$("respond52_riding_position_location")
      .$$("chk_r52_auto_refresh")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv === 1) {
          start_timer_auto_refresh();
        } else {
          clearInterval(auto_refresh);
        }
      });
    $$("respond52-page")
      .$$("respond52_riding_position_location")
      .$$("r52_station_list")
      .attachEvent("onItemClick", function (id, e) {
        let station_id = $$("respond52-page")
          .$$("respond52_riding_position_location")
          .$$("r52_station_list")
          .getItem(id);
        getRidingPositionData(station_id);
      });
  }
  function getRidingPositionData(station_id) {
    $$("loader-window").show();
    let grid_at_station = $$("respond52-page")
      .$$("respond52_riding_position_location")
      .$$("r52_at_station");
    let grid_riding_position = $$("respond52-page")
      .$$("respond52_riding_position_location")
      .$$("r52_riding_positions");
    grid_at_station.clearAll();
    grid_riding_position.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/respond52/deployments",
        { station_id: station_id.id },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            if (results) {
              let reportData = JSON.parse(results);
              if (station_id.id == 38) {
                grid_riding_position.showColumn("crew_4");
                grid_riding_position.showColumn("crew_5");
                grid_riding_position.config.columns[2].header[0].text =
                  "Crew 1";
                grid_riding_position.config.columns[3].header[0].text =
                  "Crew 2";
                grid_riding_position.config.columns[4].header[0].text =
                  "Crew 3";
                grid_riding_position.config.columns[5].header[0].text =
                  "Crew 4";
                grid_riding_position.config.columns[6].header[0].text =
                  "Crew 5";
                grid_riding_position.config.columns[7].header[0].text =
                  "Crew 6";
                grid_riding_position.refreshColumns();
              } else {
                grid_riding_position.hideColumn("crew_4");
                grid_riding_position.hideColumn("crew_5");
                grid_riding_position.config.columns[2].header[0].text =
                  "Driver";
                grid_riding_position.config.columns[3].header[0].text =
                  "Crew 1";
                grid_riding_position.config.columns[4].header[0].text =
                  "Crew 2";
                grid_riding_position.config.columns[5].header[0].text =
                  "Crew 3";
                grid_riding_position.refreshColumns();
              }
              reportData.station.forEach(function (value) {
                grid_at_station.add({
                  at_station:
                    value.rank +
                    " " +
                    value.surname +
                    " " +
                    value.first_name.slice(0, 2).toUpperCase(),
                  css_theme: value.background_css,
                });
              });
              if (station_id.id == 38) {
                reportData.vehicles.forEach(function (value) {
                  grid_riding_position.add({
                    callsign: value.callsign,
                    officer:
                      value.position1.rank +
                      " " +
                      value.position1.surname +
                      " " +
                      value.position1.first_name.slice(0, 2).toUpperCase(),
                    officer_css: value.position1.background_css,
                    driver:
                      value.position2.rank +
                      " " +
                      value.position2.surname +
                      " " +
                      value.position2.first_name.slice(0, 2).toUpperCase(),
                    driver_css: value.position2.background_css,
                    crew_1:
                      value.position3.rank +
                      " " +
                      value.position3.surname +
                      " " +
                      value.position3.first_name.slice(0, 2).toUpperCase(),
                    crew_1_css: value.position3.background_css,
                    crew_2:
                      value.position4.rank +
                      " " +
                      value.position4.surname +
                      " " +
                      value.position4.first_name.slice(0, 2).toUpperCase(),
                    crew_2_css: value.position4.background_css,
                    crew_3:
                      value.position5.rank +
                      " " +
                      value.position5.surname +
                      " " +
                      value.position5.first_name.slice(0, 2).toUpperCase(),
                    crew_3_css: value.position5.background_css,
                    crew_4:
                      value.position6.rank +
                      " " +
                      value.position6.surname +
                      " " +
                      value.position6.first_name.slice(0, 2).toUpperCase(),
                    crew_4_css: value.position6.background_css,
                    crew_5:
                      value.position7.rank +
                      " " +
                      value.position7.surname +
                      " " +
                      value.position7.first_name.slice(0, 2).toUpperCase(),
                    crew_5_css: value.position7.background_css,
                  });
                });
              } else {
                reportData.vehicles.forEach(function (value) {
                  grid_riding_position.add({
                    callsign: value.callsign,
                    officer:
                      value.position1.rank +
                      " " +
                      value.position1.surname +
                      " " +
                      value.position1.first_name.slice(0, 2).toUpperCase(),
                    officer_css: value.position1.background_css,
                    driver:
                      value.position2.rank +
                      " " +
                      value.position2.surname +
                      " " +
                      value.position2.first_name.slice(0, 2).toUpperCase(),
                    driver_css: value.position2.background_css,
                    crew_1:
                      value.position3.rank +
                      " " +
                      value.position3.surname +
                      " " +
                      value.position3.first_name.slice(0, 2).toUpperCase(),
                    crew_1_css: value.position3.background_css,
                    crew_2:
                      value.position4.rank +
                      " " +
                      value.position4.surname +
                      " " +
                      value.position4.first_name.slice(0, 2).toUpperCase(),
                    crew_2_css: value.position4.background_css,
                    crew_3:
                      value.position5.rank +
                      " " +
                      value.position5.surname +
                      " " +
                      value.position5.first_name.slice(0, 2).toUpperCase(),
                    crew_3_css: value.position5.background_css,
                  });
                });
              }
              let css_style = "";
              grid_at_station.eachRow(function (row) {
                let record = grid_at_station.getItem(row);
                css_style = "r52_" + record.css_theme;
                grid_at_station.addCellCss(row, "at_station", css_style);
              });
              grid_riding_position.eachRow(function (row) {
                let record = grid_riding_position.getItem(row);
                let officer_css_style = "r52_" + record.officer_css;
                let driver_css_style = "r52_" + record.driver_css;
                let crew_1_css_style = "r52_" + record.crew_1_css;
                let crew_2_css_style = "r52_" + record.crew_2_css;
                let crew_3_css_style = "r52_" + record.crew_3_css;
                let crew_4_css_style = "r52_" + record.crew_4_css;
                let crew_5_css_style = "r52_" + record.crew_5_css;
                grid_riding_position.addCellCss(
                  row,
                  "callsign",
                  "r52_theme_callsign",
                );
                grid_riding_position.addCellCss(
                  row,
                  "officer",
                  officer_css_style,
                );
                grid_riding_position.addCellCss(
                  row,
                  "driver",
                  driver_css_style,
                );
                grid_riding_position.addCellCss(
                  row,
                  "crew_1",
                  crew_1_css_style,
                );
                grid_riding_position.addCellCss(
                  row,
                  "crew_2",
                  crew_2_css_style,
                );
                grid_riding_position.addCellCss(
                  row,
                  "crew_3",
                  crew_3_css_style,
                );
                if (station_id.id == 38) {
                  grid_riding_position.addCellCss(
                    row,
                    "crew_4",
                    crew_4_css_style,
                  );
                  grid_riding_position.addCellCss(
                    row,
                    "crew_5",
                    crew_5_css_style,
                  );
                }
              });
              grid_riding_position.sort("callsign", "asc", "string");
              grid_riding_position.markSorting("callsign", "asc");
              $$("loader-window").hide();
            } else {
              $$("loader-window").hide();
            }
          },
        },
      );
  }
  function start_timer_auto_refresh() {
    auto_refresh = setInterval(function () {
      let station_id = $$("respond52-page")
        .$$("respond52_riding_position_location")
        .$$("r52_station_list")
        .getSelectedItem();
      if (station_id != "") {
        getRidingPositionData(station_id);
      }
    }, 6e4);
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
