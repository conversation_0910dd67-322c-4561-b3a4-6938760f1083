let locationGroups = (function () {
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("location_groups")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        $$("formGroups").clear();
        $$("location_groups-popup").show();
      });
    $$("btn_grp_close").attachEvent("onItemClick", function (id, e) {
      $$("formGroups").clear();
      $$("formGroups").clearValidation();
      $$("location_groups-popup").hide();
    });
    $$("btn_grp_save").attachEvent("onItemClick", function (id, e) {
      createGroupLocation();
    });
    $$("admin-page")
      .$$("location_groups")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteGroupLocation(id);
      });
    $$("admin-page")
      .$$("location_groups")
      .$$("grid_groups")
      .attachEvent("onAfterEditStop", function (state, editor, ignoreUpdate) {
        if (state.value !== state.old) {
          updateGroupLocations(editor.row, state.value);
        }
      });
    $$("admin-page")
      .$$("location_groups")
      .$$("grid_groups")
      .attachEvent("onResize", function (width, height) {
        $$("admin-page")
          .$$("location_groups")
          .$$("grid_groups")
          .adjustRowHeight("locations", true);
      });
    loadLocationGroups();
  }
  locations_subject.subscribe(function (data) {
    let grid = $$("admin-page").$$("location_groups").$$("grid_groups");
    let results = JSON.parse(data);
    let locations = [];
    for (let x = 0; x < results.length; x++) {
      locations.push({ id: results[x].name, value: results[x].name });
    }
    grid.config.columns = [
      { id: "id", hidden: true },
      { id: "group_name", width: 200, header: "Group Name", sort: "string" },
      {
        id: "locations",
        header: "Locations",
        fillspace: true,
        optionslist: true,
        options: locations,
        editor: "multiselect",
      },
    ];
    grid.refreshColumns();
  });
  location_groups_subject.subscribe(function (data) {
    $$("admin-page").$$("location_groups").$$("grid_groups").parse(data);
  });
  function deleteGroupLocation(id) {
    let selected_row = $$("admin-page")
      .$$("location_groups")
      .$$("grid_groups")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Location Group",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Location Group?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/location_groups",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadLocationGroups();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Location Group has been selected!");
    }
  }
  function updateGroupLocations(id, locations) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/location_groups",
        { id: id, locations: locations },
        {
          error: function (err) {},
          success: function () {
            loadLocationGroups();
          },
        },
      );
  }
  function createGroupLocation() {
    if ($$("formGroups").validate()) {
      let form = $$("formGroups").getValues();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .post(
          server_url + "/admin/location_groups",
          { group_name: form.group_name },
          {
            error: function (err) {},
            success: function () {
              $$("location_groups-popup").hide();
              loadLocationGroups();
            },
          },
        );
    }
  }
  function loadLocationGroups() {
    $$("admin-page").$$("location_groups").$$("grid_groups").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/location_groups",
        {},
        {
          error: function (err) {},
          success: function (results) {
            location_groups_subject.next(results);
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
