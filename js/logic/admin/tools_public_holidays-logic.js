let publicHolidays = (function () {
  let edit_mode;
  function initApplication() {
    eventHandlers();
    loadPublicHolidays();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("tools_public_holidays")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        addPublicHoliday();
      });
    $$("btn_publicHolidays_close").attachEvent("onItemClick", function (id, e) {
      $$("publicHolidays-popup").hide();
    });
    $$("admin-page")
      .$$("tools_public_holidays")
      .$$("grid-publicHolidays")
      .attachEvent("onItemDblClick", function (id) {
        edit_mode = "EDIT";
        let selected_row = $$("admin-page")
          .$$("tools_public_holidays")
          .$$("grid-publicHolidays")
          .getSelectedItem(id);
        if (selected_row !== undefined) {
          if (selected_row[0].id > 0) {
            let row_id = selected_row[0].id;
            getPublicHolidayInfo(row_id, id);
          }
        }
      });
    $$("btn_public_holiday_save").attachEvent("onItemClick", function (id, e) {
      savePublicHoliday();
    });
    $$("admin-page")
      .$$("tools_public_holidays")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deletePublicHoliday(id);
      });
  }
  function addPublicHoliday() {
    edit_mode = "ADD";
    $$("publicHolidays-label").define(
      "label",
      "<span class='header_font'>Add Public Holiday</span>",
    );
    $$("publicHolidays-label").refresh();
    $$("formPublicHolidays").clear();
    $$("formPublicHolidays").setValues({
      public_holiday_date: moment().toDate(),
      public_holiday_state: "SA",
    });
    $$("publicHolidays-popup").show();
  }
  function getPublicHolidayInfo(row_id, selected_id) {
    let selected_row = $$("admin-page")
      .$$("tools_public_holidays")
      .$$("grid-publicHolidays")
      .getSelectedItem(selected_id);
    let rowId = selected_row[0].id;
    let phDate = moment(selected_row[0].date, "DD/MM/YYYY").toDate();
    let name = selected_row[0].name;
    let state = selected_row[0].state;
    $$("public_holiday_id").setValue(rowId);
    $$("public_holiday_date").setValue(phDate);
    $$("public_holiday_name").setValue(name);
    $$("public_holiday_state").setValue(state);
    $$("publicHolidays-label").define(
      "label",
      "<span class='header_font'>Edit Public Holiday</span>",
    );
    $$("publicHolidays-label").refresh();
    $$("publicHolidays-popup").show();
  }
  function loadPublicHolidays() {
    let grid = $$("admin-page")
      .$$("tools_public_holidays")
      .$$("grid-publicHolidays");
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/public_holidays",
        {},
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let values = JSON.parse(results);
              values.forEach(function (result) {
                grid.add({
                  id: result.id,
                  date: result.date,
                  weekday: result.weekday,
                  name: result.name,
                  state: result.state,
                });
              });
            }
          },
        },
      );
  }
  function savePublicHoliday() {
    if ($$("formPublicHolidays").validate()) {
      let form = $$("formPublicHolidays").getValues();
      if (edit_mode === "ADD") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/admin/public_holidays",
            {
              date: form.public_holiday_date,
              date_string: moment(form.public_holiday_date).format("YYYYMMDD"),
              weekday: moment(form.public_holiday_date).format("dddd"),
              name: form.public_holiday_name,
              state: form.public_holiday_state,
            },
            {
              error: function (err) {},
              success: function () {
                $$("publicHolidays-popup").hide();
                loadPublicHolidays();
              },
            },
          );
      } else if (edit_mode === "EDIT") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .put(
            server_url + "/admin/public_holidays",
            {
              id: form.public_holiday_id,
              date: form.public_holiday_date,
              date_string: moment(form.public_holiday_date).format("YYYYMMDD"),
              weekday: moment(form.public_holiday_date).format("dddd"),
              name: form.public_holiday_name,
              state: form.public_holiday_state,
            },
            {
              error: function (err) {},
              success: function () {
                $$("publicHolidays-popup").hide();
                loadPublicHolidays();
              },
            },
          );
      }
    }
  }
  function deletePublicHoliday(id) {
    let selected_row = $$("admin-page")
      .$$("tools_public_holidays")
      .$$("grid-publicHolidays")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Public Holiday",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Public Holiday?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/public_holidays",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadPublicHolidays();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Public Holiday has been selected!");
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
