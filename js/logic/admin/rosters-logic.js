let rosters = (function () {
  let locations = [];
  let shifts = [];
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("rosters")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        $$("formRosters").clear();
        $$("rosters-popup").show();
      });
    $$("btn_roster_close").attachEvent("onItemClick", function (id, e) {
      $$("formRosters").clear();
      $$("formRosters").clearValidation();
      $$("rosters-popup").hide();
    });
    $$("btn_roster_save").attachEvent("onItemClick", function (id, e) {
      createRoster();
    });
    $$("admin-page")
      .$$("rosters")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteRoster(id);
      });
    $$("admin-page")
      .$$("rosters")
      .$$("grid-rosters")
      .attachEvent("onAfterEditStop", function (state, editor, ignoreUpdate) {
        if (state.value !== state.old) {
          let data = $$("admin-page")
            .$$("rosters")
            .$$("grid-rosters")
            .getItem(editor.row);
          updateRoster(editor.row, data.shifts, data.locations);
        }
      });
    $$("admin-page")
      .$$("rosters")
      .$$("grid-rosters")
      .attachEvent("onResize", function (width, height) {
        $$("admin-page")
          .$$("rosters")
          .$$("grid-rosters")
          .adjustRowHeight("locations", true);
      });
    loadRosters();
  }
  locations_subject.subscribe(function (data) {
    let results = JSON.parse(data);
    for (let x = 0; x < results.length; x++) {
      locations.push({
        id: results[x].station_id + " - " + results[x].name,
        value: results[x].name,
      });
    }
    refreshShiftsLocations();
  });
  shifts_subject.subscribe(function (data) {
    let results = JSON.parse(data);
    for (let x = 0; x < results.length; x++) {
      shifts.push({ id: results[x].shift_name, value: results[x].shift_name });
    }
    refreshShiftsLocations();
  });
  rosters_subject.subscribe(function (data) {
    $$("admin-page").$$("rosters").$$("grid-rosters").parse(data);
  });
  function loadRosters() {
    $$("admin-page").$$("rosters").$$("grid-rosters").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/rosters",
        {},
        {
          error: function (err) {},
          success: function (results) {
            rosters_subject.next(results);
          },
        },
      );
  }
  function createRoster() {
    if ($$("formRosters").validate()) {
      let form = $$("formRosters").getValues();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .post(
          server_url + "/admin/rosters",
          { roster_name: form.roster_name },
          {
            error: function (err) {},
            success: function () {
              $$("rosters-popup").hide();
              loadRosters();
            },
          },
        );
    }
  }
  function updateRoster(id, shifts, locations) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/rosters",
        { id: id, shifts: shifts, locations: locations },
        {
          error: function (err) {},
          success: function () {
            loadRosters();
          },
        },
      );
  }
  function deleteRoster(id) {
    let selected_row = $$("admin-page")
      .$$("rosters")
      .$$("grid-rosters")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Roster",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Roster?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/rosters",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadRosters();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Roster has been selected!");
    }
  }
  function refreshShiftsLocations() {
    let grid = $$("admin-page").$$("rosters").$$("grid-rosters");
    grid.config.columns = [
      { id: "id", hidden: true },
      { id: "roster_name", adjust: true, header: "Name", sort: "string" },
      {
        id: "shifts",
        header: "Shifts",
        optionslist: true,
        options: shifts,
        editor: "multiselect",
        fillspace: 2,
      },
      {
        id: "locations",
        header: "Locations",
        optionslist: true,
        options: locations,
        editor: "multiselect",
        fillspace: 1,
      },
    ];
    grid.refreshColumns();
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
