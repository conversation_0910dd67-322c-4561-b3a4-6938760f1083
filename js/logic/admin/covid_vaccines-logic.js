let covidVaccines = (function () {
  let loadingRecord = false;
  let recordMode = "save";
  function initApplication() {
    eventHandlers();
    setTimeout(function () {
      getUserPL(function (result) {
        if (result === 1) {
          $$("vax_status").show();
          $$("exemption_date").show();
        } else {
          $$("vax_status").hide();
          $$("exemption_date").hide();
        }
      })
    }, 1500);
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("booster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        loadCovidVaccines(function (response) {});
        if (newv == 1) {
          $$("admin-page")
            .$$("covid_vaccines")
            .$$("btn_send_sms_to_unvaxxed")
            .hide();
        } else {
          $$("admin-page")
            .$$("covid_vaccines")
            .$$("btn_send_sms_to_unvaxxed")
            .show();
        }
      });
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("sms_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let grid = $$("admin-page")
          .$$("covid_vaccines")
          .$$("grid-covidVaccines");
        $$("admin-page")
          .$$("covid_vaccines")
          .$$("pay_id_search_field")
          .setValue("");
        $$("admin-page")
          .$$("covid_vaccines")
          .$$("name_search_field")
          .setValue("");
        if (newv != "All Records") {
          $$("admin-page")
            .$$("covid_vaccines")
            .$$("pay_id_search_field")
            .disable();
          $$("admin-page")
            .$$("covid_vaccines")
            .$$("name_search_field")
            .disable();
        } else {
          $$("admin-page")
            .$$("covid_vaccines")
            .$$("pay_id_search_field")
            .enable();
          $$("admin-page")
            .$$("covid_vaccines")
            .$$("name_search_field")
            .enable();
        }
        loadCovidVaccines(function (response) {
          if (response === "ok") {
            if (newv != "All Records") {
              grid.filter((obj) => sms_filter_status(obj));
            }
          }
        });
      });
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("btn_send_sms_to_all")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1) {
          let grid = $$("admin-page")
            .$$("covid_vaccines")
            .$$("grid-covidVaccines");
          let recipients_array = [];
          let sel_count = 0;
          let sms_filter = $$("admin-page")
            .$$("covid_vaccines")
            .$$("sms_filter")
            .getValue();
          if (sms_filter == "All Records") {
            grid.eachRow(function (row) {
              const recipients_object = {};
              let sms_msg = "";
              let curr_date = moment();
              let first_date = moment(
                grid.getItem(row).first_dose_date,
                "DD/MM/YYYY",
              );
              let second_date = moment(
                grid.getItem(row).second_dose_date,
                "DD/MM/YYYY",
              );
              if (grid.getItem(row).first_dose_date == "") {
                sms_msg = "NOT COMPLETE - No vaccinations received!";
              } else if (
                grid.getItem(row).first_dose_date != "" &&
                grid.getItem(row).second_dose_date == ""
              ) {
                if (curr_date.diff(first_date, "days", false) > 183) {
                  sms_msg = "NOT COMPLETE - 2nd vaccination not received!";
                } else {
                  sms_msg = "NOT COMPLETE - Waiting on 2nd vaccine!";
                }
              } else if (
                grid.getItem(row).first_dose_date != "" &&
                grid.getItem(row).second_dose_date != "" &&
                grid.getItem(row).booster_date == ""
              ) {
                if (curr_date.diff(second_date, "days", false) > 183) {
                  sms_msg = "NOT COMPLETE - Booster vaccination not received!";
                } else {
                  if (
                    grid.getItem(row).cert_received == "Yes" &&
                    grid.getItem(row).form_received == "Yes"
                  ) {
                    sms_msg =
                      "COMPLETE - You have met all current vaccination requirements!";
                  } else if (
                    grid.getItem(row).cert_received == "Yes" &&
                    grid.getItem(row).form_received == "No"
                  ) {
                    sms_msg = "NOT COMPLETE - Waiting on vaccination form!";
                  } else if (
                    grid.getItem(row).cert_received == "No" &&
                    grid.getItem(row).form_received == "Yes"
                  ) {
                    sms_msg = "NOT COMPLETE - Waiting on digital certificate!";
                  } else if (
                    grid.getItem(row).cert_received == "No" &&
                    grid.getItem(row).form_received == "No"
                  ) {
                    sms_msg =
                      "NOT COMPLETE - Waiting on digital certificate & vaccination form!";
                  }
                }
              } else if (
                grid.getItem(row).first_dose_date != "" &&
                grid.getItem(row).second_dose_date != "" &&
                grid.getItem(row).booster_date != ""
              ) {
                if (
                  grid.getItem(row).cert_received == "Yes" &&
                  grid.getItem(row).form_received == "Yes"
                ) {
                  sms_msg =
                    "COMPLETE - You have met all current vaccination requirements!";
                } else if (
                  grid.getItem(row).cert_received == "Yes" &&
                  grid.getItem(row).form_received == "No"
                ) {
                  sms_msg = "NOT COMPLETE - Waiting on vaccination form!";
                } else if (
                  grid.getItem(row).cert_received == "No" &&
                  grid.getItem(row).form_received == "Yes"
                ) {
                  sms_msg = "NOT COMPLETE - Waiting on digital certificate!";
                } else if (
                  grid.getItem(row).cert_received == "No" &&
                  grid.getItem(row).form_received == "No"
                ) {
                  sms_msg =
                    "NOT COMPLETE - Waiting on digital certificate & vaccination form!";
                }
              }
              recipients_object.pay_id = grid.getItem(row).pay_id;
              recipients_object.phone_number = grid.getItem(row).phone_number;
              recipients_object.message = sms_msg;
              if (grid.getItem(row).phone_number != "") {
                recipients_array.push(recipients_object);
                sel_count += 1;
              }
            });
          } else {
            grid.eachRow(function (row) {
              const recipients_object = {};
              let sms_msg = "";
              if (sms_filter == "Booster Shot") {
                sms_msg =
                  "Vaccination status: You are currently recorded as non-compliant due to an outstanding booster. You must within the prescribed time (four months), receive, or have evidence of a booking to receive, a third dose (booster) of a TGA approved COVID-19 vaccine. Please review and update your records immediately by sending <NAME_EMAIL>";
              } else if (sms_filter == "Vax Form") {
                sms_msg =
                  "Vaccination status: You are currently recorded as non-compliant due to an outstanding MFS vaccination form booster. Please review and update your records immediately by sending a completed MFS vaccination <NAME_EMAIL>";
              } else if (sms_filter == "Digital Certificate") {
                sms_msg =
                  "Vaccination status: You are currently recorded as non-compliant due to an outstanding Covid-19 digital certificate. Please review and update your records immediately by sending the appropriate Covid-19 digital <NAME_EMAIL>";
              } else if (sms_filter == "Vax Form & Digital Certificate") {
                sms_msg =
                  "Vaccination status: You are currently recorded as non-compliant due to an outstanding MFS vaccination form and Covid-19 digital certificate. Please review and update your records immediately by sending a completed MFS vaccination form and the appropriate Covid-19 digital <NAME_EMAIL>";
              } else if (sms_filter == "Booster Expired") {
                sms_msg =
                  "You have advised that you were scheduled to receive your Booster dose on " +
                  grid.getItem(row).booster_date +
                  ". Notification confirming vaccination has not been received. Please email Digital Covid-19 Certificate or reason why it was not received to: <EMAIL> within 7 days.";
              } else if (sms_filter == "2nd Dose Expired") {
                sms_msg =
                  "You have advised that you were scheduled to receive your 2nd dose on " +
                  grid.getItem(row).second_dose_date +
                  ". Notification confirming vaccination has not been received. Please email Digital Covid-19 Certificate or reason why it was not received to: <EMAIL> within 7 days.";
              } else if (sms_filter == "Booster Final Notice") {
                sms_msg =
                  "You have not sent evidence of having received your Booster dose within the time specified. Failure to send evidence within 7 days will result in you becoming non-compliant and you will be removed from active duty.";
              } else if (sms_filter == "2nd Dose Final Notice") {
                sms_msg =
                  "You have not sent evidence of having received your 2nd dose within the time specified. Failure to send evidence within 7 days will result in you becoming non-compliant and you will be removed from active duty.";
              } else if (sms_filter == "Booster Required") {
                sms_msg =
                  "Covid-19 Vaccination status: You are currently recorded as non-compliant due to not having received a Covid-19 Booster within the prescribed 4 months of your 2nd Dose. If you have received a booster you need to update you records by sending an updated Covid-19 Digital <NAME_EMAIL>" +
                  "\n" +
                  "Note: You are not able to attend SAMFS premises until your records are updated and you become compliant.";
              } else if (sms_filter == "Booster Required in 7 days") {
                sms_msg =
                  "Your COVID Booster is due within the next 7 days. Failure to receive it and send your Covid Digital <NAME_EMAIL> will result in you being non-compliant and unable to attend MFS premises.";
              } else if (sms_filter == "Booster Expiring in 7 days") {
                sms_msg =
                  "Your COVID Booster is due within the next 7 days. Failure to receive it and send your Covid Digital <NAME_EMAIL> will result in you being non-compliant and unable to attend MFS premises.";
              } else if (sms_filter == "Exemption Expiring in 7 days") {
                sms_msg =
                  "Your medical exemption expires on " +
                  grid.getItem(row).exemption_date +
                  ", without evidence of a further medical exemption or being up to date with your Covid-19 vaccinations. Your vaccination status will be updated to not-compliant (NC) within SAPPHIRE and you will be subject to managerial directions 3a and 3b from the above date.";
              }
              recipients_object.pay_id = grid.getItem(row).pay_id;
              recipients_object.phone_number = grid.getItem(row).phone_number;
              recipients_object.message = sms_msg;
              if (grid.getItem(row).phone_number != "") {
                if (
                  sms_filter == "Booster Expired" &&
                  grid.getItem(row).booster_dose_expired_sms_sent == "Yes"
                ) {
                } else if (
                  sms_filter == "2nd Dose Expired" &&
                  grid.getItem(row).second_dose_expired_sms_sent == "Yes"
                ) {
                } else if (
                  sms_filter == "Booster Final Notice" &&
                  grid.getItem(row).booster_dose_final_sms_sent == "Yes"
                ) {
                } else if (
                  sms_filter == "2nd Dose Final Notice" &&
                  grid.getItem(row).second_dose_final_sms_sent == "Yes"
                ) {
                } else if (
                  sms_filter == "Booster Required" &&
                  grid.getItem(row).booster_required_sms_sent == "Yes"
                ) {
                } else if (
                  sms_filter == "Booster Required in 7 days" &&
                  grid.getItem(row).booster_required_warning_sms_sent == "Yes"
                ) {
                } else if (
                  sms_filter == "Booster Expiring in 7 days" &&
                  grid.getItem(row).booster_expiring_warning_sms_sent == "Yes"
                ) {
                } else if (
                  sms_filter == "Exemption Expiring in 7 days" &&
                  grid.getItem(row).exemption_expiring_warning_sms_sent == "Yes"
                ) {
                } else {
                  recipients_array.push(recipients_object);
                  sel_count += 1;
                }
              }
            });
          }
          if (sel_count > 0) {
            webix.confirm({
              title: "Confirm sending bulk SMS message",
              ok: "Yes",
              cancel: "No",
              width: 550,
              text:
                "You are about to send an SMS message to " +
                sel_count +
                " employee(s).</br></br>Do you want to Proceed?",
              callback: function (result) {
                switch (result) {
                  case true:
                    $$("loader-window").show();
                    webix
                      .ajax()
                      .headers({ Authorization: "Bearer " + api_key })
                      .post(
                        server_url + "/messaging/send_C19_messages",
                        {
                          subject: "SAPPHIRE",
                          recipients_array: recipients_array,
                          sms_filter: sms_filter,
                        },
                        {
                          error: function (err) {
                            if (
                              err == "Forbidden" ||
                              err == "Too many requests"
                            ) {
                              webix.alert({
                                text:
                                  "Error: " +
                                  err +
                                  " - You have reached the daily or bulk SMS send limit!",
                                width: 650,
                              });
                            } else {
                              webix.alert(
                                "There was an error sending one or more SMS messages!",
                              );
                            }
                          },
                          success: function (results) {
                            setTimeout(function () {
                              loadCovidVaccines(function (response) {
                                if (response == "ok") {
                                  if (sms_filter != "All Records") {
                                    grid.filter((obj) =>
                                      sms_filter_status(obj),
                                    );
                                    $$("loader-window").hide();
                                    webix.alert(
                                      "SMS message has been sent successfully!",
                                    );
                                  } else {
                                    $$("loader-window").hide();
                                    webix.alert(
                                      "SMS message has been sent successfully!",
                                    );
                                  }
                                }
                              });
                            }, 5e3);
                          },
                        },
                      );
                }
              },
            });
          } else {
            webix.alert("There are no records to send an SMS message to!");
          }
        } else {
          webix.alert("You don't have permission to use this function!");
        }
      });
    $$("digital_cert_received").attachEvent("onChange", function (newv, oldv) {
      let vax_form_received = $$("vax_form_received").getValue();
      if (loadingRecord === false) {
        if (newv == 1 && vax_form_received == 1) {
          $$("verified_record").setValue(1);
        } else {
          $$("verified_record").setValue(0);
        }
      }
    });
    $$("vax_form_received").attachEvent("onChange", function (newv, oldv) {
      let digital_cert_received = $$("digital_cert_received").getValue();
      if (loadingRecord === false) {
        if (newv == 1 && digital_cert_received == 1) {
          $$("verified_record").setValue(1);
        } else {
          $$("verified_record").setValue(0);
        }
      }
    });
    $$("covid_vaccines_employee").attachEvent(
      "onChange",
      function (newv, oldv) {
        getRADetails(
          newv,
          moment().format("YYYYMMDD"),
          true,
          function (results) {
            let curr_roster = "";
            if (results.length > 0) {
              curr_roster =
                "<div style='font-size: 14px; text-align: left; line-height: 20px'><strong>Current Roster:</strong> " +
                results[0].roster +
                "</br><strong>Current Shift:</strong> " +
                results[0].shift +
                "</br><strong>Current Location:</strong> " +
                results[0].location +
                "</div>";
            } else {
              curr_roster =
                "<div style='font-size: 14px; text-align: left; line-height: 20px'><strong>Current Roster:</strong> N/A</br><strong>Current Shift:</strong> N/A</br><strong>Current Location:</strong> N/A</div>";
            }
            $$("current_ra").define("template", curr_roster);
            $$("current_ra").refresh();
          },
        );
      },
    );
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("name_search_field")
      .attachEvent("onTimedKeyPress", function () {
        const value = this.getValue().toUpperCase();
        let filtered;
        $$("admin-page")
          .$$("covid_vaccines")
          .$$("grid-covidVaccines")
          .filter(function (obj) {
            if (obj.employee.indexOf(value) !== -1)
              filtered = obj.employee.indexOf(value) !== -1;
            return obj.employee.indexOf(value) !== -1;
          });
        if (!filtered) webix.message("No matching records!");
      });
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("pay_id_search_field")
      .attachEvent("onTimedKeyPress", function () {
        const value = this.getValue().toString();
        let filtered;
        let pid_length = value.length;
        $$("admin-page")
          .$$("covid_vaccines")
          .$$("grid-covidVaccines")
          .filter(function (obj) {
            if (
              obj.pay_id.toString().substring(0, pid_length).indexOf(value) !==
              -1
            )
              filtered =
                obj.pay_id
                  .toString()
                  .substring(0, pid_length)
                  .indexOf(value) !== -1;
            return (
              obj.pay_id.toString().substring(0, pid_length).indexOf(value) !==
              -1
            );
          });
        if (!filtered) webix.message("No matching records!");
      });
    $$("first_dose_type").attachEvent("onChange", function (newv, oldv) {
      let sdt = $$("second_dose_type").getText();
      if (loadingRecord === false) {
        if (newv == " ") {
          $$("first_dose_date").setValue("");
        } else {
          $$("first_dose_date").setValue(new Date());
        }
        if (
          (newv === "Vaxzevria (AstraZeneca)" ||
            newv === "Comirnaty (Pfizer)" ||
            newv === "Spikevax (Moderna)") &&
          (sdt === "Vaxzevria (AstraZeneca)" ||
            sdt === "Comirnaty (Pfizer)" ||
            sdt === "Spikevax (Moderna)")
        ) {
          $$("vax_status").setValue("Vaccinated");
        } else {
          $$("vax_status").setValue("Unvaccinated");
        }
      }
    });
    $$("vax_status").attachEvent("onChange", function (newv, oldv) {
      if (user_permission_level === 1) {
        if (newv == "Medically Exempt" || newv == "Medically Exempt Pending") {
          $$("exemption_date").show();
          $$("exemption_date").setValue(new Date());
          if (newv == "Medically Exempt") {
            $$("exemption_date").define("label", "Expiry Date");
            $$("exemption_date").refresh();
          } else if (newv == "Medically Exempt Pending") {
            $$("exemption_date").define("label", "Recorded Date");
            $$("exemption_date").refresh();
          }
        } else {
          $$("exemption_date").hide();
          $$("exemption_date").setValue("");
        }
        webix.html.removeCss($$("vax_status").getNode(), "vax_status_red");
        webix.html.removeCss($$("vax_status").getNode(), "vax_status_green");
        webix.html.removeCss($$("vax_status").getNode(), "vax_status_exempt");
        if (newv == "Unvaccinated") {
          $$("vax_status").define("css", "vax_status_red");
          $$("vax_status").refresh();
        } else if (newv == "Vaccinated") {
          $$("vax_status").define("css", "vax_status_green");
          $$("vax_status").refresh();
        } else if (
          newv == "Medically Exempt" ||
          newv == "Medically Exempt Pending"
        ) {
          $$("vax_status").define("css", "vax_status_exempt");
          $$("vax_status").refresh();
        }
      } else {
        $$("vax_status").hide();
        $$("exemption_date").hide();
      }
    });
    $$("second_dose_type").attachEvent("onChange", function (newv, oldv) {
      let fdt = $$("first_dose_type").getText();
      if (loadingRecord === false) {
        if (newv == " ") {
          $$("second_dose_date").setValue("");
        } else {
          $$("second_dose_date").setValue(new Date());
        }
        if (
          (newv === "Vaxzevria (AstraZeneca)" ||
            newv === "Comirnaty (Pfizer)" ||
            newv === "Spikevax (Moderna)") &&
          (fdt === "Vaxzevria (AstraZeneca)" ||
            fdt === "Comirnaty (Pfizer)" ||
            fdt === "Spikevax (Moderna)")
        ) {
          $$("vax_status").setValue("Vaccinated");
        } else {
          $$("vax_status").setValue("Unvaccinated");
        }
      }
    });
    $$("booster_type").attachEvent("onChange", function (newv, oldv) {
      if (loadingRecord === false) {
        if (newv == " ") {
          $$("booster_date").setValue("");
        } else {
          $$("booster_date").setValue(new Date());
        }
      }
    });
    $$("booster_type_2").attachEvent("onChange", function (newv, oldv) {
      if (loadingRecord === false) {
        if (newv == " ") {
          $$("booster_date_2").setValue("");
        } else {
          $$("booster_date_2").setValue(new Date());
        }
      }
    });
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("grid-covidVaccines")
      .attachEvent("onItemDblClick", function (id) {
        recordMode = "edit";
        let selected_row = $$("admin-page")
          .$$("covid_vaccines")
          .$$("grid-covidVaccines")
          .getSelectedItem();
        if (selected_row) {
          getVaxRecord(selected_row.id, null);
        }
      });
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        recordMode = "save";
        $$("vax_record_label").define(
          "label",
          "<span class='header_font'>Add COVID Vaccine Record</span>",
        );
        $$("vax_record_label").refresh();
        $$("covid_vaccines_employee").enable();
        $$("btn_covid_vaccines_save").define("label", "Save");
        $$("btn_covid_vaccines_save").refresh();
        $$("formCovidVaccines").clear();
        $$("covid_vaccines-popup").show();
      });
    $$("btn_covid_vaccines_close").attachEvent("onItemClick", function (id, e) {
      $$("covid_vaccines-popup").hide();
      $$("app_sub_menu").hide();
      $$("app_sub_menu_list").unselect("vax_status");
    });
    $$("btn_covid_vaccines_save").attachEvent("onItemClick", function (id, e) {
      if (recordMode === "save") {
        saveVaccineEntry();
      } else if (recordMode === "edit") {
        editVaccineEntry();
      }
    });
    $$("admin-page")
      .$$("covid_vaccines")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteVaccineEntry();
      });
    $$("future_booking").attachEvent("onChange", function (newv, oldv) {
      if (newv == 1 || newv == true) {
        $$("first_dose_date").disable();
        $$("second_dose_date").disable();
        $$("booster_date").disable();
        $$("first_dose_type").setValue("");
        $$("second_dose_type").setValue("");
        $$("booster_type").setValue("");
        $$("booster_type_2").setValue("");
        $$("future_booking_date").setValue(new Date());
        $$("first_dose_type").disable();
        $$("second_dose_type").disable();
        $$("booster_type").disable();
        $$("booster_type_2").disable();
        $$("vax_status").define("readonly", true);
        $$("vax_status").refresh();
        $$("vax_status").setValue("Unvaccinated");
      } else {
        $$("first_dose_date").enable();
        $$("second_dose_date").enable();
        $$("booster_date").enable();
        $$("booster_date_2").enable();
        $$("first_dose_type").enable();
        $$("second_dose_type").enable();
        $$("booster_type").enable();
        $$("booster_type_2").enable();
        $$("vax_status").define("readonly", false);
        $$("vax_status").refresh();
        $$("vax_status").setValue("");
        $$("future_booking_date").setValue("");
      }
    });
  }
  employees_subject.subscribe(function (data) {
    let select = $$("covid_vaccines_employee");
    let employee_list = [];
    if (data) {
      data.forEach(function (value) {
        employee_list.push({
          id: value.id,
          value: value.value + " (" + value.id + ")",
        });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  function checkExistingRecord(payId, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/check_vax_record",
        { pay_id: payId },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            if (results) {
              let values = JSON.parse(results);
              if (values.length > 0) {
                callback("yes");
              } else {
                callback("no");
              }
            } else {
              callback("no");
            }
          },
        },
      );
  }
  function getVaxRecord(id, payId) {
    $$("formCovidVaccines").clear();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/get_vax_record",
        { id: id, pay_id: payId },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let records = JSON.parse(results);
              $$("covid_vaccines_id").setValue(records[0].id);
              $$("vax_record_label").define(
                "label",
                "<span class='header_font'>Edit COVID Vaccine Record</span>",
              );
              $$("vax_record_label").refresh();
              $$("btn_covid_vaccines_save").define("label", "Update");
              $$("btn_covid_vaccines_save").refresh();
              $$("covid_vaccines-popup").show();
              loadingRecord = true;
              $$("covid_vaccines_employee").setValue(records[0].pay_id);
              $$("covid_vaccines_employee").disable();
              $$("first_dose_type").setValue(records[0].first_dose_type);
              $$("first_dose_date").setValue(
                moment(records[0].first_dose_date, "DD/MM/YYYY").toDate(),
              );
              $$("second_dose_type").setValue(records[0].second_dose_type);
              $$("second_dose_date").setValue(
                moment(records[0].second_dose_date, "DD/MM/YYYY").toDate(),
              );
              $$("booster_type").setValue(records[0].booster_type);
              $$("booster_date").setValue(
                moment(records[0].booster_date, "DD/MM/YYYY").toDate(),
              );
              $$("booster_type_2").setValue(records[0].booster_type_2);
              $$("booster_date_2").setValue(
                moment(records[0].booster_date_2, "DD/MM/YYYY").toDate(),
              );
              $$("vax_status").setValue(records[0].status);
              $$("vax_comments").setValue(records[0].comments);
              $$("future_booking").setValue(records[0].future_booking);
              $$("future_booking_date").setValue(
                moment(records[0].future_booking_date, "DD/MM/YYYY").toDate(),
              );
              $$("verified_record").setValue(records[0].verified);
              $$("digital_cert_received").setValue(
                records[0].digital_cert_received,
              );
              $$("vax_form_received").setValue(records[0].vax_form_received);
              if (
                records[0].status == "Medically Exempt" ||
                records[0].status == "Medically Exempt Pending"
              ) {
                $$("exemption_date").show();
                $$("exemption_date").setValue(
                  moment(records[0].exemption_date, "DD/MM/YYYY").toDate(),
                );
                if (records[0].status == "Medically Exempt") {
                  $$("exemption_date").define("label", "Expiry Date");
                  $$("exemption_date").refresh();
                } else if (records[0].status == "Medically Exempt Pending") {
                  $$("exemption_date").define("label", "Recorded Date");
                  $$("exemption_date").refresh();
                }
              } else {
                $$("exemption_date").hide();
                $$("exemption_date").setValue("");
              }
              webix.html.removeCss(
                $$("vax_status").getNode(),
                "vax_status_red",
              );
              webix.html.removeCss(
                $$("vax_status").getNode(),
                "vax_status_green",
              );
              webix.html.removeCss(
                $$("vax_status").getNode(),
                "vax_status_exempt",
              );
              if (records[0].status == "Unvaccinated") {
                $$("vax_status").define("css", "vax_status_red");
                $$("vax_status").refresh();
              } else if (records[0].status == "Vaccinated") {
                $$("vax_status").define("css", "vax_status_green");
                $$("vax_status").refresh();
              } else if (
                records[0].status == "Medically Exempt" ||
                records[0].status == "Medically Exempt Pending"
              ) {
                $$("vax_status").define("css", "vax_status_exempt");
                $$("vax_status").refresh();
              }
              let curr_date = moment();
              let first_date = moment(records[0].first_dose_date, "DD/MM/YYYY");
              let second_date = moment(
                records[0].second_dose_date,
                "DD/MM/YYYY",
              );
              let exempt_expiry_date = moment(
                records[0].exemption_date + " 23:59",
                "DD/MM/YYYY H:mm",
              );
              if (
                records[0].status == "Medically Exempt" &&
                exempt_expiry_date.isSameOrAfter(curr_date) === true
              ) {
                $$("compliance_status").define(
                  "template",
                  "<div style='font-size: 15px; font-weight: 500; color: darkgreen; text-align: center'>COMPLETE - All current vaccination requirements met!</div>",
                );
                $$("compliance_status").refresh();
              } else {
                if (records[0].first_dose_date == "Invalid date") {
                  $$("compliance_status").define(
                    "template",
                    "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - No vaccinations received!</div>",
                  );
                  $$("compliance_status").refresh();
                } else if (
                  records[0].first_dose_date != "Invalid date" &&
                  records[0].second_dose_date == "Invalid date"
                ) {
                  if (curr_date.diff(first_date, "days", false) > 183) {
                    $$("compliance_status").define(
                      "template",
                      "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - 2nd vaccination not received!</div>",
                    );
                    $$("compliance_status").refresh();
                  } else {
                    $$("compliance_status").define(
                      "template",
                      "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - Waiting on 2nd vaccine!</div>",
                    );
                    $$("compliance_status").refresh();
                  }
                } else if (
                  records[0].first_dose_date != "Invalid date" &&
                  records[0].second_dose_date != "Invalid date" &&
                  records[0].booster_date == "Invalid date"
                ) {
                  if (curr_date.diff(second_date, "days", false) > 183) {
                    $$("compliance_status").define(
                      "template",
                      "<div style='font-size: 15px; font-weight: 500; color: orange; text-align: center'>NOT COMPLETE - Booster vaccination not received!</div>",
                    );
                    $$("compliance_status").refresh();
                  } else {
                    if (
                      records[0].digital_cert_received == 1 &&
                      records[0].vax_form_received == 1
                    ) {
                      $$("compliance_status").define(
                        "template",
                        "<div style='font-size: 15px; font-weight: 500; color: darkgreen; text-align: center'>COMPLETE - All current vaccination requirements met!</div>",
                      );
                      $$("compliance_status").refresh();
                    } else if (
                      records[0].digital_cert_received == 1 &&
                      (records[0].vax_form_received == 0 ||
                        records[0].vax_form_received == null)
                    ) {
                      $$("compliance_status").define(
                        "template",
                        "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - Waiting on vaccination form!</div>",
                      );
                      $$("compliance_status").refresh();
                    } else if (
                      (records[0].digital_cert_received == 0 ||
                        records[0].digital_cert_received == null) &&
                      records[0].vax_form_received == 1
                    ) {
                      $$("compliance_status").define(
                        "template",
                        "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - Waiting on digital certificate!</div>",
                      );
                      $$("compliance_status").refresh();
                    } else if (
                      (records[0].digital_cert_received == 0 ||
                        records[0].digital_cert_received == null) &&
                      (records[0].vax_form_received == 0 ||
                        records[0].vax_form_received == null)
                    ) {
                      $$("compliance_status").define(
                        "template",
                        "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - Waiting on digital certificate & vaccination form!</div>",
                      );
                      $$("compliance_status").refresh();
                    }
                  }
                } else if (
                  records[0].first_dose_date != "Invalid date" &&
                  records[0].second_dose_date != "Invalid date" &&
                  records[0].booster_date != "Invalid date"
                ) {
                  if (
                    records[0].digital_cert_received == 1 &&
                    records[0].vax_form_received == 1
                  ) {
                    $$("compliance_status").define(
                      "template",
                      "<div style='font-size: 15px; font-weight: 500; color: darkgreen; text-align: center'>COMPLETE - All current vaccination requirements met!</div>",
                    );
                    $$("compliance_status").refresh();
                  } else if (
                    records[0].digital_cert_received == 1 &&
                    (records[0].vax_form_received == 0 ||
                      records[0].vax_form_received == null)
                  ) {
                    $$("compliance_status").define(
                      "template",
                      "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - Waiting on vaccination form!</div>",
                    );
                    $$("compliance_status").refresh();
                  } else if (
                    (records[0].digital_cert_received == 0 ||
                      records[0].digital_cert_received == null) &&
                    records[0].vax_form_received == 1
                  ) {
                    $$("compliance_status").define(
                      "template",
                      "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - Waiting on digital certificate!</div>",
                    );
                    $$("compliance_status").refresh();
                  } else if (
                    (records[0].digital_cert_received == 0 ||
                      records[0].digital_cert_received == null) &&
                    (records[0].vax_form_received == 0 ||
                      records[0].vax_form_received == null)
                  ) {
                    $$("compliance_status").define(
                      "template",
                      "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>NOT COMPLETE - Waiting on digital certificate & vaccination form!</div>",
                    );
                    $$("compliance_status").refresh();
                  }
                }
              }
              loadingRecord = false;
            }
            if (payId == null || payId == "") {
              $$("btn_covid_vaccines_save").enable();
            } else {
              $$("btn_covid_vaccines_save").disable();
            }
          },
        },
      );
  }
  function loadCovidVaccines(callback) {
    $$("loader-window").show();
    setTimeout(function () {
      let grid = $$("admin-page").$$("covid_vaccines").$$("grid-covidVaccines");
      let booster_filter = $$("admin-page")
        .$$("covid_vaccines")
        .$$("booster_filter")
        .getValue();
      grid.clearAll();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .sync()
        .get(
          server_url + "/admin/list_covid_vaccines",
          { booster_filter: booster_filter },
          {
            error: function (err) {
              $$("loader-window").hide();
              callback("error");
            },
            success: function (results) {
              if (results) {
                let records = JSON.parse(results);
                let vax_records = [];
                let employeeName = "";
                let futureVax = "";
                let first_dose_date = "";
                let second_dose_date = "";
                let booster_date = "";
                let booster_date_2 = "";
                let updated_date = "";
                let exemption_date = "";
                let verified_date = "";
                let verified = "";
                let digital_cert_received = "";
                let vax_form_received = "";
                let compliance_status = "";
                let second_dose_expired_sms_sent = "";
                let second_dose_final_sms_sent = "";
                let booster_dose_expired_sms_sent = "";
                let booster_dose_final_sms_sent = "";
                let booster_required_sms_sent = "";
                let booster_required_warning_sms_sent = "";
                let booster_expiring_warning_sms_sent = "";
                let exemption_expiring_warning_sms_sent = "";
                if (records.length > 0) {
                  for (let x = 0; x < records.length; x++) {
                    if (records[x].middle_name == null) {
                      employeeName =
                        records[x].surname + ", " + records[x].first_name;
                    } else {
                      employeeName =
                        records[x].surname +
                        ", " +
                        records[x].first_name +
                        " " +
                        records[x].middle_name;
                    }
                    if (records[x].future_booking == true) {
                      futureVax = records[x].future_booking_date;
                    } else {
                      futureVax = "";
                    }
                    if (records[x].first_dose_date == "Invalid date") {
                      first_dose_date = "";
                    } else {
                      first_dose_date = records[x].first_dose_date;
                    }
                    if (records[x].second_dose_date == "Invalid date") {
                      second_dose_date = "";
                    } else {
                      second_dose_date = records[x].second_dose_date;
                    }
                    if (records[x].booster_date == "Invalid date") {
                      booster_date = "";
                    } else {
                      booster_date = records[x].booster_date;
                    }
                    if (records[x].booster_date_2 == "Invalid date") {
                      booster_date_2 = "";
                    } else {
                      booster_date_2 = records[x].booster_date_2;
                    }
                    if (records[x].updated_date == "Invalid date") {
                      updated_date = "";
                    } else {
                      updated_date = records[x].updated_date;
                    }
                    if (records[x].exemption_date == "Invalid date") {
                      exemption_date = "";
                    } else {
                      exemption_date = records[x].exemption_date;
                    }
                    if (records[x].verified_date == "Invalid date") {
                      verified_date = "";
                    } else {
                      verified_date = records[x].verified_date;
                    }
                    if (
                      records[x].verified == null ||
                      records[x].verified == false ||
                      records[x].verified == 0
                    ) {
                      verified =
                        "<span class = 'webix_icon fas fa-times' style='color: red'></span>";
                    } else {
                      verified =
                        "<span class = 'webix_icon fas fa-check' style='color: green'></span>";
                    }
                    if (
                      records[x].digital_cert_received == null ||
                      records[x].digital_cert_received == false ||
                      records[x].digital_cert_received == 0
                    ) {
                      digital_cert_received = "No";
                    } else {
                      digital_cert_received = "Yes";
                    }
                    if (
                      records[x].vax_form_received == null ||
                      records[x].vax_form_received == false ||
                      records[x].vax_form_received == 0
                    ) {
                      vax_form_received = "No";
                    } else {
                      vax_form_received = "Yes";
                    }
                    if (
                      records[x].second_dose_expired_sms_sent == null ||
                      records[x].second_dose_expired_sms_sent == false ||
                      records[x].second_dose_expired_sms_sent == 0
                    ) {
                      second_dose_expired_sms_sent = "No";
                    } else {
                      second_dose_expired_sms_sent = "Yes";
                    }
                    if (
                      records[x].second_dose_final_sms_sent == null ||
                      records[x].second_dose_final_sms_sent == false ||
                      records[x].second_dose_final_sms_sent == 0
                    ) {
                      second_dose_final_sms_sent = "No";
                    } else {
                      second_dose_final_sms_sent = "Yes";
                    }
                    if (
                      records[x].booster_dose_expired_sms_sent == null ||
                      records[x].booster_dose_expired_sms_sent == false ||
                      records[x].booster_dose_expired_sms_sent == 0
                    ) {
                      booster_dose_expired_sms_sent = "No";
                    } else {
                      booster_dose_expired_sms_sent = "Yes";
                    }
                    if (
                      records[x].booster_dose_final_sms_sent == null ||
                      records[x].booster_dose_final_sms_sent == false ||
                      records[x].booster_dose_final_sms_sent == 0
                    ) {
                      booster_dose_final_sms_sent = "No";
                    } else {
                      booster_dose_final_sms_sent = "Yes";
                    }
                    if (
                      records[x].booster_required_sms_sent == null ||
                      records[x].booster_required_sms_sent == false ||
                      records[x].booster_required_sms_sent == 0
                    ) {
                      booster_required_sms_sent = "No";
                    } else {
                      booster_required_sms_sent = "Yes";
                    }
                    if (
                      records[x].booster_required_warning_sms_sent == null ||
                      records[x].booster_required_warning_sms_sent == false ||
                      records[x].booster_required_warning_sms_sent == 0
                    ) {
                      booster_required_warning_sms_sent = "No";
                    } else {
                      booster_required_warning_sms_sent = "Yes";
                    }
                    if (
                      records[x].booster_expiring_warning_sms_sent == null ||
                      records[x].booster_expiring_warning_sms_sent == false ||
                      records[x].booster_expiring_warning_sms_sent == 0
                    ) {
                      booster_expiring_warning_sms_sent = "No";
                    } else {
                      booster_expiring_warning_sms_sent = "Yes";
                    }
                    if (
                      records[x].exemption_expiring_warning_sms_sent == null ||
                      records[x].exemption_expiring_warning_sms_sent == false ||
                      records[x].exemption_expiring_warning_sms_sent == 0
                    ) {
                      exemption_expiring_warning_sms_sent = "No";
                    } else {
                      exemption_expiring_warning_sms_sent = "Yes";
                    }
                    let curr_date = moment();
                    let first_date = moment(
                      records[x].first_dose_date,
                      "DD/MM/YYYY",
                    );
                    let second_date = moment(
                      records[x].second_dose_date,
                      "DD/MM/YYYY",
                    );
                    let exempt_expiry_date = moment(
                      records[x].exemption_date + " 23:59",
                      "DD/MM/YYYY H:mm",
                    );
                    if (
                      records[x].status == "Medically Exempt" &&
                      exempt_expiry_date.isSameOrAfter(curr_date) === true
                    ) {
                      compliance_status =
                        "COMPLETE - All current vaccination requirements met";
                    } else {
                      if (records[x].first_dose_date == "Invalid date") {
                        if (
                          records[x].vax_form_received == false ||
                          records[x].vax_form_received == null
                        ) {
                          compliance_status =
                            "NOT COMPLETE - No vaccinations (Not reported)";
                        } else {
                          compliance_status =
                            "NOT COMPLETE - No vaccinations (Reported)";
                        }
                      } else if (
                        records[x].first_dose_date != "Invalid date" &&
                        records[x].second_dose_date == "Invalid date"
                      ) {
                        if (curr_date.diff(first_date, "days", false) > 183) {
                          compliance_status =
                            "NOT COMPLETE - 2nd vaccination not received";
                        } else {
                          compliance_status =
                            "NOT COMPLETE - Waiting on 2nd vaccine";
                        }
                      } else if (
                        records[x].first_dose_date != "Invalid date" &&
                        records[x].second_dose_date != "Invalid date" &&
                        records[x].booster_date == "Invalid date"
                      ) {
                        if (curr_date.diff(second_date, "days", false) > 183) {
                          compliance_status =
                            "NOT COMPLETE - Booster vaccination not received";
                        } else {
                          if (
                            records[x].digital_cert_received == true &&
                            records[x].vax_form_received == true
                          ) {
                            compliance_status =
                              "COMPLETE - All current vaccination requirements met";
                          } else if (
                            records[x].digital_cert_received == true &&
                            (records[x].vax_form_received == false ||
                              records[x].vax_form_received == null)
                          ) {
                            compliance_status =
                              "NOT COMPLETE - Waiting on vax form";
                          } else if (
                            (records[x].digital_cert_received == false ||
                              records[x].digital_cert_received == null) &&
                            records[x].vax_form_received == true
                          ) {
                            compliance_status =
                              "NOT COMPLETE - Waiting on digital cert";
                          } else if (
                            (records[x].digital_cert_received == false ||
                              records[x].digital_cert_received == null) &&
                            (records[x].vax_form_received == false ||
                              records[x].vax_form_received == null)
                          ) {
                            compliance_status =
                              "NOT COMPLETE - Waiting on digital cert & vax form";
                          }
                        }
                      } else if (
                        records[x].first_dose_date != "Invalid date" &&
                        records[x].second_dose_date != "Invalid date" &&
                        records[x].booster_date != "Invalid date"
                      ) {
                        if (
                          records[x].digital_cert_received == true &&
                          records[x].vax_form_received == true
                        ) {
                          compliance_status =
                            "COMPLETE - All current vaccination requirements met";
                        } else if (
                          records[x].digital_cert_received == true &&
                          (records[x].vax_form_received == false ||
                            records[x].vax_form_received == null)
                        ) {
                          compliance_status =
                            "NOT COMPLETE - Waiting on vax form";
                        } else if (
                          (records[x].digital_cert_received == false ||
                            records[x].digital_cert_received == null) &&
                          records[x].vax_form_received == true
                        ) {
                          compliance_status =
                            "NOT COMPLETE - Waiting on digital cert";
                        } else if (
                          (records[x].digital_cert_received == false ||
                            records[x].digital_cert_received == null) &&
                          records[x].vax_form_receiveived == null
                        ) {
                          compliance_status =
                            "NOT COMPLETE - Waiting on digital cert & vax form";
                        }
                      }
                    }
                    if (records[x].first_dose_date != "Invalid date") {
                      first_dose_date = moment(
                        records[x].first_dose_date,
                        "DD/MM/YYYY",
                      ).toDate();
                    }
                    if (records[x].second_dose_date != "Invalid date") {
                      second_dose_date = moment(
                        records[x].second_dose_date,
                        "DD/MM/YYYY",
                      ).toDate();
                    }
                    if (records[x].booster_date != "Invalid date") {
                      booster_date = moment(
                        records[x].booster_date,
                        "DD/MM/YYYY",
                      ).toDate();
                    }
                    if (records[x].booster_date_2 != "Invalid date") {
                      booster_date_2 = moment(
                        records[x].booster_date_2,
                        "DD/MM/YYYY",
                      ).toDate();
                    }
                    vax_records.push({
                      id: records[x].id,
                      pay_id: records[x].pay_id,
                      employee: employeeName,
                      roster: records[x].roster,
                      shift: records[x].shift,
                      location: records[x].location,
                      future_booking: futureVax,
                      first_dose_date: first_dose_date,
                      first_dose_type: records[x].first_dose_type,
                      second_dose_date: second_dose_date,
                      second_dose_type: records[x].second_dose_type,
                      booster_date: booster_date,
                      booster_type: records[x].booster_type,
                      vax_status: records[x].status,
                      compliance_status: compliance_status,
                      comments: records[x].comments,
                      updated_by: records[x].updated_by,
                      updated_date: updated_date,
                      exemption_date: exemption_date,
                      verified: verified,
                      verified_by: records[x].verified_by,
                      cert_received: digital_cert_received,
                      form_received: vax_form_received,
                      verified_date: verified_date,
                      phone_number: records[x].personal_mobile_no,
                      second_dose_expired_sms_sent:
                        second_dose_expired_sms_sent,
                      second_dose_final_sms_sent: second_dose_final_sms_sent,
                      booster_dose_expired_sms_sent:
                        booster_dose_expired_sms_sent,
                      booster_dose_final_sms_sent: booster_dose_final_sms_sent,
                      booster_required_sms_sent: booster_required_sms_sent,
                      booster_required_warning_sms_sent:
                        booster_required_warning_sms_sent,
                      booster_expiring_warning_sms_sent:
                        booster_expiring_warning_sms_sent,
                      exemption_expiring_warning_sms_sent:
                        exemption_expiring_warning_sms_sent,
                      booster_date_2: booster_date_2,
                      booster_type_2: records[x].booster_type_2,
                    });
                  }
                  grid.define("data", vax_records);
                  grid.refresh();
                  grid.eachRow(function (row) {
                    let record = grid.getItem(row);
                    if (
                      record.compliance_status.includes(
                        "Booster vaccination not received",
                      ) === true
                    ) {
                      grid.addCellCss(
                        row,
                        "compliance_status",
                        "booster_status",
                      );
                    } else if (
                      record.compliance_status.includes("NOT COMPLETE") === true
                    ) {
                      grid.addCellCss(
                        row,
                        "compliance_status",
                        "denied_status",
                      );
                    } else {
                      grid.addCellCss(
                        row,
                        "compliance_status",
                        "complete_status",
                      );
                    }
                    if (record.second_dose_expired_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "second_dose_expired_sms_sent",
                        "complete_status",
                      );
                    } else if (record.second_dose_expired_sms_sent === "No") {
                      grid.addCellCss(
                        row,
                        "second_dose_expired_sms_sent",
                        "denied_status",
                      );
                    }
                    if (record.second_dose_final_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "second_dose_final_sms_sent",
                        "complete_status",
                      );
                    } else if (record.second_dose_final_sms_sent === "No") {
                      grid.addCellCss(
                        row,
                        "second_dose_final_sms_sent",
                        "denied_status",
                      );
                    }
                    if (record.booster_dose_expired_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "booster_dose_expired_sms_sent",
                        "complete_status",
                      );
                    } else if (record.booster_dose_expired_sms_sent === "No") {
                      grid.addCellCss(
                        row,
                        "booster_dose_expired_sms_sent",
                        "denied_status",
                      );
                    }
                    if (record.booster_dose_final_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "booster_dose_final_sms_sent",
                        "complete_status",
                      );
                    } else if (record.booster_dose_final_sms_sent === "No") {
                      grid.addCellCss(
                        row,
                        "booster_dose_final_sms_sent",
                        "denied_status",
                      );
                    }
                    if (record.booster_required_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "booster_required_sms_sent",
                        "complete_status",
                      );
                    } else if (record.booster_required_sms_sent === "No") {
                      grid.addCellCss(
                        row,
                        "booster_required_sms_sent",
                        "denied_status",
                      );
                    }
                    if (record.booster_required_warning_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "booster_required_warning_sms_sent",
                        "complete_status",
                      );
                    } else if (
                      record.booster_required_warning_sms_sent === "No"
                    ) {
                      grid.addCellCss(
                        row,
                        "booster_required_warning_sms_sent",
                        "denied_status",
                      );
                    }
                    if (record.booster_expiring_warning_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "booster_expiring_warning_sms_sent",
                        "complete_status",
                      );
                    } else if (
                      record.booster_expiring_warning_sms_sent === "No"
                    ) {
                      grid.addCellCss(
                        row,
                        "booster_expiring_warning_sms_sent",
                        "denied_status",
                      );
                    }
                    if (record.exemption_expiring_warning_sms_sent === "Yes") {
                      grid.addCellCss(
                        row,
                        "exemption_expiring_warning_sms_sent",
                        "complete_status",
                      );
                    } else if (
                      record.exemption_expiring_warning_sms_sent === "No"
                    ) {
                      grid.addCellCss(
                        row,
                        "exemption_expiring_warning_sms_sent",
                        "denied_status",
                      );
                    }
                  });
                  $$("loader-window").hide();
                  callback("ok");
                }
              } else {
                $$("loader-window").hide();
                callback("ok");
              }
            },
          },
        );
    }, 250);
  }
  function deleteVaccineEntry() {
    let selected_row = $$("admin-page")
      .$$("covid_vaccines")
      .$$("grid-covidVaccines")
      .getSelectedItem();
    if (selected_row) {
      webix.confirm({
        title: "Delete Vaccine Record",
        ok: "Yes",
        cancel: "No",
        text: "Are you sure you want to delete the selected Vaccine Record?",
        callback: function (result) {
          switch (result) {
            case true:
              webix
                .ajax()
                .headers({ Authorization: "Bearer " + api_key })
                .del(
                  server_url + "/admin/delete_vax_record",
                  { id: selected_row.id },
                  {
                    error: function (err) {
                      webix.alert(
                        "There was an error deleting the selected record!",
                      );
                    },
                    success: function () {
                      loadCovidVaccines(function (response) {
                        if (response == "ok") {
                          $$("admin-page")
                            .$$("covid_vaccines")
                            .$$("pay_id_search_field")
                            .setValue("");
                          $$("admin-page")
                            .$$("covid_vaccines")
                            .$$("name_search_field")
                            .setValue("");
                          let grid = $$("admin-page")
                            .$$("covid_vaccines")
                            .$$("grid-covidVaccines");
                          let sms_filter = $$("admin-page")
                            .$$("covid_vaccines")
                            .$$("sms_filter")
                            .getValue();
                          if (sms_filter != "All Records") {
                            grid.filter((obj) => sms_filter_status(obj));
                          }
                        }
                      });
                    },
                  },
                );
          }
        },
      });
    } else {
      webix.alert("No record is selected to delete!");
    }
  }
  function saveVaccineEntry() {
    let pay_id = $$("covid_vaccines_employee").getValue();
    if (pay_id == 0) {
      webix.alert({ text: "You must select a valid employee!", width: 450 });
    } else {
      checkExistingRecord(pay_id, function (response) {
        if (response == "yes") {
          webix.alert({
            text: "A vaccine record already exists for this employee!",
            width: 450,
          });
        } else if (response == "error") {
          webix.alert({
            text: "There was an error checking if a vaccine record already exists for this employee!",
            width: 550,
          });
        } else {
          let future_booking = $$("future_booking").getValue();
          let future_booking_date = $$("future_booking_date").getValue();
          let first_dose_date = $$("first_dose_date").getValue();
          let first_dose_type = $$("first_dose_type").getValue();
          let second_dose_date = $$("second_dose_date").getValue();
          let second_dose_type = $$("second_dose_type").getValue();
          let booster_date = $$("booster_date").getValue();
          let booster_date_2 = $$("booster_date_2").getValue();
          let booster_type = $$("booster_type").getValue();
          let booster_type_2 = $$("booster_type_2").getValue();
          let status = $$("vax_status").getValue();
          let comments = $$("vax_comments").getValue();
          let verified = $$("verified_record").getValue();
          let exemption_date = $$("exemption_date").getValue();
          let digital_cert_received = $$("digital_cert_received").getValue();
          let vax_form_received = $$("vax_form_received").getValue();
          webix
            .ajax()
            .headers({ Authorization: "Bearer " + api_key })
            .post(
              server_url + "/admin/add_vax_record",
              {
                pay_id: pay_id,
                future_booking: future_booking,
                future_booking_date: future_booking_date,
                first_dose_date: first_dose_date,
                first_dose_type: first_dose_type,
                second_dose_date: second_dose_date,
                second_dose_type: second_dose_type,
                booster_date: booster_date,
                booster_type: booster_type,
                booster_date_2: booster_date_2,
                booster_type_2: booster_type_2,
                status: status,
                comments: comments,
                updated_by: user_logged_in,
                verified: verified,
                verified_by: user_logged_in,
                exemption_date: exemption_date,
                digital_cert_received: digital_cert_received,
                vax_form_received: vax_form_received,
              },
              {
                error: function (err) {
                  webix.alert("There was an error saving this record!");
                },
                success: function () {
                  $$("formCovidVaccines").clear();
                  $$("covid_vaccines-popup").hide();
                  loadCovidVaccines(function (response) {
                    if (response == "ok") {
                      $$("admin-page")
                        .$$("covid_vaccines")
                        .$$("pay_id_search_field")
                        .setValue("");
                      $$("admin-page")
                        .$$("covid_vaccines")
                        .$$("name_search_field")
                        .setValue("");
                      let grid = $$("admin-page")
                        .$$("covid_vaccines")
                        .$$("grid-covidVaccines");
                      let sms_filter = $$("admin-page")
                        .$$("covid_vaccines")
                        .$$("sms_filter")
                        .getValue();
                      if (sms_filter != "All Records") {
                        grid.filter((obj) => sms_filter_status(obj));
                      }
                    }
                  });
                },
              },
            );
        }
      });
    }
  }
  function editVaccineEntry() {
    let id = $$("covid_vaccines_id").getValue();
    let future_booking = $$("future_booking").getValue();
    let future_booking_date = $$("future_booking_date").getValue();
    let first_dose_date = $$("first_dose_date").getValue();
    let first_dose_type = $$("first_dose_type").getValue();
    let second_dose_date = $$("second_dose_date").getValue();
    let second_dose_type = $$("second_dose_type").getValue();
    let booster_date = $$("booster_date").getValue();
    let booster_type = $$("booster_type").getValue();
    let booster_date_2 = $$("booster_date_2").getValue();
    let booster_type_2 = $$("booster_type_2").getValue();
    let status = $$("vax_status").getValue();
    let comments = $$("vax_comments").getValue();
    let verified = $$("verified_record").getValue();
    let exemption_date = $$("exemption_date").getValue();
    let digital_cert_received = $$("digital_cert_received").getValue();
    let vax_form_received = $$("vax_form_received").getValue();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/update_vax_record",
        {
          id: id,
          future_booking: future_booking,
          future_booking_date: future_booking_date,
          first_dose_date: first_dose_date,
          first_dose_type: first_dose_type,
          second_dose_date: second_dose_date,
          second_dose_type: second_dose_type,
          booster_date: booster_date,
          booster_type: booster_type,
          booster_date_2: booster_date_2,
          booster_type_2: booster_type_2,
          status: status,
          comments: comments,
          updated_by: user_logged_in,
          verified: verified,
          verified_by: user_logged_in,
          exemption_date: exemption_date,
          digital_cert_received: digital_cert_received,
          vax_form_received: vax_form_received,
        },
        {
          error: function (err) {
            webix.alert("There was an error updating this record!");
          },
          success: function () {
            if (live_site === true) {
              let grid = $$("admin-page")
                .$$("covid_vaccines")
                .$$("grid-covidVaccines");
              let selected_id = grid.getSelectedId();
              $$("formCovidVaccines").clear();
              $$("covid_vaccines-popup").hide();
              loadCovidVaccines(function (response) {
                if (response == "ok") {
                  $$("admin-page")
                    .$$("covid_vaccines")
                    .$$("pay_id_search_field")
                    .setValue("");
                  $$("admin-page")
                    .$$("covid_vaccines")
                    .$$("name_search_field")
                    .setValue("");
                  setTimeout(function () {
                    let email_msg = "";
                    let curr_date = moment();
                    let selected_item = grid.getItem(selected_id);
                    let first_date = moment(
                      selected_item.first_dose_date,
                      "DD/MM/YYYY",
                    );
                    let second_date = moment(
                      selected_item.second_dose_date,
                      "DD/MM/YYYY",
                    );
                    let exempt_expiry_date = moment(
                      selected_item.exemption_date + " 23:59",
                      "DD/MM/YYYY H:mm",
                    );
                    if (
                      selected_item.vax_status == "Medically Exempt" &&
                      exempt_expiry_date.isSameOrAfter(curr_date) === true
                    ) {
                      email_msg =
                        "COMPLETE - You have met all current vaccination requirements!";
                    } else {
                      if (selected_item.first_dose_date == "") {
                        email_msg = "NOT COMPLETE - No vaccinations received!";
                      } else if (
                        selected_item.first_dose_date != "" &&
                        selected_item.second_dose_date == ""
                      ) {
                        if (curr_date.diff(first_date, "days", false) > 183) {
                          email_msg =
                            "NOT COMPLETE - 2nd vaccination not received!";
                        } else {
                          email_msg = "NOT COMPLETE - Waiting on 2nd vaccine!";
                        }
                      } else if (
                        selected_item.first_dose_date != "" &&
                        selected_item.second_dose_date != "" &&
                        selected_item.booster_date == ""
                      ) {
                        if (curr_date.diff(second_date, "days", false) > 183) {
                          email_msg =
                            "NOT COMPLETE - Booster vaccination not received!";
                        } else {
                          if (
                            selected_item.cert_received == "Yes" &&
                            selected_item.form_received == "Yes"
                          ) {
                            email_msg =
                              "COMPLETE - You have met all current vaccination requirements!";
                          } else if (
                            selected_item.cert_received == "Yes" &&
                            selected_item.form_received == "No"
                          ) {
                            email_msg =
                              "NOT COMPLETE - Waiting on vaccination form!";
                          } else if (
                            selected_item.cert_received == "No" &&
                            selected_item.form_received == "Yes"
                          ) {
                            email_msg =
                              "NOT COMPLETE - Waiting on digital certificate!";
                          } else if (
                            selected_item.cert_received == "No" &&
                            selected_item.form_received == "No"
                          ) {
                            email_msg =
                              "NOT COMPLETE - Waiting on digital certificate & vaccination form!";
                          }
                        }
                      } else if (
                        selected_item.first_dose_date != "" &&
                        selected_item.second_dose_date != "" &&
                        selected_item.booster_date != ""
                      ) {
                        if (
                          selected_item.cert_received == "Yes" &&
                          selected_item.form_received == "Yes"
                        ) {
                          email_msg =
                            "COMPLETE - You have met all current vaccination requirements!";
                        } else if (
                          selected_item.cert_received == "Yes" &&
                          selected_item.form_received == "No"
                        ) {
                          email_msg =
                            "NOT COMPLETE - Waiting on vaccination form!";
                        } else if (
                          selected_item.cert_received == "No" &&
                          selected_item.form_received == "Yes"
                        ) {
                          email_msg =
                            "NOT COMPLETE - Waiting on digital certificate!";
                        } else if (
                          selected_item.cert_received == "No" &&
                          selected_item.form_received == "No"
                        ) {
                          email_msg =
                            "NOT COMPLETE - Waiting on digital certificate & vaccination form!";
                        }
                      }
                    }
                  }, 500);
                }
              });
            } else {
              $$("formCovidVaccines").clear();
              $$("covid_vaccines-popup").hide();
              loadCovidVaccines(function (response) {
                if (response == "ok") {
                  $$("admin-page")
                    .$$("covid_vaccines")
                    .$$("pay_id_search_field")
                    .setValue("");
                  $$("admin-page")
                    .$$("covid_vaccines")
                    .$$("name_search_field")
                    .setValue("");
                  let grid = $$("admin-page")
                    .$$("covid_vaccines")
                    .$$("grid-covidVaccines");
                  let sms_filter = $$("admin-page")
                    .$$("covid_vaccines")
                    .$$("sms_filter")
                    .getValue();
                  if (sms_filter != "All Records") {
                    grid.filter((obj) => sms_filter_status(obj));
                  }
                }
              });
            }
          },
        },
      );
  }
  function sms_filter_status(t) {
    let sms_filter = $$("admin-page")
      .$$("covid_vaccines")
      .$$("sms_filter")
      .getValue();
    let curr_date = moment().add(-1, "days");
    let final_notice_date = moment().add(-8, "days");
    let warning_date_exp = moment().add(8, "days");
    let booster_date = moment(t.booster_date, "DD/MM/YYYY");
    let second_dose_date = moment(t.second_dose_date, "DD/MM/YYYY");
    let verified_date = moment(t.verified_date, "DD/MM/YYYY");
    let updated_date = moment(t.updated_date, "DD/MM/YYYY");
    let exemption_date = moment(t.exemption_date, "DD/MM/YYYY");
    if (
      t.vax_status == "Medically Exempt" &&
      exemption_date.isSameOrAfter(curr_date) === true &&
      sms_filter != "Exemption Expiring in 7 days"
    ) {
    } else {
      if (sms_filter == "Booster Shot") {
        if (
          t.compliance_status ==
          "NOT COMPLETE - Booster vaccination not received"
        ) {
          return t;
        }
      } else if (sms_filter == "Vax Form") {
        if (t.compliance_status == "NOT COMPLETE - Waiting on vax form") {
          return t;
        }
      } else if (sms_filter == "Digital Certificate") {
        if (t.compliance_status == "NOT COMPLETE - Waiting on digital cert") {
          return t;
        }
      } else if (sms_filter == "Vax Form & Digital Certificate") {
        if (
          t.compliance_status ==
          "NOT COMPLETE - Waiting on digital cert & vax form"
        ) {
          return t;
        }
      } else if (sms_filter == "Booster Expired") {
        if (booster_date._isValid === true) {
          if (
            moment(booster_date).isAfter(verified_date) === true &&
            moment(booster_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
          ) {
            return t;
          } else if (
            moment(booster_date).isAfter(updated_date) === true &&
            moment(booster_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-times' style='color: red'></span>"
          ) {
            return t;
          }
        }
      } else if (sms_filter == "2nd Dose Expired") {
        if (second_dose_date._isValid === true) {
          if (
            moment(second_dose_date).isAfter(verified_date) === true &&
            moment(second_dose_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
          ) {
            return t;
          } else if (
            moment(second_dose_date).isAfter(updated_date) === true &&
            moment(second_dose_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-times' style='color: red'></span>"
          ) {
            return t;
          }
        }
      } else if (sms_filter == "Booster Final Notice") {
        if (booster_date._isValid === true) {
          if (
            moment(booster_date).isAfter(verified_date) === true &&
            moment(booster_date).isSameOrBefore(curr_date) === true &&
            moment(booster_date).isSameOrBefore(final_notice_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
          ) {
            return t;
          } else if (
            moment(booster_date).isAfter(updated_date) === true &&
            moment(booster_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-times' style='color: red'></span>"
          ) {
            return t;
          }
        }
      } else if (sms_filter == "2nd Dose Final Notice") {
        if (second_dose_date._isValid === true) {
          if (
            moment(second_dose_date).isAfter(verified_date) === true &&
            moment(second_dose_date).isSameOrBefore(curr_date) === true &&
            moment(second_dose_date).isSameOrBefore(final_notice_date) ===
              true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
          ) {
            return t;
          } else if (
            moment(second_dose_date).isAfter(updated_date) === true &&
            moment(second_dose_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-times' style='color: red'></span>"
          ) {
            return t;
          }
        }
      } else if (sms_filter == "Booster Required") {
        if (
          t.compliance_status ==
          "NOT COMPLETE - Booster vaccination not received"
        ) {
          return t;
        }
      } else if (sms_filter == "Booster Required in 7 days") {
        if (second_dose_date._isValid === true) {
          if (booster_date._isValid === false) {
            if (
              warning_date_exp.diff(second_dose_date, "days", false) > 183 &&
              curr_date.diff(second_dose_date, "days", false) < 191 &&
              t.verified ===
                "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
            ) {
              return t;
            }
          }
        }
      } else if (sms_filter == "Booster Expiring in 7 days") {
        if (booster_date._isValid === true) {
          booster_date.subtract(8, "days");
          if (
            moment(booster_date).isAfter(verified_date) === true &&
            moment(booster_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
          ) {
            return t;
          } else if (
            moment(booster_date).isAfter(updated_date) === true &&
            moment(booster_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-times' style='color: red'></span>"
          ) {
            return t;
          }
        }
      } else if (sms_filter == "Exemption Expiring in 7 days") {
        if (exemption_date._isValid === true) {
          exemption_date.subtract(8, "days");
          if (
            moment(exemption_date).isAfter(verified_date) === true &&
            moment(exemption_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
          ) {
            return t;
          } else if (
            moment(exemption_date).isAfter(updated_date) === true &&
            moment(exemption_date).isSameOrBefore(curr_date) === true &&
            t.verified ===
              "<span class = 'webix_icon fas fa-times' style='color: red'></span>"
          ) {
            return t;
          }
        }
      }
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
    getCovidVaccines: function () {
      loadCovidVaccines(function (response) {
        if (response == "ok") {
          let grid = $$("admin-page")
            .$$("covid_vaccines")
            .$$("grid-covidVaccines");
          let sms_filter = $$("admin-page")
            .$$("covid_vaccines")
            .$$("sms_filter")
            .getValue();
          if (sms_filter != "All Records") {
            grid.filter((obj) => sms_filter_status(obj));
          }
        }
      });
    },
    getVaxRecord: function (id, payId) {
      getVaxRecord(id, payId);
    },
  };
})();
