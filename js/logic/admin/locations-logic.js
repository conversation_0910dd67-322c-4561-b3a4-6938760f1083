let locations = (function () {
  let edit_mode = "";
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("locations")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        addLocation();
      });
    $$("admin-page")
      .$$("locations")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteLocation(id);
      });
    $$("btn_loc_save").attachEvent("onItemClick", function (id, e) {
      saveLocation();
    });
    $$("btn_loc_close").attachEvent("onItemClick", function (id, e) {
      $$("formLocation").clear();
      $$("formLocation").clearValidation();
      $$("locations-popup").hide();
    });
    $$("admin-page")
      .$$("locations")
      .$$("grid-locations")
      .attachEvent("onItemDblClick", function (id) {
        edit_mode = "EDIT";
        let selected_row = $$("admin-page")
          .$$("locations")
          .$$("grid-locations")
          .getSelectedItem(id);
        if (selected_row !== undefined) {
          if (selected_row[0].id > 0) {
            let row_id = selected_row[0].id;
            getLocationInfo(row_id, id);
          }
        }
      });
    loadLocations();
  }
  locations_subject.subscribe(function (data) {
    $$("admin-page").$$("locations").$$("grid-locations").parse(data);
  });
  function deleteLocation(id) {
    let selected_row = $$("admin-page")
      .$$("locations")
      .$$("grid-locations")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Location",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Location?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/locations",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadLocations();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Location has been selected!");
    }
  }
  function addLocation() {
    edit_mode = "ADD";
    $$("locations-label").define(
      "label",
      "<span class='header_font'>Add Location</span>",
    );
    $$("locations-label").refresh();
    $$("formLocation").clear();
    $$("min_SO").setValue(0);
    $$("min_SF").setValue(0);
    $$("min_FF").setValue(0);
    $$("min_MOP").setValue(0);
    $$("min_MOF").setValue(0);
    $$("min_COFF").setValue(0);
    $$("min_SCOP").setValue(0);
    $$("min_COP").setValue(0);
    $$("min_CMD").setValue(0);
    $$("min_R9O").setValue(0);
    $$("min_R9").setValue(0);
    $$("min_A5O").setValue(0);
    $$("min_A5").setValue(0);
    $$("min_H6O").setValue(0);
    $$("min_H6").setValue(0);
    $$("min_C3O").setValue(0);
    $$("min_C3").setValue(0);
    $$("min_R4O").setValue(0);
    $$("min_R4").setValue(0);
    $$("min_GPO").setValue(0);
    $$("min_GP").setValue(0);
    $$("min_MCO").setValue(0);
    $$("min_DRO").setValue(0);
    $$("min_ICV").setValue(0);
    $$("min_HL").setValue(0);
    $$("min_CCO").setValue(0);
    $$("min_CCP").setValue(0);
    $$("min_MVE").setValue(0);
    $$("min_MVO").setValue(0);
    $$("locations-popup").show();
  }
  function saveLocation() {
    if ($$("formLocation").validate()) {
      let form = $$("formLocation").getValues();
      let latitude = 0;
      let longitude = 0;
      if (validateLatitude(form.location_latitude) == "error") {
        return;
      } else {
        latitude = webix.Number.format(form.location_latitude, {
          decimalSize: 6,
          groupSize: 6,
          decimalDelimiter: ".",
          groupDelimiter: "",
        });
      }
      if (validateLongitude(form.location_longitude) == "error") {
        return;
      } else {
        longitude = webix.Number.format(form.location_longitude, {
          decimalSize: 6,
          groupSize: 6,
          decimalDelimiter: ".",
          groupDelimiter: "",
        });
      }
      $$("loader-window").show();
      if (edit_mode === "ADD") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/admin/locations",
            {
              location_name: form.location_name,
              station_id: form.location_station_id,
              address: form.location_address,
              latitude: latitude,
              longitude: longitude,
              roster: form.location_roster,
              min_SO: form.min_SO,
              min_SF: form.min_SF,
              min_FF: form.min_FF,
              min_MOP: form.min_MOP,
              min_MOF: form.min_MOF,
              min_COFF: form.min_COFF,
              min_SCOP: form.min_SCOP,
              min_COP: form.min_COP,
              min_CMD: form.min_CMD,
              min_R9O: form.min_R9O,
              min_R9: form.min_R9,
              min_A5O: form.min_A5O,
              min_A5: form.min_A5,
              min_H6O: form.min_H6O,
              min_H6: form.min_H6,
              min_C3O: form.min_C3O,
              min_C3: form.min_C3,
              min_R4O: form.min_R4O,
              min_R4: form.min_R4,
              min_GPO: form.min_GPO,
              min_GP: form.min_GP,
              min_MCO: form.min_MCO,
              min_DRO: form.min_DRO,
              min_ICV: form.min_ICV,
              min_HL: form.min_HL,
              min_CCO: form.min_CCO,
              min_CCP: form.min_CCP,
              min_MVE: form.min_MVE,
              min_MVO: form.min_MVO,
            },
            {
              error: function (err) {
                $$("loader-window").hide();
              },
              success: function () {
                $$("locations-popup").hide();
                loadLocations();
                $$("loader-window").hide();
              },
            },
          );
      } else if (edit_mode === "EDIT") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .put(
            server_url + "/admin/locations",
            {
              id: form.location_id,
              location_name: form.location_name,
              station_id: form.location_station_id,
              address: form.location_address,
              latitude: latitude,
              longitude: longitude,
              roster: form.location_roster,
              min_SO: form.min_SO,
              min_SF: form.min_SF,
              min_FF: form.min_FF,
              min_MOP: form.min_MOP,
              min_MOF: form.min_MOF,
              min_COFF: form.min_COFF,
              min_SCOP: form.min_SCOP,
              min_COP: form.min_COP,
              min_CMD: form.min_CMD,
              min_R9O: form.min_R9O,
              min_R9: form.min_R9,
              min_A5O: form.min_A5O,
              min_A5: form.min_A5,
              min_H6O: form.min_H6O,
              min_H6: form.min_H6,
              min_C3O: form.min_C3O,
              min_C3: form.min_C3,
              min_R4O: form.min_R4O,
              min_R4: form.min_R4,
              min_GPO: form.min_GPO,
              min_GP: form.min_GP,
              min_MCO: form.min_MCO,
              min_DRO: form.min_DRO,
              min_ICV: form.min_ICV,
              min_HL: form.min_HL,
              min_CCO: form.min_CCO,
              min_CCP: form.min_CCP,
              min_MVE: form.min_MVE,
              min_MVO: form.min_MVO,
            },
            {
              error: function (err) {
                $$("loader-window").hide();
              },
              success: function () {
                $$("locations-popup").hide();
                loadLocations();
                $$("loader-window").hide();
              },
            },
          );
      }
    }
  }
  function loadLocations() {
    $$("admin-page").$$("locations").$$("grid-locations").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/locations",
        {},
        {
          error: function (err) {},
          success: function (results) {
            locations_subject.next(results);
          },
        },
      );
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("location_roster");
    let objValues = {};
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        objValues = { id: value.roster_name, value: value.roster_name };
        options.push(objValues);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function getLocationInfo(row_id, selected_id) {
    let selected_row = $$("admin-page")
      .$$("locations")
      .$$("grid-locations")
      .getSelectedItem(selected_id);
    let locationName = selected_row[0].name;
    let stationId = selected_row[0].station_id;
    let address = selected_row[0].address;
    let latitude = selected_row[0].latitude;
    let longitude = selected_row[0].longitude;
    let roster = selected_row[0].roster;
    let min_SO = selected_row[0].min_SO;
    let min_SF = selected_row[0].min_SF;
    let min_FF = selected_row[0].min_FF;
    let min_MOP = selected_row[0].min_MOP;
    let min_MOF = selected_row[0].min_MOF;
    let min_COFF = selected_row[0].min_COFF;
    let min_SCOP = selected_row[0].min_SCOP;
    let min_COP = selected_row[0].min_COP;
    let min_CMD = selected_row[0].min_CMD;
    let min_R9O = selected_row[0].min_R9O;
    let min_R9 = selected_row[0].min_R9;
    let min_A5O = selected_row[0].min_A5O;
    let min_A5 = selected_row[0].min_A5;
    let min_H6O = selected_row[0].min_H6O;
    let min_H6 = selected_row[0].min_H6;
    let min_C3O = selected_row[0].min_C3O;
    let min_C3 = selected_row[0].min_C3;
    let min_R4O = selected_row[0].min_R4O;
    let min_R4 = selected_row[0].min_R4;
    let min_GPO = selected_row[0].min_GPO;
    let min_GP = selected_row[0].min_GP;
    let min_MCO = selected_row[0].min_MCO;
    let min_DRO = selected_row[0].min_DRO;
    let min_ICV = selected_row[0].min_ICV;
    let min_HL = selected_row[0].min_HL;
    let min_CCO = selected_row[0].min_CCO;
    let min_CCP = selected_row[0].min_CCP;
    let min_MVE = selected_row[0].min_MVE;
    let min_MVO = selected_row[0].min_MVO;
    $$("location_id").setValue(row_id);
    $$("location_name").setValue(locationName);
    $$("location_station_id").setValue(stationId);
    $$("location_address").setValue(address);
    $$("location_latitude").setValue(latitude);
    $$("location_longitude").setValue(longitude);
    $$("location_roster").setValue(roster);
    $$("min_SO").setValue(min_SO);
    $$("min_SF").setValue(min_SF);
    $$("min_FF").setValue(min_FF);
    $$("min_MOP").setValue(min_MOP);
    $$("min_MOF").setValue(min_MOF);
    $$("min_COFF").setValue(min_COFF);
    $$("min_SCOP").setValue(min_SCOP);
    $$("min_COP").setValue(min_COP);
    $$("min_CMD").setValue(min_CMD);
    $$("min_R9O").setValue(min_R9O);
    $$("min_R9").setValue(min_R9);
    $$("min_A5O").setValue(min_A5O);
    $$("min_A5").setValue(min_A5);
    $$("min_H6O").setValue(min_H6O);
    $$("min_H6").setValue(min_H6);
    $$("min_C3O").setValue(min_C3O);
    $$("min_C3").setValue(min_C3);
    $$("min_R4O").setValue(min_R4O);
    $$("min_R4").setValue(min_R4);
    $$("min_GPO").setValue(min_GPO);
    $$("min_GP").setValue(min_GP);
    $$("min_MCO").setValue(min_MCO);
    $$("min_DRO").setValue(min_DRO);
    $$("min_ICV").setValue(min_ICV);
    $$("min_HL").setValue(min_HL);
    $$("min_CCO").setValue(min_CCO);
    $$("min_CCP").setValue(min_CCP);
    $$("min_MVE").setValue(min_MVE);
    $$("min_MVO").setValue(min_MVO);
    $$("locations-label").define(
      "label",
      "<span class='header_font'>Edit Location</span>",
    );
    $$("locations-label").refresh();
    $$("locations-popup").show();
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
