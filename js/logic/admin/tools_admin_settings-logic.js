let adminSettings = (function () {
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("tools_admin_settings")
      .$$("setting_hs_pref_bop")
      .attachEvent("onChange", function (newValue, oldValue, config) {
        if (loadingSettings === false) {
          saveSetting("hs_pref_bop", newValue, function (response) {
            if (response === "ok") {
              if (newValue == "") {
                global_settings.hs_pref_bop = false;
              } else {
                global_settings.hs_pref_bop = true;
              }
            }
          });
        }
      });
  }
  function saveSetting(settingName, value, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/update_admin_setting",
        { setting: settingName, value: value },
        {
          error: function (err) {
            webix.message({
              text: "Error Saving Setting!",
              type: "error",
              expire: 1500,
            });
            callback("error");
          },
          success: function () {
            webix.message({
              text: "Setting Saved!",
              type: "success",
              expire: 1500,
            });
            callback("ok");
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
