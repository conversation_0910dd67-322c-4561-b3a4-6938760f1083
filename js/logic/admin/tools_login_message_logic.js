let loginMessage = (function () {
  function initApplication() {
    eventHandlers();
      $$("admin-page")
          .$$("tools_login_message")
          .$$("login_message")
          .setValue(login_message[0].login_message);
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("tools_login_message")
      .$$("btnSaveMessage")
      .attachEvent("onItemClick", function (id, e) {
        let msg_text = $$("admin-page")
          .$$("tools_login_message")
          .$$("login_message")
          .getValue();
        saveLoginMessage(msg_text);
      });
  }
  function saveLoginMessage(msg_text) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/login_message",
        { login_message: msg_text },
        {
          error: function (err) {},
          success: function () {
            webix.alert("New Message Saved!");
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
