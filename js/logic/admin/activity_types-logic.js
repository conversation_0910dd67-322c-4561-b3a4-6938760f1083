let activityTypes = (function () {
  let edit_mode;
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("activity_types")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        addActivityType();
      });
    $$("admin-page")
      .$$("activity_types")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteActivityType(id);
      });
    $$("btn_activityType_save").attachEvent("onItemClick", function (id, e) {
      saveActivityType();
    });
    $$("btn_activityType_close").attachEvent("onItemClick", function (id, e) {
      $$("formActivityTypes").clear();
      $$("formActivityTypes").clearValidation();
      $$("activityTypes-popup").hide();
    });
    $$("admin-page")
      .$$("activity_types")
      .$$("grid-activitytypes")
      .attachEvent("onItemDblClick", function (id) {
        edit_mode = "EDIT";
        let selected_row = $$("admin-page")
          .$$("activity_types")
          .$$("grid-activitytypes")
          .getSelectedItem(id);
        if (selected_row !== undefined) {
          if (selected_row[0].id > 0) {
            let row_id = selected_row[0].id;
            getactivityTypesInfo(row_id, id);
          }
        }
      });
    loadActivityTypes();
  }
  function getactivityTypesInfo(row_id, selected_id) {
    let selected_row = $$("admin-page")
      .$$("activity_types")
      .$$("grid-activitytypes")
      .getSelectedItem(selected_id);
    let rowId = selected_row[0].id;
    let code = selected_row[0].code;
    let name = selected_row[0].overtime_name;
    $$("activityType_id").setValue(rowId);
    $$("activityType_code").setValue(code);
    $$("activityType_name").setValue(name);
    $$("activityType-label").define(
      "label",
      "<span class='header_font'>Edit Overtime Type</span>",
    );
    $$("activityType-label").refresh();
    $$("activityTypes-popup").show();
  }
  function addActivityType() {
    edit_mode = "ADD";
    $$("activityType-label").define(
      "label",
      "<span class='header_font'>Add Overtime Type</span>",
    );
    $$("activityType-label").refresh();
    $$("formActivityTypes").clear();
    $$("activityTypes-popup").show();
  }
  function deleteActivityType(id) {
    let selected_row = $$("admin-page")
      .$$("activity_types")
      .$$("grid-activitytypes")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Overtime Type",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Overtime Type?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/activity_types",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadActivityTypes();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Overtime Type has been selected!");
    }
  }
  function loadActivityTypes() {
    $$("admin-page").$$("activity_types").$$("grid-activitytypes").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/activity_types",
        {},
        {
          error: function (err) {},
          success: function (results) {
            overtime_types_subject.next(results);
          },
        },
      );
  }
  overtime_types_subject.subscribe(function (data) {
    $$("admin-page").$$("activity_types").$$("grid-activitytypes").parse(data);
  });
  function saveActivityType() {
    if ($$("formActivityTypes").validate()) {
      let form = $$("formActivityTypes").getValues();
      if (edit_mode === "ADD") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/admin/activity_types",
            { code: form.activityType_code, name: form.activityType_name },
            {
              error: function (err) {},
              success: function () {
                $$("activityTypes-popup").hide();
                loadActivityTypes();
              },
            },
          );
      } else if (edit_mode === "EDIT") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .put(
            server_url + "/admin/activity_types",
            {
              id: form.activityType_id,
              code: form.activityType_code,
              name: form.activityType_name,
            },
            {
              error: function (err) {},
              success: function () {
                $$("activityTypes-popup").hide();
                loadActivityTypes();
              },
            },
          );
      }
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
