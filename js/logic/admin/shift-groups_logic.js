let shiftGroups = (function () {
  let shiftArray = [];
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("shift_groups")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        $$("formShiftGroups").clear();
        $$("shift_groups-popup").show();
      });
    $$("btn_shift_grp_close").attachEvent("onItemClick", function (id, e) {
      $$("formShiftGroups").clear();
      $$("formShiftGroups").clearValidation();
      $$("shift_groups-popup").hide();
    });
    $$("btn_shift_grp_save").attachEvent("onItemClick", function (id, e) {
      createGroupShift();
    });
    $$("admin-page")
      .$$("shift_groups")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteGroupShift(id);
      });
    $$("admin-page")
      .$$("shift_groups")
      .$$("grid_shift_groups")
      .attachEvent("onAfterEditStop", function (state, editor, ignoreUpdate) {
        if (state.value !== state.old) {
          updateGroupShifts(editor.row, state.value);
        }
      });
    $$("admin-page")
      .$$("shift_groups")
      .$$("grid_shift_groups")
      .attachEvent("onResize", function (width, height) {
        $$("admin-page")
          .$$("shift_groups")
          .$$("grid_shift_groups")
          .adjustRowHeight("locations", true);
      });
    $$("shift_group_roster").attachEvent("onChange", function (newv, oldv) {
      if (newv != "") {
        load_shifts(newv);
      }
    });
    loadShiftGroups();
  }
  function deleteGroupShift(id) {
    let selected_row = $$("admin-page")
      .$$("shift_groups")
      .$$("grid_shift_groups")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Shift Group",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Shift Group?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/shift_groups",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadShiftGroups();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Shift Group has been selected!");
    }
  }
  function updateGroupShifts(id, locations) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/shift_groups",
        { id: id, locations: locations },
        {
          error: function (err) {},
          success: function () {
            loadShiftGroups();
          },
        },
      );
  }
  function createGroupShift() {
    if ($$("formShiftGroups").validate()) {
      let form = $$("formShiftGroups").getValues();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .post(
          server_url + "/admin/shift_groups",
          { roster: form.shift_group_roster, shift: form.shift_group_shift },
          {
            error: function (err) {},
            success: function () {
              $$("shift_groups-popup").hide();
              loadShiftGroups();
            },
          },
        );
    }
  }
  function loadShiftGroups() {
    $$("admin-page").$$("shift_groups").$$("grid_shift_groups").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/shift_groups",
        {},
        {
          error: function (err) {},
          success: function (results) {
            shift_groups_subject.next(results);
          },
        },
      );
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("shift_group_roster");
    let objValues = {};
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        objValues = { id: value.roster_name, value: value.roster_name };
        options.push(objValues);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  shift_groups_subject.subscribe(function (data) {
    $$("admin-page").$$("shift_groups").$$("grid_shift_groups").parse(data);
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                $$("shift_group_shift").define("options", shiftOptions);
                $$("shift_group_shift").refresh();
              }
            }
          },
        },
      );
  }
  locations_subject.subscribe(function (data) {
    let grid = $$("admin-page").$$("shift_groups").$$("grid_shift_groups");
    let results = JSON.parse(data);
    let locations = [];
    for (let x = 0; x < results.length; x++) {
      locations.push({ id: results[x].name, value: results[x].name });
    }
    grid.config.columns = [
      { id: "id", hidden: true },
      { id: "roster", width: 300, header: "Roster", sort: "string" },
      { id: "shift", width: 300, header: "Shift", sort: "string" },
      {
        id: "locations",
        header: "Locations",
        fillspace: true,
        optionslist: true,
        options: locations,
        editor: "multiselect",
      },
    ];
    grid.refreshColumns();
  });
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
