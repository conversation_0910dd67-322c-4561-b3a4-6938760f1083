let adminSearch = (function () {
    let profile_edit_mode = "";
    let skill_codes = [];
    let shiftArray = [];
    let locationsArray = [];
    let HSarray = [];
    let addressChanged = false;
    let old_street = "";
    let old_suburb = "";
    let old_state = "";
    let old_post_code = "";
    let first_load = false;

    function initApplication() {
        eventHandlers();
        if (live_site === true) {
            getR52Stations();
        }
    }

    function eventHandlers() {
        $$("overtime_hrs_save").attachEvent("onItemClick", function (id, e) {
            let hours = $$("overtime_hrs_balance").getValue();
            let hrs_date = $$("overtime_hrs_date").getValue();
            let pay_id = $$("employee_pay_id").getValue();
            if (hours != "") {
                if (hrs_date != "") {
                    updateRecruitOvertimeHours(pay_id, hours, hrs_date);
                } else {
                    webix.alert({
                        text: "You must enter a 'Date' to continue!",
                        width: 450,
                    });
                }
            } else {
                webix.alert({
                    text: "You must enter the 'Hours' to continue!",
                    width: 450,
                });
            }
        });
        $$("employee_home_street").attachEvent("onTimedKeyPress", function () {
            addressChanged = true;
        });
        $$("employee_home_suburb").attachEvent("onTimedKeyPress", function () {
            addressChanged = true;
        });
        $$("employee_home_state").attachEvent("onTimedKeyPress", function () {
            addressChanged = true;
        });
        $$("employee_home_postcode").attachEvent("onTimedKeyPress", function () {
            addressChanged = true;
        });
        $$("employee_home_street").attachEvent(
            "onChange",
            function (newValue, oldValue, config) {
                if (first_load === false) {
                    addressChanged = true;
                    first_load = false;
                }
            },
        );
        $$("employee_home_suburb").attachEvent(
            "onChange",
            function (newValue, oldValue, config) {
                if (first_load === false) {
                    addressChanged = true;
                    first_load = false;
                }
            },
        );
        $$("employee_home_state").attachEvent(
            "onChange",
            function (newValue, oldValue, config) {
                if (first_load === false) {
                    addressChanged = true;
                    first_load = false;
                }
            },
        );
        $$("employee_home_postcode").attachEvent(
            "onChange",
            function (newValue, oldValue, config) {
                if (first_load === false) {
                    addressChanged = true;
                    first_load = false;
                }
            },
        );
        $$("btn_calculate_travel_distances").attachEvent(
            "onItemClick",
            function (id, e) {
                let pay_id = $$("employee_pay_id").getValue();
                let street = $$("employee_home_street").getValue();
                let suburb = $$("employee_home_suburb").getValue();
                let state = $$("employee_home_state").getValue();
                let post_code = $$("employee_home_postcode").getValue();
                let homeAddress =
                    street + ", " + suburb + ", " + state + ", " + post_code;
                webix.confirm({
                    title: "Get Travel Distances",
                    text: "This will calculate and store all the travel distances between your home address<br>and all the Metro stations. Do you want to proceed?<br><br>Note: Any stored distances will be overwritten with new values from Google Maps!",
                    width: 600,
                    ok: "Continue",
                    cancel: "Cancel",
                    callback: function (result) {
                        switch (result) {
                            case true:
                                webix.message({
                                    id: "travel_distance_info",
                                    text: "Now calculating travel distances...",
                                    type: "debug",
                                    expire: -1,
                                });
                                retrieveTravelDistances(pay_id, homeAddress);
                                updateResDistance(pay_id);
                        }
                    },
                });
            },
        );
        $$("btn_emerald_sync").attachEvent("onItemClick", function (id, e) {
            let eso_id = $$("employee_pay_id").getValue();
            old_street = $$("employee_home_street").getValue();
            old_suburb = $$("employee_home_suburb").getValue();
            old_state = $$("employee_home_state").getValue();
            old_post_code = $$("employee_home_postcode").getValue();
            if (profile_edit_mode === "ADD") {
                if (eso_id !== "") {
                    getEmeraldSyncData(eso_id);
                } else {
                    webix.alert({
                        text: "You must enter a 'Pay/ESO ID' to continue!",
                        width: 500,
                    });
                }
            } else if (profile_edit_mode === "EDIT") {
                getEmeraldSyncData(eso_id);
            }
        });
        $$("employee_new_station").attachEvent("onChange", function (newv) {
            if (newv != "") {
                $$("loader-window").show();
                load_r52_shifts(newv);
            }
        });
        $$("btn_r52_change").attachEvent("onItemClick", function (id, e) {
            let newStation = $$("employee_new_station").getValue();
            let newShift = $$("employee_new_shift").getValue();
            if (newStation != "" && newShift != "") {
                updateStationShift(newStation, newShift);
            } else {
                webix.alert("You must select a 'Station' & 'Shift'!");
            }
        });
        $$("show_hide_pin").attachEvent("onItemClick", function (id, e) {
            const input = $$("employee_app_pin").getInputNode();
            if (input.type == "text") {
                this.define("icon", "wxi-eye");
                this.refresh();
                $$("employee_app_pin").define("type", "password");
                $$("employee_app_pin").refresh();
                webix.html.addCss($$("employee_app_pin").getNode(), "pin_code");
            } else {
                this.define("icon", "wxi-eye-slash");
                this.refresh();
                $$("employee_app_pin").define("type", "text");
                $$("employee_app_pin").refresh();
                webix.html.removeCss($$("employee_app_pin").getNode(), "pin_code");
            }
        });
        $$("default_email_personal").attachEvent("onChange", function (newv) {
            if (newv === 1) {
                $$("default_email_work").setValue(0);
            }
        });
        $$("default_email_work").attachEvent("onChange", function (newv) {
            if (newv === 1) {
                $$("default_email_personal").setValue(0);
            }
        });
        $$("admin-page")
            .$$("admin_search")
            .$$("import_uploader")
            .attachEvent("onBeforeFileAdd", function (file) {
                $$("loader-window").show();
                setTimeout(function () {
                    let sheet = $$("admin-page").$$("admin_search").$$("excel_import");
                    let deleteArray = [];
                    let rowCount = 0;
                    sheet.clearAll();
                    if (file.name.slice(0, 6) != "TASEMP") {
                        webix.alert(
                            "The imported file does not appear to be a valid TASEMP file!",
                        );
                        $$("admin-page").$$("admin_search").$$("import_sheet").disable();
                        $$("loader-window").hide();
                    } else {
                        Papa.parse(file.file, {
                            header: true,
                            worker: true,
                            chunk: true,
                            skipEmptyLines: true,
                            complete: function (results) {
                                sheet.parse(results.data);
                                $$("admin-page")
                                    .$$("admin_search")
                                    .$$("file_name")
                                    .define("label", file.name + " file loaded!");
                                $$("admin-page").$$("admin_search").$$("file_name").refresh();
                                sheet.eachRow(function (row) {
                                    const record = sheet.getItem(row);
                                    if (
                                        record["ADR TYPE"] == "P" ||
                                        record["ADR TYPE"] == "Address Type"
                                    ) {
                                        deleteArray.push(row);
                                    }
                                });
                                sheet.remove(deleteArray);
                                rowCount = sheet.count();
                                $$("admin-page")
                                    .$$("admin_search")
                                    .$$("csv_search_count")
                                    .define("template", rowCount + " employee records found!");
                                $$("admin-page")
                                    .$$("admin_search")
                                    .$$("csv_search_count")
                                    .refresh();
                                $$("loader-window").hide();
                                $$("admin-page").$$("admin_search").$$("import_sheet").enable();
                            },
                        });
                    }
                }, 250);
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("clear_sheet")
            .attachEvent("onItemClick", function (id, e) {
                $$("admin-page").$$("admin_search").$$("excel_import").clearAll();
                $$("admin-page").$$("admin_search").$$("import_sheet").disable();
                $$("admin-page")
                    .$$("admin_search")
                    .$$("file_name")
                    .define("label", "Import Employees from TASEMP");
                $$("admin-page").$$("admin_search").$$("file_name").refresh();
                $$("admin-page")
                    .$$("admin_search")
                    .$$("csv_search_count")
                    .define("template", "");
                $$("admin-page").$$("admin_search").$$("csv_search_count").refresh();
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("import_sheet")
            .attachEvent("onItemClick", function (id, e) {
                $$("loader-window").show();
                setTimeout(function () {
                    importEmployees();
                }, 250);
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("btn_add")
            .attachEvent("onItemClick", function (id, e) {
                first_load = true;
                profile_edit_mode = "ADD";
                $$("formEmployee").clear();
                $$("formEmployee").clearValidation();
                $$("employee-label").define(
                    "label",
                    "<span class='header_font'>Add Employee</span>",
                );
                $$("employee_pay_id").enable();
                $$("employee-label").refresh();
                $$("btn_employee_save").setValue("Save");
                $$("employee_permission_level").setValue(6);
                $$("employee-popup").show();
                $$("employee_rank").enable();
                $$("employee_birth_date").define("readonly", false);
                $$("employee_birth_date").refresh();
                $$("employee_birth_date").setValue(new Date());
                $$("samfs_commencement_date").define("readonly", false);
                $$("samfs_commencement_date").refresh();
                $$("samfs_commencement_date").setValue(new Date());
                $$("govt_commencement_date").define("readonly", false);
                $$("govt_commencement_date").refresh();
                $$("govt_commencement_date").setValue(new Date());
                $$("employee_position_number").define("readonly", false);
                $$("employee_position_number").refresh();
                $$("btn_clear_nfc").disable();
                $$("btn_link_nfc").disable();
                $$("employee_new_station").disable();
                $$("employee_new_shift").disable();
                $$("btn_r52_change").disable();
                $$("employee_details_notice").hide();
                $$("pref_1_save").disable();
                $$("pref_2_save").disable();
                $$("pref_3_save").disable();
                $$("employee_first_name").define("readonly", false);
                $$("employee_first_name").refresh();
                $$("employee_middle_name").define("readonly", false);
                $$("employee_middle_name").refresh();
                $$("employee_surname").define("readonly", false);
                $$("employee_surname").refresh();
                $$("employee_gender").define("readonly", false);
                $$("employee_gender").refresh();
                $$("employee_home_street").define("readonly", false);
                $$("employee_home_street").refresh();
                $$("employee_home_suburb").define("readonly", false);
                $$("employee_home_suburb").refresh();
                $$("employee_home_state").define("readonly", false);
                $$("employee_home_state").refresh();
                $$("employee_home_postcode").define("readonly", false);
                $$("employee_home_postcode").refresh();
                $$("employee_work_mobile").define("readonly", false);
                $$("employee_work_mobile").refresh();
                $$("employee_personal_mobile").define("readonly", false);
                $$("employee_personal_mobile").refresh();
                $$("employee_work_email").define("readonly", false);
                $$("employee_work_email").refresh();
                if (user_permission_level === 1 || user_permission_level === 2) {
                    $$("btn_emerald_sync").enable();
                } else {
                    $$("btn_emerald_sync").disable();
                }
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("btn_delete")
            .attachEvent("onItemClick", function (id, e) {
                let selected_row = $$("admin-page")
                    .$$("admin_search")
                    .$$("search_results")
                    .getSelectedItem(id);
                if (selected_row !== undefined) {
                    if (selected_row.length > 0) {
                        let pay_id = selected_row[0].pay_id;
                        webix.confirm({
                            title: "Delete Employee",
                            ok: "Confirm",
                            cancel: "Cancel",
                            width: 500,
                            text: "Warning: If you delete an Employee and they have active Roster Arrangements & Bookings then the Roster and any report will not show these as there would be no link to the associated Pay ID</br></br>Please confirm that you want to delete the selected Employee?",
                            callback: function (result) {
                                switch (result) {
                                    case true:
                                        deleteEmployee(pay_id);
                                }
                            },
                        });
                    }
                }
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("btn_terminate")
            .attachEvent("onItemClick", function (id, e) {
                let selected_row = $$("admin-page")
                    .$$("admin_search")
                    .$$("search_results")
                    .getSelectedItem(id);
                if (selected_row !== undefined) {
                    if (selected_row.length > 0) {
                        if (selected_row[0].emp_status == "Inactive") {
                            webix.confirm({
                                title: "Reinstate Employee",
                                ok: "Yes",
                                cancel: "No",
                                text: "Are you sure you want to reinstate the selected Employees?",
                                callback: function (result) {
                                    switch (result) {
                                        case true:
                                            unTerminateEmployee(selected_row[0].pay_id);
                                    }
                                },
                            });
                        } else {
                            $$("terminate_employee-popup").show();
                        }
                    }
                }
            });
        $$("btn_terminate_employee_close").attachEvent(
            "onItemClick",
            function (id, e) {
                $$("terminate_employee-popup").hide();
            },
        );
        $$("btn_employee_station_distances").attachEvent(
            "onItemClick",
            function (id, e) {
                if (user_permission_level === 1) {
                    let pay_id = $$("employee_pay_id").getValue();
                    getEmployeeStationDistances(pay_id);
                    $$("travel_distances-popup").show();
                } else {
                    webix.alert({
                        text: "You don't have permission to view the 'Travel Distances'!",
                        width: 450,
                    });
                }
            },
        );
        $$("btn_travel_distances_close").attachEvent(
            "onItemClick",
            function (id, e) {
                $$("travel_distances-popup").hide();
            },
        );
        $$("btn_terminate_employee").attachEvent("onItemClick", function (id, e) {
            let selected_row = $$("admin-page")
                .$$("admin_search")
                .$$("search_results")
                .getSelectedItem(id);
            let pay_id = selected_row[0].pay_id;
            terminateEmployee(pay_id);
        });
        $$("btn_employee_close").attachEvent("onItemClick", function (id, e) {
            $$("employee-popup").hide();
            $$("app_sub_menu").hide();
            $$("app_sub_menu_list").unselect("profile");
        });
        $$("admin-page")
            .$$("admin_search")
            .$$("btn_search")
            .attachEvent("onItemClick", function (id, e) {
                $$("admin-page")
                    .$$("roster_arrangement")
                    .$$("show_temp_ra")
                    .setValue(0);
                getEmployeeResults();
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("search_field")
            .attachEvent("onEnter", function (ev) {
                $$("admin-page")
                    .$$("roster_arrangement")
                    .$$("show_temp_ra")
                    .setValue(0);
                getEmployeeResults();
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("all_rosters")
            .attachEvent("onChange", function (newv, oldv) {
                if (newv === 1) {
                    $$("admin-page").$$("admin_search").$$("rosters").disable();
                    $$("admin-page").$$("admin_search").$$("rosters").setValue("");
                } else {
                    $$("admin-page").$$("admin_search").$$("rosters").enable();
                }
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("search_results")
            .attachEvent("onAfterEditStop", function (state, editor, ignoreUpdate) {
                getUserPL(function (result) {
                    if (result === 1 || result === 2 || result === 3) {
                        if (editor.column == "skill_codes") {
                            if (state.value !== state.old) {
                                let data = $$("admin-page")
                                    .$$("admin_search")
                                    .$$("search_results")
                                    .getItem(editor.row);
                                deleteAllSkills(data.pay_id, function (callback) {
                                    if (callback === "ok") {
                                        updateSkills(data.pay_id, data.skill_codes);
                                    }
                                });
                            }
                        }
                    }
                })
            });
        $$("admin-page")
            .$$("admin_search")
            .$$("search_results")
            .attachEvent("onResize", function (width, height) {
                $$("admin-page")
                    .$$("admin_search")
                    .$$("search_results")
                    .adjustRowHeight("skill_codes", true);
            });

        $$("admin-page").$$("admin_search").$$("search_results").attachEvent("onItemDblClick", function (id) {
            first_load = true;
            getUserPL(function (result) {
                if (result === 1 || result === 2) {
                    profile_edit_mode = "EDIT";
                    $$("employee_rank").disable();
                    $$("employee_birth_date").define("readonly", false);
                    $$("employee_birth_date").refresh();
                    $$("samfs_commencement_date").define("readonly", false);
                    $$("samfs_commencement_date").refresh();
                    $$("govt_commencement_date").define("readonly", false);
                    $$("govt_commencement_date").refresh();
                    $$("employee_position_number").define("readonly", false);
                    $$("employee_position_number").refresh();
                    let selected_row = $$("admin-page")
                        .$$("admin_search")
                        .$$("search_results")
                        .getSelectedItem(id);
                    if (selected_row !== undefined) {
                        if (selected_row[0].id > 0) {
                            let pay_id = selected_row[0].pay_id;
                            getEmployeeInfo(pay_id);
                            if (selected_row[0].emp_status == "Inactive") {
                                $$("employee-label").define(
                                    "label",
                                    "<span class='header_font'>Edit Employee</span>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<span style='text-align: center; background-color: white; color: orangered'>--------------- Employment Status: INACTIVE ---------------</span>",
                                );
                            } else if (selected_row[0].emp_status == "Deleted") {
                                $$("employee-label").define(
                                    "label",
                                    "<span class='header_font'>Edit Employee</span>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<span style='text-align: center; background-color: white; color: red'>--------------- Employment Status: DELETED ---------------</span>",
                                );
                            } else {
                                $$("employee-label").define(
                                    "label",
                                    "<span class='header_font'>Edit Employee</span>&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<span style='text-align: center; background-color: white; color: green'>--------------- Employment Status: ACTIVE ---------------</span>",
                                );
                            }
                            $$("employee_pay_id").disable();
                            $$("employee-label").refresh();
                            $$("btn_employee_save").setValue("Update");
                            if (result === 1 || result === 2) {
                                $$("formEmployee").enable();
                                $$("btn_emerald_sync").enable();
                            } else {
                                for (let name in $$("formEmployee").elements) {
                                    $$("formEmployee").elements[name].disable();
                                }
                                $$("btn_emerald_sync").disable();
                            }
                            if (user_logged_in != pay_id) {
                                $$("employee_app_pin").disable();
                                $$("show_hide_pin").disable();
                            } else {
                                $$("employee_app_pin").enable();
                                $$("show_hide_pin").enable();
                            }
                            if (result === 1) {
                                $$("employee_permission_level").enable();
                                $$("btn_clear_nfc").enable();
                                $$("btn_link_nfc").enable();
                                $$("employee_new_station").enable();
                                $$("employee_new_shift").enable();
                                $$("btn_r52_change").enable();
                                $$("overtime_hrs_balance").enable();
                                $$("overtime_hrs_date").enable();
                                $$("overtime_hrs_save").enable();
                            } else {
                                $$("employee_permission_level").disable();
                                $$("btn_clear_nfc").disable();
                                $$("btn_link_nfc").disable();
                                $$("employee_new_station").disable();
                                $$("employee_new_shift").disable();
                                $$("btn_r52_change").disable();
                                $$("overtime_hrs_balance").disable();
                                $$("overtime_hrs_date").disable();
                                $$("overtime_hrs_save").disable();
                            }
                            $$("employee-popup").show();
                        }
                    }
                } else {
                    $$("employee_birth_date").define("readonly", true);
                    $$("employee_birth_date").refresh();
                    $$("samfs_commencement_date").define("readonly", true);
                    $$("samfs_commencement_date").refresh();
                    $$("govt_commencement_date").define("readonly", true);
                    $$("govt_commencement_date").refresh();
                    $$("employee_position_number").define("readonly", true);
                    $$("employee_position_number").refresh();
                    $$("btn_emerald_sync").disable();
                }
                $$("employee_personal_email").enable();
            });
        })

        $$("btn_clear_nfc").attachEvent("onItemClick", function (id, e) {
            let pay_id = $$("employee_pay_id").getValue();
            webix.confirm({
                title: "Clear NFC tag",
                ok: "Yes",
                cancel: "No",
                text: "Are you sure you want to clear the current NFC tag linked to this employee?",
                width: 500,
                callback: function (result) {
                    switch (result) {
                        case true:
                            webix.ajax().post(
                                "https://samfs.respond52.com/mobile/firebase/user/nfc/clear",
                                {user_id: pay_id, api_key: api_key},
                                {
                                    error: function (err) {
                                        webix.alert("There was an error clearing the NFC tag!");
                                    },
                                    success: function (result) {
                                        if (result == "OK") {
                                            webix.alert("NFC tag cleared successfully!");
                                        }
                                    },
                                },
                            );
                    }
                },
            });
        });

        $$("btn_link_nfc").attachEvent("onItemClick", function (id, e) {
            let pay_id = $$("employee_pay_id").getValue();
            webix.ajax().get(
                "https://samfs.respond52.com/mobile/firebase/user/exists",
                {user_id: pay_id, api_key: api_key},
                {
                    error: function (err) {
                    },
                    success: function (response) {
                        if (response == "Error: User Not Found") {
                            let form = $$("formEmployee").getValues();
                            let curr_rank = "";
                            let curr_shift = "";
                            getEmployeeRankShift(pay_id, function (results) {
                                curr_rank = results[0].rank;
                                curr_shift = results[0].shift;
                                webix.ajax().post(
                                    "https://samfs.respond52.com/mobile/firebase/user/create",
                                    {
                                        user_id: pay_id,
                                        api_key: api_key,
                                        given_name: form.employee_first_name,
                                        family_name: form.employee_surname,
                                        station_id: form.employee_station_id,
                                        rank: curr_rank,
                                        shift: curr_shift,
                                        phone: form.employee_personal_mobile,
                                    },
                                    {
                                        error: function (err) {
                                            webix.alert({
                                                text: "There was an error attempting to create and entry for this user in Respond 52!",
                                                width: 400,
                                            });
                                        },
                                        success: function (response) {
                                            $$("nfc_tag-popup").show();
                                        },
                                    },
                                );
                            });
                        } else {
                            $$("nfc_tag-popup").show();
                        }
                    },
                },
            );
        });
        $$("btn_nfc_tag_close").attachEvent("onItemClick", function (id, e) {
            $$("nfc_tag-popup").hide();
        });
        $$("btn_leave_balances_close").attachEvent("onItemClick", function (id, e) {
            $$("leave_balances-popup").hide();
            $$("app_sub_menu").hide();
            $$("app_sub_menu_list").unselect("balances");
        });
        $$("btn_assign_nfc_tag").attachEvent("onItemClick", function (id, e) {
            let pay_id = $$("employee_pay_id").getValue();
            let nfc_id = $$("nfc_tag_string").getValue();
            if (nfc_id != "") {
                webix.ajax().post(
                    "https://samfs.respond52.com/mobile/firebase/user/nfc/assign",
                    {user_id: pay_id, api_key: api_key, nfc_id: nfc_id},
                    {
                        error: function (err) {
                            webix.alert("There was an error assigning the NFC tag!");
                        },
                        success: function () {
                            webix.alert("NFC tag assigned successfully!");
                            $$("formNFCtag").clear();
                            $$("nfc_tag-popup").hide();
                            getUserExistsInR52(pay_id);
                        },
                    },
                );
            } else {
                webix.alert({
                    text: "No NFC tag has been scanned / registered!",
                    width: 400,
                });
            }
        });
        $$("btn_employee_save").attachEvent("onItemClick", function (id, e) {
            let work_phone_no = $$("employee_work_mobile").getValue();
            let personal_phone_no = $$("employee_personal_mobile").getValue();
            let pay_id = $$("employee_pay_id").getValue();
            let rank = $$("employee_rank").getValue();
            let eso_email = $$("employee_work_email").getValue();
            if (profile_edit_mode == "ADD") {
                if (isNumber(pay_id)) {
                    checkPayID(pay_id, function (callback) {
                        if (callback.length == 0) {
                            if (isNumber(personal_phone_no)) {
                                if (rank != "?" && rank != "") {
                                    if (eso_email == "") {
                                        webix.alert({
                                            text: "<div>The Work/ESO email address field cannot be left blank.<br>This must be a valid ESO email address as this is used to link this profile to the Microsoft SSO login!<br>Note: The email should be in the format, example <i>{first name}<strong>.</strong>{surname}</i><strong>@eso.sa.gov.au</strong></div>",
                                            width: 650,
                                        });
                                    } else if (eso_email.slice(-14) != "@eso.sa.gov.au") {
                                        webix.alert({
                                            text: "<div>The Work/ESO email address field must be a valid ESO email address<br>as this is used to link this profile to the Microsoft SSO login!<br>Note: The email should be in the format, example <i>{first name}<strong>.</strong>{surname}</i><strong>@eso.sa.gov.au</strong></div>",
                                            width: 650,
                                        });
                                    } else {
                                        updateEmployeeInfo(pay_id);
                                    }
                                } else {
                                    webix.alert({
                                        text: "You must specify a valid Rank!",
                                        width: 400,
                                    });
                                }
                            } else {
                                webix.alert({
                                    text: "The 'Personal' mobile number(s) do not appear to be valid!</br>It must not be blank or less than 10 digits (with no spaces)!",
                                    width: 400,
                                });
                            }
                        } else {
                            let employeeName =
                                callback[0].surname + ", " + callback[0].first_name;
                            webix.alert({
                                text:
                                    "The entered Pay ID already exists for " +
                                    employeeName +
                                    "</br></br>You can either change the Pay ID or search for this employee and edit the details if required!",
                                width: 400,
                            });
                        }
                    });
                } else {
                    webix.alert({
                        text: "The Pay ID does not appear to be valid!</br>It must be a 7 digit number with no spaces!",
                        width: 400,
                    });
                }
            } else if (profile_edit_mode == "EDIT") {
                if (isNumber(pay_id)) {
                    if (isNumber(work_phone_no) || isNumber(personal_phone_no)) {
                        if (rank != "?" && rank != "") {
                            if (
                                $$("default_email_personal").getValue() === 0 &&
                                $$("default_email_work").getValue() === 0
                            ) {
                                if (user_permission_level === 1) {
                                    if (eso_email == "") {
                                        webix.alert({
                                            text: "<div>The Work/ESO email address field cannot be left blank.<br>This must be a valid ESO email address as this is used to link this profile to the Microsoft SSO login!<br>Note: The email should be in the format, example <i>{first name}<strong>.</strong>{surname}</i><strong>@eso.sa.gov.au</strong></div>",
                                            width: 650,
                                        });
                                    } else if (eso_email.slice(-14) != "@eso.sa.gov.au") {
                                        webix.alert({
                                            text: "<div>The Work/ESO email address field must be a valid ESO email address<br>as this is used to link this profile to the Microsoft SSO login!<br>Note: The email should be in the format, example <i>{first name}<strong>.</strong>{surname}</i><strong>@eso.sa.gov.au</strong></div>",
                                            width: 650,
                                        });
                                    } else {
                                        updateEmployeeInfo(pay_id);
                                    }
                                } else {
                                    webix.alert({
                                        text: "You must select a notification email by placing a tick next to either your Personal or Work/ESO email address!",
                                        width: 650,
                                    });
                                }
                            } else {
                                if (
                                    $$("default_email_personal").getValue() === 1 &&
                                    $$("employee_personal_email").getValue() == ""
                                ) {
                                    webix.alert({
                                        text: "You can't use the selected notification email because you have not specified a Personal email address!",
                                        width: 650,
                                    });
                                } else if (
                                    $$("default_email_work").getValue() === 1 &&
                                    $$("employee_work_email").getValue() == ""
                                ) {
                                    webix.alert({
                                        text: "You can't use the selected notification email because you have not specified a Work/ESO email address!",
                                        width: 650,
                                    });
                                } else {
                                    if (eso_email == "") {
                                        webix.alert({
                                            text: "<div>The Work/ESO email address field cannot be left blank.<br>This must be a valid ESO email address as this is used to link this profile to the Microsoft SSO login!<br>Note: The email should be in the format, example <i>{first name}<strong>.</strong>{surname}</i><strong>@eso.sa.gov.au</strong></div>",
                                            width: 650,
                                        });
                                    } else if (eso_email.slice(-14) != "@eso.sa.gov.au") {
                                        webix.alert({
                                            text: "<div>The Work/ESO email address field must be a valid ESO email address<br>as this is used to link this profile to the Microsoft SSO login!<br>Note: The email should be in the format, example <i>{first name}<strong>.</strong>{surname}</i><strong>@eso.sa.gov.au</strong></div>",
                                            width: 650,
                                        });
                                    } else {
                                        updateEmployeeInfo(pay_id);
                                    }
                                }
                            }
                        } else {
                            webix.alert({
                                text: "You must specify a valid Rank!",
                                width: 400,
                            });
                        }
                    } else {
                        webix.alert({
                            text: "The 'Work' or 'Personal' mobile number(s) do not appear to be valid!</br>They must be a 10 digit number with no spaces!",
                            width: 400,
                        });
                    }
                } else {
                    webix.alert({
                        text: "The Pay ID does not appear to be valid!</br>It must be a 7 digit number with no spaces!",
                        width: 400,
                    });
                }
            }
        });
        $$("employee_roster_pref_1").attachEvent("onChange", function (newv, oldv) {
            $$("employee_shift_pref_1").setValue("");
            $$("employee_location_pref_1").setValue("");
            load_shifts(newv, 1);
            if (newv == "OTR") {
                $$("employee_shift_pref_1").setValue("OTR");
                $$("employee_location_pref_1").setValue("OTR");
            }
        });
        $$("employee_roster_pref_2").attachEvent("onChange", function (newv, oldv) {
            $$("employee_shift_pref_2").setValue("");
            $$("employee_location_pref_2").setValue("");
            load_shifts(newv, 2);
            if (newv == "OTR") {
                $$("employee_shift_pref_2").setValue("OTR");
                $$("employee_location_pref_2").setValue("OTR");
            }
        });
        $$("employee_roster_pref_3").attachEvent("onChange", function (newv, oldv) {
            $$("employee_shift_pref_3").setValue("");
            $$("employee_location_pref_3").setValue("");
            load_shifts(newv, 3);
            if (newv == "OTR") {
                $$("employee_shift_pref_3").setValue("OTR");
                $$("employee_location_pref_3").setValue("OTR");
            }
        });
        $$("pref_1_save").attachEvent("onItemClick", function (id, e) {
            let selected_rank = $$("employee_rank_pref_1").getValue();
            let current_rank = HSarray[0].employee_rank_pref_1;
            if (
                selected_rank == "" ||
                selected_rank == null ||
                selected_rank == undefined
            ) {
                webix.alert("You must select a 'Rank' for the new Preference!");
            } else {
                if (selected_rank == current_rank) {
                    updateHSpreference(1);
                } else {
                    webix.alert({
                        text: "The selected Rank for the new Home Station preference is different to your current Rank preference, this is not allowed.</br></br>Note: A SP18 & SP19 (if change of shift) form is required to be sent to Workforce Rostering.",
                        width: 450,
                    });
                }
            }
        });
        $$("pref_2_save").attachEvent("onItemClick", function (id, e) {
            let selected_rank = $$("employee_rank_pref_2").getValue();
            let current_rank = HSarray[0].employee_rank_pref_2;
            if (
                selected_rank == "" ||
                selected_rank == null ||
                selected_rank == undefined
            ) {
                webix.alert("You must select a 'Rank' for the new Preference!");
            } else {
                if (selected_rank == current_rank) {
                    updateHSpreference(2);
                } else {
                    webix.alert({
                        text: "The selected Rank for the new Home Station preference is different to your current Rank preference, this is not allowed.</br></br>Note: A SP18 & SP19 (if change of shift) form is required to be sent to Workforce Rostering.",
                        width: 450,
                    });
                }
            }
        });
        $$("pref_3_save").attachEvent("onItemClick", function (id, e) {
            let selected_rank = $$("employee_rank_pref_3").getValue();
            let current_rank = HSarray[0].employee_rank_pref_3;
            if (
                selected_rank == "" ||
                selected_rank == null ||
                selected_rank == undefined
            ) {
                webix.alert("You must select a 'Rank' for the new Preference!");
            } else {
                if (selected_rank == current_rank) {
                    updateHSpreference(3);
                } else {
                    webix.alert({
                        text: "The selected Rank for the new Home Station preference is different to your current Rank preference, this is not allowed.</br></br>Note: A SP18 & SP19 (if change of shift) form is required to be sent to Workforce Rostering.",
                        width: 450,
                    });
                }
            }
        });
        $$("employee_pref_logs").attachEvent("onItemClick", function (id, e) {
            getHSChangeLogs();
            $$("hs_preferences-popup").show();
        });
        $$("btn_hs_preferences_close").attachEvent("onItemClick", function (id, e) {
            $$("hs_preferences-popup").hide();
        });
    }

    function getHSChangeLogs() {
        let grid = $$("hs_preferences_grid");
        let pay_id = $$("employee_pay_id").getValue();
        let log_results = [];
        grid.clearAll();
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_hs_change_logs",
                {pay_id: pay_id},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        let result = JSON.parse(results);
                        let pref_no = "";
                        if (result.length > 0) {
                            for (let x = 0; x < result.length; x++) {
                                if (result[x].pref_no == 1) {
                                    pref_no = "1st";
                                } else if (result[x].pref_no == 2) {
                                    pref_no = "2nd";
                                }
                                if (result[x].pref_no == 3) {
                                    pref_no = "3rd";
                                }
                                log_results.push({
                                    pref_no: pref_no,
                                    roster: result[x].roster,
                                    shift: result[x].shift,
                                    location: result[x].location,
                                    rank: result[x].rank,
                                    updated_date: result[x].updated_date,
                                    updated_by: result[x].updated_by,
                                });
                            }
                        }
                        grid.define("data", log_results);
                        grid.refresh();
                    },
                },
            );
    }

    function load_r52_shifts(station_id) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/respond52/get_r52_shifts",
                {station_id: station_id},
                {
                    error: function (err) {
                        $$("loader-window").hide();
                        webix.alert("Error loading shifts. Please try again!");
                    },
                    success: function (results) {
                        let values = JSON.parse(results);
                        $$("employee_new_shift").define("options", values);
                        $$("employee_new_shift").refresh();
                        $$("loader-window").hide();
                    },
                },
            );
    }

    function getEmployeeRankShift(pay_id, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_employee_rank_shift",
                {pay_id: pay_id},
                {
                    error: function (err) {
                        callback([]);
                    },
                    success: function (results) {
                        let values = JSON.parse(results);
                        callback(values);
                    },
                },
            );
    }

    function deleteEmployee(pay_id) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .put(
                server_url + "/admin/delete_employee",
                {pay_id: pay_id, removed_by: user_logged_in},
                {
                    error: function (err) {
                        webix.alert("There was an error deleting the selected Employee!");
                    },
                    success: function (results) {
                        webix.alert("Employee was deleted successfully!");
                        $$("admin-page")
                            .$$("roster_arrangement")
                            .$$("show_temp_ra")
                            .setValue(0);
                        getEmployeeResults();
                    },
                },
            );
    }

    function unTerminateEmployee(pay_id) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .put(
                server_url + "/admin/un_terminate_employee",
                {pay_id: pay_id},
                {
                    error: function (err) {
                        webix.alert(
                            "There was an error updating the selected Employee's status!",
                        );
                    },
                    success: function (results) {
                        webix.alert("Employee's status was changed successfully!");
                        getEmployeeResults();
                    },
                },
            );
    }

    function terminateEmployee(pay_id) {
        $$("loader-window").show();
        let termination_date = $$("terminate_employee_date").getValue();
        let termination_date_string = moment(termination_date).format("YYYYMMDD");
        setTimeout(function () {
            webix
                .ajax()
                .headers({Authorization: "Bearer " + api_key})
                .put(
                    server_url + "/admin/terminate_employee",
                    {pay_id: pay_id, terminated_by: user_logged_in},
                    {
                        error: function (err) {
                            $$("loader-window").hide();
                            webix.alert(
                                "There was an error updating the selected Employee's status!",
                            );
                        },
                        success: function (results) {
                            webix.alert("Employee's status was changed successfully!");
                            $$("admin-page")
                                .$$("roster_arrangement")
                                .$$("show_temp_ra")
                                .setValue(0);
                            updateRAEndDate(
                                pay_id,
                                termination_date_string,
                                function (response) {
                                    if (response == "ok") {
                                        $$("loader-window").hide();
                                        $$("terminate_employee-popup").hide();
                                        getEmployeeResults();
                                    }
                                },
                            );
                        },
                    },
                );
        }, 250);
    }

    function updateRAEndDate(pay_id, termination_date, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .del(
                server_url + "/admin/delete_ra_days",
                {pay_id: pay_id, termination_date: termination_date},
                {
                    error: function (err) {
                        callback("error");
                    },
                    success: function () {
                        callback("ok");
                    },
                },
            );
    }

    function deleteAllSkills(payId, callback) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .del(
                server_url + "/admin/employee_skills",
                {pay_id: payId},
                {
                    error: function (err) {
                    },
                    success: function () {
                        callback("ok");
                    },
                },
            );
    }

    function updateSkills(PayId, codes) {
        let skillCodes = [];
        if (codes !== "") {
            skillCodes = csvStringToArray(codes);
            for (let x = 0; x < skillCodes[0].length; x++) {
                webix
                    .ajax()
                    .headers({Authorization: "Bearer " + api_key})
                    .sync()
                    .put(
                        server_url + "/admin/employee_skills",
                        {
                            pay_id: PayId,
                            code: skillCodes[0][x],
                            expire_date: new Date("2099-12-31 23:59:59"),
                        },
                        {
                            error: function (err) {
                            }, success: function () {
                            }
                        },
                    );
            }
        }
    }

    function getHSpreferences(pay_id) {
        HSarray = [];
        $$("employee_roster_pref_1").setValue("");
        $$("employee_shift_pref_1").setValue("");
        $$("employee_location_pref_1").setValue("");
        $$("employee_rank_pref_1").setValue("");
        $$("employee_roster_pref_2").setValue("");
        $$("employee_shift_pref_2").setValue("");
        $$("employee_location_pref_2").setValue("");
        $$("employee_rank_pref_2").setValue("");
        $$("employee_roster_pref_3").setValue("");
        $$("employee_shift_pref_3").setValue("");
        $$("employee_location_pref_3").setValue("");
        $$("employee_rank_pref_3").setValue("");
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .sync()
            .get(
                server_url + "/admin/get_employee_hs_preference",
                {pay_id: pay_id},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        let values = JSON.parse(results);
                        const HSobject = {};
                        if (values.length > 0) {
                            values.forEach(function (value) {
                                if (value.pref_no === 1) {
                                    $$("employee_roster_pref_1").setValue(value.roster);
                                    $$("employee_shift_pref_1").setValue(value.shift);
                                    $$("employee_location_pref_1").setValue(value.location);
                                    $$("employee_rank_pref_1").setValue(value.rank);
                                    HSobject.employee_roster_pref_1 = value.roster;
                                    HSobject.employee_shift_pref_1 = value.shift;
                                    HSobject.employee_location_pref_1 = value.location;
                                    HSobject.employee_rank_pref_1 = value.rank;
                                } else if (value.pref_no === 2) {
                                    $$("employee_roster_pref_2").setValue(value.roster);
                                    $$("employee_shift_pref_2").setValue(value.shift);
                                    $$("employee_location_pref_2").setValue(value.location);
                                    $$("employee_rank_pref_2").setValue(value.rank);
                                    HSobject.employee_roster_pref_2 = value.roster;
                                    HSobject.employee_shift_pref_2 = value.shift;
                                    HSobject.employee_location_pref_2 = value.location;
                                    HSobject.employee_rank_pref_2 = value.rank;
                                } else if (value.pref_no === 3) {
                                    $$("employee_roster_pref_3").setValue(value.roster);
                                    $$("employee_shift_pref_3").setValue(value.shift);
                                    $$("employee_location_pref_3").setValue(value.location);
                                    $$("employee_rank_pref_3").setValue(value.rank);
                                    HSobject.employee_roster_pref_3 = value.roster;
                                    HSobject.employee_shift_pref_3 = value.shift;
                                    HSobject.employee_location_pref_3 = value.location;
                                    HSobject.employee_rank_pref_3 = value.rank;
                                }
                            });
                            HSarray.push(HSobject);
                        } else {
                            HSobject.employee_roster_pref_1 = "";
                            HSobject.employee_shift_pref_1 = "";
                            HSobject.employee_location_pref_1 = "";
                            HSobject.employee_rank_pref_1 = "";
                            HSobject.employee_roster_pref_2 = "";
                            HSobject.employee_shift_pref_2 = "";
                            HSobject.employee_location_pref_2 = "";
                            HSobject.employee_rank_pref_2 = "";
                            HSobject.employee_roster_pref_3 = "";
                            HSobject.employee_shift_pref_3 = "";
                            HSobject.employee_location_pref_3 = "";
                            HSobject.employee_rank_pref_3 = "";
                            HSarray.push(HSobject);
                        }
                    },
                },
            );
    }

    function updateHSpreference(prefNo) {
        let form = $$("formEmployee").getValues();
        let roster = "";
        let shift = "";
        let location = "";
        let rank = "";
        let roster_text = "";
        let continueSave = true;
        let stPrefNo = "";
        if (prefNo === 1) {
            stPrefNo = "1st";
            roster = $$("employee_roster_pref_1").getValue();
            shift = $$("employee_shift_pref_1").getValue();
            location = $$("employee_location_pref_1").getValue();
            rank = $$("employee_rank_pref_1").getValue();
            roster_text = $$("employee_roster_pref_1").getText();
            if (
                shift != curr_user_shift &&
                (user_permission_level === 3 ||
                    user_permission_level === 4 ||
                    user_permission_level === 5 ||
                    user_permission_level === 6)
            ) {
                continueSave = false;
                webix.alert({
                    text: "In order to change a preference to another 'Shift' you will need</br>to submit a SP18 form to Workforce Rostering.</br></br>Note: No changes have been saved!",
                    width: 500,
                });
                getHSpreferences(form.employee_pay_id);
            }
            if (
                roster == HSarray[0].employee_roster_pref_1 &&
                shift == HSarray[0].employee_shift_pref_1 &&
                location == HSarray[0].employee_location_pref_1 &&
                rank == HSarray[0].employee_rank_pref_1
            ) {
                continueSave = false;
                webix.alert({
                    text: "The new values are the same as the currently stored/saved values!</br></br>Note: No changes have been saved.",
                    width: 500,
                });
            }
        } else if (prefNo === 2) {
            stPrefNo = "2nd";
            roster = $$("employee_roster_pref_2").getValue();
            shift = $$("employee_shift_pref_2").getValue();
            location = $$("employee_location_pref_2").getValue();
            rank = $$("employee_rank_pref_2").getValue();
            roster_text = $$("employee_roster_pref_2").getText();
            if (
                shift != curr_user_shift &&
                (user_permission_level === 3 ||
                    user_permission_level === 4 ||
                    user_permission_level === 5 ||
                    user_permission_level === 6)
            ) {
                continueSave = false;
                webix.alert({
                    text: "In order to change a preference to another 'Shift' you will need</br>to submit a SP18 form to Workforce Rostering.</br></br>Note: No changes have been saved!",
                    width: 500,
                });
                getHSpreferences(form.employee_pay_id);
            }
            if (
                roster == HSarray[0].employee_roster_pref_2 &&
                shift == HSarray[0].employee_shift_pref_2 &&
                location == HSarray[0].employee_location_pref_2 &&
                rank == HSarray[0].employee_rank_pref_2
            ) {
                continueSave = false;
                webix.alert({
                    text: "The new values are the same as the currently stored/saved values!</br></br>Note: No changes have been saved.",
                    width: 500,
                });
            }
        } else if (prefNo === 3) {
            stPrefNo = "3rd";
            roster = $$("employee_roster_pref_3").getValue();
            shift = $$("employee_shift_pref_3").getValue();
            location = $$("employee_location_pref_3").getValue();
            rank = $$("employee_rank_pref_3").getValue();
            roster_text = $$("employee_roster_pref_3").getText();
            if (
                shift != curr_user_shift &&
                (user_permission_level === 3 ||
                    user_permission_level === 4 ||
                    user_permission_level === 5 ||
                    user_permission_level === 6)
            ) {
                continueSave = false;
                webix.alert({
                    text: "In order to change a preference to another 'Shift' you will need</br>to submit a SP18 form to Workforce Rostering.</br></br>Note: No changes have been saved!",
                    width: 500,
                });
                getHSpreferences(form.employee_pay_id);
            }
            if (
                roster == HSarray[0].employee_roster_pref_3 &&
                shift == HSarray[0].employee_shift_pref_3 &&
                location == HSarray[0].employee_location_pref_3 &&
                rank == HSarray[0].employee_rank_pref_3
            ) {
                continueSave = false;
                webix.alert({
                    text: "The new values are the same as the currently stored/saved values!</br></br>Note: No changes have been saved.",
                    width: 500,
                });
            }
        }
        if (roster_text !== "" && (shift == "" || location == "")) {
            webix.alert({
                text: "You must select all 4 columns before you can Save!",
                width: 450,
            });
        } else {
            if (
                roster_text === "" ||
                roster_text === undefined ||
                roster_text == null
            ) {
                roster = "";
                shift = "";
                location = "";
            }
            if (continueSave === true) {
                webix
                    .ajax()
                    .headers({Authorization: "Bearer " + api_key})
                    .post(
                        server_url + "/admin/update_employee_hs_preference",
                        {
                            pay_id: form.employee_pay_id,
                            pref_no: prefNo,
                            roster: roster,
                            shift: shift,
                            location: location,
                            rank: rank,
                            updated_by: user_logged_in,
                        },
                        {
                            error: function (err) {
                                webix.alert({
                                    text: "There was an error updating the Station preference.</br>Please try again!",
                                    width: 450,
                                });
                            },
                            success: function () {
                                if (prefNo === 1) {
                                    webix.alert({
                                        text: "Employee's 1st Home Station preference was updated!",
                                        width: 500,
                                    });
                                } else if (prefNo === 2) {
                                    webix.alert({
                                        text: "Employee's 2nd Home Station preference was updated!",
                                        width: 500,
                                    });
                                } else if (prefNo === 3) {
                                    webix.alert({
                                        text: "Employee's 3rd Home Station preference was updated!",
                                        width: 500,
                                    });
                                }
                                getHSpreferences(form.employee_pay_id);
                                if (live_site === true) {
                                    getEmployeeData(form.employee_pay_id, function (values) {
                                        let properSurname = toProperCase(values[0].surname);
                                        let emailBody1 =
                                            "A change of <strong>(" +
                                            stPrefNo +
                                            ")</strong> station preference for " +
                                            values[0].first_name +
                                            " " +
                                            properSurname +
                                            " (" +
                                            form.employee_pay_id +
                                            ") was made on " +
                                            moment().format("DD/MM/YYYY") +
                                            " to the following;";
                                        let emailBody2 =
                                            "It has been noted that you have requested a change of your <strong>(" +
                                            stPrefNo +
                                            ")</strong> station preference made on " +
                                            moment().format("DD/MM/YYYY") +
                                            " to the following;";
                                        sendEmail(
                                            "SAPPHIRE<<EMAIL>>",
                                            "SAPPHIRE<<EMAIL>>",
                                            "RE: Home Station Preference Change",
                                            emailBody1,
                                            "<strong>" +
                                            roster +
                                            " | " +
                                            shift +
                                            " | " +
                                            location +
                                            "</strong>",
                                            "WFR Staff Member",
                                            "Note: Please process this change in Chris 21",
                                            "Regards, The SAPPHIRE Team",
                                        );
                                        sendEmail(
                                            "SAPPHIRE<<EMAIL>>",
                                            values[0].notifications_email,
                                            "RE: Home Station Preference Change",
                                            emailBody2,
                                            "<strong>" +
                                            roster +
                                            " | " +
                                            shift +
                                            " | " +
                                            location +
                                            "</strong>",
                                            values[0].first_name + " " + properSurname,
                                            "Note: This <u>will not</u> become effective until entered into Chris 21 by Workforce Rostering!",
                                            "Regards, The SAPPHIRE Team",
                                        );
                                    });
                                }
                            },
                        },
                    );
            }
        }
    }

    function updateEmployeeInfo(pay_id) {
        let form = $$("formEmployee").getValues();
        let rank_index = 0;
        let default_email = "";
        let no_email_default_selected = true;
        let hide_phone = 0;
        if (form.employee_rank_class == "FFA001") {
            rank_index = 13;
        } else if (form.employee_rank_class == "FFA002") {
            rank_index = 14;
        } else if (form.employee_rank_class == "FFA003") {
            rank_index = 15;
        } else if (form.employee_rank_class == "FFB001") {
            rank_index = 16;
        } else if (form.employee_rank_class == "FFC001") {
            rank_index = 17;
        } else if (form.employee_rank_class == "FFD001") {
            rank_index = 18;
        } else if (form.employee_rank_class == "FFS001") {
            rank_index = 7;
        } else if (form.employee_rank_class == "FFS002") {
            rank_index = 7;
        } else if (form.employee_rank_class == "RFS001") {
            rank_index = 11;
        } else if (form.employee_rank_class == "RFS002") {
            rank_index = 10;
        } else if (form.employee_rank_class == "FSO001") {
            rank_index = 3;
        } else if (form.employee_rank_class == "FSO002") {
            rank_index = 3;
        } else if (form.employee_rank_class == "RSO001") {
            rank_index = 4;
        } else if (form.employee_rank_class == "FSC001") {
            rank_index = 7;
        } else if (form.employee_rank_class == "FSC002") {
            rank_index = 6;
        } else if (form.employee_rank_class == "FCA001") {
            rank_index = 11;
        } else if (form.employee_rank_class == "FCA002") {
            rank_index = 12;
        } else if (form.employee_rank_class == "FCS001") {
            rank_index = 9;
        } else if (form.employee_rank_class == "FCS002") {
            rank_index = 9;
        } else if (form.employee_rank_class == "FDO001") {
            rank_index = 2;
        } else if (form.employee_rank_class == "FDO002") {
            rank_index = 2;
        } else if (form.employee_rank_class == "FXC001") {
            rank_index = 1;
        } else if (form.employee_rank_class == "FXC002") {
            rank_index = 1;
        } else if (form.employee_rank_class == "FFMOF1") {
            rank_index = 6;
        } else if (form.employee_rank_class == "FFMOF2") {
            rank_index = 6;
        } else if (form.employee_rank_class == "FCO001") {
            rank_index = 5;
        } else if (form.employee_rank_class == "FCO201") {
            rank_index = 5;
        } else if (form.employee_rank_class == "RRF001") {
            rank_index = 20;
        } else if (form.employee_rank_class == "RFF001") {
            rank_index = 19;
        } else if (form.employee_rank_class == "FFMOP1") {
            rank_index = 8;
        } else if (form.employee_rank_class == "FFMOP2") {
            rank_index = 8;
        }
        if ($$("default_email_personal").getValue() === 1) {
            default_email = form.employee_personal_email;
            no_email_default_selected = false;
        } else if ($$("default_email_work").getValue() === 1) {
            default_email = form.employee_work_email;
            no_email_default_selected = false;
        } else {
            default_email = "";
            no_email_default_selected = false;
        }
        if ($$("hide_personal_mobile").getValue() == 1) {
            hide_phone = 1;
        } else {
            hide_phone = 0;
        }
        if (no_email_default_selected === false) {
            if (form.employee_app_pin.length === 4 || form.employee_app_pin == "") {
                if (profile_edit_mode == "EDIT") {
                    webix
                        .ajax()
                        .headers({Authorization: "Bearer " + api_key})
                        .put(
                            server_url + "/admin/employee",
                            {
                                pay_id: pay_id,
                                first_name: form.employee_first_name,
                                middle_name: form.employee_middle_name,
                                surname: form.employee_surname.toUpperCase(),
                                date_of_birth: form.employee_birth_date,
                                gender: form.employee_gender,
                                rank: form.employee_rank,
                                rank_class: form.employee_rank_class,
                                home_station: form.employee_station_name,
                                home_station_id: form.employee_station_id,
                                personal_mobile_no: form.employee_personal_mobile.toString(),
                                personal_email_address: form.employee_personal_email,
                                work_mobile_no: form.employee_work_mobile.toString(),
                                work_email_address: form.employee_work_email,
                                street: form.employee_home_street,
                                suburb: form.employee_home_suburb.toUpperCase(),
                                state: form.employee_home_state,
                                post_code: form.employee_home_postcode,
                                rank_index: rank_index,
                                permission_level: form.employee_permission_level,
                                notifications_email: default_email,
                                hide_phone: hide_phone,
                                pin_code: form.employee_app_pin,
                                position_number: form.employee_position_number,
                                updated_by: user_logged_in,
                            },
                            {
                                error: function (err) {
                                    webix.alert(
                                        "There was an error updating the employee details!",
                                    );
                                },
                                success: function () {
                                    if (addressChanged === false) {
                                        webix.alert("Employee details have been updated!");
                                        $$("employee-popup").hide();
                                        $$("app_sub_menu").hide();
                                        $$("app_sub_menu_list").unselect("profile");
                                    } else {
                                        webix.message({
                                            id: "travel_distance_info",
                                            text: "Change to address detected...",
                                            type: "debug",
                                            expire: -1,
                                        });
                                        let homeAddress =
                                            form.employee_home_street +
                                            ", " +
                                            form.employee_home_suburb +
                                            ", " +
                                            form.employee_home_state +
                                            ", " +
                                            form.employee_home_postcode;
                                        retrieveTravelDistances(
                                            pay_id,
                                            homeAddress,
                                            addressChanged,
                                        );
                                        updateResDistance(pay_id);
                                    }
                                },
                            },
                        );
                } else if (profile_edit_mode == "ADD") {
                    webix
                        .ajax()
                        .headers({Authorization: "Bearer " + api_key})
                        .post(
                            server_url + "/admin/employee",
                            {
                                pay_id: pay_id,
                                first_name: form.employee_first_name,
                                middle_name: form.employee_middle_name,
                                surname: form.employee_surname.toUpperCase(),
                                date_of_birth: form.employee_birth_date,
                                gender: form.employee_gender,
                                rank: form.employee_rank,
                                rank_class: form.employee_rank_class,
                                home_station: form.employee_station_name,
                                home_station_id: form.employee_station_id,
                                personal_mobile_no: form.employee_personal_mobile.toString(),
                                personal_email_address: form.employee_personal_email,
                                work_mobile_no: form.employee_work_mobile.toString(),
                                work_email_address: form.employee_work_email,
                                street: form.employee_home_street,
                                suburb: form.employee_home_suburb.toUpperCase(),
                                state: form.employee_home_state,
                                post_code: form.employee_home_postcode,
                                rank_index: rank_index,
                                permission_level: form.employee_permission_level,
                                notifications_email: default_email,
                                hide_phone: hide_phone,
                                samfs_joined_date: form.samfs_commencement_date,
                                govt_joined_date: form.govt_commencement_date,
                                pin_code: 0,
                            },
                            {
                                error: function (err) {
                                    webix.alert("There was an error adding this employee!");
                                },
                                success: function () {
                                    createSoilToilEntries(pay_id);
                                    webix.alert("New employee has been saved!");
                                    $$("employee-popup").hide();
                                },
                            },
                        );
                }
            } else {
                webix.alert({
                    text: "The Mobile App Login PIN code must be 4 digits!",
                    width: 450,
                });
            }
        } else {
            webix.alert({
                text: "You must select an email address as your default for receiving notifications!</br></br>Note: Tick the box next to the email address to set as the default.",
                width: 600,
            });
        }
    }

    function createSoilToilEntries(pay_id) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .post(
                server_url + "/admin/add_soil_toil_adjustment",
                {
                    pay_id: pay_id,
                    adjustment_type: "Accumulated",
                    leave_type: "SOIL",
                    actual_hours: 0,
                    accum_hours: 0,
                    total_hours: 0,
                    comments: "Initial entry - auto generated",
                    updated_by: user_logged_in,
                    accum_date: "20100101",
                },
                {
                    error: function (err) {
                    }, success: function () {
                    }
                },
            );
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .post(
                server_url + "/admin/add_soil_toil_adjustment",
                {
                    pay_id: pay_id,
                    adjustment_type: "Accumulated",
                    leave_type: "TOIL",
                    actual_hours: 0,
                    accum_hours: 0,
                    total_hours: 0,
                    comments: "Initial entry - auto generated",
                    updated_by: user_logged_in,
                    accum_date: "20100101",
                },
                {
                    error: function (err) {
                    }, success: function () {
                    }
                },
            );
    }

    function getEmployeeResults() {
        let grid = $$("admin-page").$$("admin_search").$$("search_results");
        let allRosters = $$("admin-page")
            .$$("admin_search")
            .$$("all_rosters")
            .getValue();
        let rosterType = $$("admin-page")
            .$$("admin_search")
            .$$("rosters")
            .getText();
        let searchText = $$("admin-page")
            .$$("admin_search")
            .$$("search_field")
            .getValue();
        let searchType = "";
        if (searchText !== "") {
            $$("loader-window").show();
            if (allRosters === 0 && !rosterType) {
                $$("loader-window").hide();
                webix.alert(
                    "You must select a Roster or check 'All Rosters' to continue!",
                );
                return;
            } else {
                if (isNaN(searchText)) {
                    searchType = "surname";
                } else {
                    searchType = "pay_id";
                }
            }
            grid.clearAll();
            $$("admin-page")
                .$$("admin_search")
                .$$("search_count")
                .define("template", "");
            $$("admin-page").$$("admin_search").$$("search_count").refresh();
            webix
                .ajax()
                .headers({Authorization: "Bearer " + api_key})
                .get(
                    server_url + "/admin/search",
                    {
                        search_type: searchType,
                        search_value: searchText,
                        all_rosters: allRosters,
                        roster_name: rosterType,
                    },
                    {
                        error: function (err) {
                            $$("loader-window").hide();
                        },
                        success: function (results) {
                            let result = JSON.parse(results);
                            let search_results = [];
                            let middle_name = "";
                            let rank = "";
                            let emp_status = "";
                            if (result.length > 0) {
                                for (let x = 0; x < result.length; x++) {
                                    if (result[x].middle_name === null) {
                                        middle_name = "";
                                    } else {
                                        middle_name = result[x].middle_name;
                                    }
                                    if (result[x].ra_rank != null) {
                                        rank = result[x].ra_rank;
                                    } else {
                                        rank = result[x].rank;
                                    }
                                    if (
                                        result[x].terminated == 1 ||
                                        result[x].terminated == true
                                    ) {
                                        emp_status = "Inactive";
                                    } else if (
                                        result[x].removed == 1 ||
                                        result[x].removed == true
                                    ) {
                                        emp_status = "Deleted";
                                    } else {
                                        emp_status = "Active";
                                    }
                                    search_results.push({
                                        id: result[x].id,
                                        pay_id: result[x].pay_id,
                                        name:
                                            result[x].surname +
                                            ", " +
                                            result[x].first_name +
                                            " " +
                                            middle_name,
                                        rank: rank,
                                        roster: result[x].roster,
                                        shift: result[x].shift,
                                        location: result[x].location,
                                        skill_codes: result[x].skill_codes,
                                        emp_status: emp_status,
                                    });
                                }
                            }
                            grid.define("data", search_results);
                            grid.refresh();
                            grid.eachRow(function (row) {
                                let record = grid.getItem(row);
                                if (record.emp_status == "Inactive") {
                                    grid.addCellCss(row, "emp_status", "booster_status");
                                } else if (record.emp_status == "Deleted") {
                                    grid.addCellCss(row, "emp_status", "denied_status");
                                } else {
                                    grid.addCellCss(row, "emp_status", "complete_status");
                                }
                            });
                            $$("admin-page")
                                .$$("admin_search")
                                .$$("search_count")
                                .define("template", result.length + " records found");
                            $$("admin-page").$$("admin_search").$$("search_count").refresh();
                            $$("loader-window").hide();
                        },
                    },
                );
        } else {
            $$("loader-window").hide();
            webix.alert("You must enter a search value to continue!");
        }
    }

    rosters_subject.subscribe(function (data) {
        let select = $$("admin-page").$$("admin_search").$$("rosters");
        if (data) {
            let options = [];
            let roster_names = JSON.parse(data);
            roster_names.forEach(function (value) {
                options.push(value.roster_name);
            });
            select.define("options", options);
            select.refresh();
        }
    });
    skill_codes_subject.subscribe(function (data) {
        let results = JSON.parse(data);
        for (let x = 0; x < results.length; x++) {
            skill_codes.push({id: results[x].code, value: results[x].code});
        }
        refreshSkillCodes();
    });

    function refreshSkillCodes() {
        let grid = $$("admin-page").$$("admin_search").$$("search_results");
        grid.config.columns = [
            {id: "id", hidden: true},
            {
                id: "pay_id",
                header: "Pay ID",
                width: 70,
                sort: "int",
                css: {"text-align": "center"},
            },
            {
                id: "name",
                header: "Employee",
                minWidth: 160,
                adjust: true,
                sort: "string",
            },
            {
                id: "rank",
                header: "Current Rank",
                width: 110,
                sort: "string",
                css: {"text-align": "center"},
            },
            {
                id: "roster",
                header: "Current Roster",
                minWidth: 140,
                adjust: true,
                sort: "string",
            },
            {
                id: "shift",
                header: "Current Shift",
                minWidth: 220,
                adjust: true,
                sort: "string",
            },
            {
                id: "location",
                header: "Current Location",
                minWidth: 180,
                adjust: true,
                sort: "string",
            },
            {
                id: "skill_codes",
                header: "Skill Codes",
                minWidth: 120,
                optionslist: true,
                options: skill_codes,
                editor: "multiselect",
                optionWidth: 50,
                adjust: true,
            },
            {
                id: "emp_status",
                header: "Employment Status",
                width: 160,
                sort: "string",
                css: {"text-align": "center"},
            },
        ];
        grid.refreshColumns();
    }

    function getEmployeeInfo(payId) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/admin/get_employee_info",
                {pay_id: payId},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        let values = JSON.parse(results);
                        let rank = "";
                        let overtime_date = "";
                        if (
                            values[0].notifications_email == values[0].personal_email_address
                        ) {
                            $$("default_email_personal").setValue(1);
                        } else if (
                            values[0].notifications_email == values[0].work_email_address
                        ) {
                            $$("default_email_work").setValue(1);
                        } else {
                            $$("default_email_work").setValue(0);
                            $$("default_email_personal").setValue(0);
                        }
                        if (values[0].hide_phone === true) {
                            $$("hide_personal_mobile").setValue(1);
                        } else {
                            $$("hide_personal_mobile").setValue(0);
                        }
                        if (values[0].ra_rank == null) {
                            rank = values[0].rank;
                        } else {
                            rank = values[0].ra_rank;
                        }
                        if (values[0].pay_id == user_logged_in) {
                            $$("employee_app_pin").enable();
                            $$("show_hide_pin").enable();
                        } else {
                            $$("employee_app_pin").disable();
                            $$("show_hide_pin").disable();
                        }
                        if (
                            values[0].updated_from_api == 1 ||
                            values[0].updated_from_api == true
                        ) {
                        } else {
                            $$("employee_details_notice").hide();
                            $$("employee_first_name").define("readonly", false);
                            $$("employee_first_name").refresh();
                            $$("employee_middle_name").define("readonly", false);
                            $$("employee_middle_name").refresh();
                            $$("employee_surname").define("readonly", false);
                            $$("employee_surname").refresh();
                            $$("employee_gender").define("readonly", false);
                            $$("employee_gender").refresh();
                            $$("employee_home_street").define("readonly", false);
                            $$("employee_home_street").refresh();
                            $$("employee_home_suburb").define("readonly", false);
                            $$("employee_home_suburb").refresh();
                            $$("employee_home_state").define("readonly", false);
                            $$("employee_home_state").refresh();
                            $$("employee_home_postcode").define("readonly", false);
                            $$("employee_home_postcode").refresh();
                            $$("employee_work_mobile").define("readonly", false);
                            $$("employee_work_mobile").refresh();
                            $$("employee_personal_mobile").define("readonly", false);
                            $$("employee_personal_mobile").refresh();
                            $$("employee_work_email").define("readonly", false);
                            $$("employee_work_email").refresh();
                            $$("employee_personal_email").define("readonly", false);
                            $$("employee_personal_email").refresh();
                        }
                        $$("formEmployee").setValues({
                            employee_pay_id: values[0].pay_id,
                            employee_gender: values[0].gender,
                            employee_first_name: values[0].first_name,
                            employee_middle_name: values[0].middle_name,
                            employee_surname: values[0].surname,
                            employee_rank: rank,
                            employee_rank_class: values[0].rank_class,
                            employee_station_name: values[0].home_station,
                            employee_station_id: values[0].home_station_id,
                            employee_personal_mobile: values[0].personal_mobile_no,
                            employee_work_mobile: values[0].work_mobile_no,
                            employee_personal_email: values[0].personal_email_address,
                            employee_work_email: values[0].work_email_address,
                            employee_home_street: values[0].street,
                            employee_home_suburb: values[0].suburb,
                            employee_home_state: values[0].state,
                            employee_home_postcode: values[0].post_code,
                            employee_birth_date: new Date(values[0].date_of_birth),
                            employee_permission_level: values[0].permission_level,
                            employee_res_to_hs: values[0].res_to_hs_kms,
                            hide_personal_mobile: values[0].hide_phone,
                            samfs_commencement_date: new Date(values[0].samfs_joined_date),
                            govt_commencement_date: new Date(values[0].govt_joined_date),
                            employee_position_number: values[0].position_number,
                            employee_app_pin: values[0].pin_code,
                        });
                        if (
                            values[0].overtime_hrs_balance == null ||
                            values[0].overtime_hrs_balance == undefined
                        ) {
                            $$("overtime_hrs_balance").setValue("");
                            $$("overtime_hrs_date").setValue("");
                        } else {
                            $$("overtime_hrs_balance").setValue(
                                values[0].overtime_hrs_balance,
                            );
                            $$("overtime_hrs_date").setValue(
                                moment(values[0].overtime_hrs_date).toDate(),
                            );
                        }
                        getLeaveGroup(payId, "employee");
                        getUserExistsInR52(payId);
                        getHSpreferences(payId);
                        addressChanged = false;
                        first_load = false;
                    },
                },
            );
    }

    function getUserExistsInR52(pay_id) {
        webix.ajax().get(
            "https://samfs.respond52.com/mobile/firebase/user/exists",
            {user_id: pay_id, api_key: api_key},
            {
                error: function (err) {
                },
                success: function (response) {
                    if (response == "Error: User Not Found") {
                        $$("r52_exists").setValue("NO");
                        webix.html.removeCss($$("r52_exists").getNode(), "is_found");
                        webix.html.addCss($$("r52_exists").getNode(), "not_found");
                    } else {
                        $$("r52_exists").setValue("YES");
                        webix.html.removeCss($$("r52_exists").getNode(), "not_found");
                        webix.html.addCss($$("r52_exists").getNode(), "is_found");
                    }
                },
            },
        );
    }

    function importEmployees() {
        let sheet = $$("admin-page").$$("admin_search").$$("excel_import");
        let rowCount = sheet.count();
        let employeeArray = [];
        let rank = "";
        let rank_index = 0;
        let home_station_id = 0;
        let mobileNo = "";
        let record = "";
        let homeStation = "";
        if ((rowCount) => 1e3) {
            setTimeout(function () {
                sheet.eachRow(function (row) {
                    let employeeEntry = {};
                    record = sheet.getItem(row);
                    if (record["SMN CLASS"] == "FFA001") {
                        rank = "FF";
                        rank_index = 13;
                    } else if (record["SMN CLASS"] == "FFA002") {
                        rank = "FF";
                        rank_index = 14;
                    } else if (record["SMN CLASS"] == "FFA003") {
                        rank = "FF";
                        rank_index = 15;
                    } else if (record["SMN CLASS"] == "FFB001") {
                        rank = "FF";
                        rank_index = 16;
                    } else if (record["SMN CLASS"] == "FFC001") {
                        rank = "FF";
                        rank_index = 17;
                    } else if (record["SMN CLASS"] == "FFD001") {
                        rank = "FF";
                        rank_index = 18;
                    } else if (record["SMN CLASS"] == "FFS001") {
                        rank = "SFF";
                        rank_index = 7;
                    } else if (record["SMN CLASS"] == "FFS002") {
                        rank = "SFF";
                        rank_index = 7;
                    } else if (record["SMN CLASS"] == "RFS001") {
                        rank = "RFS";
                        rank_index = 11;
                    } else if (record["SMN CLASS"] == "RFS002") {
                        rank = "RFS2";
                        rank_index = 10;
                    } else if (record["SMN CLASS"] == "FSO001") {
                        rank = "SO";
                        rank_index = 3;
                    } else if (record["SMN CLASS"] == "FSO002") {
                        rank = "SO";
                        rank_index = 3;
                    } else if (record["SMN CLASS"] == "RSO001") {
                        rank = "RSO";
                        rank_index = 4;
                    } else if (record["SMN CLASS"] == "FSC001") {
                        rank = "SCOF";
                        rank_index = 7;
                    } else if (record["SMN CLASS"] == "FSC002") {
                        rank = "SCOF";
                        rank_index = 6;
                    } else if (record["SMN CLASS"] == "FCA001") {
                        rank = "COP";
                        rank_index = 11;
                    } else if (record["SMN CLASS"] == "FCA002") {
                        rank = "COP";
                        rank_index = 12;
                    } else if (record["SMN CLASS"] == "FCS001") {
                        rank = "SCOP";
                        rank_index = 9;
                    } else if (record["SMN CLASS"] == "FCS002") {
                        rank = "SCOP";
                        rank_index = 9;
                    } else if (record["SMN CLASS"] == "FDO001") {
                        rank = "CMD";
                        rank_index = 2;
                    } else if (record["SMN CLASS"] == "FDO002") {
                        rank = "CMD";
                        rank_index = 2;
                    } else if (record["SMN CLASS"] == "FXC001") {
                        rank = "ACFO";
                        rank_index = 1;
                    } else if (record["SMN CLASS"] == "FXC002") {
                        rank = "ACFO";
                        rank_index = 1;
                    } else if (record["SMN CLASS"] == "FFMOF1") {
                        rank = "MOFF";
                        rank_index = 6;
                    } else if (record["SMN CLASS"] == "FFMOF2") {
                        rank = "MOFF";
                        rank_index = 6;
                    } else if (record["SMN CLASS"] == "FCO001") {
                        rank = "COFF";
                        rank_index = 5;
                    } else if (record["SMN CLASS"] == "FCO201") {
                        rank = "COFF";
                        rank_index = 5;
                    } else if (record["SMN CLASS"] == "RRF001") {
                        rank = "RRF";
                        rank_index = 20;
                    } else if (record["SMN CLASS"] == "RFF001") {
                        rank = "RFF";
                        rank_index = 19;
                    } else if (record["SMN CLASS"] == "FFMOP1") {
                        rank = "SFF";
                        rank_index = 8;
                    } else if (record["SMN CLASS"] == "FFMOP2") {
                        rank = "SFF";
                        rank_index = 8;
                    } else {
                        rank = "NON";
                        rank_index = 0;
                    }
                    homeStation = record["ZPS CRT STAT"];
                    if (
                        record["ZPS CRT STAT"] == "Adelaide" ||
                        record["ZPS CRT STAT"] == "3rd Floor" ||
                        record["ZPS CRT STAT"] == "4th Floor" ||
                        record["ZPS CRT STAT"] == "Headquarters"
                    ) {
                        home_station_id = 20;
                    } else if (record["ZPS CRT STAT"] == "Whyalla F/S") {
                        home_station_id = 52;
                        homeStation = "Whyalla";
                    } else if (record["ZPS CRT STAT"] == "Kadina F/S") {
                        home_station_id = 66;
                        homeStation = "Kadina";
                    } else if (record["ZPS CRT STAT"] == "Moonta F/S") {
                        home_station_id = 68;
                        homeStation = "Moonta";
                    } else if (
                        record["ZPS CRT STAT"] == "Mount Barker F/S" ||
                        record["ZPS CRT STAT"] == "Mount Barker"
                    ) {
                        home_station_id = 73;
                        homeStation = "Mount Barker";
                    } else if (record["ZPS CRT STAT"] == "Wallaroo F/S") {
                        home_station_id = 67;
                        homeStation = "Wallaroo";
                    } else if (
                        record["ZPS CRT STAT"] == "Port Pirie F/S" ||
                        record["ZPS CRT STAT"] == "Port Pirie"
                    ) {
                        home_station_id = 50;
                        homeStation = "Port Pirie";
                    } else if (record["ZPS CRT STAT"] == "Berri F/S") {
                        home_station_id = 60;
                        homeStation = "Berri";
                    } else if (record["ZPS CRT STAT"] == "Loxton F/S") {
                        home_station_id = 62;
                        homeStation = "Loxton";
                    } else if (record["ZPS CRT STAT"] == "Renmark F/S") {
                        home_station_id = 61;
                        homeStation = "Renmark";
                    } else if (record["ZPS CRT STAT"] == "Tanunda F/S") {
                        home_station_id = 63;
                        homeStation = "Tanunda";
                    } else if (record["ZPS CRT STAT"] == "Kapunda F/S") {
                        home_station_id = 64;
                        homeStation = "Kapunda";
                    } else if (record["ZPS CRT STAT"] == "Mt Gambier F/S") {
                        home_station_id = 70;
                        homeStation = "Mt Gambier";
                    } else if (record["ZPS CRT STAT"] == "Murray Bridge F/S") {
                        home_station_id = 72;
                        homeStation = "Murray Bridge";
                    } else if (record["ZPS CRT STAT"] == "Victor Harbor F/S") {
                        home_station_id = 71;
                        homeStation = "Victor Harbor";
                    } else if (record["ZPS CRT STAT"] == "Peterborough F/S") {
                        home_station_id = 55;
                        homeStation = "Peterborough";
                    } else if (record["ZPS CRT STAT"] == "Port Augusta F/S") {
                        home_station_id = 51;
                        homeStation = "Port Augusta";
                    } else if (record["ZPS CRT STAT"] == "Port Lincoln F/S") {
                        home_station_id = 54;
                        homeStation = "Port Lincoln";
                    } else {
                        home_station_id = 0;
                        homeStation = "";
                    }
                    if (record["ADR MOBILE"] != undefined) {
                        if (record["ADR MOBILE"].slice(0, 1) == 4) {
                            mobileNo = "0" + record["ADR MOBILE"].replace(/\s/g, "");
                        } else {
                            mobileNo = record["ADR MOBILE"].replace(/\s/g, "");
                        }
                    } else {
                        mobileNo = "";
                    }
                    employeeEntry.pay_id = record["DET NUMBER"];
                    employeeEntry.first_name = record["DET G1 NAME1"];
                    employeeEntry.middle_name = record["DET G1 NAME2"];
                    employeeEntry.surname = record["DET SURNAME"];
                    employeeEntry.date_of_birth = record["DET BIR DATE"];
                    employeeEntry.gender = record["DET SEX"];
                    employeeEntry.rank_class = record["SMN CLASS"];
                    employeeEntry.rank = rank;
                    employeeEntry.home_station = homeStation;
                    employeeEntry.home_station_id = home_station_id;
                    employeeEntry.personal_mobile_no = mobileNo;
                    employeeEntry.personal_email_address = record["ADR EMAIL"];
                    employeeEntry.eso_email_address = record["DET EMAIL AD"];
                    employeeEntry.work_email_address = record["DET EMAIL AD"];
                    employeeEntry.street = record["ADR LINE 1"];
                    employeeEntry.suburb = record["ADR LINE 3"];
                    employeeEntry.state = "SA";
                    employeeEntry.post_code = record["ADR PST CODE"];
                    employeeEntry.rank_index = rank_index;
                    employeeEntry.permission_level = 6;
                    employeeEntry.notifications_email = record["ADR EMAIL"];
                    employeeEntry.hide_phone = 1;
                    employeeEntry.res_to_hs_kms = 0;
                    employeeEntry.retained = 1;
                    employeeArray.push(employeeEntry);
                });
                $$("loader-window").hide();
                webix
                    .ajax()
                    .headers({Authorization: "Bearer " + api_key})
                    .post(
                        server_url + "/admin/employees",
                        {employeeArray: employeeArray},
                        {
                            error: function (err) {
                                $$("loader-window").hide();
                            },
                            success: function (result) {
                                $$("loader-window").hide();
                                let response = JSON.parse(result);
                                if (response.response == "OK") {
                                    webix.alert("Employees have been imported successfully!");
                                    $$("admin-page")
                                        .$$("admin_search")
                                        .$$("clear_sheet")
                                        .callEvent("onItemClick");
                                }
                            },
                        },
                    );
            }, 250);
        } else {
            $$("loader-window").hide();
            webix.alert({
                text: "There are less than 1000 employee records in the import list.</br>The import process cannot continue as records may be lost.</br>Please check the TASEMP file and try again!",
                width: 500,
            });
        }
    }

    function getUserLeaveBalances(payId) {
        let grid = $$("leave_balances_grid");
        grid.clearAll();
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/admin/get_user_leave_balances",
                {pay_id: payId},
                {
                    error: function (err) {
                        webix.alert("There was an error retrieving your leave balances!");
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            if (values.length > 0) {
                                grid.add({
                                    type: "SOIL",
                                    description: "Shift Off In Lieu",
                                    total_hours: values[0].soil_balance,
                                    day_shifts: values[0].soil_balance / 10,
                                });
                                grid.add({
                                    type: "TOIL",
                                    description: "Time Off In Lieu",
                                    total_hours: values[0].toil_balance,
                                    day_shifts: values[0].toil_balance / 8,
                                });
                                grid.add({
                                    type: "PHOL",
                                    description: "Public Holiday Leave",
                                    total_hours: values[0].phol_balance,
                                    day_shifts: "-",
                                });
                            } else {
                                webix.alert({
                                    text: "There were no SOIL or TOIL or PHOL records found for you!</br></br>Please contact Workforce Rostering to create these entries.",
                                    width: 500,
                                });
                            }
                        }
                    },
                },
            );
    }

    rosters_subject.subscribe(function (data) {
        let select1 = $$("employee_roster_pref_1");
        let select2 = $$("employee_roster_pref_2");
        let select3 = $$("employee_roster_pref_3");
        if (data) {
            let options = [];
            let roster_names = JSON.parse(data);
            roster_names.forEach(function (value) {
                options.push({id: value.roster_name, value: value.roster_name});
            });
            options.unshift({id: "", value: ""});
            select1.define("options", options);
            select1.refresh();
            select2.define("options", options);
            select2.refresh();
            select3.define("options", options);
            select3.refresh();
        }
    });

    function load_shifts(rosterName, prefNo) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/schedule/shifts",
                {roster_name: rosterName},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        if (results) {
                            let shiftList = JSON.parse(results);
                            if (shiftList.data.length > 0) {
                                shiftArray = shiftList.data[0].shifts.split(",");
                                locationsArray = shiftList.data[0].locations.split(",");
                                let shiftOptions = [];
                                shiftArray.forEach(function (value) {
                                    shiftOptions.push(value);
                                });
                                let locationOptions = [];
                                let locationName = "";
                                let locationSplit = [];
                                locationsArray.forEach(function (value) {
                                    locationSplit = value.split("-");
                                    locationName = locationSplit[1].trim();
                                    locationOptions.push({id: locationName, value: value});
                                });
                                if (prefNo === 1) {
                                    $$("employee_shift_pref_1").define("options", shiftOptions);
                                    $$("employee_shift_pref_1").refresh();
                                    $$("employee_location_pref_1").define(
                                        "options",
                                        locationOptions,
                                    );
                                    $$("employee_location_pref_1").refresh();
                                } else if (prefNo === 2) {
                                    $$("employee_shift_pref_2").define("options", shiftOptions);
                                    $$("employee_shift_pref_2").refresh();
                                    $$("employee_location_pref_2").define(
                                        "options",
                                        locationOptions,
                                    );
                                    $$("employee_location_pref_2").refresh();
                                } else if (prefNo === 3) {
                                    $$("employee_shift_pref_3").define("options", shiftOptions);
                                    $$("employee_shift_pref_3").refresh();
                                    $$("employee_location_pref_3").define(
                                        "options",
                                        locationOptions,
                                    );
                                    $$("employee_location_pref_3").refresh();
                                }
                            }
                        }
                    },
                },
            );
    }

    function getR52Stations() {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/respond52/get_r52_stations",
                {},
                {
                    error: function (err) {
                    },
                    success: function (results) {
                        let locationsArray = [];
                        if (results) {
                            let locations = JSON.parse(results);
                            locations.forEach(function (values) {
                                if (
                                    values.station_id != "29" &&
                                    values.station_id != "39" &&
                                    values.station_id != "49" &&
                                    values.station_id != "79" &&
                                    values.station_id != "89" &&
                                    values.station_id != "59" &&
                                    values.station_id != "69" &&
                                    values.station_id != "38"
                                ) {
                                    locationsArray.push({
                                        id: values.station_id,
                                        value: values.name,
                                    });
                                }
                            });
                            locationsArray.unshift({id: "38", value: "APTC"});
                            locationsArray.unshift({id: "20", value: "Adelaide"});
                            $$("employee_new_station").define("options", locationsArray);
                            $$("employee_new_station").refresh();
                        }
                    },
                },
            );
    }

    function updateStationShift(newStation, newShift) {
        let sel_pay_id = $$("employee_pay_id").getValue();
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/respond52/r52_update_station_shift",
                {user_id: sel_pay_id, station_id: newStation, shift: newShift},
                {
                    error: function (err) {
                        webix.alert("Error updating new details. Please try again!");
                    },
                    success: function (results) {
                        webix.alert("Updated successfully!");
                    },
                },
            );
    }

    function getEmeraldSyncData(eso_id) {
        $$("loader-window").show();
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/admin/get_emerald_profile_data",
                {eso_id: eso_id},
                {
                    error: function (err) {
                        $$("loader-window").hide();
                        webix.message({
                            text: "There was an error retrieving contact details!",
                            type: "error",
                            expire: 2500,
                        });
                    },
                    success: function (results) {
                        if (results) {
                            let values = JSON.parse(results);
                            if (values.length > 0) {
                                let employee_birth_date = moment();
                                let samfs_commencement_date = moment();
                                let govt_commencement_date = moment();
                                let employee_personal_mobile = "";
                                let employee_work_mobile = "";
                                employee_birth_date = values[0].DateOfBirth;
                                samfs_commencement_date = values[0].Memberships[0].StartDate;
                                govt_commencement_date = values[0].Memberships[0].StartDate;
                                if (
                                    values[0].PersonalMobile == null ||
                                    values[0].PersonalMobile == ""
                                ) {
                                    employee_personal_mobile = "";
                                    $$("hide_personal_mobile").setValue(0);
                                } else {
                                    employee_personal_mobile =
                                        values[0].PersonalMobile.replaceAll(" ", "");
                                    if (values[0].PersonalMobileHidden == true) {
                                        $$("hide_personal_mobile").setValue(1);
                                    } else {
                                        $$("hide_personal_mobile").setValue(0);
                                    }
                                }
                                if (isEmailAddressValid(values[0].PersonalEmail) === true) {
                                    $$("employee_personal_email").setValue(
                                        values[0].PersonalEmail,
                                    );
                                } else {
                                    $$("employee_personal_email").setValue("");
                                    webix.alert({
                                        text:
                                            "Emerald has '" +
                                            values[0].PersonalEmail +
                                            "' as the personal email address</br>which appears invalid so it has not been imported!",
                                        width: 450,
                                    });
                                }
                                if (isEmailAddressValid(values[0].PortalUserName) === true) {
                                    $$("employee_work_email").setValue(
                                        values[0].Memberships[0].PortalUserName,
                                    );
                                } else {
                                    $$("employee_work_email").setValue("");
                                    webix.alert({
                                        text:
                                            "Emerald has '" +
                                            values[0].Memberships[0].PortalUserName +
                                            "' as the ESO email address</br>which appears invalid so it has not been imported!",
                                        width: 450,
                                    });
                                }
                                if (values[0].EsoMobile == null || values[0].EsoMobile == "") {
                                    employee_work_mobile = "";
                                } else {
                                    employee_work_mobile = values[0].EsoMobile.replaceAll(
                                        " ",
                                        "",
                                    );
                                }
                                $$("employee_first_name").setValue(values[0].FirstName);
                                if (
                                    values[0].MiddleName == null ||
                                    values[0].MiddleName == ""
                                ) {
                                    $$("employee_middle_name").setValue("");
                                } else {
                                    $$("employee_middle_name").setValue(values[0].MiddleName);
                                }
                                $$("employee_surname").setValue(values[0].LastName);
                                if (values[0].Gender === "Male") {
                                    $$("employee_gender").setValue("M");
                                } else if (values[0].Gender === "Female") {
                                    $$("employee_gender").setValue("F");
                                } else {
                                    $$("employee_gender").setValue("");
                                }
                                if (profile_edit_mode == "ADD") {
                                    $$("employee_birth_date").setValue(employee_birth_date);
                                    $$("samfs_commencement_date").setValue(
                                        samfs_commencement_date,
                                    );
                                    $$("govt_commencement_date").setValue(govt_commencement_date);
                                }
                                $$("employee_home_street").setValue(
                                    values[0].AddressNumber + " " + values[0].AddressStreet,
                                );
                                $$("employee_home_suburb").setValue(values[0].AddressSuburb);
                                $$("employee_home_state").setValue(values[0].AddressState);
                                $$("employee_home_postcode").setValue(
                                    values[0].AddressPostcode,
                                );
                                $$("employee_personal_mobile").setValue(
                                    employee_personal_mobile,
                                );
                                $$("employee_work_mobile").setValue(employee_work_mobile);
                                $$("default_email_work").setValue(1);
                                if (
                                    values[0].AddressNumber + " " + values[0].AddressStreet !=
                                    old_street
                                ) {
                                    addressChanged = true;
                                }
                                if (
                                    values[0].AddressSuburb.toUpperCase() !=
                                    old_suburb.toUpperCase()
                                ) {
                                    addressChanged = true;
                                }
                                if (values[0].AddressPostcode != old_post_code) {
                                    addressChanged = true;
                                }
                                if (values[0].AddressState != old_state) {
                                    addressChanged = true;
                                }
                                $$("loader-window").hide();
                                if (profile_edit_mode == "ADD") {
                                    webix.alert({
                                        text: "Contact info has been retrieved from Emerald!</br></br>Note: Please check these values and also enter all other required values then click on <strong>'Save'</strong>",
                                        width: 600,
                                    });
                                } else if (profile_edit_mode == "EDIT") {
                                    webix.alert({
                                        text: "Contact info has been retrieved from Emerald!</br></br>Note: Please check the values and click on <strong>'Update'</strong> to save them</br>Note: to discard these new values simply close the form and old values will be retained",
                                        width: 600,
                                    });
                                }
                            } else {
                                $$("loader-window").hide();
                                webix.alert({
                                    text:
                                        "The Pay/ESO ID <strong>" +
                                        eso_id +
                                        "</strong> was not found in Emerald!",
                                    width: 550,
                                });
                            }
                        } else {
                            $$("loader-window").hide();
                            webix.alert({
                                text:
                                    "The Pay/ESO ID <strong>" +
                                    eso_id +
                                    "</strong> was not found in Emerald!",
                                width: 550,
                            });
                        }
                    },
                },
            );
    }

    function getEmployeeStationDistances(pay_id) {
        let grid = $$("travel_distances_grid");
        grid.clearAll();
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/admin/get_employee_station_distances",
                {pay_id: pay_id},
                {
                    error: function (err) {
                        addressChanged = false;
                        webix.alert({
                            text: "There was an error retrieving the travel distances!",
                            width: 500,
                        });
                    },
                    success: function (results) {
                        addressChanged = false;
                        if (results) {
                            let values = JSON.parse(results);
                            if (values.length > 0) {
                                values.forEach(function (result) {
                                    grid.add({
                                        station_id: result.station_id,
                                        station_name: result.station_name,
                                        distance: result.distance,
                                        updated_date: result.updated_date,
                                    });
                                });
                            } else {
                                webix.alert({text: "No stored locations found!", width: 400});
                            }
                        }
                    },
                },
            );
    }

    function retrieveTravelDistances(pay_id, home_address, addressChanged) {
        $$("loader-window").show();
        webix.message.pull["travel_distance_info"].firstChild.innerHTML =
            "Now calculating travel distances...";
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .get(
                server_url + "/admin/calculate_employee_station_distances",
                {pay_id: pay_id, home_address: home_address},
                {
                    error: function (err) {
                        $$("loader-window").hide();
                        webix.alert({
                            text: "There was an error calculation the travel distances!",
                            width: 500,
                        });
                    },
                    success: function (results) {
                        if (results) {
                            webix.message.hide("travel_distance_info");
                            $$("loader-window").hide();
                            if (addressChanged === true) {
                                addressChanged = false;
                                webix.alert("Employee details have been updated!");
                                $$("employee-popup").hide();
                                $$("app_sub_menu").hide();
                                $$("app_sub_menu_list").unselect("profile");
                            } else {
                                addressChanged = false;
                                getEmployeeStationDistances(pay_id);
                            }
                        } else {
                            $$("loader-window").hide();
                        }
                    },
                },
            );
    }

    function updateRecruitOvertimeHours(pay_id, hours, hrs_date) {
        webix
            .ajax()
            .headers({Authorization: "Bearer " + api_key})
            .put(
                server_url + "/admin/update_overtime_hours",
                {pay_id: pay_id, hours: hours, hrs_date: hrs_date},
                {
                    error: function (err) {
                        webix.alert(
                            "There was an error updating the Overtime Hours Balance!",
                        );
                    },
                    success: function (results) {
                        webix.alert("Overtime Hours updated successfully!");
                    },
                },
            );
    }

    return {
        initialise: function () {
            initApplication();
        },
        getEmployeeData: function (payId) {
            getEmployeeInfo(payId);
        },
        setEditMode: function (value) {
            profile_edit_mode = value;
        },
        getUserLeaveBalances: function (payId) {
            getUserLeaveBalances(payId);
        },
    };
})();
