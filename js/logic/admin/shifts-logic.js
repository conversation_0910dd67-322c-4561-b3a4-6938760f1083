let shifts = (function () {
  let edit_mode;
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("shifts")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        addShift();
      });
    $$("admin-page")
      .$$("shifts")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteShift();
      });
    $$("btn_shift_save").attachEvent("onItemClick", function (id, e) {
      saveShift();
    });
    $$("btn_shift_close").attachEvent("onItemClick", function (id, e) {
      $$("formShifts").clear();
      $$("formShifts").clearValidation();
      $$("shifts-popup").hide();
    });
    $$("admin-page")
      .$$("shifts")
      .$$("grid-shifts")
      .attachEvent("onItemDblClick", function (id) {
        edit_mode = "EDIT";
        let selected_row = $$("admin-page")
          .$$("shifts")
          .$$("grid-shifts")
          .getSelectedItem(id);
        if (selected_row !== undefined) {
          if (selected_row[0].id > 0) {
            getShiftInfo(selected_row);
          }
        }
      });
    $$("shift_sequence_days").attachEvent("onChange", function (newv, oldv) {
      if (newv !== oldv) {
        loadShiftDays(newv);
      }
    });
    $$("grid-shift_days").attachEvent("onAfterEditStart", function (id) {
      const editor = this.getEditor(id);
      if (editor && editor.getPopup) {
        const popup = editor.getPopup();
        popup.getBody().define("type", "time");
        popup.getBody().refresh();
      }
    });
    $$("grid-shift_days").attachEvent(
      "onAfterEditStop",
      function (state, editor, ignoreUpdate) {
        if (editor.column === "start_time") {
          let defIcon = "";
          let selHours = state.value.getHours();
          if (selHours >= "4" && selHours <= "14") {
            defIcon = "sun";
          } else {
            defIcon = "moon";
          }
          $$("grid-shift_days").updateItem(editor.row, {
            ["icon_type"]: defIcon,
          });
          $$("grid-shift_days").updateItem(editor.row, { ["day_off"]: 0 });
        }
      },
    );
    $$("grid-shift_days").attachEvent("onCheck", function (row, column, state) {
      let startTime = $$("grid-shift_days").getItem(row).start_time.getHours();
      if (state === 1) {
        $$("grid-shift_days").updateItem(row, { ["icon_type"]: "ban" });
      } else {
        if (startTime >= "4" && startTime <= "14") {
          $$("grid-shift_days").updateItem(row, { ["icon_type"]: "sun" });
        } else {
          $$("grid-shift_days").updateItem(row, { ["icon_type"]: "moon" });
        }
      }
    });
    loadShifts();
  }
  shifts_subject.subscribe(function (data) {
    $$("admin-page").$$("shifts").$$("grid-shifts").clearAll();
    let result = JSON.parse(data);
    if (result.length > 0) {
      for (let x = 0; x < result.length; x++) {
        let startDate = moment(result[x].seq_start_date).format("DD/MM/YYYY");
        $$("admin-page")
          .$$("shifts")
          .$$("grid-shifts")
          .add(
            {
              id: result[x].id,
              shift_name: result[x].shift_name,
              roster_name: result[x].roster_name,
              roster_id: result[x].roster_id,
              colour: result[x].colour,
              seq_start_date: startDate,
              sequence_days: result[x].sequence_days,
              duration_days: result[x].duration_days / 365 + " year(s)",
            },
            x,
          );
      }
    }
  });
  function loadShiftDays(value) {
    let days_list = [];
    $$("grid-shift_days").clearAll();
    let startTime = webix.i18n.timeFormatDate("08:00 AM");
    let endTime = webix.i18n.timeFormatDate("06:00 PM");
    for (let x = 1; x <= value; x++) {
      days_list.push({
        id: x,
        day: x,
        start_time: startTime,
        end_time: endTime,
        day_off: 0,
        icon_type: "sun",
      });
    }
    $$("grid-shift_days").define("data", days_list);
    $$("grid-shift_days").refresh();
  }
  function getShiftInfo(selected_row) {
    let dateFormat = webix.Date.strToDate("%d/%m/%Y");
    let duration = selected_row[0].duration_days;
    let noOfYears = duration.replace(" year(s)", "");
    $$("shifts_id").setValue(selected_row[0].id);
    $$("shift_name").setValue(selected_row[0].shift_name);
    $$("shift_roster_name").setValue(selected_row[0].roster_id);
    $$("shift_colour").setValue(selected_row[0].colour);
    $$("shift_sequence_days").setValue(selected_row[0].sequence_days);
    if (isNumber(noOfYears) === true) {
      $$("shift_duration").setValue(noOfYears);
    } else {
      $$("shift_duration").setValue(0);
    }
    $$("shift_sequence_start_date").setValue(
      dateFormat(selected_row[0].seq_start_date),
    );
    $$("shifts-label").define(
      "label",
      "<span class='header_font'>Edit Shift</span>",
    );
    $$("shifts-label").refresh();
    getShiftDays(selected_row[0].id);
    $$("shifts-popup").show();
  }
  function deleteShift() {
    let selected_row = $$("admin-page")
      .$$("shifts")
      .$$("grid-shifts")
      .getSelectedItem();
    if (selected_row.id > 0) {
      let row_id = selected_row.id;
      webix.confirm({
        title: "Delete Shift",
        ok: "Yes",
        cancel: "No",
        text: "Are you sure you want to delete the selected Shift?",
        callback: function (result) {
          switch (result) {
            case true:
              webix
                .ajax()
                .headers({ Authorization: "Bearer " + api_key })
                .del(
                  server_url + "/admin/shifts",
                  { id: row_id },
                  {
                    error: function (err) {
                      webix.alert(
                        "There was an error deleting the shift. Please try again!",
                      );
                    },
                    success: function () {
                      deleteShiftDays(row_id);
                    },
                  },
                );
          }
        },
      });
    } else {
      webix.alert("No Shift has been selected!");
    }
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("shift_roster_name");
    let objValues = {};
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        objValues = { id: value.id, value: value.roster_name };
        options.push(objValues);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function getShiftDays(shiftId) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/shifts/days",
        { shift_id: shiftId },
        {
          error: function (err) {},
          success: function (results) {
            $$("grid-shift_days").clearAll();
            let result = JSON.parse(results);
            if (result.data.length > 0) {
              let timeFormat = webix.Date.strToDate("%H:%i");
              for (let x = 0; x < result.data.length; x++) {
                $$("grid-shift_days").add(
                  {
                    id: result.data[x].id,
                    day: result.data[x].day_number,
                    start_time: timeFormat(result.data[x].start_time),
                    end_time: timeFormat(result.data[x].end_time),
                    day_off: result.data[x].day_off,
                    icon_type: result.data[x].icon,
                  },
                  x,
                );
              }
            }
          },
        },
      );
  }
  function loadShifts() {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/shifts",
        {},
        {
          error: function (err) {},
          success: function (results) {
            shifts_subject.next(results);
          },
        },
      );
  }
  function addShift() {
    edit_mode = "ADD";
    $$("shifts-label").define(
      "label",
      "<span class='header_font'>Add Shift</span>",
    );
    $$("shifts-label").refresh();
    $$("formShifts").clear();
    $$("shifts-popup").show();
    $$("shift_sequence_days").setValue(8);
  }
  function saveShift() {
    if ($$("formShifts").validate()) {
      $$("btn_shift_save").disable();
      $$("loader-window").show();
      let form = $$("formShifts").getValues();
      let rosterName = $$("shift_roster_name").getText();
      let dateFormat = webix.Date.dateToStr("%Y-%m-%d");
      let duration = 1;
      let noOfDays = 0;
      duration = form.shift_duration;
      noOfDays = duration * 365;
      if (edit_mode === "ADD") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/admin/shifts",
            {
              roster_name: rosterName,
              roster_id: form.shift_roster_name,
              shift_name: form.shift_name,
              colour: form.shift_colour,
              sequence_days: form.shift_sequence_days,
              start_date: dateFormat(form.shift_sequence_start_date),
              duration_days: noOfDays,
              created_by: user_logged_in,
            },
            {
              error: function (err) {
                webix.alert(
                  "There was an error creating the shift. Please try again!",
                );
              },
              success: function (result) {
                let json = JSON.parse(result);
                let new_shiftId = json.data;
                saveShiftInfo(new_shiftId, function (callback) {
                  if (callback === "ok") {
                    createShiftDays(new_shiftId, function (callback) {
                      if (callback === "ok") {
                      } else {
                        webix.alert(
                          "There was an error creating the shift days. Please try again!",
                        );
                      }
                    });
                    $$("shifts-popup").hide();
                    $$("btn_shift_save").enable();
                    $$("loader-window").hide();
                    loadShifts();
                  } else {
                    $$("btn_shift_save").enable();
                    $$("loader-window").hide();
                    webix.alert(
                      "There was an error creating the shift. Please try again!",
                    );
                  }
                });
              },
            },
          );
      } else if (edit_mode === "EDIT") {
        $$("loader-window").hide();
        webix.alert({
          text: "You can't edit an existing Shift. You must delete the Shift and then re-create it!",
          width: 500,
        });
        $$("btn_shift_save").enable();
      }
    }
  }
  function saveShiftInfo(new_shiftId, callback) {
    let timeFormat = webix.Date.dateToStr("%H:%i");
    let noErrors = false;
    $$("grid-shift_days").eachRow(function (row) {
      let record = $$("grid-shift_days").getItem(row);
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .sync()
        .post(
          server_url + "/admin/shifts/days",
          {
            shift_id: new_shiftId,
            day_number: record.day,
            start_time: timeFormat(record.start_time),
            end_time: timeFormat(record.end_time),
            day_off: record.day_off,
            icon_type: record.icon_type,
          },
          {
            error: function (err) {
              noErrors = false;
            },
            success: function () {
              noErrors = true;
            },
          },
        );
    });
    if (noErrors === true) {
      callback("ok");
    } else {
      callback("error");
    }
  }
  function createShiftDays(shiftId, callback) {
    let form = $$("formShifts").getValues();
    let rosterName = $$("shift_roster_name").getText();
    let startDate = form.shift_sequence_start_date;
    let grid = $$("grid-shift_days");
    let timeFormat = webix.Date.dateToStr("%H:%i");
    let patternArray = [];
    let arrayItem = {};
    let duration = 1;
    let noOfDays = 0;
    grid.eachRow(function (row) {
      let record = grid.getItem(row);
      arrayItem = {
        dayNo: record.day,
        startTime: timeFormat(record.start_time),
        endTime: timeFormat(record.end_time),
        dayOff: record.day_off,
        icon: record.icon_type,
      };
      patternArray.push(arrayItem);
    });
    duration = form.shift_duration;
    noOfDays = duration * 365;
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .post(
        server_url + "/admin/roster_lookup",
        {
          shift_id: shiftId,
          patternArray: patternArray,
          roster: rosterName,
          shift: form.shift_name,
          startDate: startDate,
          noOfDays: noOfDays,
          gridCount: grid.count(),
        },
        {
          error: function (err) {
            callback(err);
          },
          success: function () {
            callback("ok");
          },
        },
      );
  }
  function deleteShiftDays(shiftId) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .del(
        server_url + "/admin/shifts/days",
        { shift_id: shiftId },
        {
          error: function (err) {
            webix.alert(
              "There was an error deleting the shift. Please try again!",
            );
          },
          success: function () {
            deleteRosterShiftDays(shiftId);
          },
        },
      );
  }
  function deleteRosterShiftDays(shiftId) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .del(
        server_url + "/admin/shifts/roster_days",
        { shift_id: shiftId },
        {
          error: function (err) {
            webix.alert(
              "There was an error deleting the shift. Please try again!",
            );
          },
          success: function () {
            loadShifts();
            webix.alert("Shift has been deleted successfully!");
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
