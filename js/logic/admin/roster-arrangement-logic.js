let rosterArrangement = (function () {
  let loadIndex = 1;
  let work_day_bookings_show = false;
  let exp_grid;
  let exp_employee;
  let prev_period = false;
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 11, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 11, bold: true } },
    2: { font: { name: "Arial", sz: 11, bold: true } },
  };
  function initApplication() {
    eventHandlers();
    exp_grid = $$("ra_bookings-window_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Showing Deleted Bookings List for " + exp_employee,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "***** Employee Moved to Day Work Roster Export File *****",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("btn_edit")
      .attachEvent("onItemClick", function (id) {
        let grid = $$("admin-page")
          .$$("roster_arrangement")
          .$$("roster_arrangements");
        let arrangementInfo = grid.getSelectedItem(id);
        let form = $$("ra_edit-form");
        let acting_up = 0;
        if (arrangementInfo.length > 0) {
          if (arrangementInfo[0].acting_up == "Yes") {
            acting_up = 1;
          } else {
            acting_up = 0;
          }
          form.setValues({
            ra_edit_id: arrangementInfo[0].roster_arrangement_id,
            ra_edit_rosters: arrangementInfo[0].roster,
            ra_edit_shifts: arrangementInfo[0].shift,
            ra_edit_locations: arrangementInfo[0].location,
            ra_edit_rank: arrangementInfo[0].rank,
            ra_edit_acting_up: acting_up,
            ra_edit_start_date: moment(
              arrangementInfo[0].start_date,
              "DD/MM/YYYY",
            ).toDate(),
            ra_edit_end_date: moment(
              arrangementInfo[0].end_date,
              "DD/MM/YYYY",
            ).toDate(),
            ra_edit_start_time: new Date("01/01/2019 00:00"),
            ra_edit_end_time: new Date("01/01/2019 23:59"),
          });
          let ra_s_date = $$("ra_edit_start_date").getPopup().getBody();
          ra_s_date.define(
            "minDate",
            moment(arrangementInfo[0].start_date, "DD/MM/YYYY").toDate(),
          );
          ra_s_date.define(
            "maxDate",
            moment(arrangementInfo[0].end_date, "DD/MM/YYYY").toDate(),
          );
          ra_s_date.refresh();
          let ra_e_date = $$("ra_edit_end_date").getPopup().getBody();
          ra_e_date.define(
            "minDate",
            moment(arrangementInfo[0].start_date, "DD/MM/YYYY").toDate(),
          );
          ra_e_date.define(
            "maxDate",
            moment(arrangementInfo[0].end_date, "DD/MM/YYYY").toDate(),
          );
          ra_e_date.refresh();
          $$("ra_edit-window").show();
        } else {
          webix.alert("You must select an RA to edit!");
        }
      });
    $$("btn_ra_edit-window_close").attachEvent("onItemClick", function (id) {
      $$("ra_edit-window").hide();
    });
    $$("btn_ra_edit_update").attachEvent("onItemClick", function (id) {
      let form = $$("ra_edit-form");
      let form_values = $$("ra_edit-form").getValues();
      if (form.validate()) {
        if (
          moment(form_values.ra_edit_end_date).isBefore(
            moment(form_values.ra_edit_start_date),
          )
        ) {
          webix.alert({
            text: "The 'End Date' cannot be before the 'Start Date' !",
            width: 400,
          });
        } else {
          if (form_values.ra_edit_rank != "?") {
            $$("loader-window").show();
            let roster_arrangement_id = formatUuid(getRandomValuesFunc());
            let roster_arrangement_id_2 = formatUuid(getRandomValuesFunc());
            let rankIndex = 11;
            setTimeout(function () {
              if (form_values.ra_edit_rank == "ACFO") {
                rankIndex = 1;
              } else if (form_values.ra_edit_rank == "CMD") {
                rankIndex = 2;
              } else if (form_values.ra_edit_rank == "SO") {
                rankIndex = 3;
              } else if (form_values.ra_edit_rank == "COFF") {
                rankIndex = 4;
              } else if (form_values.ra_edit_rank == "MOFF") {
                rankIndex = 5;
              } else if (form_values.ra_edit_rank == "SFF") {
                rankIndex = 6;
              } else if (form_values.ra_edit_rank == "ESFF") {
                rankIndex = 6.1;
              } else if (form_values.ra_edit_rank == "SCOP") {
                rankIndex = 8;
              } else if (form_values.ra_edit_rank == "COP") {
                rankIndex = 9;
              } else if (form_values.ra_edit_rank == "FF") {
                rankIndex = 10;
              } else if (form_values.ra_edit_rank == "EFF") {
                rankIndex = 10.1;
              } else if (form_values.ra_edit_rank == "CO") {
                rankIndex = -1;
              } else if (form_values.ra_edit_rank == "DCO") {
                rankIndex = 0;
              } else if (form_values.ra_edit_rank == "RSO") {
                rankIndex = 12;
              } else if (form_values.ra_edit_rank == "ARSO") {
                rankIndex = 13;
              } else if (form_values.ra_edit_rank == "RFS2") {
                rankIndex = 14;
              } else if (form_values.ra_edit_rank == "RFS") {
                rankIndex = 15;
              } else if (form_values.ra_edit_rank == "ARFS") {
                rankIndex = 16;
              } else if (form_values.ra_edit_rank == "RFF") {
                rankIndex = 17;
              } else if (form_values.ra_edit_rank == "RRF") {
                rankIndex = 18;
              } else {
                rankIndex = 11;
              }
              webix
                .ajax()
                .headers({ Authorization: "Bearer " + api_key })
                .sync()
                .put(
                  server_url + "/admin/edit_existing_ra",
                  {
                    roster_arrangement_id: roster_arrangement_id,
                    roster_arrangement_id_2: roster_arrangement_id_2,
                    curr_ra_id: form_values.ra_edit_id,
                    rank: form_values.ra_edit_rank,
                    rank_index: rankIndex,
                    from_date: moment(
                      form_values.ra_edit_start_date,
                      "YYYY-MM-DD HH:mm",
                    ).format("YYYYMMDD"),
                    to_date: moment(
                      form_values.ra_edit_end_date,
                      "YYYY-MM-DD HH:mm",
                    ).format("YYYYMMDD"),
                    acting_up: form_values.ra_edit_acting_up,
                    created_by: user_logged_in,
                  },
                  {
                    error: function (err) {
                      $$("loader-window").hide();
                      form.clearValidation();
                      webix.alert("There was an error updating this RA!");
                    },
                    success: function (result) {
                      $$("loader-window").hide();
                      form.clearValidation();
                      $$("ra_edit-window").hide();
                      getRosterArrangements();
                    },
                  },
                );
            }, 250);
          } else {
            webix.alert(
              "You must specify a valid 'Rank' for this Roster Arrangement!",
            );
          }
        }
      }
    });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("import_uploader")
      .attachEvent("onBeforeFileAdd", function (upload) {
        $$("loader-window").show();
        let sheet = $$("admin-page")
          .$$("roster_arrangement")
          .$$("excel_import");
        sheet.clearAll();
        sheet.parse(upload.file, "excel");
        $$("admin-page")
          .$$("roster_arrangement")
          .$$("file_name")
          .define("label", upload.name + " file loaded!");
        $$("admin-page").$$("roster_arrangement").$$("file_name").refresh();
        loadIndex = 1;
        return false;
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("excel_import")
      .attachEvent("onAfterLoad", function () {
        if (loadIndex == 1) {
          let sheet = $$("admin-page")
            .$$("roster_arrangement")
            .$$("excel_import");
          let rowId = 0;
          sheet.eachRow(function (row) {
            rowId += 1;
            if (rowId == 1) {
              sheet.remove(row);
            }
          });
          $$("admin-page").$$("roster_arrangement").$$("import_sheet").enable();
        }
        loadIndex += 1;
        $$("loader-window").hide();
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id) {
        let grid = $$("admin-page")
          .$$("roster_arrangement")
          .$$("roster_arrangements");
        let arrangementInfo = grid.getSelectedItem(id);
        if (arrangementInfo.length > 0) {
          let arrangementId = arrangementInfo[0].roster_arrangement_id;
          let payId = $$("admin-page")
            .$$("roster_arrangement")
            .$$("employees")
            .getValue();
          let form = $$("admin-page")
            .$$("roster_arrangement")
            .$$("formArrangement")
            .getValues();
          let start_date = arrangementInfo[0].start_date;
          let end_date = arrangementInfo[0].end_date;
          if (arrangementId !== "") {
            if (
              moment(start_date, "DD/MM/YYYY").isBefore(moment()) &&
              moment(end_date, "DD/MM/YYYY").isBefore(moment())
            ) {
              prev_period = true;
            }
            webix.confirm({
              title: "Confirm Delete",
              text: "Are you sure you want to delete the selected roster arrangement?",
              ok: "Yes",
              cancel: "No",
              callback: function (result) {
                switch (result) {
                  case true:
                    let bookingsTitle = "";
                    let bookingsList = "";
                    let no_of_bookings = 0;
                    let dateString = "";
                    getRABookings(
                      form.employees,
                      moment(start_date, "DD/MM/YYYY").format("YYYYMMDD"),
                      moment(end_date, "DD/MM/YYYY").format("YYYYMMDD"),
                      function (results) {
                        if (results.length > 0) {
                          no_of_bookings = results.length;
                          if (
                            prev_period === true &&
                            user_permission_level > 1
                          ) {
                            prev_period = false;
                            $$("loader-window").hide();
                            webix.alert({
                              text:
                                "You are trying to delete a Roster Arrangement for a period that has already passed.<br><br>Due to <strong>(" +
                                no_of_bookings +
                                ")</strong> bookings being detected during the selected period this RA can't be deleted.<br><br>Note: Issues may arise if the existing bookings are not managed/adjusted correctly<br>therefore this function needs to be reviewed and processed by a Sapphire support person.<br><br>",
                              width: 600,
                            });
                          } else {
                            if (no_of_bookings >= 20) {
                              bookingsTitle =
                                no_of_bookings +
                                " bookings were found linked to the Roster Arrangement you are trying to delete!";
                            } else {
                              results.forEach(function (result) {
                                if (result.booking_type != "standby_link") {
                                  bookingsList =
                                    bookingsList +
                                    "</br><b>(" +
                                    result.leave_type_code +
                                    ") " +
                                    result.leave_type_description +
                                    " on " +
                                    result.start_date +
                                    "</b>";
                                }
                              });
                              bookingsTitle =
                                "The following bookings were found linked to the Roster Arrangement you are trying to delete!";
                            }
                            webix
                              .modalbox({
                                title: "Existing Bookings Found!",
                                buttons: ["Yes", "No", "Cancel"],
                                width: 650,
                                text:
                                  bookingsTitle +
                                  "</br>" +
                                  bookingsList +
                                  "</br>" +
                                  "Do you also want to delete these bookings?</br></br><i style='font-size:12px'>Note: Only select 'No' if you intend on creating a new Roster Arrangement</br>at the same location covering the dates of all the bookings.</i></br></br><strong>WARNING: regardless of selecting [Yes] or [No] all 'Staff Movements' for this RA period will be deleted!</strong>",
                              })
                              .then(function (result) {
                                switch (result) {
                                  case "0":
                                    $$("loader-window").show();
                                    setTimeout(function () {
                                      if (live_site === true) {
                                        getEmployeeData(
                                          payId,
                                          function (values) {
                                            let properSurname = toProperCase(
                                              values[0].surname,
                                            );
                                            sendEmail(
                                              "SAPPHIRE<<EMAIL>>",
                                              values[0].notifications_email,
                                              "RE: Roster Arrangement",
                                              "A Roster Arrangement has been deleted for " +
                                                values[0].first_name +
                                                " " +
                                                properSurname +
                                                " to " +
                                                arrangementInfo[0].roster +
                                                " - " +
                                                arrangementInfo[0].shift +
                                                " - " +
                                                arrangementInfo[0].location +
                                                " from " +
                                                start_date +
                                                " to " +
                                                end_date +
                                                " by " +
                                                user_logged_in +
                                                " on " +
                                                moment().format(
                                                  "DD/MM/YYYY H:mm",
                                                ) +
                                                "",
                                              "",
                                              values[0].first_name +
                                                " " +
                                                properSurname,
                                              "Please contact Workforce <NAME_EMAIL> for any queries regarding this roster arrangement.",
                                              "Regards, The SAPPHIRE Team",
                                            );
                                          },
                                        );
                                      }
                                      deleteRosterArrangement(
                                        payId,
                                        arrangementId,
                                        function (response) {
                                          if (response) {
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("arrangement_id")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("employees")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("rank")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("rosters")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("shifts")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("locations")
                                              .setValue("");
                                          }
                                        },
                                      );
                                      let bkArray = [];
                                      results.forEach(function (result) {
                                        if (
                                          result.leave_type_code === "TOIL" ||
                                          result.leave_type_code === "SOIL"
                                        ) {
                                          soilToilBalances.reverseSoilToilEntry(
                                            result.leave_type_code,
                                            result.booking_id,
                                            result.date_string,
                                          );
                                        }
                                        bkArray.push(result.id);
                                      });
                                      webix
                                        .ajax()
                                        .headers({
                                          Authorization: "Bearer " + api_key,
                                        })
                                        .sync()
                                        .put(
                                          server_url +
                                            "/bookings/bulk_bookings",
                                          {
                                            bkArray: bkArray,
                                            deleted_by: user_logged_in,
                                          },
                                          {
                                            error: function (err) {},
                                            success: function (result) {},
                                          },
                                        );
                                      setTimeout(function () {
                                        webix.message({
                                          text:
                                            no_of_bookings +
                                            " linked Bookings Deleted...",
                                          type: "debug",
                                          expire: 3e3,
                                        });
                                      }, 250);
                                      getRosterArrangements();
                                      if (ro_view_showing == true) {
                                        availabilityReport.loadReport(
                                          $$("schedule-page").$$(
                                            "grid_ro_view_report",
                                          ),
                                          function (callback) {
                                            if (callback == "ok") {
                                              schedule.reload_roster();
                                            }
                                          },
                                        );
                                      } else {
                                        schedule.reload_roster();
                                      }
                                      $$("loader-window").hide();
                                    }, 250);
                                    break;
                                  case "1":
                                    $$("loader-window").show();
                                    setTimeout(function () {
                                      if (live_site === true) {
                                        getEmployeeData(
                                          payId,
                                          function (values) {
                                            let properSurname = toProperCase(
                                              values[0].surname,
                                            );
                                            sendEmail(
                                              "SAPPHIRE<<EMAIL>>",
                                              values[0].notifications_email,
                                              "RE: Roster Arrangement",
                                              "A Roster Arrangement has been deleted for " +
                                                values[0].first_name +
                                                " " +
                                                properSurname +
                                                " to " +
                                                arrangementInfo[0].roster +
                                                " - " +
                                                arrangementInfo[0].shift +
                                                " - " +
                                                arrangementInfo[0].location +
                                                " from " +
                                                start_date +
                                                " to " +
                                                end_date +
                                                " by " +
                                                user_logged_in +
                                                " on " +
                                                moment().format(
                                                  "DD/MM/YYYY H:mm",
                                                ) +
                                                "",
                                              "",
                                              values[0].first_name +
                                                " " +
                                                properSurname,
                                              "Please contact Workforce <NAME_EMAIL> for any queries regarding this roster arrangement.",
                                              "Regards, The SAPPHIRE Team",
                                            );
                                          },
                                        );
                                      }
                                      deleteRosterArrangement(
                                        payId,
                                        arrangementId,
                                        function (response) {
                                          if (response) {
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("arrangement_id")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("employees")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("rank")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("rosters")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("shifts")
                                              .setValue("");
                                            $$("admin-page")
                                              .$$("roster_arrangement")
                                              .$$("locations")
                                              .setValue("");
                                            if (ro_view_showing == true) {
                                              availabilityReport.loadReport(
                                                $$("schedule-page").$$(
                                                  "grid_ro_view_report",
                                                ),
                                                function (callback) {
                                                  if (callback == "ok") {
                                                    schedule.reload_roster();
                                                  }
                                                },
                                              );
                                            } else {
                                              schedule.reload_roster();
                                            }
                                          }
                                        },
                                      );
                                      let sm_count = 0;
                                      results.forEach(function (result) {
                                        if (
                                          result.booking_type ==
                                          "staff_movement"
                                        ) {
                                          sm_count += 1;
                                          dateString = moment(
                                            result.start_date,
                                            "DD/MM/YYYY HH:mm",
                                          ).format("YYYYMMDD");
                                          webix
                                            .ajax()
                                            .headers({
                                              Authorization:
                                                "Bearer " + api_key,
                                            })
                                            .sync()
                                            .del(
                                              server_url +
                                                "/bookings/booking_day",
                                              {
                                                booking_id: result.booking_id,
                                                date_string: dateString,
                                                status: result.status,
                                                deleted_by: user_logged_in,
                                              },
                                              {
                                                error: function (err) {},
                                                success: function (result) {},
                                              },
                                            );
                                        }
                                      });
                                      setTimeout(function () {
                                        webix.message({
                                          text:
                                            sm_count +
                                            " linked Staff Movements Deleted...",
                                          type: "debug",
                                          expire: 3e3,
                                        });
                                      }, 250);
                                      getRosterArrangements();
                                      $$("loader-window").hide();
                                    }, 250);
                                    break;
                                  case "2":
                                    webix.alert("Nothing was deleted!");
                                    break;
                                }
                              });
                          }
                        } else {
                          $$("loader-window").show();
                          setTimeout(function () {
                            if (live_site === true) {
                              getEmployeeData(payId, function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Roster Arrangement",
                                  "A Roster Arrangement has been deleted for " +
                                    values[0].first_name +
                                    " " +
                                    properSurname +
                                    " to " +
                                    arrangementInfo[0].roster +
                                    " - " +
                                    arrangementInfo[0].shift +
                                    " - " +
                                    arrangementInfo[0].location +
                                    " from " +
                                    start_date +
                                    " to " +
                                    end_date +
                                    " by " +
                                    user_logged_in +
                                    " on " +
                                    moment().format("DD/MM/YYYY H:mm") +
                                    "",
                                  "",
                                  values[0].first_name + " " + properSurname,
                                  "Please contact Workforce <NAME_EMAIL> for any queries regarding this roster arrangement.",
                                  "Regards, The SAPPHIRE Team",
                                );
                              });
                            }
                            deleteRosterArrangement(
                              payId,
                              arrangementId,
                              function (response) {
                                if (response) {
                                  $$("admin-page")
                                    .$$("roster_arrangement")
                                    .$$("arrangement_id")
                                    .setValue("");
                                  $$("admin-page")
                                    .$$("roster_arrangement")
                                    .$$("employees")
                                    .setValue("");
                                  $$("admin-page")
                                    .$$("roster_arrangement")
                                    .$$("rank")
                                    .setValue("");
                                  $$("admin-page")
                                    .$$("roster_arrangement")
                                    .$$("rosters")
                                    .setValue("");
                                  $$("admin-page")
                                    .$$("roster_arrangement")
                                    .$$("shifts")
                                    .setValue("");
                                  $$("admin-page")
                                    .$$("roster_arrangement")
                                    .$$("locations")
                                    .setValue("");
                                }
                              },
                            );
                            getRosterArrangements();
                            $$("loader-window").hide();
                          }, 250);
                        }
                      },
                    );
                }
              },
            });
          }
        } else {
          webix.alert("You must select an RA to delete!");
        }
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("no_end_date")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv === 1) {
          $$("admin-page").$$("roster_arrangement").$$("end_date").disable();
        } else {
          $$("admin-page").$$("roster_arrangement").$$("end_date").enable();
        }
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("show_temp_ra")
      .attachEvent("onChange", function (newv) {
        getRosterArrangements();
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("employees")
      .attachEvent("onChange", function (newv) {
        if (newv != "") {
          $$("admin-page").$$("roster_arrangement").$$("rank").setValue("");
          getRosterArrangements();
        }
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("rosters")
      .attachEvent("onChange", function (newv) {
        let pay_id = $$("admin-page")
          .$$("roster_arrangement")
          .$$("employees")
          .getValue();
        if (newv == "Comms") {
          $$("admin-page").$$("roster_arrangement").$$("rank").setValue("");
          $$("admin-page")
            .$$("roster_arrangement")
            .$$("rank")
            .define("options", [
              { id: "COFF", value: "COFF" },
              { id: "SCOP", value: "SCOP" },
              { id: "COP", value: "COP" },
            ]);
        } else if (newv == "Regional Operations") {
          $$("admin-page")
            .$$("roster_arrangement")
            .$$("rank")
            .define("options", all_rank_types);
        } else {
          $$("admin-page")
            .$$("roster_arrangement")
            .$$("rank")
            .define("options", all_rank_types);
          if (
            newv !== "Community Safety & Resilience" &&
            newv !== "Long Term Leave"
          ) {
            $$("admin-page")
              .$$("roster_arrangement")
              .$$("rank")
              .getList()
              .remove("COFF");
          }
          $$("admin-page")
            .$$("roster_arrangement")
            .$$("rank")
            .getList()
            .remove("SCOP");
          $$("admin-page")
            .$$("roster_arrangement")
            .$$("rank")
            .getList()
            .remove("COP");
        }
        getRank(pay_id, "roster_arrangement");
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("rank")
      .attachEvent("onChange", function (newv) {
        $$("admin-page")
          .$$("roster_arrangement")
          .$$("formArrangement")
          .clearValidation();
      });
    $$("btn_ra_recurrence-window_close").attachEvent(
      "onItemClick",
      function (id) {
        if (popupShowing === false) {
          webix.confirm({
            title: "Warning: Skip Creating Day Work Bookings",
            text: "Are you sure you want to skip creating the recurring work bookings?",
            width: 500,
            callback: function (result) {
              if (result == true) {
                $$("ra_recurrence-window_pattern").setValue("");
                $$("ra_recurrence-window_roster").setValue("");
                $$("ra_recurrence-window_shift").setValue("");
                $$("ra_recurrence-window_location").setValue("");
                $$("ra_recurrence-window_start_date").setValue(new Date());
                $$("ra_recurrence-window_end_date").setValue(new Date());
                $$("ra_recurrence-window").hide();
              }
            },
          });
        } else {
          $$("ra_recurrence-window_pattern").setValue("");
          $$("ra_recurrence-window_roster").setValue("");
          $$("ra_recurrence-window_shift").setValue("");
          $$("ra_recurrence-window_location").setValue("");
          $$("ra_recurrence-window_start_date").setValue(new Date());
          $$("ra_recurrence-window_end_date").setValue(new Date());
          $$("ra_recurrence-window").hide();
        }
      },
    );
    $$("ra_recurrence-window_pattern").attachEvent(
      "onChange",
      function (newv, oldv) {
        if (newv === "DW1") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Fri - <strong>8</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Fri - <strong>8</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW2") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Fri - <strong>8.4</strong> hrs/day - Week 1 Hours = <strong>42</strong></br><strong>Week 2</strong> - Mon~Fri - <strong>8.4</strong> hrs/day - Week 2 Hours = <strong>42</strong></br>RDO: <strong>Yes</strong> - Total Fortnightly Hours = <strong>84</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW3") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW4") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Thu - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Thu - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW5") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon/Tue/Wed/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Tue/Wed/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW6") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW7") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Fri - <strong>8.4</strong> hrs/day - Week 1 Hours = <strong>42</strong></br><strong>Week 2</strong> - Tue~Fri - <strong>9.5</strong> hrs/day - Week 2 Hours = <strong>38</strong></br>RDO: <strong>Yes</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW8") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon/Wed/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Wed/Thu/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW9") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW10") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Thu - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW11") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Thu 8.5/Fri 8 - <strong>8.5/8</strong> hrs/day - Week 1 Hours = <strong>42</strong></br><strong>Week 2</strong> - Mon~Thu 8.5/Fri 8 - <strong>8.5/8</strong> hrs/day - Week 2 Hours = <strong>42</strong></br>RDO: <strong>Yes</strong> - Total Fortnightly Hours = <strong>84</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW12") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Thu 8.1/Fri 7.6 - <strong>8.1/7.6</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Thu 8.1/Fri 7.6 - <strong>8.1/7.6</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW13") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Wed 5/Thu 12/Fri 11.5 - <strong>5.5/12/11.5</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Wed 5.5/Thu 12/Fri 11.5 - <strong>5.5/12/11.5</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW14") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Thu - <strong>9</strong> hrs/day - Week 1 Hours = <strong>36</strong></br><strong>Week 2</strong> - Mon~Thu 9/Fri 8 - <strong>9/8</strong> hrs/day - Week 2 Hours = <strong>44</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW15") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon~Fri - <strong>8.9</strong> hrs/day - Week 1 Hours = <strong>44.5</strong></br><strong>Week 2</strong> - Tue~Thu 8.9/Fri 8.8 - <strong>8.4</strong> hrs/day - Week 2 Hours = <strong>35.5</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW16") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        } else if (newv === "DW17") {
          $$("ra_recurrence-window_shift_info").define(
            "template",
            "<strong>Week 1</strong> - Tue~Thu 8.9/Fri 8.8 - <strong>8.9/8.8</strong> hrs/day - Week 1 Hours = <strong>35.5</strong></br><strong>Week 2</strong> - Mon~Fri - <strong>8.9</strong> hrs/day - Week 2 Hours = <strong>44.5</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
          );
          $$("ra_recurrence-window_shift_info").refresh();
        }
      },
    );
    $$("btn_ra_recurrence-window_create").attachEvent(
      "onItemClick",
      function (id) {
        let work_pattern = $$("ra_recurrence-window_pattern").getValue();
        let employee_name = "";
        let payId;
        if (popupShowing === false) {
          payId = $$("admin-page")
            .$$("roster_arrangement")
            .$$("employees")
            .getValue();
          employee_name = $$("admin-page")
            .$$("roster_arrangement")
            .$$("employees")
            .getText();
        } else {
          payId = selected_employee_info.pay_id;
          employee_name =
            selected_employee_info.surname +
            " " +
            selected_employee_info.first_name +
            " (" +
            payId +
            ")";
        }
        if (
          work_pattern == "" ||
          work_pattern == null ||
          work_pattern == undefined
        ) {
          webix.alert(
            "You must select a 'Work Pattern' from the drop down list!",
          );
        } else {
          webix.confirm({
            title: "Confirm Create Recurring Work Bookings",
            text:
              "Are you sure you want to create the selected " +
              work_pattern +
              " as</br>recurring work bookings for " +
              employee_name +
              "?",
            width: 500,
            callback: function (result) {
              if (result == true) {
                saveRecurringBooking(work_pattern, payId);
              }
            },
          });
        }
      },
    );
    $$("ra_recurrence-window_ra_end_date").attachEvent(
      "onChange",
      function (newv, oldv) {
        let ra_end_date = new Date();
        let sel_start_date = $$("ra_recurrence-window_start_date").getValue();
        let sel_roster = $$("ra_recurrence-window_roster").getValue();
        let sel_shift = $$("ra_recurrence-window_shift").getValue();
        let sel_location = $$("ra_recurrence-window_location").getValue();
        if (newv === 1) {
          getRAEndDate(
            selected_employee_info.pay_id,
            sel_roster,
            sel_shift,
            sel_location,
            function (value) {
              ra_end_date = moment(value, "DD/MM/YYYY HH:mm").format(
                "YYYY-MM-DD HH:mm",
              );
              $$("ra_recurrence-window_end_date").setValue(ra_end_date);
            },
          );
        } else {
          $$("ra_recurrence-window_end_date").setValue(sel_start_date);
        }
      },
    );
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("btn_roster_save")
      .attachEvent("onItemClick", function (id) {
        if (
          $$("admin-page")
            .$$("roster_arrangement")
            .$$("formArrangement")
            .validate()
        ) {
          let form = $$("admin-page")
            .$$("roster_arrangement")
            .$$("formArrangement")
            .getValues();
          let noEndDate = form.no_end_date;
          let roster = form.rosters;
          let shift = form.shifts;
          let location = form.locations;
          let endDate = new Date();
          let newRosterDate =
            moment(form.start_date).format("YYYY-MM-DD") +
            " " +
            form.start_time;
          let hidden_bks = 0;
          if (moment(form.end_date).isBefore(moment(form.start_date))) {
            webix.alert({
              text: "The 'End Date' cannot be before the 'Start Date' !",
              width: 400,
            });
          } else {
            if (moment(form.start_date).isBefore(moment())) {
              prev_period = true;
            }
            if (
              roster !== "Metro" &&
              roster !== "Comms" &&
              roster !== "Port Pirie" &&
              roster !== "Mt Gambier" &&
              roster !== "OTR" &&
              roster !== "Long Term Leave"
            ) {
              work_day_bookings_show = true;
            } else {
              work_day_bookings_show = false;
            }
            work_day_bookings_show = false;
            getEmployeeStatus(form.employees, function (response) {
              if (response[0].terminated == 1 || response[0].removed == 1) {
                webix.alert({
                  text: "The selected employee no longer has an active employment status!</br>Note: if you believe this is an error please contact WFR or Sapphire Support",
                  width: 550,
                });
              } else {
                if (form.rank != "?") {
                  $$("loader-window").show();
                  setTimeout(function () {
                    if (noEndDate == 1) {
                      getShiftEndDate(roster, shift, function (callback) {
                        endDate = callback;
                      });
                    } else {
                      endDate = form.end_date;
                    }
                    getRAShiftInfo(
                      form.rosters,
                      form.shifts,
                      newRosterDate,
                      function (callback) {
                        if (callback == "no shift") {
                          $$("loader-window").hide();
                          webix.alert(
                            "No shift named '" +
                              form.shifts +
                              "' has been found for the selected Roster. Please create the shift before you can assign any Roster Arrangements to it!",
                          );
                        } else if (callback == "date error") {
                          $$("loader-window").hide();
                          webix.alert(
                            "No shift named '" +
                              form.shifts +
                              "' has been found for the selected Roster within the selected Date range!",
                          );
                        } else {
                          let y = callback.dayNo - 1;
                          let patternArray = callback.array.data;
                          if (patternArray.length > 0) {
                            getRABookings(
                              form.employees,
                              moment(form.start_date).format("YYYYMMDD"),
                              moment(endDate).format("YYYYMMDD"),
                              function (results) {
                                if (results.length > 0) {
                                  if (
                                    prev_period === true &&
                                    user_permission_level > 1
                                  ) {
                                    prev_period = false;
                                    $$("loader-window").hide();
                                    webix.alert({
                                      text:
                                        "You are trying to create or replace a Roster Arrangement for a period that has already passed.<br><br>Due to <strong>(" +
                                        results.length +
                                        ")</strong> bookings being detected during the selected period this new RA can't be processed.<br><br>Note: Issues may arise if the existing bookings are not managed/adjusted correctly<br>therefore this function needs to be reviewed and processed by a Sapphire support person.<br><br>",
                                      width: 600,
                                    });
                                  } else {
                                    if (work_day_bookings_show === true) {
                                      let grid = $$("ra_bookings-window_grid");
                                      grid.clearAll();
                                      results.forEach(function (result) {
                                        if (
                                          result.booking_type != "standby_link"
                                        ) {
                                          grid.add({
                                            bk_id: result.id,
                                            booking_id: result.booking_id,
                                            roster: result.roster,
                                            shift: result.shift,
                                            location: result.location,
                                            start_date: result.start_date,
                                            end_date: result.end_date,
                                            hours: result.hours,
                                            type: result.leave_type_code,
                                            status: result.status,
                                          });
                                        }
                                      });
                                      $$("ra_bookings-window-label").define(
                                        "template",
                                        "<b>" +
                                          results.length +
                                          " bookings were found between the selected date range which will be affected</br>due to the Roster Arrangement adjustment being made.</b></br></br>Select the bookings that you want deleted by placing a tick in the checkbox next to each one.</br>Note: any bookings that are not selected for deletion will be moved to the new Roster Arrangement.</br><strong>Important: ALL 'Staff Movements' & 'Recalls' will be deleted regardless of selection!</strong>",
                                      );
                                      $$("ra_bookings-window-label").refresh();
                                      $$("move_ra_bookings_roster").setValue(
                                        roster,
                                      );
                                      $$("move_ra_bookings_shift").setValue(
                                        shift,
                                      );
                                      $$("move_ra_bookings_location").setValue(
                                        location,
                                      );
                                      grid.refresh();
                                      exp_employee = $$("admin-page")
                                        .$$("roster_arrangement")
                                        .$$("employees")
                                        .getText();
                                      webix
                                        .toExcel(grid, {
                                          filename:
                                            "Day Work RA - Deleted Bookings List for " +
                                            form.employees +
                                            "",
                                          name: "Bookings",
                                          ignore: { select: true },
                                          styles: true,
                                          heights: true,
                                        })
                                        .then(function () {
                                          webix.message({
                                            text: "Deleted Bookings Log File Exported...",
                                            type: "success",
                                            expire: 4e3,
                                          });
                                          let bkArray = [];
                                          results.forEach(function (result) {
                                            bkArray.push(result.id);
                                          });
                                          webix
                                            .ajax()
                                            .headers({
                                              Authorization:
                                                "Bearer " + api_key,
                                            })
                                            .sync()
                                            .put(
                                              server_url +
                                                "/bookings/bulk_bookings",
                                              {
                                                bkArray: bkArray,
                                                deleted_by: user_logged_in,
                                              },
                                              {
                                                error: function (err) {},
                                                success: function (result) {
                                                  webix.message({
                                                    text: "Old RA Bookings Are Being Deleted...",
                                                    type: "debug",
                                                    expire: 4e3,
                                                  });
                                                },
                                              },
                                            );
                                        });
                                    } else {
                                      let grid = $$("ra_bookings-window_grid");
                                      grid.clearAll();
                                      results.forEach(function (result) {
                                        if (
                                          result.booking_type != "standby_link"
                                        ) {
                                          grid.add({
                                            bk_id: result.id,
                                            booking_id: result.booking_id,
                                            roster: result.roster,
                                            shift: result.shift,
                                            location: result.location,
                                            start_date: result.start_date,
                                            end_date: result.end_date,
                                            hours: result.hours,
                                            type: result.leave_type_code,
                                            status: result.status,
                                          });
                                        } else {
                                          hidden_bks += 1;
                                        }
                                      });
                                      $$("ra_bookings-window-label").define(
                                        "template",
                                        "<b>" +
                                          (results.length - hidden_bks) +
                                          " bookings were found between the selected date range which will be affected</br>due to the Roster Arrangement adjustment being made.</b></br></br>Select the bookings that you want deleted by placing a tick in the checkbox next to each one.</br>Note: any bookings that are not selected for deletion will be moved to the new Roster Arrangement.</br><strong>Important: ALL 'Staff Movements' & 'Recalls' will be deleted regardless of selection!</strong>",
                                      );
                                      $$("ra_bookings-window-label").refresh();
                                      $$("move_ra_bookings_roster").setValue(
                                        roster,
                                      );
                                      $$("move_ra_bookings_shift").setValue(
                                        shift,
                                      );
                                      $$("move_ra_bookings_location").setValue(
                                        location,
                                      );
                                      grid.refresh();
                                    }
                                    deleteRAperiod(
                                      form.employees,
                                      moment(form.start_date).format(
                                        "YYYYMMDD",
                                      ),
                                      moment(endDate).format("YYYYMMDD"),
                                      function (response) {
                                        if (response == "ok") {
                                          createRosterArrangement(
                                            endDate,
                                            y,
                                            patternArray,
                                            callback.array.data.length,
                                            function (response) {
                                              if (response == "ok") {
                                                setTimeout(function () {
                                                  webix.message({
                                                    text: "New Roster Arrangement created!",
                                                    type: "success",
                                                    expire: 1500,
                                                  });
                                                  getRosterArrangements();
                                                  $$("loader-window").hide();
                                                  if (
                                                    work_day_bookings_show ===
                                                    true
                                                  ) {
                                                    if (live_site === false) {
                                                      $$(
                                                        "ra_recurrence-window",
                                                      ).show();
                                                      $$(
                                                        "ra_recurrence-window_roster",
                                                      ).setValue(roster);
                                                      $$(
                                                        "ra_recurrence-window_shift",
                                                      ).setValue(shift);
                                                      $$(
                                                        "ra_recurrence-window_location",
                                                      ).setValue(location);
                                                      $$(
                                                        "ra_recurrence-window_start_date",
                                                      ).setValue(
                                                        form.start_date,
                                                      );
                                                      $$(
                                                        "ra_recurrence-window_end_date",
                                                      ).setValue(endDate);
                                                      $$(
                                                        "ra_recurrence-window_end_date",
                                                      ).define(
                                                        "readonly",
                                                        true,
                                                      );
                                                      $$(
                                                        "ra_recurrence-window_end_date",
                                                      ).refresh();
                                                    }
                                                  } else {
                                                    if (
                                                      results.length -
                                                        hidden_bks >
                                                      0
                                                    ) {
                                                      $$(
                                                        "ra_bookings-window",
                                                      ).show();
                                                    }
                                                  }
                                                }, 3e3);
                                              }
                                            },
                                          );
                                        } else {
                                          $$("loader-window").hide();
                                        }
                                      },
                                    );
                                  }
                                } else {
                                  deleteRAperiod(
                                    form.employees,
                                    moment(form.start_date).format("YYYYMMDD"),
                                    moment(endDate).format("YYYYMMDD"),
                                    function (response) {
                                      if (response == "ok") {
                                        createRosterArrangement(
                                          endDate,
                                          y,
                                          patternArray,
                                          callback.array.data.length,
                                          function (response) {
                                            if (response == "ok") {
                                              setTimeout(function () {
                                                webix.message({
                                                  text: "New Roster Arrangement created!",
                                                  type: "success",
                                                  expire: 1500,
                                                });
                                                getRosterArrangements();
                                                $$("loader-window").hide();
                                                if (
                                                  work_day_bookings_show ===
                                                  true
                                                ) {
                                                  if (live_site === false) {
                                                    $$(
                                                      "ra_recurrence-window",
                                                    ).show();
                                                    $$(
                                                      "ra_recurrence-window_roster",
                                                    ).setValue(roster);
                                                    $$(
                                                      "ra_recurrence-window_shift",
                                                    ).setValue(shift);
                                                    $$(
                                                      "ra_recurrence-window_location",
                                                    ).setValue(location);
                                                    $$(
                                                      "ra_recurrence-window_start_date",
                                                    ).setValue(form.start_date);
                                                    $$(
                                                      "ra_recurrence-window_end_date",
                                                    ).setValue(endDate);
                                                    $$(
                                                      "ra_recurrence-window_end_date",
                                                    ).define("readonly", true);
                                                    $$(
                                                      "ra_recurrence-window_end_date",
                                                    ).refresh();
                                                  }
                                                }
                                              }, 3e3);
                                            }
                                          },
                                        );
                                      } else {
                                        $$("loader-window").hide();
                                      }
                                    },
                                  );
                                }
                              },
                            );
                          } else {
                            $$("loader-window").hide();
                            webix.alert(
                              "A corresponding Shift was not found. You must have a Shift created before you can create Roster arrangements!",
                            );
                          }
                        }
                      },
                    );
                  }, 250);
                } else {
                  webix.alert(
                    "You must specify a valid 'Rank' for this Roster Arrangement!",
                  );
                }
              }
            });
          }
        }
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        $$("admin-page").$$("roster_arrangement").$$("shifts").setValue("");
        $$("admin-page").$$("roster_arrangement").$$("locations").setValue("");
        let rosterName = $$("admin-page")
          .$$("roster_arrangement")
          .$$("rosters")
          .getText();
        load_shifts(rosterName);
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("shifts")
      .attachEvent("onChange", function (newv, oldv) {
        $$("admin-page").$$("roster_arrangement").$$("locations").setValue("");
        let rosterName = $$("admin-page")
          .$$("roster_arrangement")
          .$$("rosters")
          .getText();
        load_shifts(rosterName);
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("start_date")
      .attachEvent("onChange", function (newv, oldv) {
        let start_date = $$("admin-page")
          .$$("roster_arrangement")
          .$$("start_date")
          .getValue();
        if (newv !== oldv) {
          $$("admin-page")
            .$$("roster_arrangement")
            .$$("end_date")
            .setValue(start_date);
        }
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("clear_sheet")
      .attachEvent("onItemClick", function (id, e) {
        $$("admin-page").$$("roster_arrangement").$$("excel_import").clearAll();
        $$("admin-page").$$("roster_arrangement").$$("import_sheet").disable();
        $$("admin-page")
          .$$("roster_arrangement")
          .$$("file_name")
          .define("label", "Import Arrangement Reports from Gartan");
        $$("admin-page").$$("roster_arrangement").$$("file_name").refresh();
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("import_sheet")
      .attachEvent("onItemClick", function (id, e) {
        importRosterArrangement();
      });
    $$("admin-page")
      .$$("roster_arrangement")
      .$$("import_uploader")
      .attachEvent("onFileUploadError", function (file, response) {
        $$("loader-window").hide();
        webix.alert(response);
      });
    $$("btn_ra_bookings-window_close").attachEvent(
      "onItemClick",
      function (id, e) {
        let grid = $$("ra_bookings-window_grid");
        let newRoster = $$("move_ra_bookings_roster").getValue();
        let newShift = $$("move_ra_bookings_shift").getValue();
        let newLocation = $$("move_ra_bookings_location").getValue();
        let bkMoveArray = [];
        $$("loader-window").show();
        setTimeout(function () {
          let sm_count = 0;
          let dateString = "";
          grid.eachRow(function (row) {
            let record = grid.getItem(row);
            if (
              record.type.slice(-1) != "R" &&
              record.type != "OC" &&
              record.type != "ComER" &&
              record.type != "ComR" &&
              record.type != "Depl" &&
              record.type != "OD" &&
              record.type != "OTRRC" &&
              record.type != "OTRRN" &&
              record.type != "OTRRS" &&
              record.type != "CSaR" &&
              record.type != "OTRR" &&
              record.type != "RC"
            ) {
              bkMoveArray.push(record.bk_id);
            } else {
              sm_count += 1;
              dateString = moment(record.start_date, "DD/MM/YYYY HH:mm").format(
                "YYYYMMDD",
              );
              if (record.type === "TOIL" || record.type === "SOIL") {
                soilToilBalances.reverseSoilToilEntry(
                  record.type,
                  record.booking_id,
                  moment(record.start_date, "DD/MM/YYYY HH:mm").format(
                    "YYYYMMDD",
                  ),
                );
              }
              webix
                .ajax()
                .headers({ Authorization: "Bearer " + api_key })
                .sync()
                .del(
                  server_url + "/bookings/booking_day",
                  {
                    booking_id: record.booking_id,
                    date_string: dateString,
                    status: record.status,
                    deleted_by: user_logged_in,
                  },
                  { error: function (err) {}, success: function (result) {} },
                );
            }
          });
          if (bkMoveArray.length > 0) {
            webix
              .ajax()
              .headers({ Authorization: "Bearer " + api_key })
              .sync()
              .put(
                server_url + "/bookings/bulk_move_bookings",
                {
                  bkMoveArray: bkMoveArray,
                  roster: newRoster,
                  shift: newShift,
                  location: newLocation,
                },
                { error: function (err) {}, success: function (result) {} },
              );
          }
          setTimeout(function () {
            webix.message({
              text: sm_count + " linked Staff Movements and Recalls Deleted...",
              type: "debug",
              expire: 3e3,
            });
          }, 250);
          $$("loader-window").hide();
          $$("ra_bookings-window").hide();
          webix.message({
            text: "Bookings were moved successfully!",
            type: "success",
            expire: 1500,
          });
          let startDate = $$("admin-page")
            .$$("roster_arrangement")
            .$$("start_date")
            .getValue();
          let noEndDate = $$("admin-page")
            .$$("roster_arrangement")
            .$$("no_end_date")
            .getValue();
          let formEndDate = $$("admin-page")
            .$$("roster_arrangement")
            .$$("end_date")
            .getValue();
          let endDate = new Date();
          if (noEndDate == 1) {
            getShiftEndDate(newRoster, newShift, function (callback) {
              endDate = callback;
            });
          } else {
            endDate = formEndDate;
          }
          if (work_day_bookings_show === true) {
            $$("ra_recurrence-window").show();
            $$("ra_recurrence-window_roster").setValue(newRoster);
            $$("ra_recurrence-window_shift").setValue(newShift);
            $$("ra_recurrence-window_location").setValue(newLocation);
            $$("ra_recurrence-window_start_date").setValue(startDate);
            $$("ra_recurrence-window_end_date").setValue(endDate);
            $$("ra_recurrence-window_end_date").define("readonly", true);
            $$("ra_recurrence-window_end_date").refresh();
            work_day_bookings_show = false;
          }
        }, 250);
      },
    );
    $$("btn_ra_unselect_all_bookings").attachEvent(
      "onItemClick",
      function (id, e) {
        let grid = $$("ra_bookings-window_grid");
        grid.eachRow(function (row) {
          let record = grid.getItem(row);
          record.select = 0;
        });
        grid.refresh();
      },
    );
    $$("btn_ra_select_all_bookings").attachEvent(
      "onItemClick",
      function (id, e) {
        let grid = $$("ra_bookings-window_grid");
        grid.eachRow(function (row) {
          let record = grid.getItem(row);
          record.select = 1;
        });
        grid.refresh();
      },
    );
    $$("btn_ra_confirm_bookings").attachEvent("onItemClick", function (id, e) {
      let grid = $$("ra_bookings-window_grid");
      let dateString = "";
      let newRoster = $$("move_ra_bookings_roster").getValue();
      let newShift = $$("move_ra_bookings_shift").getValue();
      let newLocation = $$("move_ra_bookings_location").getValue();
      let noSelection = true;
      let bkArray = [];
      let bkMoveArray = [];
      let sm_count = 0;
      grid.eachRow(function (row) {
        let record = grid.getItem(row);
        if (record.select == 1) {
          noSelection = false;
        }
      });
      if (noSelection === false) {
        $$("loader-window").show();
        setTimeout(function () {
          grid.eachRow(function (row) {
            let record = grid.getItem(row);
            if (record.select == 1) {
              bkArray.push(record.bk_id);
              if (record.type === "TOIL" || record.type === "SOIL") {
                soilToilBalances.reverseSoilToilEntry(
                  record.type,
                  record.booking_id,
                  moment(record.start_date, "DD/MM/YYYY HH:mm").format(
                    "YYYYMMDD",
                  ),
                );
              }
            } else {
              if (
                record.type.slice(-1) != "R" &&
                record.type != "OC" &&
                record.type != "ComER" &&
                record.type != "ComR" &&
                record.type != "Depl" &&
                record.type != "OD" &&
                record.type != "OTRRC" &&
                record.type != "OTRRN" &&
                record.type != "OTRRS" &&
                record.type != "CSaR" &&
                record.type != "OTRR"
              ) {
                bkMoveArray.push(record.bk_id);
              } else {
                sm_count += 1;
                dateString = moment(
                  record.start_date,
                  "DD/MM/YYYY HH:mm",
                ).format("YYYYMMDD");
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .sync()
                  .del(
                    server_url + "/bookings/booking_day",
                    {
                      booking_id: record.booking_id,
                      date_string: dateString,
                      status: record.status,
                      deleted_by: user_logged_in,
                    },
                    { error: function (err) {}, success: function (result) {} },
                  );
              }
            }
          });
          if (bkArray.length > 0) {
            webix
              .ajax()
              .headers({ Authorization: "Bearer " + api_key })
              .sync()
              .put(
                server_url + "/bookings/bulk_bookings",
                { bkArray: bkArray, deleted_by: user_logged_in },
                { error: function (err) {}, success: function (result) {} },
              );
          }
          if (bkMoveArray.length > 0) {
            webix
              .ajax()
              .headers({ Authorization: "Bearer " + api_key })
              .sync()
              .put(
                server_url + "/bookings/bulk_move_bookings",
                {
                  bkMoveArray: bkMoveArray,
                  roster: newRoster,
                  shift: newShift,
                  location: newLocation,
                },
                { error: function (err) {}, success: function (result) {} },
              );
          }
          $$("loader-window").hide();
          $$("ra_bookings-window").hide();
          webix.message({
            text: "Bookings were changed successfully!",
            type: "success",
            expire: 1500,
          });
          setTimeout(function () {
            webix.message({
              text: sm_count + " linked Staff Movements and Recalls Deleted...",
              type: "debug",
              expire: 3e3,
            });
          }, 250);
          let startDate = $$("admin-page")
            .$$("roster_arrangement")
            .$$("start_date")
            .getValue();
          let noEndDate = $$("admin-page")
            .$$("roster_arrangement")
            .$$("no_end_date")
            .getValue();
          let formEndDate = $$("admin-page")
            .$$("roster_arrangement")
            .$$("end_date")
            .getValue();
          let endDate = new Date();
          if (noEndDate == 1) {
            getShiftEndDate(newRoster, newShift, function (callback) {
              endDate = callback;
            });
          } else {
            endDate = formEndDate;
          }
          if (work_day_bookings_show === true) {
            $$("ra_recurrence-window").show();
            $$("ra_recurrence-window_roster").setValue(newRoster);
            $$("ra_recurrence-window_shift").setValue(newShift);
            $$("ra_recurrence-window_location").setValue(newLocation);
            $$("ra_recurrence-window_start_date").setValue(startDate);
            $$("ra_recurrence-window_end_date").setValue(endDate);
            $$("ra_recurrence-window_end_date").define("readonly", true);
            $$("ra_recurrence-window_end_date").refresh();
            work_day_bookings_show = false;
          }
        }, 250);
      } else {
        webix.alert("No booking was selected for deletion!");
      }
    });
  }
  employees_subject.subscribe(function (data) {
    let select = $$("admin-page").$$("roster_arrangement").$$("employees");
    let employee_list = [];
    if (data) {
      data.forEach(function (value) {
        employee_list.push({
          id: value.id,
          value: value.value + " (" + value.id + ")",
        });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  function getRABookings(payId, startDate, endDate, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/ra_bookings",
        { pay_id: payId, start_date: startDate, end_date: endDate },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let value = JSON.parse(results);
            if (value) {
              callback(value);
            } else {
              callback([]);
            }
          },
        },
      );
  }
  function getRosterArrangements() {
    let payId = $$("admin-page")
      .$$("roster_arrangement")
      .$$("employees")
      .getValue();
    let ra_filter = $$("admin-page")
      .$$("roster_arrangement")
      .$$("show_temp_ra")
      .getValue();
    let grid = $$("admin-page")
      .$$("roster_arrangement")
      .$$("roster_arrangements");
    let timeline = $$("admin-page")
      .$$("roster_arrangement")
      .$$("ra_scheduler")
      .getGantt();
    $$("loader-window").show();
    grid.clearAll();
    timeline.clearAll();
    if (payId != "") {
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/admin/arrangements",
          { pay_id: payId, show_temp: ra_filter },
          {
            error: function (err) {
              $$("admin-page")
                .$$("roster_arrangement")
                .$$("roster_arrangements")
                .clearAll();
              $$("loader-window").hide();
            },
            success: function (results) {
              let result = JSON.parse(results);
              let search_results = [];
              let endDate = "";
              let location = "";
              let noEndDate = 0;
              let timelineData = [];
              let timelineObject = {};
              let au_hd = "";
              if (result.length > 0) {
                for (let x = 0; x < result.length; x++) {
                  if (result[x].end_date === null) {
                    endDate = "";
                  } else {
                    endDate = result[x].end_date;
                  }
                  if (result[x].location === null) {
                    location = "";
                  } else {
                    location = result[x].location;
                  }
                  if (result[x].no_end_date === false) {
                    noEndDate = 0;
                  } else {
                    noEndDate = 1;
                  }
                  if (
                    result[x].acting_up === 1 ||
                    result[x].acting_up === true
                  ) {
                    au_hd = "Yes";
                  } else {
                    au_hd = "No";
                  }
                  search_results.push({
                    id: result[x].id,
                    roster_arrangement_id: result[x].roster_arrangement_id,
                    roster: result[x].roster,
                    shift: result[x].shift,
                    location: location,
                    rank: result[x].rank,
                    acting_up: au_hd,
                    start_date: result[x].start_date.slice(0, 10),
                    end_date: endDate.slice(0, 10),
                    no_end_date: noEndDate,
                    linked_booking_id: result[x].linked_booking_id,
                    type: result[x].type,
                    created_by: result[x].created_by,
                    created_date: result[x].created_date,
                  });
                  timelineData.push({
                    id: result[x].id,
                    text:
                      result[x].start_date.slice(0, 10) +
                      " ~ " +
                      result[x].end_date.slice(0, 10) +
                      "</br>Days: " +
                      result[x].duration +
                      " - Rank: " +
                      result[x].rank,
                    roster:
                      result[x].roster +
                      " - " +
                      result[x].shift +
                      " - " +
                      location,
                    start_date: result[x].start_date,
                    end_date: endDate,
                    editable: false,
                  });
                }
                grid.define("data", search_results);
                grid.refresh();
                grid.eachRow(function (row) {
                  let record = grid.getItem(row);
                  if (record.acting_up === "Yes") {
                    grid.addCellCss(row, "acting_up", "low_soil");
                  }
                });
                timelineObject.data = timelineData;
                timeline.parse(timelineObject);
                $$("loader-window").hide();
              } else {
                grid.clearAll();
                $$("loader-window").hide();
              }
            },
          },
        );
    } else {
      grid.clearAll();
      $$("loader-window").hide();
    }
  }
  function importRosterArrangement() {
    let sheet = $$("admin-page").$$("roster_arrangement").$$("excel_import");
    let count = sheet.count() - 1;
    let rowNo = 0;
    let record = "";
    let patternArray = [];
    let noOfDays = 0;
    let roster_arrangement_id = "";
    let y = 0;
    let endDate = "";
    let errorReport = [];
    let period = $$("admin-page")
      .$$("roster_arrangement")
      .$$("import_duration")
      .getValue();
    if (count > 1) {
      $$("loader-window").show();
      setTimeout(function () {
        let importStartDate = $$("admin-page")
          .$$("roster_arrangement")
          .$$("import_start_date")
          .getValue();
        let start_date = moment(importStartDate, "YYYY-MM-DD").format(
          "DD/MM/YYYY",
        );
        let roster = "";
        let shift = "";
        let location = "";
        let rank = "";
        let rankIndex = 0;
        let newRosterDate = "";
        let pay_id = 0;
        let employeeName = "";
        let ra_type = "";
        let roster_id = "";
        let date_string = record.data9;
        let roster_date = record.data10;
        let seq_no = record.data11;
        let seq_day = record.data12;
        webix.message({
          id: "ra_counter",
          text: "Now processing entry " + rowNo,
          type: "debug",
          expire: -1,
        });
        sheet.eachRow(function (row) {
          webix.message.pull["ra_counter"].firstChild.innerHTML =
            "Now processing entry " + rowNo;
          rowNo += 1;
          record = sheet.getItem(row);
          if (rowNo >= 2) {
            let errorEntry = {};
            roster_id = record.data0;
            pay_id = record.data1;
            roster = record.data5;
            shift = record.data6;
            location = record.data7;
            rank = record.data8;
            date_string = record.data9;
            roster_date = record.data10;
            seq_no = record.data11;
            seq_day = record.data12;
            endDate = "2030-12-31 08:00";
            noOfDays = 1;
            if (noOfDays > 0) {
              getShiftDetails(roster, shift, newRosterDate, function (result) {
                if (result == "no shift") {
                  errorEntry.pay_id = pay_id;
                  errorEntry.employee = employeeName;
                  errorEntry.roster = roster;
                  errorEntry.shift = shift;
                  errorEntry.location = location;
                  errorEntry.start_date = newRosterDate;
                  errorEntry.type = ra_type;
                  errorEntry.error = "No Shift exists for this RA entry";
                  errorReport.push(errorEntry);
                } else if (result == "date error") {
                  errorEntry.pay_id = pay_id;
                  errorEntry.employee = employeeName;
                  errorEntry.roster = roster;
                  errorEntry.shift = shift;
                  errorEntry.location = location;
                  errorEntry.start_date = newRosterDate;
                  errorEntry.type = ra_type;
                  errorEntry.error =
                    "This RA's Start Date is before the existing Shift's Start Date";
                  errorReport.push(errorEntry);
                } else {
                  y = result.dayNo - 1;
                  patternArray = result.array.data;
                  rankIndex = 15;
                  if (patternArray.length > 0) {
                    webix
                      .ajax()
                      .headers({ Authorization: "Bearer " + api_key })
                      .sync()
                      .post(
                        server_url + "/admin/arrangements",
                        {
                          roster_arrangement_id: roster_arrangement_id,
                          pay_id: pay_id,
                          roster: roster,
                          shift: shift,
                          location: location,
                          rank: rank,
                          created_by: user_logged_in,
                          patternArray: patternArray,
                          noOfDays: noOfDays,
                          newRosterDate: start_date,
                          arraySize: result.array.data.length,
                          type: "Permanent",
                          y: y,
                          rank_index: rankIndex,
                        },
                        {
                          error: function (err) {},
                          success: function (result) {},
                        },
                      );
                  } else {
                    errorEntry.pay_id = pay_id;
                    errorEntry.employee = employeeName;
                    errorEntry.roster = roster;
                    errorEntry.shift = shift;
                    errorEntry.location = location;
                    errorEntry.start_date = newRosterDate;
                    errorEntry.type = ra_type;
                    errorEntry.error =
                      "No Shift information was found for this entry!";
                    errorReport.push(errorEntry);
                  }
                }
              });
            }
          }
        });
        console.table(errorReport);
        webix.message.hide("ra_counter");
        $$("loader-window").hide();
        webix.alert({
          text:
            count +
            " roster arrangement entries were successfully imported!</br></br>Note: it can take several minutes for the server to process all the entries so some</br> of the imported roster arrangements may not appear in the roster immediately.",
          width: 500,
        });
      }, 250);
    } else {
      webix.alert("No data was found in the imported file!");
    }
  }
  function getShiftDetails(roster, shift, startDate, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/shifts/info",
        { roster: roster, shift: shift },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            if (results) {
              let values = JSON.parse(results);
              if (values.data.length > 0) {
                if (
                  moment(startDate).isSameOrAfter(
                    values.data[0].seq_start_date,
                  ) === true
                ) {
                  getDaySeqNo(
                    values.data[0].shift_id,
                    startDate,
                    function (data) {
                      let result = JSON.parse(data);
                      callback({
                        array: values,
                        dayNo: result.data[0].seq_number,
                      });
                    },
                  );
                } else {
                  callback("date error");
                }
              } else {
                callback("no shift");
              }
            }
          },
        },
      );
  }
  function getShiftEndDate(roster, shift, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/shifts/end_date",
        { roster: roster, shift: shift },
        {
          error: function (err) {},
          success: function (result) {
            let value = JSON.parse(result);
            callback(value[0].end_date);
          },
        },
      );
  }
  function deleteRosterArrangement(pay_id, roster_arrangement_id, callback) {
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .del(
        server_url + "/admin/arrangements",
        { pay_id: pay_id, roster_arrangement_id: roster_arrangement_id },
        {
          error: function (err) {
            callback("error");
          },
          success: function () {
            webix.message({
              text: "Roster Arrangement Deleted...",
              type: "debug",
              expire: 2e3,
            });
            callback("ok");
          },
        },
      );
  }
  function deleteRAperiod(pay_id, startDate, endDate, callback) {
    let new_ra_id = formatUuid(getRandomValuesFunc());
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .del(
        server_url + "/admin/del_ra_period",
        {
          pay_id: pay_id,
          from_date: startDate,
          to_date: endDate,
          new_ra_id: new_ra_id,
        },
        {
          error: function (err) {
            callback("error");
          },
          success: function () {
            callback("ok");
          },
        },
      );
  }
  function createRosterArrangement(
    end_date,
    y,
    patternArray,
    arrayLength,
    callback,
  ) {
    let form = $$("admin-page")
      .$$("roster_arrangement")
      .$$("formArrangement")
      .getValues();
    let newRosterDate =
      moment(form.start_date).format("YYYY-MM-DD") + " " + form.start_time;
    let endDate = "";
    let noOfDays = 0;
    let roster_arrangement_id = "";
    let rankIndex = 0;
    endDate = moment(end_date).format("YYYY-MM-DD") + " " + form.end_time;
    noOfDays = moment(endDate).diff(newRosterDate, "days", true);
    roster_arrangement_id = formatUuid(getRandomValuesFunc());
    let acting_up = form.acting_up;
    if (form.rank == "ACFO") {
      rankIndex = 1;
    } else if (form.rank == "CMD") {
      rankIndex = 2;
    } else if (form.rank == "SO") {
      rankIndex = 3;
    } else if (form.rank == "COFF") {
      rankIndex = 4;
    } else if (form.rank == "MOFF") {
      rankIndex = 5;
    } else if (form.rank == "SFF") {
      rankIndex = 6;
    } else if (form.rank == "ESFF") {
      rankIndex = 6.1;
    } else if (form.rank == "SCOP") {
      rankIndex = 8;
    } else if (form.rank == "COP") {
      rankIndex = 9;
    } else if (form.rank == "FF") {
      rankIndex = 10;
    } else if (form.rank == "EFF") {
      rankIndex = 10.1;
    } else if (form.rank == "CO") {
      rankIndex = -1;
    } else if (form.rank == "DCO") {
      rankIndex = 0;
    } else if (form.rank == "RSO") {
      rankIndex = 12;
    } else if (form.rank == "ARSO") {
      rankIndex = 13;
    } else if (form.rank == "RFS2") {
      rankIndex = 14;
    } else if (form.rank == "RFS") {
      rankIndex = 15;
    } else if (form.rank == "ARFS") {
      rankIndex = 16;
    } else if (form.rank == "RFF") {
      rankIndex = 17;
    } else if (form.rank == "RRF") {
      rankIndex = 18;
    } else {
      rankIndex = 11;
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .post(
        server_url + "/admin/arrangements",
        {
          roster_arrangement_id: roster_arrangement_id,
          pay_id: form.employees,
          roster: form.rosters,
          shift: form.shifts,
          location: form.locations,
          rank: form.rank,
          created_by: user_logged_in,
          patternArray: patternArray,
          noOfDays: noOfDays,
          newRosterDate: newRosterDate,
          arraySize: arrayLength,
          type: "Permanent",
          y: y,
          rank_index: rankIndex,
          acting_up: acting_up,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
            webix.alert(
              "There was an error creating the Roster Arrangement. Please try again!",
            );
            callback();
          },
          success: function (result) {
            $$("admin-page")
              .$$("roster_arrangement")
              .$$("arrangement_id")
              .setValue(roster_arrangement_id);
            $$("admin-page")
              .$$("roster_arrangement")
              .$$("arrangement_id")
              .setValue("");
            $$("admin-page")
              .$$("roster_arrangement")
              .$$("rosters")
              .setValue("");
            $$("admin-page").$$("roster_arrangement").$$("shifts").setValue("");
            $$("admin-page")
              .$$("roster_arrangement")
              .$$("locations")
              .setValue("");
            $$("admin-page")
              .$$("roster_arrangement")
              .$$("acting_up")
              .setValue(0);
            if (live_site === true) {
              getEmployeeData(form.employees, function (values) {
                let properSurname = toProperCase(values[0].surname);
                sendEmail(
                  "SAPPHIRE<<EMAIL>>",
                  values[0].notifications_email,
                  "RE: Roster Arrangement",
                  "A Roster Arrangement has been created for " +
                    values[0].first_name +
                    " " +
                    properSurname +
                    " to " +
                    form.rosters +
                    " - " +
                    form.shifts +
                    " - " +
                    form.locations +
                    " from " +
                    moment(newRosterDate, "YYYY-MM-DD H:mm").format(
                      "DD/MM/YYYY",
                    ) +
                    " to " +
                    moment(endDate, "YYYY-MM-DD H:mm").format("DD/MM/YYYY") +
                    " by " +
                    user_logged_in +
                    " on " +
                    moment().format("DD/MM/YYYY H:mm") +
                    "",
                  "",
                  values[0].first_name + " " + properSurname,
                  "Please contact Workforce <NAME_EMAIL> for any queries regarding this roster arrangement.",
                  "Regards, The SAPPHIRE Team",
                );
              });
            }
            callback("ok");
          },
        },
      );
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("admin-page").$$("roster_arrangement").$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        if (value.roster_name != "Day Work") {
          options.push(value.roster_name);
        }
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  if (locationName !== "Christie Downs") {
                    locationOptions.push(locationName);
                  }
                });
                $$("admin-page")
                  .$$("roster_arrangement")
                  .$$("shifts")
                  .define("options", shiftOptions);
                $$("admin-page")
                  .$$("roster_arrangement")
                  .$$("shifts")
                  .refresh();
                $$("admin-page")
                  .$$("roster_arrangement")
                  .$$("locations")
                  .define("options", locationOptions);
                $$("admin-page")
                  .$$("roster_arrangement")
                  .$$("locations")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function saveRecurringBooking(workPattern, payId) {
    let roster = $$("ra_recurrence-window_roster").getValue();
    let shift = $$("ra_recurrence-window_shift").getValue();
    let location = $$("ra_recurrence-window_location").getValue();
    let bookingId = "";
    let noOfHours_1 = 0;
    let noOfHours_2 = 0;
    let noOfHours_3 = 0;
    let startDate = $$("ra_recurrence-window_start_date").getValue();
    let endDate = $$("ra_recurrence-window_end_date").getValue();
    let startTime_1 = "";
    let endTime_1 = "";
    let startTime_2 = "";
    let endTime_2 = "";
    let startTime_3 = "";
    let endTime_3 = "";
    let type_code_1 = "";
    let type_code_2 = "";
    let type_code_3 = "";
    let type_desc_1 = "";
    let type_desc_2 = "";
    let type_desc_3 = "";
    let recLinkId = "";
    let no_of_occurrences = 0;
    let weekNo = 1;
    let repetitions = [];
    let bookingsArray = [];
    let weekDayNo = 0;
    $$("loader-window").show();
    setTimeout(function () {
      if (workPattern === "DW1") {
        type_code_1 = "8";
        type_code_2 = "8";
        type_desc_1 = "8 Hour Day Work";
        type_desc_2 = "8 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "16:00";
        startTime_2 = "08:00";
        endTime_2 = "16:00";
        noOfHours_1 = 8;
        noOfHours_2 = 8;
      } else if (workPattern === "DW2") {
        type_code_1 = "8.4";
        type_code_2 = "8.4";
        type_desc_1 = "8.4 Hour Day Work";
        type_desc_2 = "8.4 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "16:24";
        startTime_2 = "08:00";
        endTime_2 = "16:24";
        noOfHours_1 = 8.4;
        noOfHours_2 = 8.4;
      } else if (workPattern === "DW3") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW4") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW5") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW6") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW7") {
        type_code_1 = "8.4";
        type_code_2 = "9.5";
        type_desc_1 = "8.4 Hour Day Work";
        type_desc_2 = "9.5 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "16:24";
        startTime_2 = "08:00";
        endTime_2 = "17:30";
        noOfHours_1 = 8.4;
        noOfHours_2 = 9.5;
      } else if (workPattern === "DW8") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW9") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW10") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW11") {
        type_code_1 = "8.5";
        type_code_2 = "8.5";
        type_desc_1 = "8.5 Hour Day Work";
        type_desc_2 = "8.5 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "16:30";
        startTime_2 = "08:00";
        endTime_2 = "16:30";
        noOfHours_1 = 8.5;
        noOfHours_2 = 8.5;
      } else if (workPattern === "DW12") {
        type_code_1 = "8.1";
        type_code_2 = "8.1";
        type_desc_1 = "8.1 Hour Day Work";
        type_desc_2 = "8.1 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "16:06";
        startTime_2 = "08:00";
        endTime_2 = "16:06";
        noOfHours_1 = 8.1;
        noOfHours_2 = 8.1;
      } else if (workPattern === "DW13") {
        type_code_1 = "5.5";
        type_code_2 = "5.5";
        type_desc_1 = "5.5 Hour Day Work";
        type_desc_2 = "5.5 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "13:30";
        startTime_2 = "08:00";
        endTime_2 = "13:30";
        noOfHours_1 = 5.5;
        noOfHours_2 = 5.5;
      } else if (workPattern === "DW14") {
        type_code_1 = "9";
        type_code_2 = "9";
        type_desc_1 = "9 Hour Day Work";
        type_desc_2 = "9 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "17:00";
        startTime_2 = "08:00";
        endTime_2 = "17:00";
        noOfHours_1 = 9;
        noOfHours_2 = 9;
      } else if (workPattern === "DW15") {
        type_code_1 = "8.9";
        type_code_2 = "8.9";
        type_desc_1 = "8.9 Hour Day Work";
        type_desc_2 = "8.9 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "16:54";
        startTime_2 = "08:00";
        endTime_2 = "16:54";
        noOfHours_1 = 8.9;
        noOfHours_2 = 8.9;
      } else if (workPattern === "DW16") {
        type_code_1 = "10";
        type_code_2 = "10";
        type_desc_1 = "10 Hour Day Work";
        type_desc_2 = "10 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "18:00";
        startTime_2 = "08:00";
        endTime_2 = "18:00";
        noOfHours_1 = 10;
        noOfHours_2 = 10;
      } else if (workPattern === "DW17") {
        type_code_1 = "8.9";
        type_code_2 = "8.9";
        type_desc_1 = "8.9 Hour Day Work";
        type_desc_2 = "8.9 Hour Day Work";
        startTime_1 = "08:00";
        endTime_1 = "16:54";
        startTime_2 = "08:00";
        endTime_2 = "16:54";
        noOfHours_1 = 8.9;
        noOfHours_2 = 8.9;
      }
      let periodStartDate = moment(startDate, "YYYY-MM-DD 12:00");
      let periodEndDate = moment(endDate, "YYYY-MM-DD 12:00");
      no_of_occurrences = periodEndDate.diff(periodStartDate, "days", true);
      for (let x = 0; x <= Math.abs(no_of_occurrences); x++) {
        repetitions.push(x);
      }
      recLinkId = formatUuid(getRandomValuesFunc());
      let recurring_link_id = recLinkId.slice(0, 8) + moment().format("ss");
      bookingId = formatUuid(getRandomValuesFunc());
      async.eachSeries(
        repetitions,
        function (x, callback) {
          let bookingObject = {};
          let beginDate = "";
          let finishDate = "";
          let chkDateTime = moment(
            moment(startDate, "YYYY-MM-DD 12:00").add(x, "days"),
          ).format("YYYY-MM-DD HH:mm");
          weekDayNo = moment(chkDateTime).weekday();
          if (weekNo === 1) {
            beginDate =
              moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
              " " +
              startTime_1;
            if (workPattern === "DW11" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 16:00";
            } else if (workPattern === "DW12" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 15:36";
            } else if (workPattern === "DW13" && weekDayNo === 4) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 20:00";
            } else if (workPattern === "DW13" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 19:30";
            } else if (workPattern === "DW17" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 16:48";
            } else {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " " +
                endTime_1;
            }
          } else {
            beginDate =
              moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
              " " +
              startTime_2;
            if (workPattern === "DW11" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 16:00";
            } else if (workPattern === "DW12" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 15:36";
            } else if (workPattern === "DW13" && weekDayNo === 4) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 20:00";
            } else if (workPattern === "DW13" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 19:30";
            } else if (workPattern === "DW14" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 16:00";
            } else if (workPattern === "DW15" && weekDayNo === 5) {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " 16:48";
            } else {
              finishDate =
                moment(periodStartDate).format("YYYY-MM-DD").substring(0, 10) +
                " " +
                endTime_2;
            }
          }
          let beginDateTime = moment(moment(beginDate).add(x, "days")).format(
            "YYYY-MM-DD HH:mm",
          );
          let endDateTime = moment(moment(finishDate).add(x, "days")).format(
            "YYYY-MM-DD HH:mm",
          );
          let dateString = moment(beginDateTime).format("YYYYMMDD");
          if (weekDayNo !== 6 && weekDayNo !== 0) {
            if (
              public_holiday_dates_array.some(
                (public_holiday_dates_array) =>
                  public_holiday_dates_array.date_string == dateString,
              )
            ) {
              bookingObject.booking_id = bookingId;
              bookingObject.pay_id = payId;
              bookingObject.leave_type_code = "PH";
              bookingObject.leave_type_description = "Public Holiday";
              bookingObject.hours = 0;
              bookingObject.roster = roster;
              bookingObject.shift = shift;
              bookingObject.location = location;
              bookingObject.start_date = beginDateTime;
              bookingObject.end_date = endDateTime;
              bookingObject.date_string = dateString;
              bookingObject.booking_first_date = beginDateTime;
              bookingObject.booking_last_date = endDateTime;
              bookingObject.bk_period = "day";
              bookingObject.created_by = user_logged_in;
              bookingObject.booking_type = "day_work";
              bookingObject.recurring_link_id = recurring_link_id;
            } else {
              if (weekNo === 1) {
                bookingObject.booking_id = bookingId;
                bookingObject.pay_id = payId;
                if (workPattern === "DW3" && weekDayNo === 1) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW4" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW5" && weekDayNo === 4) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW6" && weekDayNo === 3) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW8" && weekDayNo === 2) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW9" && weekDayNo === 3) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW10" && weekDayNo === 1) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW11" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "8";
                  bookingObject.leave_type_description = "8 Hour Day Work";
                  bookingObject.hours = 8;
                } else if (workPattern === "DW12" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "7.6";
                  bookingObject.leave_type_description = "7.6 Hour Day Work";
                  bookingObject.hours = 7.6;
                } else if (workPattern === "DW13" && weekDayNo === 4) {
                  bookingObject.leave_type_code = "12";
                  bookingObject.leave_type_description = "12 Hour Day Work";
                  bookingObject.hours = 12;
                } else if (workPattern === "DW13" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "11.5";
                  bookingObject.leave_type_description = "11.5 Hour Day Work";
                  bookingObject.hours = 11.5;
                } else if (workPattern === "DW14" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW16" && weekDayNo === 3) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW17" && weekDayNo === 1) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW17" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "8.8";
                  bookingObject.leave_type_description = "8.8 Hour Day Work";
                  bookingObject.hours = 8.8;
                } else {
                  bookingObject.leave_type_code = type_code_1;
                  bookingObject.leave_type_description = type_desc_1;
                  bookingObject.hours = noOfHours_1;
                }
                bookingObject.roster = roster;
                bookingObject.shift = shift;
                bookingObject.location = location;
                bookingObject.start_date = beginDateTime;
                bookingObject.end_date = endDateTime;
                bookingObject.date_string = dateString;
                bookingObject.booking_first_date = beginDateTime;
                bookingObject.booking_last_date = endDateTime;
                bookingObject.bk_period = "day";
                bookingObject.created_by = user_logged_in;
                bookingObject.booking_type = "day_work";
                bookingObject.recurring_link_id = recurring_link_id;
              } else {
                bookingObject.booking_id = bookingId;
                bookingObject.pay_id = payId;
                if (workPattern === "DW3" && weekDayNo === 1) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW4" && weekDayNo === 4) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW5" && weekDayNo === 3) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW6" && weekDayNo === 1) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW7" && weekDayNo === 1) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW8" && weekDayNo === 2) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW9" && weekDayNo === 3) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW10" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW11" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "8";
                  bookingObject.leave_type_description = "8 Hour Day Work";
                  bookingObject.hours = 8;
                } else if (workPattern === "DW12" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "7.6";
                  bookingObject.leave_type_description = "7.6 Hour Day Work";
                  bookingObject.hours = 7.6;
                } else if (workPattern === "DW13" && weekDayNo === 4) {
                  bookingObject.leave_type_code = "12";
                  bookingObject.leave_type_description = "12 Hour Day Work";
                  bookingObject.hours = 12;
                } else if (workPattern === "DW13" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "11.5";
                  bookingObject.leave_type_description = "11.5 Hour Day Work";
                  bookingObject.hours = 11.5;
                } else if (workPattern === "DW14" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "8";
                  bookingObject.leave_type_description = "8 Hour Day Work";
                  bookingObject.hours = 8;
                } else if (workPattern === "DW15" && weekDayNo === 1) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else if (workPattern === "DW15" && weekDayNo === 5) {
                  bookingObject.leave_type_code = "8.8";
                  bookingObject.leave_type_description = "8.8 Hour Day Work";
                  bookingObject.hours = 8.8;
                } else if (workPattern === "DW16" && weekDayNo === 3) {
                  bookingObject.leave_type_code = "NWD";
                  bookingObject.leave_type_description = "Non Work Day";
                  bookingObject.hours = 0;
                } else {
                  bookingObject.leave_type_code = type_code_2;
                  bookingObject.leave_type_description = type_desc_2;
                  bookingObject.hours = noOfHours_2;
                }
                bookingObject.roster = roster;
                bookingObject.shift = shift;
                bookingObject.location = location;
                bookingObject.start_date = beginDateTime;
                bookingObject.end_date = endDateTime;
                bookingObject.date_string = dateString;
                bookingObject.booking_first_date = beginDateTime;
                bookingObject.booking_last_date = endDateTime;
                bookingObject.bk_period = "day";
                bookingObject.created_by = user_logged_in;
                bookingObject.booking_type = "day_work";
                bookingObject.recurring_link_id = recurring_link_id;
              }
            }
            bookingsArray.push(bookingObject);
            callback();
          } else {
            if (weekDayNo === 0) {
              if (weekNo === 1) {
                weekNo = 2;
              } else {
                weekNo = 1;
              }
            }
            callback();
          }
        },
        function () {
          webix
            .ajax()
            .headers({ Authorization: "Bearer " + api_key })
            .sync()
            .post(
              server_url + "/bookings/insert_work_day_bookings",
              { bookingsArray: bookingsArray, day_work_pattern: workPattern },
              {
                error: function (err) {
                  $$("loader-window").hide();
                  $$("ra_recurrence-window").hide();
                  webix.alert({
                    text: "There was an error creating the Day Work bookings!",
                    width: 500,
                  });
                },
                success: function (result) {
                  $$("loader-window").hide();
                  $$("ra_recurrence-window_pattern").setValue("");
                  $$("ra_recurrence-window_roster").setValue("");
                  $$("ra_recurrence-window_shift").setValue("");
                  $$("ra_recurrence-window_location").setValue("");
                  $$("ra_recurrence-window_start_date").setValue(new Date());
                  $$("ra_recurrence-window_end_date").setValue(new Date());
                  $$("ra_recurrence-window").hide();
                  webix.alert({
                    text: "The Day Work bookings were successfully created!",
                    width: 450,
                  });
                  if (popupShowing === true) {
                    schedule.reload_roster();
                  }
                },
              },
            );
        },
      );
    }, 250);
  }
  function getRAEndDate(pay_id, roster, shift, location, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/get_ra_end_date",
        { pay_id: pay_id, roster: roster, shift: shift, location: location },
        {
          error: function (err) {
            callback([]);
          },
          success: function (result) {
            callback(result);
          },
        },
      );
  }
  function updateRank(ra_id, old_rank, new_rank) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/admin/update_ra_rank",
        {
          roster_arrangement_id: ra_id,
          old_rank: old_rank,
          new_rank: new_rank,
        },
        {
          error: function (err) {
            webix.alert({
              text: "There was an error updating the Rank for the selected RA!",
              width: 450,
            });
          },
          success: function (result) {
            getRosterArrangements();
            $$("admin-page")
              .$$("roster_arrangement")
              .$$("change_rank")
              .setValue("");
            webix.alert({
              text: "Rank has been updated for the selected RA!",
              width: 450,
            });
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
