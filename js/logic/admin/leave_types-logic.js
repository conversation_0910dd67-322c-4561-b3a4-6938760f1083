let leaveTypes = (function () {
  let edit_mode;
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("leave_types")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        addLeaveType();
      });
    $$("admin-page")
      .$$("leave_types")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteLeaveType(id);
      });
    $$("btn_leaveType_save").attachEvent("onItemClick", function (id, e) {
      saveLeaveType();
    });
    $$("btn_leaveType_close").attachEvent("onItemClick", function (id, e) {
      $$("formLeaveTypes").clear();
      $$("formLeaveTypes").clearValidation();
      $$("leaveTypes-popup").hide();
    });
    $$("admin-page")
      .$$("leave_types")
      .$$("grid-leavetypes")
      .attachEvent("onItemDblClick", function (id) {
        edit_mode = "EDIT";
        let selected_row = $$("admin-page")
          .$$("leave_types")
          .$$("grid-leavetypes")
          .getSelectedItem(id);
        if (selected_row !== undefined) {
          if (selected_row[0].id > 0) {
            let row_id = selected_row[0].id;
            getleaveTypesInfo(row_id, id);
          }
        }
      });
    loadLeaveTypes();
  }
  function getleaveTypesInfo(row_id, selected_id) {
    let selected_row = $$("admin-page")
      .$$("leave_types")
      .$$("grid-leavetypes")
      .getSelectedItem(selected_id);
    let rowId = selected_row[0].id;
    let code = selected_row[0].code;
    let description = selected_row[0].description;
    let type = selected_row[0].type;
    $$("leaveType_id").setValue(rowId);
    $$("leaveType_code").setValue(code);
    $$("leaveType_description").setValue(description);
    $$("leaveType_type").setValue(type);
    $$("leaveType-label").define(
      "label",
      "<span class='header_font'>Edit Leave Type</span>",
    );
    $$("leaveType-label").refresh();
    $$("leaveTypes-popup").show();
  }
  function addLeaveType() {
    edit_mode = "ADD";
    $$("leaveType-label").define(
      "label",
      "<span class='header_font'>Add Leave Type</span>",
    );
    $$("leaveType-label").refresh();
    $$("formLeaveTypes").clear();
    $$("leaveTypes-popup").show();
  }
  function deleteLeaveType(id) {
    let selected_row = $$("admin-page")
      .$$("leave_types")
      .$$("grid-leavetypes")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Leave Type",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Leave Type?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/leave_types",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadLeaveTypes();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Leave Type has been selected!");
    }
  }
  function loadLeaveTypes() {
    $$("admin-page").$$("leave_types").$$("grid-leavetypes").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/leave_types",
        {},
        {
          error: function (err) {},
          success: function (results) {
            leave_types_subject.next(results);
          },
        },
      );
  }
  leave_types_subject.subscribe(function (data) {
    $$("admin-page").$$("leave_types").$$("grid-leavetypes").parse(data);
  });
  function saveLeaveType() {
    if ($$("formLeaveTypes").validate()) {
      let form = $$("formLeaveTypes").getValues();
      if (edit_mode === "ADD") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/admin/leave_types",
            {
              code: form.leaveType_code,
              description: form.leaveType_description,
              type: form.leaveType_type,
            },
            {
              error: function (err) {},
              success: function () {
                $$("leaveTypes-popup").hide();
                loadLeaveTypes();
              },
            },
          );
      } else if (edit_mode === "EDIT") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .put(
            server_url + "/admin/leave_types",
            {
              id: form.leaveType_id,
              code: form.leaveType_code,
              description: form.leaveType_description,
              type: form.leaveType_type,
            },
            {
              error: function (err) {},
              success: function () {
                $$("leaveTypes-popup").hide();
                loadLeaveTypes();
              },
            },
          );
      }
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
