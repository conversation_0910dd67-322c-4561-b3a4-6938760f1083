let shiftTypes = (function () {
  let edit_mode;
  function initApplication() {
    eventHandlers();
    loadShiftTypes();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("shift_types")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        addShiftType();
      });
    $$("admin-page")
      .$$("shift_types")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteShiftType(id);
      });
    $$("btn_shiftType_save").attachEvent("onItemClick", function (id, e) {
      saveShiftType();
    });
    $$("btn_shiftType_close").attachEvent("onItemClick", function (id, e) {
      $$("formShiftTypes").clear();
      $$("formShiftTypes").clearValidation();
      $$("shiftTypes-popup").hide();
    });
    $$("admin-page")
      .$$("shift_types")
      .$$("grid-shifttypes")
      .attachEvent("onItemDblClick", function (id) {
        edit_mode = "EDIT";
        let selected_row = $$("admin-page")
          .$$("shift_types")
          .$$("grid-shifttypes")
          .getSelectedItem(id);
        if (selected_row !== undefined) {
          if (selected_row[0].id > 0) {
            let row_id = selected_row[0].id;
            getshiftTypesInfo(row_id, id);
          }
        }
      });
  }
  function addShiftType() {
    edit_mode = "ADD";
    $$("shiftType-label").define(
      "label",
      "<span class='header_font'>Add Relieving Code</span>",
    );
    $$("shiftType-label").refresh();
    $$("formShiftTypes").clear();
    $$("shiftTypes-popup").show();
  }
  function deleteShiftType(id) {
    let selected_row = $$("admin-page")
      .$$("shift_types")
      .$$("grid-shifttypes")
      .getSelectedItem(id);
    if (selected_row.length > 0) {
      if (selected_row[0].id > 0) {
        let row_id = selected_row[0].id;
        webix.confirm({
          title: "Delete Shift Type",
          ok: "Yes",
          cancel: "No",
          text: "Are you sure you want to delete the selected Shift Type?",
          callback: function (result) {
            switch (result) {
              case true:
                webix
                  .ajax()
                  .headers({ Authorization: "Bearer " + api_key })
                  .del(
                    server_url + "/admin/shift_types",
                    { id: row_id },
                    {
                      error: function (err) {},
                      success: function () {
                        loadShiftTypes();
                      },
                    },
                  );
            }
          },
        });
      }
    } else {
      webix.alert("No Shift Type has been selected!");
    }
  }
  shift_types_subject.subscribe(function (data) {
    $$("admin-page").$$("shift_types").$$("grid-shifttypes").parse(data);
  });
  function getshiftTypesInfo(row_id, selected_id) {
    let selected_row = $$("admin-page")
      .$$("shift_types")
      .$$("grid-shifttypes")
      .getSelectedItem(selected_id);
    let rowId = selected_row[0].id;
    let code = selected_row[0].code;
    let description = selected_row[0].description;
    $$("shiftType_id").setValue(rowId);
    $$("shiftType_code").setValue(code);
    $$("shiftType_description").setValue(description);
    $$("shiftType-label").define(
      "label",
      "<span class='header_font'>Edit Relieving Code</span>",
    );
    $$("shiftType-label").refresh();
    $$("shiftTypes-popup").show();
  }
  function loadShiftTypes() {
    $$("admin-page").$$("shift_types").$$("grid-shifttypes").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/shift_types",
        {},
        {
          error: function (err) {},
          success: function (results) {
            shift_types_subject.next(results);
          },
        },
      );
  }
  function saveShiftType() {
    if ($$("formShiftTypes").validate()) {
      let form = $$("formShiftTypes").getValues();
      if (edit_mode === "ADD") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/admin/shift_types",
            {
              code: form.shiftType_code,
              description: form.shiftType_description,
            },
            {
              error: function (err) {},
              success: function () {
                $$("shiftTypes-popup").hide();
                loadShiftTypes();
              },
            },
          );
      } else if (edit_mode === "EDIT") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .put(
            server_url + "/admin/shift_types",
            {
              id: form.shiftType_id,
              code: form.shiftType_code,
              description: form.shiftType_description,
            },
            {
              error: function (err) {},
              success: function () {
                $$("shiftTypes-popup").hide();
                loadShiftTypes();
              },
            },
          );
      }
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
