let skillCodes = (function () {
  let edit_mode;
  let skill_codes = [];
  function initApplication() {
    eventHandlers();
    loadSkillCodes();
  }
  function eventHandlers() {
    $$("admin-page")
      .$$("skill_codes")
      .$$("btn_add")
      .attachEvent("onItemClick", function (id, e) {
        addSkillCode();
      });
    $$("btn_skillCode_close").attachEvent("onItemClick", function (id, e) {
      $$("formSkillCodes").clear();
      $$("formSkillCodes").clearValidation();
      $$("skillCodes-popup").hide();
    });
    $$("admin-page")
      .$$("skill_codes")
      .$$("grid-skillCodes")
      .attachEvent("onItemDblClick", function (id) {
        edit_mode = "EDIT";
        let selected_row = $$("admin-page")
          .$$("skill_codes")
          .$$("grid-skillCodes")
          .getSelectedItem(id);
        if (selected_row !== undefined) {
          if (selected_row[0].id > 0) {
            let row_id = selected_row[0].id;
            getskillCodeInfo(row_id, id);
          }
        }
      });
    $$("btn_skillCode_save").attachEvent("onItemClick", function (id, e) {
      saveSkillCode();
    });
    $$("admin-page")
      .$$("skill_codes")
      .$$("btn_delete")
      .attachEvent("onItemClick", function (id, e) {
        deleteSkillCode();
      });
    $$("admin-page")
      .$$("skill_codes")
      .$$("grid-skillCodes")
      .attachEvent("onAfterEditStop", function (state, editor, ignoreUpdate) {
        if (editor.column == "linked_codes") {
          if (state.value !== state.old) {
            let data = $$("admin-page")
              .$$("skill_codes")
              .$$("grid-skillCodes")
              .getItem(editor.row);
            deleteAllLinkedSkills(data.code, function (callback) {
              if (callback === "ok") {
                updateLinkedSkills(data.code, data.linked_codes);
              }
            });
          }
        }
      });
    $$("admin-page")
      .$$("skill_codes")
      .$$("grid-skillCodes")
      .attachEvent("onResize", function (width, height) {
        $$("admin-page")
          .$$("skill_codes")
          .$$("grid-skillCodes")
          .adjustRowHeight("linked_codes", true);
      });
  }
  function addSkillCode() {
    edit_mode = "ADD";
    $$("skillCode-label").define(
      "label",
      "<span class='header_font'>Add Skill Code</span>",
    );
    $$("skillCode-label").refresh();
    $$("formSkillCodes").clear();
    $$("skillCodes-popup").show();
  }
  function deleteAllLinkedSkills(code, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .del(
        server_url + "/admin/linked_skills",
        { code: code },
        {
          error: function (err) {},
          success: function () {
            callback("ok");
          },
        },
      );
  }
  function getskillCodeInfo(row_id, selected_id) {
    let selected_row = $$("admin-page")
      .$$("skill_codes")
      .$$("grid-skillCodes")
      .getSelectedItem(selected_id);
    let rowId = selected_row[0].id;
    let code = selected_row[0].code;
    let description = selected_row[0].description;
    $$("skillCode_id").setValue(rowId);
    $$("skillCode_code").setValue(code);
    $$("skillCode_description").setValue(description);
    $$("skillCode-label").define(
      "label",
      "<span class='header_font'>Edit Skill Code</span>",
    );
    $$("skillCode-label").refresh();
    $$("skillCodes-popup").show();
  }
  function loadSkillCodes() {
    $$("admin-page").$$("skill_codes").$$("grid-skillCodes").clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/skill_codes",
        {},
        {
          error: function (err) {},
          success: function (results) {
            skill_codes_subject.next(results);
          },
        },
      );
  }
  function saveSkillCode() {
    if ($$("formSkillCodes").validate()) {
      let form = $$("formSkillCodes").getValues();
      if (edit_mode === "ADD") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .post(
            server_url + "/admin/skill_codes",
            {
              code: form.skillCode_code,
              description: form.skillCode_description,
              group: form.skillCode_group,
            },
            {
              error: function (err) {},
              success: function () {
                $$("skillCodes-popup").hide();
                loadSkillCodes();
              },
            },
          );
      } else if (edit_mode === "EDIT") {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .put(
            server_url + "/admin/skill_codes",
            {
              id: form.skillCode_id,
              code: form.skillCode_code,
              description: form.skillCode_description,
              group: form.skillCode_group,
            },
            {
              error: function (err) {},
              success: function () {
                $$("skillCodes-popup").hide();
                loadSkillCodes();
              },
            },
          );
      }
    }
  }
  skill_codes_subject.subscribe(function (data) {
    $$("admin-page").$$("skill_codes").$$("grid-skillCodes").parse(data);
    let results = JSON.parse(data);
    for (let x = 0; x < results.length; x++) {
      skill_codes.push({ id: results[x].code, value: results[x].code });
    }
    refreshSkillCodes();
  });
  function refreshSkillCodes() {
    let grid = $$("admin-page").$$("skill_codes").$$("grid-skillCodes");
    grid.config.columns = [
      { id: "id", hidden: true },
      { id: "code", header: "Code", sort: "string", width: 120 },
      { id: "description", header: "Description", sort: "string", width: 320 },
      { id: "group", header: "Group", sort: "string", width: 160 },
      {
        id: "linked_codes",
        header: "Linked Codes",
        optionslist: true,
        options: skill_codes,
        editor: "multiselect",
        optionWidth: 100,
        fillspace: true,
      },
    ];
    grid.refreshColumns();
  }
  function updateLinkedSkills(skillCode, codes) {
    let skillCodes = [];
    if (codes !== "") {
      skillCodes = csvStringToArray(codes);
      for (let x = 0; x < skillCodes[0].length; x++) {
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .sync()
          .put(
            server_url + "/admin/linked_skills",
            { main_skill_code: skillCode, linked_skill_code: skillCodes[0][x] },
            { error: function (err) {}, success: function () {} },
          );
      }
    }
  }
  function deleteSkillCode() {
    let selected_row = $$("admin-page")
      .$$("skill_codes")
      .$$("grid-skillCodes")
      .getSelectedItem();
    let code = selected_row.code;
    if (code != "") {
      webix.confirm({
        title: "Delete Skill Code",
        ok: "Yes",
        cancel: "No",
        text: "Are you sure you want to delete the selected Skill Code?",
        callback: function (result) {
          switch (result) {
            case true:
              webix
                .ajax()
                .headers({ Authorization: "Bearer " + api_key })
                .del(
                  server_url + "/admin/skill_codes",
                  { code: code },
                  {
                    error: function (err) {},
                    success: function () {
                      loadSkillCodes();
                    },
                  },
                );
          }
        },
      });
    } else {
      webix.alert("No Skill Code has been selected!");
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
