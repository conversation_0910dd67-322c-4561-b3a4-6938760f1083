let routes = new (Backbone.Router.extend({
    routes: {
        "": "login",
        schedule: "schedule",
        admin: "admin",
        bookings: "bookings",
        reports: "reports",
        messaging: "messaging",
        applications: "applications",
        respond52: "respond52",
        booking_create: "booking_create",
        booking_requests: "booking_requests",
        booking_edit: "booking_edit",
        booking_leave_counts: "booking_leave_counts",
        booking_log: "booking_log",
        leave_swap: "leave_swap",
        leave_swap_list: "leave_swap_list",
        leave_groups: "leave_groups",
        leave_log: "leave_log",
        acting_up_log: "acting_up_log",
        standby_log: "standby_log",
        staff_movement_log: "staff_movement_log",
        overtime_log: "overtime_log",
        admin_search: "admin_search",
        roster_arrangement: "roster_arrangement",
        travel_create: "travel_create",
        travel_requests: "travel_requests",
        sick_certificates: "sick_certificates",
        sick_bookings_log: "sick_bookings_log",
        locations: "locations",
        location_groups: "location_groups",
        shifts: "shifts",
        shift_groups: "shift_groups",
        rosters: "rosters",
        leave_types: "leave_types",
        shift_types: "shift_types",
        skill_codes: "skill_codes",
        activity_types: "activity_types",
        reporting_bookings: "reporting_bookings",
        reporting_leave_taken_breakdown: "reporting_leave_taken_breakdown",
        reporting_deletion: "reporting_deletion",
        reporting_overtime: "reporting_overtime",
        reporting_overtime_summary: "reporting_overtime_summary",
        reporting_staff_overtime_summary: "reporting_staff_overtime_summary",
        reporting_reason_overtime_summary: "reporting_reason_overtime_summary",
        reporting_travel: "reporting_travel",
        reporting_sickness: "reporting_sickness",
        reporting_sickness_summary: "reporting_sickness_summary",
        reporting_sickness_certificate: "reporting_sickness_certificate",
        reporting_supplementary: "reporting_supplementary",
        reporting_staff_movement: "reporting_staff_movement",
        reporting_skill_codes: "reporting_skill_codes",
        reporting_skill_codes_availability: "reporting_skill_codes_availability",
        reporting_roster_arrangements: "reporting_roster_arrangements",
        reporting_ra_summary: "reporting_ra_summary",
        reporting_ras_by_rank: "reporting_ras_by_rank",
        reporting_shift_summary: "reporting_shift_summary",
        reporting_rrl: "reporting_rrl",
        reporting_booking_errors: "reporting_booking_errors",
        reporting_covid_vax_status: "reporting_covid_vax_status",
        reporting_covid_vax_summary: "reporting_covid_vax_summary",
        reporting_covid_status_summary: "reporting_covid_status_summary",
        covid_vaccines: "covid_vaccines",
        soil_toil_balances: "soil_toil_balances",
        tools_login_message: "tools_login_message",
        tools_public_holidays: "tools_public_holidays",
        tools_admin_settings: "tools_admin_settings",
        respond52_incident: "respond52_incident",
        respond52_incident_type_summary: "respond52_incident_type_summary",
        respond52_incident_location_summary: "respond52_incident_location_summary",
        respond52_riding_position_location: "respond52_riding_position_location",
        respond52_incident_info_search: "respond52_incident_info_search",
        reporting_station_preference: "reporting_station_preference",
        reporting_overtime_fatigue: "reporting_overtime_fatigue",
        reporting_overtime_distance_fatigue: "reporting_overtime_distance_fatigue",
        day_work_requests: "day_work_requests",
    },
    login: function () {
        $$("login-page").show();
    },
    schedule: function (id) {
        $$("schedule-page").show();
        $$("main_menu").setValue("1");
        $$("btn_ro_view").show();
        $$("overtime_filter").show();
    },
    bookings: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        if (
            user_permission_level == 1 ||
            user_permission_level == 2 ||
            user_permission_level == 3
        ) {
            $$("bookings-page").$$("booking_requests").show();
            $$("bookings-page").$$("bookings_sidebar").select("booking_requests");
        } else if (
            user_permission_level == 4 ||
            user_permission_level == 5 ||
            user_permission_level == 6
        ) {
            $$("bookings-page").$$("booking_log").show();
            $$("bookings-page").$$("bookings_sidebar").select("booking_log");
        }
        $$("bookings-page").$$("bookings_sidebar").open("bookings");
        functionsPopup.closePopup();
        $$("availability_report_window").hide();
        $$("btn_ro_view").hide();
        $$("overtime_filter").hide();
    },
    admin: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("admin_sidebar").select("admin_search");
        functionsPopup.closePopup();
        $$("availability_report_window").hide();
        $$("btn_ro_view").hide();
        $$("overtime_filter").hide();
    },
    reports: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        if (user_permission_level == 1) {
            $$("reports-page")
                .$$("reports_sidebar")
                .select("reporting_leave_taken_breakdown");
        } else if (user_permission_level == 2) {
            $$("reports-page")
                .$$("reports_sidebar")
                .select("reporting_leave_taken_breakdown");
        } else if (user_permission_level == 3 || user_permission_level == 4) {
            $$("reports-page").$$("reports_sidebar").select("reporting_overtime");
        } else if (user_permission_level == 5) {
            $$("reports-page")
                .$$("reports_sidebar")
                .select("reporting_sickness_summary");
        } else if (user_permission_level == 6) {
            $$("reports-page")
                .$$("reports_sidebar")
                .select("reporting_sickness_summary");
        }
        functionsPopup.closePopup();
        $$("availability_report_window").hide();
        $$("btn_ro_view").hide();
        $$("overtime_filter").hide();
    },
    messaging: function () {
        $$("messaging-page").show();
        $$("main_menu").setValue("5");
        functionsPopup.closePopup();
        $$("availability_report_window").hide();
        $$("btn_ro_view").hide();
        $$("overtime_filter").hide();
        if (
            user_permission_level === 1 ||
            user_permission_level === 2 ||
            user_permission_level === 3 ||
            user_permission_level === 4
        ) {
            $$("messaging-page").$$("show_fatigue").setValue(1);
        }
    },
    applications: function () {
        $$("applications-page").show();
        $$("main_menu").setValue("6");
        functionsPopup.closePopup();
        $$("availability_report_window").hide();
        $$("btn_ro_view").hide();
        $$("overtime_filter").hide();
        let dateString = moment(new Date()).format("YYYYMMDD");
        getRADetails(user_logged_in, dateString, true, function (values) {
            if (values.length > 0) {
                curr_user_roster = values[0].roster;
                curr_user_shift = values[0].shift;
                curr_user_location = values[0].location;
            } else {
                curr_user_roster = "";
                curr_user_shift = "";
                curr_user_location = "";
            }
        });
        applicationsStandbys.displayShiftSequence();
        applicationsStandbys.loadSBRequests();
        applicationsRRLs.loadRRLRequests();
        $$("applications-page")
            .$$("from_employee")
            .setValue(user_logged_in_name + " (" + user_logged_in + ")");
        getRank(user_logged_in, "RRL_change");
        getLeaveGroup(user_logged_in, "RRL_change");
        applicationsRRLs.getRRLBookings(user_logged_in, "from", function (grid) {
            applicationsRRLs.setRRLGrouping(grid);
        });
    },
    respond52: function () {
        $$("respond52-page").show();
        $$("main_menu").setValue("7");
        $$("respond52-page").$$("respond52_sidebar").select("respond52_incident");
        functionsPopup.closePopup();
        $$("availability_report_window").hide();
        $$("btn_ro_view").hide();
        $$("overtime_filter").hide();
    },
    respond52_incident: function () {
        $$("respond52-page").show();
        $$("main_menu").setValue("7");
        $$("respond52-page").$$("respond52_incident").show();
        $$("respond52-page").$$("respond52_sidebar").select("respond52_incident");
    },
    respond52_incident_type_summary: function () {
        $$("respond52-page").show();
        $$("main_menu").setValue("7");
        $$("respond52-page").$$("respond52_incident_type_summary").show();
        $$("respond52-page")
            .$$("respond52_sidebar")
            .select("respond52_incident_type_summary");
    },
    respond52_incident_location_summary: function () {
        $$("respond52-page").show();
        $$("main_menu").setValue("7");
        $$("respond52-page").$$("respond52_incident_location_summary").show();
        $$("respond52-page")
            .$$("respond52_sidebar")
            .select("respond52_incident_location_summary");
    },
    respond52_riding_position_location: function () {
        $$("respond52-page").show();
        $$("main_menu").setValue("7");
        $$("respond52-page").$$("respond52_riding_position_location").show();
        $$("respond52-page")
            .$$("respond52_sidebar")
            .select("respond52_riding_position_location");
    },
    respond52_incident_info_search: function () {
        $$("respond52-page").show();
        $$("main_menu").setValue("7");
        $$("respond52-page").$$("respond52_incident_info_search").show();
        $$("respond52-page")
            .$$("respond52_sidebar")
            .select("respond52_incident_info_search");
    },
    booking_create: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("booking_create").show();
        $$("bookings-page").$$("bookings_sidebar").select("booking_create");
    },
    booking_requests: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("booking_requests").show();
        $$("bookings-page").$$("bookings_sidebar").select("booking_requests");
        $$("bookings-page")
            .$$("booking_requests")
            .$$("btn_search")
            .callEvent("onItemClick");
    },
    booking_edit: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("booking_edit").show();
        $$("bookings-page").$$("bookings_sidebar").select("booking_edit");
    },
    booking_leave_counts: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("booking_leave_counts").show();
        $$("bookings-page").$$("bookings_sidebar").select("booking_leave_counts");
    },
    booking_log: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("booking_log").show();
        $$("bookings-page").$$("bookings_sidebar").select("booking_log");
    },
    leave_swap: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("leave_swap").show();
        $$("bookings-page").$$("bookings_sidebar").select("leave_swap");
    },
    leave_swap_list: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("leave_swap_list").show();
        $$("bookings-page").$$("bookings_sidebar").select("leave_swap_list");
    },
    leave_groups: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("leave_groups").show();
        $$("bookings-page").$$("bookings_sidebar").select("leave_groups");
    },
    leave_log: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("leave_log").show();
        $$("bookings-page").$$("bookings_sidebar").select("leave_log");
    },
    acting_up_log: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("acting_up_log").show();
        $$("bookings-page").$$("bookings_sidebar").select("acting_up_log");
    },
    standby_log: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("standby_log").show();
        $$("bookings-page").$$("bookings_sidebar").select("standby_log");
    },
    staff_movement_log: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("staff_movement_log").show();
        $$("bookings-page").$$("bookings_sidebar").select("staff_movement_log");
    },
    overtime_log: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("overtime_log").show();
        $$("bookings-page").$$("bookings_sidebar").select("overtime_log");
    },
    day_work_requests: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("day_work_requests").show();
        $$("bookings-page").$$("bookings_sidebar").select("day_work_requests");
        $$("bookings-page")
            .$$("day_work_requests")
            .$$("btn_search")
            .callEvent("onItemClick");
    },
    admin_search: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("admin_search").show();
        $$("admin-page").$$("admin_sidebar").select("admin_search");
    },
    locations: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("locations").show();
        $$("admin-page").$$("admin_sidebar").select("locations");
    },
    location_groups: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("location_groups").show();
        $$("admin-page").$$("admin_sidebar").select("location_groups");
    },
    shifts: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("shifts").show();
        $$("admin-page").$$("admin_sidebar").select("shifts");
    },
    shift_groups: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("shift_groups").show();
        $$("admin-page").$$("admin_sidebar").select("shift_groups");
    },
    rosters: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("rosters").show();
        $$("admin-page").$$("admin_sidebar").select("rosters");
    },
    roster_arrangement: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("roster_arrangement").show();
        $$("admin-page").$$("admin_sidebar").select("roster_arrangement");
        $$("admin-page").$$("roster_arrangement").$$("arrangement_id").setValue("");
        $$("admin-page").$$("roster_arrangement").$$("employees").setValue("");
        $$("admin-page").$$("roster_arrangement").$$("rank").setValue("");
        $$("admin-page").$$("roster_arrangement").$$("rosters").setValue("");
        $$("admin-page").$$("roster_arrangement").$$("shifts").setValue("");
        $$("admin-page").$$("roster_arrangement").$$("locations").setValue("");
        $$("admin-page")
            .$$("roster_arrangement")
            .$$("roster_arrangements")
            .clearAll();
        $$("admin-page").$$("roster_arrangement").$$("excel_import").clearAll();
        $$("admin-page").$$("roster_arrangement").$$("import_sheet").disable();
        $$("admin-page")
            .$$("roster_arrangement")
            .$$("file_name")
            .define("label", "Import Arrangement Reports from Gartan");
        $$("admin-page").$$("roster_arrangement").$$("file_name").refresh();
        let timeline = $$("admin-page")
            .$$("roster_arrangement")
            .$$("ra_scheduler")
            .getGantt();
        timeline.clearAll();
    },
    travel_create: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("travel_create").show();
        $$("bookings-page").$$("bookings_sidebar").select("travel_create");
    },
    travel_requests: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("travel_requests").show();
        $$("bookings-page").$$("bookings_sidebar").select("travel_requests");
    },
    sick_certificates: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("sick_certificates").show();
        $$("bookings-page").$$("bookings_sidebar").select("sick_certificates");
    },
    sick_bookings_log: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("sick_bookings_log").show();
        $$("bookings-page").$$("bookings_sidebar").select("sick_bookings_log");
    },
    soil_toil_balances: function () {
        $$("bookings-page").show();
        $$("main_menu").setValue("2");
        $$("bookings-page").$$("soil_toil_balances").show();
        $$("bookings-page").$$("bookings_sidebar").select("soil_toil_balances");
        soilToilBalances.loadSoilToilRecords(function (response) {
        });
    },
    leave_types: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("leave_types").show();
        $$("admin-page").$$("admin_sidebar").select("leave_types");
    },
    shift_types: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("shift_types").show();
        $$("admin-page").$$("admin_sidebar").select("shift_types");
    },
    skill_codes: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("skill_codes").show();
        $$("admin-page").$$("admin_sidebar").select("skill_codes");
    },
    activity_types: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("activity_types").show();
        $$("admin-page").$$("admin_sidebar").select("activity_types");
    },
    covid_vaccines: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("covid_vaccines").show();
        $$("admin-page").$$("admin_sidebar").select("covid_vaccines");
        covidVaccines.getCovidVaccines();
    },
    reporting_bookings: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_bookings").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_bookings");
        $$("reports-page").$$("reporting_bookings").$$("bookings_grid").clearAll();
    },
    reporting_leave_taken_breakdown: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_leave_taken_breakdown").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_leave_taken_breakdown");
        $$("reports-page")
            .$$("reporting_leave_taken_breakdown")
            .$$("leave_taken_grid")
            .clearAll();
    },
    reporting_deletion: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_deletion").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_deletion");
        $$("reports-page").$$("reporting_deletion").$$("deletion_grid").clearAll();
    },
    reporting_overtime: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_overtime").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_overtime");
        $$("reports-page").$$("reporting_overtime").$$("overtime_grid").clearAll();
    },
    reporting_overtime_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_overtime_summary").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_overtime_summary");
        $$("reports-page")
            .$$("reporting_overtime_summary")
            .$$("overtime_summary_grid")
            .clearAll();
    },
    reporting_staff_overtime_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_staff_overtime_summary").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_staff_overtime_summary");
        $$("reports-page")
            .$$("reporting_staff_overtime_summary")
            .$$("staff_overtime_summary_grid")
            .clearAll();
    },
    reporting_reason_overtime_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_reason_overtime_summary").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_reason_overtime_summary");
        $$("reports-page")
            .$$("reporting_reason_overtime_summary")
            .$$("reason_overtime_summary_grid")
            .clearAll();
    },
    reporting_travel: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_travel").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_travel");
        $$("reports-page").$$("reporting_travel").$$("travel_grid").clearAll();
    },
    reporting_sickness: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_sickness").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_sickness");
        $$("reports-page").$$("reporting_sickness").$$("sickness_grid").clearAll();
    },
    reporting_sickness_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_sickness_summary").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_sickness_summary");
        $$("reports-page")
            .$$("reporting_sickness_summary")
            .$$("sickness_summary_grid")
            .clearAll();
    },
    reporting_sickness_certificate: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_sickness_certificate").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_sickness_certificate");
        $$("reports-page")
            .$$("reporting_sickness_certificate")
            .$$("sickness_certificate_grid")
            .clearAll();
    },
    reporting_supplementary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_supplementary").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_supplementary");
    },
    reporting_staff_movement: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_staff_movement").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_staff_movement");
        $$("reports-page")
            .$$("reporting_staff_movement")
            .$$("staff_movement_grid")
            .clearAll();
    },
    reporting_skill_codes: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_skill_codes").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_skill_codes");
        $$("reports-page")
            .$$("reporting_skill_codes")
            .$$("skill_codes_grid")
            .clearAll();
    },
    reporting_skill_codes_availability: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_skill_codes_availability").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_skill_codes_availability");
        $$("reports-page")
            .$$("reporting_skill_codes_availability")
            .$$("skill_codes_availability_grid")
            .clearAll();
    },
    reporting_roster_arrangements: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_roster_arrangements").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_roster_arrangements");
        $$("reports-page")
            .$$("reporting_roster_arrangements")
            .$$("roster_arrangement_grid")
            .clearAll();
    },
    reporting_ra_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_ra_summary").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_ra_summary");
        $$("reports-page")
            .$$("reporting_ra_summary")
            .$$("roster_arrangement_summary_grid")
            .clearAll();
    },
    reporting_ras_by_rank: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_ras_by_rank").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_ras_by_rank");
        $$("reports-page")
            .$$("reporting_ras_by_rank")
            .$$("roster_arrangement_by_rank_grid")
            .clearAll();
    },
    reporting_shift_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_shift_summary").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_shift_summary");
        $$("reports-page")
            .$$("reporting_shift_summary")
            .$$("shift_summary_grid")
            .clearAll();
    },
    reporting_rrl: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_rrl").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_rrl");
        $$("reports-page").$$("reporting_rrl").$$("rrl_grid").clearAll();
    },
    reporting_booking_errors: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_booking_errors").show();
        $$("reports-page").$$("reports_sidebar").select("reporting_booking_errors");
        $$("reports-page")
            .$$("reporting_booking_errors")
            .$$("booking_errors_grid")
            .clearAll();
    },
    reporting_covid_vax_status: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_covid_vax_status").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_covid_vax_status");
        $$("reports-page")
            .$$("reporting_covid_vax_status")
            .$$("covid_vax_status_grid")
            .clearAll();
    },
    reporting_covid_vax_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_covid_vax_summary").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_covid_vax_summary");
    },
    reporting_covid_status_summary: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_covid_status_summary").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_covid_status_summary");
    },
    reporting_station_preference: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_station_preference").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_station_preference");
    },
    reporting_overtime_fatigue: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_overtime_fatigue").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_overtime_fatigue");
    },
    reporting_overtime_distance_fatigue: function () {
        $$("reports-page").show();
        $$("main_menu").setValue("4");
        $$("reports-page").$$("reporting_overtime_distance_fatigue").show();
        $$("reports-page")
            .$$("reports_sidebar")
            .select("reporting_overtime_distance_fatigue");
    },
    tools_login_message: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("tools_login_message").show();
        $$("admin-page").$$("admin_sidebar").open("tools");
        $$("admin-page").$$("admin_sidebar").select("tools_login_message");
    },
    tools_public_holidays: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("tools_public_holidays").show();
        $$("admin-page").$$("admin_sidebar").open("tools");
        $$("admin-page").$$("admin_sidebar").select("tools_public_holidays");
    },
    tools_admin_settings: function () {
        $$("admin-page").show();
        $$("main_menu").setValue("3");
        $$("admin-page").$$("tools_admin_settings").show();
        $$("admin-page").$$("admin_sidebar").open("tools");
        $$("admin-page").$$("admin_sidebar").select("tools_admin_settings");
        loadingSettings = true;
        $$("admin-page")
            .$$("tools_admin_settings")
            .$$("setting_hs_pref_bop")
            .setValue(global_settings.hs_pref_bop);
        loadingSettings = false;
    },
}))();
let application = (function () {
    function initApplication() {
        eventHandlers();
        $$("todayDate").setValue(moment().format("ddd, DD/MM/YYYY"));
    }

    function eventHandlers() {
        $$("main_menu").attachEvent("onItemClick", function (id, e) {
            let value = $$("main_menu").getValue();
            if (value) {
                if (value === "1") {
                    routes.navigate("schedule", {trigger: true});
                } else if (value === "2") {
                    routes.navigate("bookings", {trigger: true});
                } else if (value === "3") {
                    routes.navigate("admin", {trigger: true});
                } else if (value === "4") {
                    routes.navigate("reports", {trigger: true});
                } else if (value === "5") {
                    routes.navigate("messaging", {trigger: true});
                } else if (value === "6") {
                    routes.navigate("applications", {trigger: true});
                } else if (value === "7") {
                    routes.navigate("respond52", {trigger: true});
                }
            }
        });

        $$("app_sub_menu_list").attachEvent("onItemClick", function (id, e) {
            if (id === "logout") {
                routes.navigate("login", {trigger: true});
                $$("views").hide();
                if (live_site === true) {
                    window.open(
                        "https://login.microsoftonline.com/4abb7af4-36d5-4cd4-b6ab-10d21f35a591/oauth2/v2.0/logout?post_logout_redirect_uri=https://sapphire.mfs.sa.gov.au",
                        "_self",
                    );
                } else {
                    window.open(
                        "https://login.microsoftonline.com/4abb7af4-36d5-4cd4-b6ab-10d21f35a591/oauth2/v2.0/logout?post_logout_redirect_uri=https://sapphire-uat.eso.sa.gov.au",
                        "_self",
                    );
                }
            } else if (id === "profile") {
                adminSearch.setEditMode("EDIT");
                adminSearch.getEmployeeData(user_logged_in);
                $$("employee-label").define(
                    "label",
                    "<span class='header_font'>Edit Employee</span>",
                );
                $$("employee_pay_id").disable();
                $$("employee-label").refresh();
                $$("btn_employee_save").setValue("Update");

                getUserPL(function (result) {
                    if (result === 1 || result === 2) {
                        $$("formEmployee").enable();
                        $$("btn_emerald_sync").enable();
                    } else {
                        for (let name in $$("formEmployee").elements) {
                            $$("formEmployee").elements[name].disable();
                        }
                        $$("btn_emerald_sync").disable();
                    }
                });

                getUserPL(function (result) {
                    if (result === 1) {
                        $$("employee_permission_level").enable();
                    } else {
                        $$("employee_permission_level").disable();
                    }
                })
                $$("employee_personal_email").enable();
                $$("employee_personal_mobile").enable();
                $$("employee_home_street").enable();
                $$("employee_home_suburb").enable();
                $$("employee_home_state").enable();
                $$("employee_home_postcode").enable();
                if (global_settings.hs_pref_bop === true) {
                    $$("employee_pref_notice").show();
                    $$("pref_1_save").disable();
                    $$("pref_2_save").disable();
                    $$("pref_3_save").disable();
                } else {
                    $$("employee_pref_notice").hide();
                    $$("pref_1_save").enable();
                    $$("pref_2_save").enable();
                    $$("pref_3_save").enable();
                }
                $$("employee-popup").show();
            } else if (id === "vax_status") {
                $$("covid_vaccines-popup").show();
                covidVaccines.getVaxRecord(null, user_logged_in);
            } else if (id === "balances") {
                $$("leave_balances-popup").show();
                adminSearch.getUserLeaveBalances(user_logged_in);
            }
        });
    }

    return {
        initialise: function () {
            initApplication();
        },
    };
})();
