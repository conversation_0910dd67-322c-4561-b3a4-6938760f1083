let server_url = "";
let live_site = false;
let serverOnline = false;
let api_key = "";
const { Subject } = rxjs;
let loginFinishLoading = false;
let gridColour =
  "<span style='background-color:#colour#; text-align: center; border-radius:1px; padding-right:44px;'></span>";
let scheduleIcons = [
  { id: "sun", value: "<span class='webix_icon fas fa-sun'></span>" },
  { id: "moon", value: "<span class='webix_icon fas fa-moon'></span>" },
  { id: "ban", value: "<span class='webix_icon fas fa-ban'></span>" },
];
let global_settings = {};
let selected_employee_info = {};
let currentGridId = 0;
let user_logged_in = 0;
let user_permission_level = 6;
let user_logged_in_name = "";
let user_logged_in_email = "";
let user_logged_in_rank = "";
let curr_user_roster = "";
let curr_user_shift = "";
let curr_user_location = "";
let selectedShift = "";
let selectedLocation = "";
let selectedDayType = "";
let selectedDayShiftType = "";
let selectedShiftType = "";
let selected_shift_start_time = "";
let selected_shift_end_time = "";
let daySeqNumber = 0;
let overtime_activity_types = [];
let overtime_employees = [];
let rank_options = [];
let popupShowing = false;
let australian_states = [];
let globalSelectedDate = "";
let all_rank_types = [];
let all_rank_classes = [];
let ro_view_showing = false;
let scroll_position = {};
let windowWidth = 0;
let windowHeight = 0;
let overtimeMode = false;
let toolbarWidth = 0;
let public_holiday_dates_array = [];
let loadingApp = true;
let loadingSettings = false;
let loading_roster = false;
let locations_subject = new Subject();
let location_groups_subject = new Subject();
let shift_groups_subject = new Subject();
let shifts_subject = new Subject();
let rosters_subject = new Subject();
let leave_types_subject = new Subject();
let overtime_types_subject = new Subject();
let shift_types_subject = new Subject();
let employees_subject = new Subject();
let skill_codes_subject = new Subject();
let login_message = "";

const lut = Array(256)
  .fill()
  .map((_, i) => (i < 16 ? "0" : "") + i.toString(16));
const formatUuid = ({ d0, d1, d2, d3 }) =>
  lut[d0 & 255] +
  lut[(d0 >> 8) & 255] +
  lut[(d0 >> 16) & 255] +
  lut[(d0 >> 24) & 255] +
  "-" +
  lut[d1 & 255] +
  lut[(d1 >> 8) & 255] +
  "-" +
  lut[((d1 >> 16) & 15) | 64] +
  lut[(d1 >> 24) & 255] +
  "-" +
  lut[(d2 & 63) | 128] +
  lut[(d2 >> 8) & 255] +
  "-" +
  lut[(d2 >> 16) & 255] +
  lut[(d2 >> 24) & 255] +
  lut[d3 & 255] +
  lut[(d3 >> 8) & 255] +
  lut[(d3 >> 16) & 255] +
  lut[(d3 >> 24) & 255];
const getRandomValuesFunc =
  window.crypto && window.crypto.getRandomValues
    ? () => {
        const dvals = new Uint32Array(4);
        window.crypto.getRandomValues(dvals);
        return { d0: dvals[0], d1: dvals[1], d2: dvals[2], d3: dvals[3] };
      }
    : () => ({
        d0: (Math.random() * 4294967296) >>> 0,
        d1: (Math.random() * 4294967296) >>> 0,
        d2: (Math.random() * 4294967296) >>> 0,
        d3: (Math.random() * 4294967296) >>> 0,
      });

function loadGlobalSettings() {
  webix.ajax().get(
    server_url + "/admin/get_admin_settings",
    {},
    {
      error: function (err) {},
      success: function (result) {
        let settings = JSON.parse(result);
        global_settings.hs_pref_bop = settings[0].hs_pref_bop;
        global_settings.login_message = settings[0].login_message;
        global_settings.curr_app_version = settings[0].curr_app_version;
      },
    },
  );
}
function isNumber(value) {
  return (
    !isNaN(value) &&
    (function (x) {
      return (x | 0) === x;
    })(parseFloat(value))
  );
}
function getUserESO(token, callback) {
  let graphEndpoint = "https://graph.microsoft.com/v1.0/me";
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + token })
    .sync()
    .get(
      graphEndpoint,
      {},
      {
        error: function (err) {
          let errorInfo = JSON.parse(err);
          if (errorInfo.error.code === "InvalidAuthenticationToken") {
            webix.alert({
              text: "The Microsoft SSO access token is invalid or has expired!</br></br>Click on the 'Login' button to re-authenticate your credentials",
              width: 500,
            });
            window.location.reload(true);
          }
        },
        success: function (results) {
          let values = JSON.parse(results);
          callback(values);
        },
      },
    );
}

function renderMSTimePicker(id) {
    webix.protoUI(
        {
            name: id,
            render: function () {
                this.$view.innerHTML =
                    '<div class="webix_el_box"style="width:250px; height:24px"><label class="webix_inp_label" style="text-align:left; width:90px;"><input id=' +
                    id +
                    ' class="webix_inp_static" role="combobox" aria-label="" tabindex="0" onclick="" style="color: dimgray; width: 60px; text-align: left;" aria-autocomplete="list" aria-expanded="false" aria-live="assertive" aria-atomic="true"><span class="webix_input_icon wxi-clock" style="height:17px; background-color: white;"></span></div>';
                $("#" + id)
                    .mobiscroll()
                    .time({
                        theme: "ios",
                        display: "bubble",
                        controls: ["time"],
                        mode: "mixed",
                        timeFormat: "HH:ii",
                        timeWheels: "HHii",
                        steps: { minute: 15 },
                        returnFormat: "locale",
                    });
            },
        },
        webix.ui.template,
    );
}


function checkPayID(pay_id, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/get_employee",
      { pay_id: pay_id },
      {
        error: function (err) {},
        success: function (results) {
          let values = JSON.parse(results);
          callback(values);
        },
      },
    );
}


function getRAShiftInfo(roster, shift, startDate, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/shifts/info",
      { roster: roster, shift: shift },
      {
        error: function (err) {
          callback("error");
        },
        success: function (results) {
          if (results) {
            let values = JSON.parse(results);
            if (values.data.length > 0) {
              if (
                moment(startDate).isSameOrAfter(
                  values.data[0].seq_start_date,
                ) === true
              ) {
                getDaySeqNo(
                  values.data[0].shift_id,
                  startDate,
                  function (data) {
                    let result = JSON.parse(data);
                    callback({
                      array: values,
                      dayNo: result.data[0].seq_number,
                    });
                  },
                );
              } else {
                callback("date error");
              }
            } else {
              callback("no shift");
            }
          }
        },
      },
    );
}
function getDaySeqNo(shift_id, startDate, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/shifts/day_no",
      { shift_id: shift_id, start_date: moment(startDate).format("YYYYMMDD") },
      {
        error: function (err) {},
        success: function (results) {
          callback(results);
        },
      },
    );
}
const csvStringToArray = (strData) => {
  const objPattern = new RegExp(
    '(\\,|\\r?\\n|\\r|^)(?:"([^"]*(?:""[^"]*)*)"|([^\\,\\r\\n]*))',
    "gi",
  );
  let arrMatches = null,
    arrData = [[]];
  while ((arrMatches = objPattern.exec(strData))) {
    if (arrMatches[1].length && arrMatches[1] !== ",") arrData.push([]);
    arrData[arrData.length - 1].push(
      arrMatches[2]
        ? arrMatches[2].replace(new RegExp('""', "g"), '"')
        : arrMatches[3],
    );
  }
  return arrData;
};
function getLeaveGroup(pay_id, type) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .get(
      server_url + "/bookings/get_leave_group",
      { pay_id: pay_id },
      {
        error: function (err) {
          $$("bookings-page")
            .$$("leave_swap")
            .$$("rrl_group_from")
            .define("template", "Current Leave Group: N/A");
          $$("bookings-page").$$("leave_swap").$$("rrl_group_from").refresh();
          $$("bookings-page")
            .$$("leave_swap")
            .$$("rrl_group_swap")
            .define("template", "Current Leave Group: N/A");
          $$("bookings-page").$$("leave_swap").$$("rrl_group_swap").refresh();
          $$("bookings-page")
            .$$("leave_groups")
            .$$("curr_leave_group")
            .define("template", "Current Leave Group: N/A");
          $$("bookings-page")
            .$$("leave_groups")
            .$$("curr_leave_group")
            .refresh();
        },
        success: function (results) {
          let values = JSON.parse(results);
          let leaveGroup = "";
          let newLeaveGroup = "";
          let newLeaveGroupDate = "";
          if (values.length > 0) {
            if (values[0].current_leave_group == null) {
              leaveGroup = values[0].upcoming_leave_group;
            } else {
              leaveGroup = values[0].current_leave_group;
            }
            if (leaveGroup == null) {
              leaveGroup = "N/A";
            }
            if (values[0].upcoming_leave_group == null) {
              newLeaveGroup = "-";
              newLeaveGroupDate = "-";
            } else {
              newLeaveGroup = values[0].upcoming_leave_group;
              newLeaveGroupDate = moment(
                values[0].upcoming_leave_group_date,
              ).format("DD/MM/YYYY");
            }
            if (type == "from") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_from")
                .define("template", "Current Leave Group: " + leaveGroup);
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_from")
                .refresh();
              $$("bookings-page")
                .$$("leave_swap")
                .$$("txt_rrl_group_from")
                .setValue(leaveGroup);
            } else if (type == "swap") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_swap")
                .define("template", "Current Leave Group: " + leaveGroup);
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_swap")
                .refresh();
              $$("bookings-page")
                .$$("leave_swap")
                .$$("txt_rrl_group_swap")
                .setValue(leaveGroup);
            } else if (type == "assign") {
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_leave_group")
                .define("template", "Current Leave Group: " + leaveGroup);
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_leave_group")
                .refresh();
              $$("bookings-page")
                .$$("leave_groups")
                .$$("current_leave_group")
                .setValue(leaveGroup);
            } else if (type == "RRL_change") {
              $$("applications-page").$$("rrl_group_from").setValue(leaveGroup);
            } else if (type == "RRL_change_swap") {
              $$("applications-page").$$("rrl_group_swap").setValue(leaveGroup);
            } else if (type == "employee") {
              $$("employee_rrl_group").setValue(leaveGroup);
              $$("employee_new_rrl_group").setValue(newLeaveGroup);
              $$("employee_new_rrl_group_date").setValue(newLeaveGroupDate);
            }
          } else {
            if (type == "from") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_from")
                .define("template", "Current Leave Group: N/A");
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_from")
                .refresh();
            } else if (type == "swap") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_swap")
                .define("template", "Current Leave Group: N/A");
              $$("bookings-page")
                .$$("leave_swap")
                .$$("rrl_group_swap")
                .refresh();
            } else if (type == "assign") {
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_leave_group")
                .define("template", "Current Leave Group: N/A");
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_leave_group")
                .refresh();
            } else if (type == "RRL_change") {
              $$("applications-page").$$("rrl_group_from").setValue("N/A");
            } else if (type == "RRL_change_swap") {
              $$("applications-page").$$("rrl_group_swap").setValue("N/A");
            } else if (type == "employee") {
              $$("employee_rrl_group").setValue("");
              $$("employee_new_rrl_group").setValue("");
              $$("employee_new_rrl_group_date").setValue("");
            }
          }
        },
      },
    );
}
function getRank(pay_id, type) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .get(
      server_url + "/bookings/get_swap_rank",
      { pay_id: pay_id },
      {
        error: function (err) {
          $$("bookings-page")
            .$$("leave_swap")
            .$$("curr_rank_swap")
            .define("template", "");
          $$("bookings-page").$$("leave_swap").$$("curr_rank_swap").refresh();
        },
        success: function (results) {
          let values = JSON.parse(results);
          if (values.length > 0) {
            if (type == "from") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_from")
                .define("template", "Current Rank: " + values[0].rank);
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_from")
                .refresh();
              $$("bookings-page")
                .$$("leave_swap")
                .$$("txt_curr_rank_from")
                .setValue(values[0].rank);
            } else if (type == "swap") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .define("template", "Current Rank: " + values[0].rank);
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .refresh();
              $$("bookings-page")
                .$$("leave_swap")
                .$$("txt_curr_rank_swap")
                .setValue(values[0].rank);
            } else if (type == "assign") {
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_rank")
                .setValue(values[0].rank);
              $$("bookings-page")
                .$$("leave_groups")
                .$$("current_rank")
                .define("template", "Current Rank: " + values[0].rank);
              $$("bookings-page")
                .$$("leave_groups")
                .$$("current_rank")
                .refresh();
            } else if (type == "standby") {
              $$("standbys_rank").setValue(values[0].rank);
            } else if (type == "roster_arrangement") {
              $$("admin-page")
                .$$("roster_arrangement")
                .$$("rank")
                .setValue(values[0].rank);
            } else if (type == "RRL_change") {
              $$("applications-page")
                .$$("curr_rank_from")
                .setValue(values[0].rank);
            } else if (type == "RRL_change_swap") {
              $$("applications-page")
                .$$("curr_rank_swap")
                .setValue(values[0].rank);
            } else {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .define("template", "");
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .refresh();
            }
          } else {
            if (type == "from") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_from")
                .define("template", "Current Rank: N/A");
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_from")
                .refresh();
              $$("bookings-page")
                .$$("leave_swap")
                .$$("txt_curr_rank_from")
                .setValue("");
            } else if (type == "swap") {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .define("template", "Current Rank: N/A");
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .refresh();
              $$("bookings-page")
                .$$("leave_swap")
                .$$("txt_curr_rank_swap")
                .setValue("");
            } else if (type == "assign") {
              $$("bookings-page")
                .$$("leave_groups")
                .$$("curr_rank")
                .setValue("");
              $$("bookings-page")
                .$$("leave_groups")
                .$$("current_rank")
                .define("template", "Current Rank: N/A");
              $$("bookings-page")
                .$$("leave_groups")
                .$$("current_rank")
                .refresh();
            } else if (type == "standby") {
              $$("standbys_rank").setValue("");
            } else if (type == "roster_arrangement") {
              $$("admin-page").$$("roster_arrangement").$$("rank").setValue("");
            } else if (type == "RRL_change") {
              $$("applications-page").$$("curr_rank_from").setValue("");
            } else if (type == "RRL_change_swap") {
              $$("applications-page").$$("curr_rank_swap").setValue("");
            } else {
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .define("template", "");
              $$("bookings-page")
                .$$("leave_swap")
                .$$("curr_rank_swap")
                .refresh();
            }
          }
        },
      },
    );
}
function getRADetails(payId, date_string, permanent, callback) {
  let RSInfo = [];
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/bookings/arrangement_details",
      { pay_id: payId, date_string: date_string, permanent: permanent },
      {
        error: function (err) {
          callback([]);
        },
        success: function (results) {
          RSInfo = JSON.parse(results);
          callback(RSInfo);
        },
      },
    );
}
function getLocationId(stationName, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/get_location_id",
      { station: stationName },
      {
        error: function (err) {
          callback(0);
        },
        success: function (results) {
          let locationInfo = JSON.parse(results);
          callback(locationInfo[0].station_id);
        },
      },
    );
}
function getLocationName(stationNo, callback) {
  let station_id = stationNo.slice(0, -1);
  if (isNumber(station_id)) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/get_location",
        { station_id: station_id },
        {
          error: function (err) {
            callback("");
          },
          success: function (results) {
            let locationName = JSON.parse(results);
            callback({
              roster: locationName[0].roster,
              location: locationName[0].name,
            });
          },
        },
      );
  } else {
    callback("");
  }
}
function validateLatitude(value) {
  let regex = /^[+-]?\d{0,2}(\.\d{1,6})?$/;
  if (
    isNaN(value) ||
    value < -90 ||
    value > 90 ||
    value.length > 10 ||
    regex.test(value) == false
  ) {
    webix.alert({
      text: "Latitude co-ordinate value not valid!</br>Note: <strong>Latitude</strong> value must be between <strong>-90.000000</strong> and <strong>+90.000000</strong> and should have no more than 2 digits before the decimal point and 6 digits after the decimal point.",
      width: 460,
    });
    return "error";
  } else {
    return "ok";
  }
}
function validateLongitude(value) {
  let regex = /^[+-]?\d{0,3}(\.\d{1,6})?$/;
  if (
    isNaN(value) ||
    value < -180 ||
    value > 180 ||
    value.length > 11 ||
    regex.test(value) == false
  ) {
    webix.alert({
      text: "Longitude co-ordinate value not valid!</br>Note: <strong>Longitude</strong> value must be between <strong>-180.000000</strong> and <strong>+180.000000</strong> and should have no more than 3 digits before the decimal point and 6 digits after the decimal point.",
      width: 460,
    });
    return "error";
  } else {
    return "ok";
  }
}
function getUserBookings(payId, dateStamp, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/bookings/bookings",
      { pay_id: payId, date_string: dateStamp },
      {
        error: function (err) {
          callback([]);
        },
        success: function (results) {
          let value = JSON.parse(results);
          if (value) {
            callback(value);
          } else {
            callback([]);
          }
        },
      },
    );
}
function getBookingInfo(bookingId, dateStamp, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/bookings/booking_info",
      { booking_id: bookingId, date_string: dateStamp },
      {
        error: function (err) {
          callback([]);
        },
        success: function (results) {
          let values = JSON.parse(results);
          if (values) {
            callback(values);
          } else {
            callback([]);
          }
        },
      },
    );
}
function checkWeekends(startDate, endDate, callback) {
  let totalDays = moment(endDate).diff(moment(startDate), "days", true);
  let checkDate = "";
  let newStartDate = "";
  $$("loader-window").show();
  setTimeout(function () {
    if (moment(startDate).isoWeekday() == 6) {
      newStartDate = moment(startDate).add(2, "days");
      newStartDate = moment(newStartDate).startOf("day");
    } else if (moment(startDate).isoWeekday() == 7) {
      newStartDate = moment(startDate).add(1, "days");
      newStartDate = moment(newStartDate).startOf("day");
    } else {
      newStartDate = startDate;
    }
    for (
      let m = moment(newStartDate);
      m.diff(endDate, "days", true) <= 0;
      m.add(1, "days")
    ) {
      if (m.isoWeekday() == 6 || m.isoWeekday() == 7) {
        totalDays -= 1;
      } else {
        checkDate = m.format("YYYYMMDD");
        if (
          public_holiday_dates_array.some(
            (public_holiday_dates_array) =>
              public_holiday_dates_array.date_string == checkDate,
          )
        ) {
          totalDays -= 1;
        }
      }
    }
    $$("loader-window").hide();
    callback(totalDays);
  }, 250);
}
function getTimesForShift(shiftDate, currRoster, currShift, callback) {
  let dateString = moment(shiftDate, "MM-DD-YYYY").format("YYYYMMDD");
  let sel_pay_id = "";
  if (selected_employee_info.pay_id === "Pay ID") {
    sel_pay_id = user_logged_in;
  } else {
    sel_pay_id = selected_employee_info.pay_id;
  }
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/bookings/get_shift_times",
      {
        date_string: dateString,
        roster: currRoster,
        shift: currShift,
        sel_pay_id: sel_pay_id,
        log_pay_id: user_logged_in,
      },
      {
        error: function (err) {
          callback("");
        },
        success: function (results) {
          callback(results);
        },
      },
    );
}
function getShiftTimes(dateString, currRoster, currShift) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/shifts/times",
      { roster: currRoster, shift: currShift, date_string: dateString },
      {
        error: function (err) {},
        success: function (results) {
          if (results) {
            let values = JSON.parse(results);
            selected_shift_start_time = values[0].start_time;
            selected_shift_end_time = values[0].end_time;
            daySeqNumber = values[0].seq_number;
          }
        },
      },
    );
}
function setDatesForBookingForms(
  dateString,
  currRoster,
  currShift,
  icon,
  radioValue,
  menuItem,
) {
  let overtimeSelected = $$("overtime_activity").getValue();
  let datePickerStart = new Date(
    moment(dateString, "YYYYMMDD").format("YYYY/MM/DD"),
  );
  let leaveType = $$("bookings_leave_type").getValue();
  let datePickerEnd = "";
  let start_time = "";
  let end_time = "";
  let day_off = false;
  let day_type = "";
  let OTendTime = "";
  if (popupShowing === true || overtimeMode === true) {
    if (leaveType == "UGAD" || leaveType == "EXCH") {
      $("#bookings_start_time").mobiscroll("setVal", "08:00", true);
      $("#bookings_end_time").mobiscroll("setVal", "08:00", true);
    } else {
      if (menuItem == "leave") {
        if (
          leaveType == "LSL" ||
          leaveType == "XLSL" ||
          leaveType == "RRL" ||
          leaveType == "LSLH" ||
          leaveType == "ULSL"
        ) {
          if (currRoster == "Port Pirie") {
            radioValue = $$("pp_lsl_days").getValue();
          } else {
            radioValue = $$("lsl_days_radio").getValue();
          }
        } else {
          if (radioValue == "") {
            radioValue = 1;
          }
        }
      }
      $$("bookings_start_date").setValue(datePickerStart);
      $("#bookings_start_time").mobiscroll(
        "setVal",
        selected_shift_start_time,
        true,
      );
      $$("shift_adjustment_start_date").setValue(datePickerStart);
      $("#shift_adjustment_start_time").mobiscroll(
        "setVal",
        selected_shift_start_time,
        true,
      );
      $$("overtime_start_date").setValue(datePickerStart);
      $("#overtime_start_time").mobiscroll(
        "setVal",
        selected_shift_start_time,
        true,
      );
      $$("standbys_start_date").setValue(datePickerStart);
      $("#standbys_start_time").mobiscroll(
        "setVal",
        selected_shift_start_time,
        true,
      );
      $$("actups_start_date").setValue(datePickerStart);
      $("#actups_start_time").mobiscroll(
        "setVal",
        selected_shift_start_time,
        true,
      );
      $$("day_work_start_date").setValue(datePickerStart);
      $$("day_work_end_date").setValue(datePickerStart);
      if (radioValue == 1) {
        if (currRoster == "Port Pirie") {
          if ($$("bookings_day_hours").getValue() == 1) {
            datePickerEnd = new Date(moment(datePickerStart).add(0, "d"));
          } else {
            datePickerEnd = new Date(moment(datePickerStart).add(1, "d"));
          }
        } else {
          if (icon.includes("sun") == true) {
            datePickerEnd = new Date(moment(datePickerStart).add(0, "d"));
          } else {
            datePickerEnd = new Date(moment(datePickerStart).add(1, "d"));
          }
        }
        getTimesForShift(
          datePickerEnd,
          currRoster,
          currShift,
          function (times) {
            let shiftInfo = JSON.parse(times);
            start_time = shiftInfo[0].start_time;
            end_time = shiftInfo[0].end_time;
            day_off = shiftInfo[0].day_off;
            day_type = shiftInfo[0].icon;
          },
        );
        if (day_type == "sun") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL" &&
            overtimeSelected != "VSBW"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            $$("overtime_end_date").setValue(datePickerStart);
          }
        } else if (day_type == "moon") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        } else if (day_type == "ban") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", start_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll(
            "setVal",
            start_time,
            true,
          );
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", start_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", start_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        }
      } else if (radioValue == 2) {
        if (icon.includes("sun") == true) {
          datePickerEnd = new Date(moment(datePickerStart).add(1, "d"));
        } else {
          datePickerEnd = new Date(moment(datePickerStart).add(2, "d"));
        }
        getTimesForShift(
          datePickerEnd,
          currRoster,
          currShift,
          function (times) {
            let shiftInfo = JSON.parse(times);
            start_time = shiftInfo[0].start_time;
            end_time = shiftInfo[0].end_time;
            day_off = shiftInfo[0].day_off;
            day_type = shiftInfo[0].icon;
          },
        );
        if (day_type == "sun") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            $$("overtime_end_date").setValue(datePickerStart);
          }
        } else if (day_type == "moon") {
          datePickerEnd = new Date(moment(datePickerEnd).add(1, "d"));
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        } else if (day_type == "ban") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", start_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll(
            "setVal",
            start_time,
            true,
          );
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", start_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", start_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        }
      } else if (radioValue == 3) {
        if (icon.includes("sun") == true) {
          datePickerEnd = new Date(moment(datePickerStart).add(2, "d"));
        } else {
          datePickerEnd = new Date(moment(datePickerStart).add(3, "d"));
        }
        getTimesForShift(
          datePickerEnd,
          currRoster,
          currShift,
          function (times) {
            let shiftInfo = JSON.parse(times);
            start_time = shiftInfo[0].start_time;
            end_time = shiftInfo[0].end_time;
            day_off = shiftInfo[0].day_off;
            day_type = shiftInfo[0].icon;
          },
        );
        if (day_type == "sun") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            $$("overtime_end_date").setValue(datePickerStart);
          }
        } else if (day_type == "moon") {
          datePickerEnd = new Date(moment(datePickerEnd).add(1, "d"));
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        } else if (day_type == "ban") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", start_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll(
            "setVal",
            start_time,
            true,
          );
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", start_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", start_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        }
      } else if (radioValue == 4) {
        if (currRoster == "Port Pirie") {
          datePickerEnd = new Date(moment(datePickerStart).add(4, "d"));
        } else {
          if (icon.includes("sun") == true) {
            datePickerEnd = new Date(moment(datePickerStart).add(3, "d"));
          } else {
            datePickerEnd = new Date(moment(datePickerStart).add(4, "d"));
          }
        }
        getTimesForShift(
          datePickerEnd,
          currRoster,
          currShift,
          function (times) {
            let shiftInfo = JSON.parse(times);
            start_time = shiftInfo[0].start_time;
            end_time = shiftInfo[0].end_time;
            day_off = shiftInfo[0].day_off;
            day_type = shiftInfo[0].icon;
          },
        );
        if (day_type == "sun") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            $$("overtime_end_date").setValue(datePickerStart);
          }
        } else if (day_type == "moon") {
          datePickerEnd = new Date(moment(datePickerEnd).add(1, "d"));
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", end_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll("setVal", end_time, true);
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", end_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", end_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        } else if (day_type == "ban") {
          $$("bookings_end_date").setValue(datePickerEnd);
          $("#bookings_end_time").mobiscroll("setVal", start_time, true);
          $$("shift_adjustment_end_date").setValue(datePickerEnd);
          $("#shift_adjustment_end_time").mobiscroll(
            "setVal",
            start_time,
            true,
          );
          $$("actups_end_date").setValue(datePickerEnd);
          $("#actups_end_time").mobiscroll("setVal", start_time, true);
          $$("standbys_end_date").setValue(datePickerEnd);
          $("#standbys_end_time").mobiscroll("setVal", start_time, true);
          $$("overtime_end_date").setValue(datePickerEnd);
          if (
            overtimeSelected != "RC" &&
            overtimeSelected != "DRILL" &&
            overtimeSelected != "REL"
          ) {
            OTendTime = moment(selected_shift_start_time, "HH:mm").add(
              15,
              "minutes",
            );
            OTendTime = moment(OTendTime).format("HH:mm");
            $("#overtime_end_time").mobiscroll("setVal", OTendTime, true);
            $$("overtime_end_date").setValue(datePickerStart);
          } else {
            $("#overtime_end_time").mobiscroll("setVal", end_time, true);
            let endDate = new Date(moment(datePickerStart).add(1, "d"));
            $$("overtime_end_date").setValue(endDate);
          }
        }
      } else if (radioValue > 4) {
        datePickerEnd = new Date(moment(datePickerStart).add(radioValue, "d"));
        getTimesForShift(
          datePickerEnd,
          currRoster,
          currShift,
          function (times) {
            let shiftInfo = JSON.parse(times);
            start_time = shiftInfo[0].start_time;
            end_time = shiftInfo[0].end_time;
            day_off = shiftInfo[0].day_off;
            day_type = shiftInfo[0].icon;
          },
        );
        $$("bookings_end_date").setValue(datePickerEnd);
        $("#bookings_end_time").mobiscroll("setVal", start_time, true);
        $$("shift_adjustment_end_date").setValue(datePickerEnd);
        $("#shift_adjustment_end_time").mobiscroll("setVal", start_time, true);
      }
      if (currRoster == "Port Pirie") {
        datePickerEnd = new Date(moment(datePickerStart).add(radioValue, "d"));
        $$("standbys_end_date").setValue(datePickerEnd);
        $$("actups_end_date").setValue(datePickerEnd);
        $$("overtime_end_date").setValue(datePickerEnd);
        $$("shift_adjustment_end_date").setValue(datePickerEnd);
        if (radioValue >= 4) {
          $("#bookings_end_time").mobiscroll("setVal", "08:00", true);
        } else {
          $("#bookings_end_time").mobiscroll("setVal", "18:00", true);
        }
        if (menuItem == "overtime") {
          $$("overtime_start_date").enable();
          $$("overtime_end_date").enable();
        }
      } else {
        $$("overtime_start_date").disable();
        $$("overtime_end_date").disable();
      }
    }
  }
}
function reportTimeHighlight(value, config) {
  if (value == "08:00") {
    return {
      "background-color": "#ffdbd0 !important",
      color: "firebrick !important",
      "text-align": "center",
    };
  } else {
    return {
      "background-color": "#A9CCE3 !important",
      color: "blue !important",
      "text-align": "center",
    };
  }
}
function reportLocationHighlight(value, config) {
  if (
    value == "Adelaide Comms" ||
    value == "Port Pirie" ||
    value == "Mt Gambier"
  ) {
    return {
      "background-color": "lawngreen !important",
      color: "darkgreen !important",
      "text-align": "center",
    };
  } else {
    return { "text-align": "center" };
  }
}
function reportDifference(value, config) {
  if (value < 0) {
    return {
      "background-color": "red !important",
      color: "white !important",
      "text-align": "center",
    };
  } else if (value > 0) {
    return {
      "background-color": "lawngreen !important",
      color: "black !important",
      "text-align": "center",
    };
  } else {
    return { "text-align": "center" };
  }
}
function fatigueHours(value, config) {
  if (value > 76) {
    return {
      "background-color": "red !important",
      color: "white !important",
      "text-align": "center",
    };
  } else {
    return { "text-align": "center" };
  }
}
function travelMeals(value, config) {
  if (config.route == "Res-50") {
    return { "text-align": "center" };
  } else {
    return {
      "background-color": "darkgray !important",
      "text-align": "center",
    };
  }
}
function highlight_yes(value) {
  if (value == "No") {
    return { "text-align": "center" };
  } else {
    return { "background-color": "#ffb3b3 !important", "text-align": "center" };
  }
}
function construct_overtime_grid(type) {
  let grid = $$("overtime_employees");
  let reason_list = [];
  if (type === "OTFC") {
    reason_list = overtime_reasons_OTFC;
  } else {
    reason_list = overtime_reasons_RC;
  }
  grid.config.columns = [
    {
      id: "overtime_employee",
      header: { text: "Employee", height: 27, css: { "text-align": "left" } },
      width: 260,
      editor: "combo",
      options: overtime_employees,
    },
    {
      id: "overtime_acting_rank",
      header: { text: "Rank", height: 27, css: { "text-align": "left" } },
      editor: "combo",
      options: all_rank_types,
      width: 70,
    },
    {
      id: "overtime_reason",
      header: { text: "Reason", height: 27, css: { "text-align": "left" } },
      width: 190,
      editor: "combo",
      options: reason_list,
    },
    {
      id: "overtime_roster",
      header: { text: "Roster", height: 27, css: { "text-align": "center" } },
      width: 180,
    },
    {
      id: "overtime_shift",
      header: { text: "Shift", height: 27, css: { "text-align": "center" } },
      width: 180,
    },
    {
      id: "overtime_location",
      header: { text: "Location", height: 27, css: { "text-align": "center" } },
      width: 180,
    },
    {
      id: "btn_delete_row",
      header: "",
      template: function (obj) {
        return "<span class= 'overtime_dt_remove fas fa-trash'></span>";
      },
      width: 40,
    },
  ];
  grid.refreshColumns();
}
function sendEmail(
  from_address,
  to_address,
  subject,
  message,
  extra_message,
  recipient,
  signature,
  signature2,
) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .post(
      server_url + "/login/send_email",
      {
        from_address: from_address,
        to_address: to_address,
        subject: subject,
        message: message,
        extra_message: extra_message,
        recipient: recipient,
        signature: signature,
        signature2: signature2,
      },
      {
        error: function (err) {
          webix.message({
            text: "Email was not sent!",
            type: "error",
            expire: 2e3,
          });
        },
        success: function (results) {
          webix.message({
            text: "Email was sent successfully!",
            type: "success",
            expire: 2e3,
          });
        },
      },
    );
}
function sendEmailWithAttachment(
  from_address,
  to_address,
  subject,
  file_attachment,
  file_name,
  sender_name,
) {
  $$("loader-window").show();
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .post(
      server_url + "/login/send_attachment_email",
      {
        from_address: from_address,
        to_address: to_address,
        subject: subject,
        file_name: file_name,
        sender_name: sender_name,
        filename: file_name,
        attachment: file_attachment,
      },
      {
        error: function (err) {
          webix.message({
            text: "Email was not sent!",
            type: "error",
            expire: 2e3,
          });
          $$("loader-window").hide();
        },
        success: function (results) {
          $$("loader-window").hide();
          webix.message({
            text: "Email was sent successfully!",
            type: "success",
            expire: 2e3,
          });
          $$("overtime_export_info").clear();
          $$("overtime_report-popup").hide();
        },
      },
    );
}
australian_states = [
  { id: "NSW", value: "NSW" },
  { id: "VIC", value: "VIC" },
  { id: "QLD", value: "QLD" },
  { id: "SA", value: "SA" },
  { id: "ACT", value: "ACT" },
  { id: "WA", value: "WA" },
  { id: "NT", value: "NT" },
  { id: "TAS", value: "TAS" },
];
all_rank_types = [
  { id: "ACFO", value: "ACFO" },
  { id: "CO", value: "CO" },
  { id: "DCO", value: "DCO" },
  { id: "CMD", value: "CMD" },
  { id: "SO", value: "SO" },
  { id: "SFF", value: "SFF" },
  { id: "FF", value: "FF" },
  { id: "COFF", value: "COFF" },
  { id: "SCOP", value: "SCOP" },
  { id: "COP", value: "COP" },
  { id: "MOFF", value: "MOFF" },
  { id: "RSO", value: "RSO" },
  { id: "ARSO", value: "ARSO" },
  { id: "RFS2", value: "RFS2" },
  { id: "RFS", value: "RFS" },
  { id: "ARFS", value: "ARFS" },
  { id: "RFF", value: "RFF" },
  { id: "RRF", value: "RRF" },
  { id: "REC", value: "REC" },
  { id: "MAN", value: "MAN" },
  { id: "NON", value: "NON" },
  { id: "EFF", value: "EFF" },
  { id: "ESFF", value: "ESFF" },
  { id: "?", value: "?" },
];
all_rank_classes = [
  { id: "FFA001", value: "FFA001" },
  { id: "FFA002", value: "FFA002" },
  { id: "FFA003", value: "FFA003" },
  { id: "FFB001", value: "FFB001" },
  { id: "FFC001", value: "FFC001" },
  { id: "FFD001", value: "FFD001" },
  { id: "FFS001", value: "FFS001" },
  { id: "FFS002", value: "FFS002" },
  { id: "RFS001", value: "RFS001" },
  { id: "RFS002", value: "RFS002" },
  { id: "FSO001", value: "FSO001" },
  { id: "FSO002", value: "FSO002" },
  { id: "RSO001", value: "RSO001" },
  { id: "FSC001", value: "FSC001" },
  { id: "FSC002", value: "FSC002" },
  { id: "FCA001", value: "FCA001" },
  { id: "FCA002", value: "FCA002" },
  { id: "FCS001", value: "FCS001" },
  { id: "FCS002", value: "FCS002" },
  { id: "FDO001", value: "FDO001" },
  { id: "FDO002", value: "FDO002" },
  { id: "FXC001", value: "FXC001" },
  { id: "FXC002", value: "FXC002" },
  { id: "FFMOF1", value: "FFMOF1" },
  { id: "FFMOF2", value: "FFMOF2" },
  { id: "FCO001", value: "FCO001" },
  { id: "FCO201", value: "FCO201" },
  { id: "RRF001", value: "RRF001" },
  { id: "RFF001", value: "RFF001" },
  { id: "FTR000", value: "FTR000" },
  { id: "FFMOP1", value: "FFMOP1" },
  { id: "FFMOP2", value: "FFMOP2" },
];
let overtime_reasons_RC = [
  { id: "Rostered Rec Leave", value: "Rostered Rec Leave" },
  { id: "Sick Leave", value: "Sick Leave" },
  { id: "Booked Leave", value: "Booked Leave" },
  { id: "Covid", value: "Covid" },
  { id: "Industrial Action", value: "Industrial Action" },
  { id: "Other Leave", value: "Other Leave" },
  { id: "Course Instructor", value: "Course Instructor" },
  { id: "Course Participant", value: "Course Participant" },
  { id: "Deployment Interstate", value: "Deployment Interstate" },
  { id: "Deployment Intrastate", value: "Deployment Intrastate" },
  { id: "Out Duty", value: "Out Duty" },
  { id: "Special Leave", value: "Special Leave" },
  { id: "Unknown Reason", value: "Unknown Reason" },
];
let overtime_reasons_OTFC = [
  { id: "Alarm Call", value: "Alarm Call" },
  { id: "Assist ESO Agency (SAAS)", value: "Assist ESO Agency (SAAS)" },
  { id: "Structure Fire", value: "Structure Fire" },
  { id: "Other Fires", value: "Other Fires" },
  { id: "Hazmat", value: "Hazmat" },
  { id: "Change of Quarters", value: "Change of Quarters" },
  { id: "Rescue", value: "Rescue" },
  { id: "Weather Related", value: "Weather Related" },
  { id: "MVA", value: "MVA" },
  { id: "Other", value: "Other" },
];
function toProperCase(s) {
  return s
    .toLowerCase()
    .replace(/\b((m)(a?c))?(\w)(?!\s)/g, function ($1, $2, $3, $4, $5) {
      if ($2) {
        return $3.toUpperCase() + $4 + $5.toUpperCase();
      }
      return $1.toUpperCase();
    });
}
function getEmployeeData(payId, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/get_employee_info",
      { pay_id: payId },
      {
        error: function (err) {
          callback("error");
        },
        success: function (results) {
          let values = JSON.parse(results);
          callback(values);
        },
      },
    );
}
function getAllPublicHolidays(callback) {
  webix.ajax().get(
    server_url + "/schedule/get_all_public_holidays",
    {},
    {
      error: function (err) {
        callback([]);
      },
      success: function (results) {
        let result = JSON.parse(results);
        if (result.length > 0) {
          callback(result);
        } else {
          callback([]);
        }
      },
    },
  );
}
function isEmailAddressValid(email) {
  const checker = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return checker.test(email);
}
function checkRAdate(pay_id, date_string, roster, shift, location, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/bookings/check_ra_date",
      {
        pay_id: pay_id,
        date_string: date_string,
        roster: roster,
        shift: shift,
        location: location,
      },
      {
        error: function (err) {
          callback([]);
        },
        success: function (results) {
          let result = JSON.parse(results);
          if (result.length > 0) {
            callback(result);
          } else {
            callback([]);
          }
        },
      },
    );
}
function getSkillCodes(payId, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/get_skill_codes",
      { pay_id: payId },
      {
        error: function (err) {
          callback([]);
        },
        success: function (results) {
          let result = JSON.parse(results);
          if (result.length > 0) {
            callback(result);
          } else {
            callback([]);
          }
        },
      },
    );
}
function getEmployeeStatus(payId, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/get_employee_status",
      { pay_id: payId },
      {
        error: function (err) {
          callback([]);
        },
        success: function (results) {
          let result = JSON.parse(results);
          if (result.length > 0) {
            callback(result);
          } else {
            callback([]);
          }
        },
      },
    );
}

function getCovidComplianceStatus(payId, callback) {
  webix
    .ajax()
    .headers({ Authorization: "Bearer " + api_key })
    .sync()
    .get(
      server_url + "/admin/get_covid_compliance_status",
      { pay_id: payId },
      {
        error: function (err) {
          callback(false);
        },
        success: function (results) {
          if (results != "") {
            let data = JSON.parse(results);
            if (data.length > 0) {
              if (data[0].terminated === null) {
                if (
                  data[0].vax_form_received == false ||
                  data[0].vax_form_received == null
                ) {
                  callback(true);
                } else {
                  callback(true);
                }
              } else {
                callback(false);
              }
            } else {
              callback(true);
            }
          } else {
            callback(true);
          }
        },
      },
    );
}

function getUserPL(callback){
    webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .sync()
        .get(
            server_url + "/login/get_user_pl",
            {},
            {
                error: function (err) {
                    callback(6);
                },
                success: function (results) {
                    if (results) {
                        let data = JSON.parse(results);
                        if (data.length > 0) {
                            callback(data[0].permission_level);
                        }
                    } else {
                        callback(6);
                    }
                },
            },
        );
}

function getUserID(callback){
    webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .sync()
        .get(
            server_url + "/login/get_user_id",
            {},
            {
                error: function (err) {
                    callback(6);
                },
                success: function (results) {
                    if (results) {
                        let data = JSON.parse(results);
                        if (data.length > 0) {
                            callback(data[0].permission_level);
                        }
                    } else {
                        callback(6);
                    }
                },
            },
        );
}

function sortWithExclusionSO(a, b) {
  if (a.location == "GRAND TOTALS" || a.location == "AVERAGE VALUES") {
    return 0;
  } else {
    a = a.avg_so;
    b = b.avg_so;
    return a > b ? 1 : a < b ? -1 : 0;
  }
}
function sortWithExclusionSFF(a, b) {
  if (a.location == "GRAND TOTALS" || a.location == "AVERAGE VALUES") {
    return 0;
  } else {
    a = a.avg_sff_ff;
    b = b.avg_sff_ff;
    return a > b ? 1 : a < b ? -1 : 0;
  }
}
