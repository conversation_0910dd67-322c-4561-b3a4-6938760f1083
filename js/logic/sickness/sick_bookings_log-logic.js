let sickBookingsLog = (function () {
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate = "";
  let exp_toDate = "";
  let sick_logs_export = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("from")
      .setValue(startOfMonth);
    $$("bookings-page").$$("sick_bookings_log").$$("to").setValue(endOfMonth);
    $$("bookings-page").$$("sick_bookings_log").$$("status_filter").setValue(1);
    exp_grid = $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("sick_bookings_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Log print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "All Locations - " +
            "Selected Date Range: " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Sick Bookings Log (SICM/FAML) by Approved Date",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateSickBookingsLog();
      });
    $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("bookings-page")
          .$$("sick_bookings_log")
          .$$("from")
          .getValue();
        exp_toDate = $$("bookings-page")
          .$$("sick_bookings_log")
          .$$("to")
          .getValue();
        sick_logs_export = "";
        if (exp_grid.count() > 0) {
          webix
            .toExcel(exp_grid, {
              filename:
                "Sick Bookings Log - " +
                moment(exp_fromDate).format("DD-MM-YYYY") +
                " to " +
                moment(exp_toDate).format("DD-MM-YYYY") +
                " created on " +
                moment().format("DD-MM-YYYY"),
              styles: true,
              ignore: { exported: true },
              name: "All Locations",
            })
            .then(function () {
              exp_grid.eachRow(function (row) {
                let record = exp_grid.getItem(row);
                sick_logs_export = sick_logs_export + record.db_id + ",";
              });
              webix.confirm({
                title: "Mark bookings as 'Exported'",
                ok: "Yes",
                cancel: "No",
                width: 650,
                text:
                  "Do you want to mark these " +
                  exp_grid.count() +
                  " sick bookings as being 'Exported'?</br>",
                callback: function (result) {
                  switch (result) {
                    case true:
                      webix
                        .ajax()
                        .headers({ Authorization: "Bearer " + api_key })
                        .post(
                          server_url + "/admin/mark_sick_bookings_exported",
                          {
                            exported_by: user_logged_in,
                            sick_logs_export: sick_logs_export,
                          },
                          {
                            error: function (err) {},
                            success: function () {
                              generateSickBookingsLog();
                              webix.alert("Processing completed successfully!");
                            },
                          },
                        );
                  }
                },
              });
            });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  function generateSickBookingsLog() {
    let grid = $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("sick_bookings_grid");
    let fromDate = $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("from")
      .getValue();
    let toDate = $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("to")
      .getValue();
    let show_exported = $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("show_exported")
      .getValue();
    let status = $$("bookings-page")
      .$$("sick_bookings_log")
      .$$("status_filter")
      .getText();
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/get_sick_bookings_log",
        {
          from_date: fromDate,
          to_date: toDate,
          show_exported: show_exported,
          status: status,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let sickness_logs = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let approval_date = "";
              let approved_by = "";
              let is_exported = "";
              let status = "";
              let doc_provided = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (
                    data[x].approved_denied_date == null ||
                    data[x].approved_denied_date == "Invalid date"
                  ) {
                    approval_date = "";
                    approved_by = "";
                  } else {
                    approval_date = data[x].approved_denied_date;
                    approved_by = data[x].approved_denied_by;
                  }
                  if (data[x].exported == null) {
                    is_exported = "No";
                  } else {
                    is_exported = "Yes";
                  }
                  if (
                    data[x].sick_certificate === true ||
                    data[x].statutory_declaration === true
                  ) {
                    status = "Approved";
                    if (data[x].sick_certificate === true) {
                      doc_provided = "Sick Certificate";
                    } else if (data[x].statutory_declaration === true) {
                      doc_provided = "Statutory Declaration";
                    }
                  } else if (data[x].sick_certificate === false) {
                    status = "Denied";
                    doc_provided = "-";
                  } else {
                    status = "Pending";
                    doc_provided = "-";
                  }
                  sickness_logs.push({
                    db_id: data[x].id,
                    pay_id: data[x].pay_id,
                    employee: empName.toUpperCase(),
                    rank: data[x].rank,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    start_date: data[x].start_date,
                    hours: data[x].hours,
                    leave_type: data[x].leave_type_code,
                    status: status,
                    doc_provided: doc_provided,
                    approved_by: approved_by,
                    approved_denied_date: approval_date,
                    comments: data[x].request_comments,
                    exported: is_exported,
                  });
                }
              }
            }
            grid.define("data", sickness_logs);
            grid.refresh();
            $$("bookings-page")
              .$$("sick_bookings_log")
              .$$("records_count")
              .define(
                "template",
                sickness_logs.length + " sick bookings found!",
              );
            $$("bookings-page")
              .$$("sick_bookings_log")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
