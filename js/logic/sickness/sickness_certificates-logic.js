let sicknessCertificates = (function () {
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let lastEmployeeName = "";
  let dtableHeight = 0;
  function initApplication() {
    eventHandlers();
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("bookings-page").$$("sick_certificates").$$("status_filter").setValue(1);
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("employee_filter")
      .setValue(1);
    exp_grid = $$("bookings-page")
      .$$("sick_certificates")
      .$$("sickness_pending_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Roster: " + exp_roster]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Elapsed Sick Bookings with No Medical Certificates",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("select_all")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("bookings-page")
          .$$("sick_certificates")
          .$$("sickness_pending_grid");
        let pay_id = $$("bookings-page")
          .$$("sick_certificates")
          .$$("employee_filter")
          .getValue();
        let currEmployeeName = $$("bookings-page")
          .$$("sick_certificates")
          .$$("employee_filter")
          .getText();
        let sel_count = 0;
        if (pay_id > 1) {
          grid.eachRow(function (row) {
            let record = grid.getItem(row);
            record.select = 1;
            sel_count += 1;
          });
          grid.refresh();
          if (sel_count > 0) {
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .define(
                "template",
                "Selected Sickness Bookings: <strong>" +
                  sel_count +
                  "</strong> for " +
                  currEmployeeName,
              );
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .refresh();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_sicm")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_change_to_sic")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvil")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvfl")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvsl")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvwr")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("stat_dec_provided")
              .enable();
            $$("bookings-page").$$("sick_certificates").$$("btn_deny").enable();
          } else {
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .define("template", "Selected Sickness Bookings: None");
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .refresh();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_sicm")
              .disable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_change_to_sic")
              .disable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvil")
              .disable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvfl")
              .disable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvsl")
              .disable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvwr")
              .disable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("stat_dec_provided")
              .disable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_deny")
              .disable();
          }
        } else {
          webix.alert({
            text: "This function only works when the 'Employee' filter is used to select just 1 employee!",
            width: 550,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("bookings-page")
          .$$("sick_certificates")
          .$$("roster_filter")
          .getText();
        let status = $$("bookings-page")
          .$$("sick_certificates")
          .$$("status_filter")
          .getText();
        if (status == "Pending") {
          exp_grid.filter((obj) => filter_elapsed(obj));
          if (exp_grid.count() > 0) {
            webix
              .toExcel(exp_grid, {
                filename:
                  "Elapsed Sick Bookings Log - " +
                  moment().format("DD-MM-YYYY"),
                styles: true,
                ignore: {
                  emailed: true,
                  edit: true,
                  select: true,
                  status: true,
                  approved_date: true,
                  approved_by: true,
                },
                heights: true,
              })
              .then(function () {
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("selected_request")
                  .define("template", "Selected Sickness Bookings: None");
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("selected_request")
                  .refresh();
                loadSicknessBookings();
              });
          } else {
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .define("template", "Selected Sickness Bookings: None");
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .refresh();
            loadSicknessBookings();
            webix.alert("No data to Export!");
          }
        } else {
          webix.alert({
            text: "The function is only for 'Pending' records!",
            width: 450,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("selected_request")
          .define("template", "Selected Sickness Bookings: None");
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("selected_request")
          .refresh();
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("btn_approve_sicm")
          .disable();
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("btn_change_to_sic")
          .disable();
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("btn_approve_cvil")
          .disable();
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("btn_approve_cvfl")
          .disable();
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("btn_approve_cvsl")
          .disable();
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("btn_approve_cvwr")
          .disable();
        $$("bookings-page")
          .$$("sick_certificates")
          .$$("stat_dec_provided")
          .disable();
        $$("bookings-page").$$("sick_certificates").$$("btn_deny").disable();
        lastEmployeeName = "";
        loadSicknessBookings();
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("sickness_pending_grid")
      .attachEvent(
        "onAfterRender",
        webix.once(function () {
          dtableHeight = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid").$height;
        }),
      );
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("sickness_pending_grid")
      .attachEvent("onCheck", function (row, column, state) {
        let grid = $$("bookings-page")
          .$$("sick_certificates")
          .$$("sickness_pending_grid");
        let sel_count = 0;
        let currEmployeeName = grid.getItem(row).employee;
        grid.eachRow(function (row) {
          if (grid.getItem(row).select == 1) {
            sel_count += 1;
          }
        });
        if (sel_count > 0) {
          if (currEmployeeName == lastEmployeeName || lastEmployeeName == "") {
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .define(
                "template",
                "Selected Sickness Bookings: <strong>" +
                  sel_count +
                  "</strong> for " +
                  currEmployeeName,
              );
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .refresh();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_sicm")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_change_to_sic")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvil")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvfl")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvsl")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("btn_approve_cvwr")
              .enable();
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("stat_dec_provided")
              .enable();
            $$("bookings-page").$$("sick_certificates").$$("btn_deny").enable();
            lastEmployeeName = currEmployeeName;
          } else {
            grid.eachRow(function (rowId) {
              if (rowId != row) {
                let record = grid.getItem(rowId);
                record.select = 0;
                grid.updateItem(rowId, { checked: "off" });
              } else {
                sel_count = 1;
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("selected_request")
                  .define(
                    "template",
                    "Selected Sickness Bookings: <strong>" +
                      sel_count +
                      "</strong> for " +
                      currEmployeeName,
                  );
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("selected_request")
                  .refresh();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("btn_approve_sicm")
                  .enable();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("btn_change_to_sic")
                  .enable();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("btn_approve_cvil")
                  .enable();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("btn_approve_cvfl")
                  .enable();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("btn_approve_cvsl")
                  .enable();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("btn_approve_cvwr")
                  .enable();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("stat_dec_provided")
                  .enable();
                $$("bookings-page")
                  .$$("sick_certificates")
                  .$$("btn_deny")
                  .enable();
                lastEmployeeName = currEmployeeName;
              }
            });
          }
        } else {
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("selected_request")
            .define("template", "Selected Sickness Bookings: None");
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("selected_request")
            .refresh();
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("btn_approve_sicm")
            .disable();
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("btn_change_to_sic")
            .disable();
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("btn_approve_cvil")
            .disable();
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("btn_approve_cvfl")
            .disable();
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("btn_approve_cvsl")
            .disable();
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("btn_approve_cvwr")
            .disable();
          $$("bookings-page")
            .$$("sick_certificates")
            .$$("stat_dec_provided")
            .disable();
          $$("bookings-page").$$("sick_certificates").$$("btn_deny").disable();
          lastEmployeeName = "";
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_email")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("bookings-page")
          .$$("sick_certificates")
          .$$("sickness_pending_grid");
        let status = $$("bookings-page")
          .$$("sick_certificates")
          .$$("status_filter")
          .getText();
        let count = 0;
        let properSurname = "";
        let properName = "";
        if (user_permission_level === 1 || user_permission_level === 2) {
          if (live_site === true) {
            if (status == "Pending") {
              if (grid.count() > 0) {
                webix.confirm({
                  title: "Send Email Reminders",
                  ok: "Proceed",
                  cancel: "Cancel",
                  width: 500,
                  text: "You are about to send email reminders to employees that</br>have not yet provided sick certificates within 14 days.</br></br>Please confirm that you want to proceed!",
                  callback: function (result) {
                    switch (result) {
                      case true:
                        grid.eachRow(function (row) {
                          let record = grid.getItem(row);
                          if (record.elapsed >= 14) {
                            if (
                              record.emailed !=
                              "<span class = 'webix_icon fas fa-check' style='color: green'></span>"
                            ) {
                              let nameSplit = record.employee.split(", ");
                              properSurname = toProperCase(nameSplit[0]);
                              properName = nameSplit[1].split(" ");
                              if (record.type == "SICM") {
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  record.email_address,
                                  "RE: SICM Certificate Reminder",
                                  "A Medical Certificate/Statutory Declaration has not been received for the SICM on " +
                                    record.start_date,
                                  "It is a requirement to provide such evidence within 14 days of sickness. If no evidence is received within 7 days then this SICM will be changed to SLUP (Sick Leave Unpaid) and your salary will be adjusted accordingly. Repayment of any deduction, following the provision of a Medical Certificate/Statutory Declaration, will be, where practicable, during the next available pay period.",
                                  properName[0] + " " + properSurname,
                                  "If you have recently emailed your Medical Certificate/Statutory <NAME_EMAIL> then please disregard this email.\n If you have any queries please contact your line manager. Do not reply to this email!",
                                  "Regards, The SAPPHIRE Team",
                                );
                              } else if (record.type == "FAML") {
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  record.email_address,
                                  "RE: FAML Certificate Reminder",
                                  "A Medical Certificate/Statutory Declaration has not been received for the FAML on " +
                                    record.start_date,
                                  "It is a requirement to provide such evidence within 14 days of sickness. If no evidence is received within 7 days then this FAML will be changed to SLUP (Sick Leave Unpaid) and your salary will be adjusted accordingly. Repayment of any deduction, following the provision of a Medical Certificate/Statutory Declaration, will be, where practicable, during the next available pay period.",
                                  properName[0] + " " + properSurname,
                                  "If you have recently emailed your Medical Certificate/Statutory <NAME_EMAIL> then please disregard this email.\n If you have any queries please contact your line manager. Do not reply to this email!",
                                  "Regards, The SAPPHIRE Team",
                                );
                              }
                              markEmailAsSent(
                                record.booking_id,
                                record.start_date,
                                record.type,
                                function (response) {
                                  if (response === "ok") {
                                    count += 1;
                                  }
                                },
                              );
                            }
                          }
                        });
                        loadSicknessBookings();
                        webix.alert(count + " reminder email(s) were sent!");
                    }
                  },
                });
              } else {
                webix.alert("No records found to send an email to!");
              }
            } else {
              webix.alert({
                text: "The email function is only for 'Pending' records!",
                width: 450,
              });
            }
          } else {
            webix.alert({
              text: "This function is only available on the Live site!",
              width: 450,
            });
          }
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_print")
      .attachEvent("onItemClick", function (id, e) {
        let grid = $$("bookings-page")
          .$$("sick_certificates")
          .$$("sickness_pending_grid");
        let status = $$("bookings-page")
          .$$("sick_certificates")
          .$$("status_filter")
          .getText();
        grid.filter((obj) => filter_elapsed(obj));
        if (status == "Pending") {
          if (grid.count() > 0) {
            grid.hideColumn("select");
            grid.hideColumn("approved_by");
            grid.hideColumn("approved_date");
            grid.hideColumn("status");
            grid.hideColumn("comments");
            grid.hideColumn("emailed");
            webix.print(
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("sickness_pending_grid"),
              {
                mode: "landscape",
                docFooter:
                  "List of Employees still to provide a Medical Certificate/Statutory Declaration after 14 days</br><i>Printed by " +
                  user_logged_in_name +
                  " on " +
                  moment().format("DD/MM/YYYY H:mm") +
                  "</i>",
                fit: "data",
                margin: { left: 15, right: 15 },
                trim: true,
              },
            );
            grid.showColumn("select");
            grid.showColumn("approved_by");
            grid.showColumn("approved_date");
            grid.showColumn("status");
            grid.showColumn("comments");
            grid.showColumn("emailed");
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .define("template", "Selected Sickness Bookings: None");
            $$("bookings-page")
              .$$("sick_certificates")
              .$$("selected_request")
              .refresh();
            loadSicknessBookings();
          } else {
            webix.alert("Nothing to print!");
          }
        } else {
          webix.alert({
            text: "The print function is only for 'Pending' records!",
            width: 450,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_approve_sicm")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Approved";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let rowCount = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              sel_count += 1;
            }
          });
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              rowCount += 1;
              if (rowCount == sel_count) {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  true,
                  grid.getItem(row).type,
                );
              } else {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  false,
                  grid.getItem(row).type,
                );
              }
              if (live_site === true) {
                let email_body =
                  "A Medical Certificate (or other evidence) has been received and recorded against the " +
                  grid.getItem(row).type +
                  " booking dated " +
                  moment(
                    grid.getItem(row).start_date,
                    "DD/MM/YYYY HH:mm",
                  ).format("DD/MM/YYYY");
                getEmployeeData(
                  grid.getItem(row).service_no,
                  function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Sick Certificate",
                      email_body,
                      "Comments: " + comments,
                      values[0].first_name + " " + properSurname,
                      "",
                      "Regards, The SAPPHIRE Team",
                    );
                  },
                );
              }
            }
          });
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_change_to_sic")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Approved";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let rowCount = 0;
          let validation = true;
          let WY_sick_days_count = 0;
          let sickness_logs = "";
          let payId = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              sel_count += 1;
              payId = grid.getItem(row).service_no;
            }
          });
          getUserSickBookings(payId, "SIC", function (response) {
            response.forEach(function (value) {
              if (value.roster == "Port Pirie") {
                if (value.bk_period == "both") {
                  WY_sick_days_count += 2;
                } else {
                  WY_sick_days_count += 1;
                }
              } else {
                WY_sick_days_count += 1;
              }
            });
            if (WY_sick_days_count >= 5) {
              validation = false;
              response.forEach(function (values) {
                sickness_logs =
                  sickness_logs +
                  values.start_date +
                  " (" +
                  toProperCase(values.bk_period) +
                  " Shift) - " +
                  values.roster +
                  " | " +
                  values.shift +
                  " | " +
                  values.location +
                  " " +
                  "</br>";
              });
              webix.alert({
                text:
                  "Selected employee already has " +
                  WY_sick_days_count +
                  " 'SIC' bookings for the current service year so can't make this change!</br>Below is a list of the 'SIC' bookings found for the current service year;" +
                  "</br>" +
                  "</br>" +
                  "<div style='text-align: left'>" +
                  sickness_logs +
                  "</div></br></br>",
                width: 650,
              });
            } else {
              if (WY_sick_days_count + sel_count > 5) {
                validation = false;
                webix.alert({
                  text:
                    "Selected employee already has " +
                    WY_sick_days_count +
                    " 'SIC' bookings so with the selected " +
                    sel_count +
                    " it will exceed the 5 allowed for the service year!",
                  width: 500,
                });
              }
            }
          });
          if (validation == true) {
            grid.eachRow(function (row) {
              if (grid.getItem(row).select == 1) {
                rowCount += 1;
                if (rowCount == sel_count) {
                  updateSicknessStatus(
                    grid.getItem(row).booking_id,
                    grid.getItem(row).type,
                    newStatus,
                    comments,
                    grid.getItem(row).date_string,
                    true,
                    "SIC",
                  );
                } else {
                  updateSicknessStatus(
                    grid.getItem(row).booking_id,
                    grid.getItem(row).type,
                    newStatus,
                    comments,
                    grid.getItem(row).date_string,
                    false,
                    "SIC",
                  );
                }
                if (live_site === true) {
                  let email_body =
                    "A " +
                    grid.getItem(row).type +
                    " booking dated " +
                    moment(
                      grid.getItem(row).start_date,
                      "DD/MM/YYYY HH:mm",
                    ).format("DD/MM/YYYY") +
                    " has been changed to SIC type";
                  getEmployeeData(
                    grid.getItem(row).service_no,
                    function (values) {
                      let properSurname = toProperCase(values[0].surname);
                      sendEmail(
                        "SAPPHIRE<<EMAIL>>",
                        values[0].notifications_email,
                        "RE: Sick Certificate",
                        email_body,
                        "Comments: " + comments,
                        values[0].first_name + " " + properSurname,
                        "",
                        "Regards, The SAPPHIRE Team",
                      );
                    },
                  );
                }
              }
            });
          } else {
          }
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_approve_cvil")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Approved";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let rowCount = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              sel_count += 1;
            }
          });
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              rowCount += 1;
              if (rowCount == sel_count) {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  true,
                  "CVIL",
                );
              } else {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  false,
                  "CVIL",
                );
              }
              if (live_site === true) {
                let email_body =
                  "A Medical Certificate (or other evidence) has been received and recorded against the " +
                  grid.getItem(row).type +
                  " booking dated " +
                  moment(
                    grid.getItem(row).start_date,
                    "DD/MM/YYYY HH:mm",
                  ).format("DD/MM/YYYY");
                getEmployeeData(
                  grid.getItem(row).service_no,
                  function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Sick Certificate",
                      email_body,
                      "Comments: " + comments,
                      values[0].first_name + " " + properSurname,
                      "Note: This booking has now been recorded as CVIL (Covid Isolation Leave)",
                      "Regards, The SAPPHIRE Team",
                    );
                  },
                );
              }
            }
          });
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_approve_cvfl")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Approved";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let rowCount = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              sel_count += 1;
            }
          });
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              rowCount += 1;
              if (rowCount == sel_count) {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  true,
                  "CVFL",
                );
              } else {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  false,
                  "CVFL",
                );
              }
              if (live_site === true) {
                let email_body =
                  "A Medical Certificate (or other evidence) has been received and recorded against the " +
                  grid.getItem(row).type +
                  " booking dated " +
                  moment(
                    grid.getItem(row).start_date,
                    "DD/MM/YYYY HH:mm",
                  ).format("DD/MM/YYYY");
                getEmployeeData(
                  grid.getItem(row).service_no,
                  function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Sick Certificate",
                      email_body,
                      "Comments: " + comments,
                      values[0].first_name + " " + properSurname,
                      "Note: This booking has now been recorded as CVFL (Covid Family Leave)",
                      "Regards, The SAPPHIRE Team",
                    );
                  },
                );
              }
            }
          });
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_approve_cvsl")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Approved";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let rowCount = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              sel_count += 1;
            }
          });
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              rowCount += 1;
              if (rowCount == sel_count) {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  true,
                  "CVSL",
                );
              } else {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  false,
                  "CVSL",
                );
              }
              if (live_site === true) {
                let email_body =
                  "A Medical Certificate (or other evidence) has been received and recorded against the " +
                  grid.getItem(row).type +
                  " booking dated " +
                  moment(
                    grid.getItem(row).start_date,
                    "DD/MM/YYYY HH:mm",
                  ).format("DD/MM/YYYY");
                getEmployeeData(
                  grid.getItem(row).service_no,
                  function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Sick Certificate",
                      email_body,
                      "Comments: " + comments,
                      values[0].first_name + " " + properSurname,
                      "Note: This booking has now been recorded as CVSL (Covid Sick Leave)",
                      "Regards, The SAPPHIRE Team",
                    );
                  },
                );
              }
            }
          });
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_approve_cvwr")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Approved";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let rowCount = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              sel_count += 1;
            }
          });
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              rowCount += 1;
              if (rowCount == sel_count) {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  true,
                  "CVWR",
                );
              } else {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  false,
                  "CVWR",
                );
              }
              if (live_site === true) {
                let email_body =
                  "A Medical Certificate (or other evidence) has been received and recorded against the " +
                  grid.getItem(row).type +
                  " booking dated " +
                  moment(
                    grid.getItem(row).start_date,
                    "DD/MM/YYYY HH:mm",
                  ).format("DD/MM/YYYY");
                getEmployeeData(
                  grid.getItem(row).service_no,
                  function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Sick Certificate",
                      email_body,
                      "Comments: " + comments,
                      values[0].first_name + " " + properSurname,
                      "Note: This booking has now been recorded as CVWR (Covid Work Related)",
                      "Regards, The SAPPHIRE Team",
                    );
                  },
                );
              }
            }
          });
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("stat_dec_provided")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Approved-SD";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let row_count = 0;
          let rowCount = 0;
          let selPayID = "";
          let selEmployee = "";
          let stat_dec_count = 0;
          let stat_dec_info = "";
          let total_stat_decs = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              selPayID = grid.getItem(row).service_no;
              selEmployee = grid.getItem(row).employee;
              row_count += 1;
              if (grid.getItem(row).roster == "Port Pirie") {
                if (grid.getItem(row).bk_period == "both") {
                  sel_count += 2;
                } else {
                  sel_count += 1;
                }
              } else {
                sel_count += 1;
              }
            }
          });
          getStatDecCount(selPayID, function (results) {
            stat_dec_info =
              "Summary of used Statutory Declarations for <strong>" +
              selEmployee +
              "</strong></br></br>";
            results.forEach(function (result) {
              if (result.roster == "Port Pirie") {
                if (result.bk_period == "both") {
                  stat_dec_count += 2;
                } else {
                  stat_dec_count += 1;
                }
              } else {
                stat_dec_count += 1;
              }
              stat_dec_info =
                stat_dec_info +
                "<strong>Date: </strong>" +
                result.start_date +
                " - " +
                "<strong>Type: </strong>" +
                result.leave_type_code +
                " - " +
                "<strong>Period: </strong>" +
                result.bk_period.toUpperCase() +
                "</br>";
            });
          });
          total_stat_decs = stat_dec_count + sel_count;
          if (total_stat_decs > 5) {
            if (stat_dec_count == 0) {
              webix.confirm({
                title: "Statutory Declaration Limit",
                text:
                  "The selected user has used <strong>(" +
                  stat_dec_count +
                  ")</strong> statutory declarations plus with the selected <strong>(" +
                  sel_count +
                  ")</strong> it will exceed the limit of <strong>(5)</strong> allowed per work year!</br></br>Do you want to allow anyway?",
                width: 520,
                callback: function (result) {
                  switch (result) {
                    case true:
                      grid.eachRow(function (row) {
                        if (grid.getItem(row).select == 1) {
                          rowCount += 1;
                          if (rowCount == row_count) {
                            updateSicknessStatus(
                              grid.getItem(row).booking_id,
                              grid.getItem(row).type,
                              newStatus,
                              comments,
                              grid.getItem(row).date_string,
                              true,
                              "",
                            );
                          } else {
                            updateSicknessStatus(
                              grid.getItem(row).booking_id,
                              grid.getItem(row).type,
                              newStatus,
                              comments,
                              grid.getItem(row).date_string,
                              false,
                              "",
                            );
                          }
                          if (live_site === true) {
                            let email_body =
                              "A Statutory Declaration (or other evidence) has been received and recorded against the " +
                              grid.getItem(row).type +
                              " booking dated " +
                              moment(
                                grid.getItem(row).start_date,
                                "DD/MM/YYYY HH:mm",
                              ).format("DD/MM/YYYY");
                            getEmployeeData(
                              grid.getItem(row).service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  "Comments: " + comments,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      });
                  }
                },
              });
            } else {
              webix.confirm({
                title: "Statutory Declaration Limit",
                text:
                  "The selected user has used <strong>(" +
                  stat_dec_count +
                  ")</strong> statutory declarations plus with the selected <strong>(" +
                  sel_count +
                  ")</strong> it will exceed the limit of <strong>(5)</strong> allowed per work year!</br></br>" +
                  stat_dec_info +
                  "</br></br>Do you want to allow anyway?",
                width: 520,
                callback: function (result) {
                  switch (result) {
                    case true:
                      grid.eachRow(function (row) {
                        if (grid.getItem(row).select == 1) {
                          rowCount += 1;
                          if (rowCount == row_count) {
                            updateSicknessStatus(
                              grid.getItem(row).booking_id,
                              grid.getItem(row).type,
                              newStatus,
                              comments,
                              grid.getItem(row).date_string,
                              true,
                              "",
                            );
                          } else {
                            updateSicknessStatus(
                              grid.getItem(row).booking_id,
                              grid.getItem(row).type,
                              newStatus,
                              comments,
                              grid.getItem(row).date_string,
                              false,
                              "",
                            );
                          }
                          if (live_site === true) {
                            let email_body =
                              "A Statutory Declaration (or other evidence) has been received and recorded against the " +
                              grid.getItem(row).type +
                              " booking dated " +
                              moment(
                                grid.getItem(row).start_date,
                                "DD/MM/YYYY HH:mm",
                              ).format("DD/MM/YYYY");
                            getEmployeeData(
                              grid.getItem(row).service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  "Comments: " + comments,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      });
                  }
                },
              });
            }
          } else {
            grid.eachRow(function (row) {
              if (grid.getItem(row).select == 1) {
                rowCount += 1;
                if (rowCount == row_count) {
                  updateSicknessStatus(
                    grid.getItem(row).booking_id,
                    grid.getItem(row).type,
                    newStatus,
                    comments,
                    grid.getItem(row).date_string,
                    true,
                    "",
                  );
                } else {
                  updateSicknessStatus(
                    grid.getItem(row).booking_id,
                    grid.getItem(row).type,
                    newStatus,
                    comments,
                    grid.getItem(row).date_string,
                    false,
                    "",
                  );
                }
                if (live_site === true) {
                  let email_body =
                    "A Statutory Declaration (or other evidence) has been received and recorded against the " +
                    grid.getItem(row).type +
                    " booking dated " +
                    moment(
                      grid.getItem(row).start_date,
                      "DD/MM/YYYY HH:mm",
                    ).format("DD/MM/YYYY");
                  getEmployeeData(
                    grid.getItem(row).service_no,
                    function (values) {
                      let properSurname = toProperCase(values[0].surname);
                      sendEmail(
                        "SAPPHIRE<<EMAIL>>",
                        values[0].notifications_email,
                        "RE: Sick Certificate",
                        email_body,
                        "Comments: " + comments,
                        values[0].first_name + " " + properSurname,
                        "",
                        "Regards, The SAPPHIRE Team",
                      );
                    },
                  );
                }
              }
            });
          }
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("btn_deny")
      .attachEvent("onItemClick", function (id, e) {
        if (user_permission_level === 1 || user_permission_level === 2) {
          let newStatus = "Denied";
          let comments = $$("bookings-page")
            .$$("sick_certificates")
            .$$("request_comments")
            .getValue();
          let grid = $$("bookings-page")
            .$$("sick_certificates")
            .$$("sickness_pending_grid");
          let sel_count = 0;
          let rowCount = 0;
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              sel_count += 1;
            }
          });
          grid.eachRow(function (row) {
            if (grid.getItem(row).select == 1) {
              rowCount += 1;
              if (rowCount == sel_count) {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  true,
                  "",
                );
              } else {
                updateSicknessStatus(
                  grid.getItem(row).booking_id,
                  grid.getItem(row).type,
                  newStatus,
                  comments,
                  grid.getItem(row).date_string,
                  false,
                  "",
                );
              }
              if (live_site === true) {
                let email_body =
                  "A Medical Certificate/Statutory Declaration <u>has not</u> been received for the " +
                  grid.getItem(row).type +
                  " booking on the " +
                  moment(
                    grid.getItem(row).start_date,
                    "DD/MM/YYYY HH:mm",
                  ).format("DD/MM/YYYY");
                let body_extra =
                  "The (" +
                  grid.getItem(row).type +
                  ") recorded has now been changed to SLUP - Sick Leave Unpaid";
                let email_extra =
                  "If a Medical Certificate/Statutory Declaration is not <NAME_EMAIL> AND <EMAIL> within 7 days you will be considered to be on leave without pay for the period of the absence and your salary will be adjusted accordingly!";
                getEmployeeData(
                  grid.getItem(row).service_no,
                  function (values) {
                    let properSurname = toProperCase(values[0].surname);
                    sendEmail(
                      "SAPPHIRE<<EMAIL>>",
                      values[0].notifications_email,
                      "RE: Sick Certificate",
                      email_body,
                      body_extra + "<br><br>" + "Comments: " + comments,
                      values[0].first_name + " " + properSurname,
                      email_extra,
                      "Regards, The SAPPHIRE Team",
                    );
                  },
                );
              }
            }
          });
        } else {
          webix.alert({
            text: "You don't have permission to use this function!",
            width: 400,
          });
        }
      });
    $$("bookings-page")
      .$$("sick_certificates")
      .$$("sickness_pending_grid")
      .attachEvent("onItemClick", function (id, e, node) {
        let selectedRow = $$("bookings-page")
          .$$("sick_certificates")
          .$$("sickness_pending_grid")
          .getSelectedItem(id);
        let bookingId = selectedRow[0].booking_id;
        let bookingDate = moment(
          selectedRow[0].start_date,
          "DD/MM/YYYY HH:mm",
        ).format("YYYYMMDD");
        let employeeName = selectedRow[0].employee;
        let prevType = selectedRow[0].comments.slice(5, 9);
        let currType = selectedRow[0].type;
        let stripDateFormat = selectedRow[0].start_date.substring(0, 10);
        let bkCheckFormat = moment(
          stripDateFormat + " 07:00",
          "DD/MM/YYYY HH:mm",
        );
        let prevbkDate = moment();
        if (
          moment(
            moment(selectedRow[0].start_date, "DD/MM/YYYY HH:mm"),
          ).isBefore(bkCheckFormat)
        ) {
          prevbkDate = moment(moment(bookingDate).subtract(1, "days")).format(
            "YYYYMMDD",
          );
          bookingDate = prevbkDate;
        }
        if (id.column == "edit") {
          if (
            selectedRow[0].status ==
            "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>"
          ) {
            if (
              prevType.includes("SICM") ||
              prevType.includes("FAML") ||
              prevType.includes("CVIL") ||
              prevType.includes("CVFL") ||
              prevType.includes("CVSL") ||
              prevType.includes("CVWR")
            ) {
            } else {
              prevType = "SICM";
            }
            webix
              .modalbox({
                title: "Change Sickness Status",
                text:
                  employeeName +
                  " on " +
                  selectedRow[0].start_date.slice(0, 10) +
                  "</br></br><div align='left'>-------------------------------------------------------------------------------------------------------------------------------</br>[Pending] - change status of booking from <strong>SLUP</strong> back to <strong>(" +
                  prevType +
                  ")</strong></br>[Approved] - change status of booking from <strong>SLUP</strong> to <strong>" +
                  prevType +
                  "</strong></br>[CVIL] - change status of booking from <strong>SLUP</strong> to <strong>CVIL</strong></br>[CVFL] - change status of booking from <strong>SLUP</strong> to <strong>CVFL</strong></br>[CVSL] - change status of booking from <strong>SLUP</strong> to <strong>CVSL</strong></br>[CVWR] - change status of booking from <strong>SLUP</strong> to <strong>CVWR</strong></br>[Cancel] - don't change anything</br>-------------------------------------------------------------------------------------------------------------------------------</div>",
                buttons: [
                  "Pending",
                  "Approved",
                  "CVIL",
                  "CVFL",
                  "CVSL",
                  "CVWR",
                  "Cancel",
                ],
                width: 600,
              })
              .then(function (result) {
                switch (result) {
                  case "0":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      prevType,
                      "Pending",
                      "",
                      "SICM",
                      function (response) {},
                    );
                    break;
                  case "1":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      prevType,
                      "Approved",
                      "",
                      "SICM",
                      function (response) {
                        if (response == "ok") {
                          if (live_site === true) {
                            let email_body =
                              "A Medical Certificate (or other evidence) has now been received and recorded against the " +
                              prevType +
                              " booking dated " +
                              selectedRow[0].start_date.slice(0, 10);
                            let email_extra =
                              "Note: The sickness type has been changed from SLUP to an approved " +
                              prevType;
                            getEmployeeData(
                              selectedRow[0].service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  email_extra,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      },
                    );
                    break;
                  case "2":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      "CVIL",
                      "Approved",
                      "",
                      "CVIL",
                      function (response) {
                        if (response == "ok") {
                          if (live_site === true) {
                            let email_body =
                              "A Medical Certificate (or other evidence) has now been received and recorded against the " +
                              prevType +
                              " booking dated " +
                              selectedRow[0].start_date.slice(0, 10);
                            let email_extra =
                              "Note: The sickness type has been changed from SLUP to an approved CVIL";
                            getEmployeeData(
                              selectedRow[0].service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  email_extra,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      },
                    );
                    break;
                  case "3":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      "CVFL",
                      "Approved",
                      "",
                      "CVFL",
                      function (response) {
                        if (response == "ok") {
                          if (live_site === true) {
                            let email_body =
                              "A Medical Certificate (or other evidence) has now been received and recorded against the " +
                              prevType +
                              " booking dated " +
                              selectedRow[0].start_date.slice(0, 10);
                            let email_extra =
                              "Note: The sickness type has been changed from SLUP to an approved CVFL";
                            getEmployeeData(
                              selectedRow[0].service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  email_extra,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      },
                    );
                    break;
                  case "4":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      "CVSL",
                      "Approved",
                      "",
                      "CVSL",
                      function (response) {
                        if (response == "ok") {
                          if (live_site === true) {
                            let email_body =
                              "A Medical Certificate (or other evidence) has now been received and recorded against the " +
                              prevType +
                              " booking dated " +
                              selectedRow[0].start_date.slice(0, 10);
                            let email_extra =
                              "Note: The sickness type has been changed from SLUP to an approved CVSL";
                            getEmployeeData(
                              selectedRow[0].service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  email_extra,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      },
                    );
                    break;
                  case "5":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      "CVWR",
                      "Approved",
                      "",
                      "CVWR",
                      function (response) {
                        if (response == "ok") {
                          if (live_site === true) {
                            let email_body =
                              "A Medical Certificate (or other evidence) has now been received and recorded against the " +
                              prevType +
                              " booking dated " +
                              selectedRow[0].start_date.slice(0, 10);
                            let email_extra =
                              "Note: The sickness type has been changed from SLUP to an approved CVWR";
                            getEmployeeData(
                              selectedRow[0].service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  email_extra,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      },
                    );
                    break;
                  case "6":
                    break;
                }
              });
          } else if (
            selectedRow[0].status ==
            "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>"
          ) {
            if (
              prevType.includes("SICM") ||
              prevType.includes("FAML") ||
              prevType.includes("CVIL") ||
              prevType.includes("CVFL") ||
              prevType.includes("CVSL") ||
              prevType.includes("CVWR")
            ) {
            } else {
              prevType = currType;
            }
            let buttons = [];
            let buttonText = "";
            let buttonTitle = "";
            if (currType == "SICM" || currType == "FAML") {
              buttons = [
                "Pending",
                "Denied",
                "CVIL",
                "CVFL",
                "CVSL",
                "CVWR",
                "Cancel",
              ];
              buttonText =
                employeeName +
                " on " +
                selectedRow[0].start_date.slice(0, 10) +
                "</br></br><div align='left'>-------------------------------------------------------------------------------------------------------------------------------</br>[Pending] - change status of booking from <strong>" +
                currType +
                "</strong> back to <strong>(" +
                prevType +
                ")</strong></br>[Denied] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>SLUP</strong></br>[CVIL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVIL</strong></br>[CVFL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVFL</strong></br>[CVSL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVSL</strong></br>[CVWR] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVWR</strong></br>[Cancel] - don't change anything</br>-------------------------------------------------------------------------------------------------------------------------------</div>";
              buttonTitle = "Change Sickness Status";
            } else if (currType == "CVIL") {
              buttons = [
                "Pending",
                "Denied",
                "-",
                "CVFL",
                "CVSL",
                "CVWR",
                "Cancel",
              ];
              buttonText =
                employeeName +
                " on " +
                selectedRow[0].start_date.slice(0, 10) +
                "</br></br><div align='left'>-------------------------------------------------------------------------------------------------------------------------------</br>[Pending] - change status of booking from <strong>" +
                currType +
                "</strong> back to <strong>(" +
                prevType +
                ")</strong></br>[Denied] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>SLUP</strong></br><strike>[CVIL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVIL</strong></strike></br>[CVFL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVFL</strong></br>[CVSL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVSL</strong></br>[CVWR] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVWR</strong></br>[Cancel] - don't change anything</br>-------------------------------------------------------------------------------------------------------------------------------</div>";
              buttonTitle = "Change Sickness Type";
            } else if (currType == "CVFL") {
              buttons = [
                "Pending",
                "Denied",
                "CVIL",
                "-",
                "CVSL",
                "CVWR",
                "Cancel",
              ];
              buttonText =
                employeeName +
                " on " +
                selectedRow[0].start_date.slice(0, 10) +
                "</br></br><div align='left'>-------------------------------------------------------------------------------------------------------------------------------</br>[Pending] - change status of booking from <strong>" +
                currType +
                "</strong> back to <strong>(" +
                prevType +
                ")</strong></br>[Denied] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>SLUP</strong></br>[CVIL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVIL</strong></br><strike>[CVFL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVFL</strong></strike></br>[CVSL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVSL</strong></br>[CVWR] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVWR</strong></br>[Cancel] - don't change anything</br>-------------------------------------------------------------------------------------------------------------------------------</div>";
              buttonTitle = "Change Sickness Type";
            } else if (currType == "CVSL") {
              buttons = [
                "Pending",
                "Denied",
                "CVIL",
                "CVFL",
                "-",
                "CVWR",
                "Cancel",
              ];
              buttonText =
                employeeName +
                " on " +
                selectedRow[0].start_date.slice(0, 10) +
                "</br></br><div align='left'>-------------------------------------------------------------------------------------------------------------------------------</br>[Pending] - change status of booking from <strong>" +
                currType +
                "</strong> back to <strong>(" +
                prevType +
                ")</strong></br>[Denied] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>SLUP</strong></br>[CVIL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVIL</strong></br>[CVFL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVFL</strong></br><strike>[CVSL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVSL</strong></strike></br>[CVWR] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVWR</strong></br>[Cancel] - don't change anything</br>-------------------------------------------------------------------------------------------------------------------------------</div>";
              buttonTitle = "Change Sickness Type";
            } else if (currType == "CVWR") {
              buttons = [
                "Pending",
                "Denied",
                "CVIL",
                "CVFL",
                "CVSL",
                "-",
                "Cancel",
              ];
              buttonText =
                employeeName +
                " on " +
                selectedRow[0].start_date.slice(0, 10) +
                "</br></br><div align='left'>-------------------------------------------------------------------------------------------------------------------------------</br>[Pending] - change status of booking from <strong>" +
                currType +
                "</strong> back to <strong>(" +
                prevType +
                ")</strong></br>[Denied] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>SLUP</strong></br>[CVIL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVIL</strong></br>[CVFL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVFL</strong></br>[CVSL] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVSL</strong></br><strike>[CVWR] - change status of booking from <strong>" +
                currType +
                "</strong> to <strong>CVWR</strong></strike></br>[Cancel] - don't change anything</br>-------------------------------------------------------------------------------------------------------------------------------</div>";
              buttonTitle = "Change Sickness Type";
            }
            webix
              .modalbox({
                title: buttonTitle,
                text: buttonText,
                buttons: buttons,
                width: 600,
              })
              .then(function (result) {
                switch (result) {
                  case "0":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      currType,
                      "Pending",
                      prevType,
                      "",
                      function (response) {},
                    );
                    break;
                  case "1":
                    revertBookingStatus(
                      bookingId,
                      bookingDate,
                      currType,
                      "Denied",
                      prevType,
                      "",
                      function (response) {
                        if (response == "ok") {
                          if (live_site === true) {
                            let email_body =
                              "A valid Medical Certificate/Statutory Declaration <u>was not</u> accepted for the " +
                              prevType +
                              " booking dated " +
                              selectedRow[0].start_date.slice(0, 10);
                            let email_extra =
                              "Note: The sickness type has now been changed to SLUP - Sick Leave Unpaid";
                            getEmployeeData(
                              selectedRow[0].service_no,
                              function (values) {
                                let properSurname = toProperCase(
                                  values[0].surname,
                                );
                                sendEmail(
                                  "SAPPHIRE<<EMAIL>>",
                                  values[0].notifications_email,
                                  "RE: Sick Certificate",
                                  email_body,
                                  email_extra,
                                  values[0].first_name + " " + properSurname,
                                  "",
                                  "Regards, The SAPPHIRE Team",
                                );
                              },
                            );
                          }
                        }
                      },
                    );
                    break;
                  case "2":
                    if (currType != "CVIL") {
                      revertBookingStatus(
                        bookingId,
                        bookingDate,
                        currType,
                        "Approved",
                        prevType,
                        "CVIL",
                        function (response) {
                          if (response == "ok") {
                            if (live_site === true) {
                              let email_body =
                                "A " +
                                currType +
                                " booking dated " +
                                selectedRow[0].start_date.slice(0, 10) +
                                " has been updated!";
                              let email_extra =
                                "Note: The sickness type has been changed from " +
                                currType +
                                " to an approved CVIL";
                              getEmployeeData(
                                selectedRow[0].service_no,
                                function (values) {
                                  let properSurname = toProperCase(
                                    values[0].surname,
                                  );
                                  sendEmail(
                                    "SAPPHIRE<<EMAIL>>",
                                    values[0].notifications_email,
                                    "RE: Sick Certificate",
                                    email_body,
                                    email_extra,
                                    values[0].first_name + " " + properSurname,
                                    "",
                                    "Regards, The SAPPHIRE Team",
                                  );
                                },
                              );
                            }
                          }
                        },
                      );
                    }
                    break;
                  case "3":
                    if (currType != "CVFL") {
                      revertBookingStatus(
                        bookingId,
                        bookingDate,
                        currType,
                        "Approved",
                        prevType,
                        "CVFL",
                        function (response) {
                          if (response == "ok") {
                            if (live_site === true) {
                              let email_body =
                                "A " +
                                currType +
                                " booking dated " +
                                selectedRow[0].start_date.slice(0, 10) +
                                " has been updated!";
                              let email_extra =
                                "Note: The sickness type has been changed from " +
                                currType +
                                " to an approved CVFL";
                              getEmployeeData(
                                selectedRow[0].service_no,
                                function (values) {
                                  let properSurname = toProperCase(
                                    values[0].surname,
                                  );
                                  sendEmail(
                                    "SAPPHIRE<<EMAIL>>",
                                    values[0].notifications_email,
                                    "RE: Sick Certificate",
                                    email_body,
                                    email_extra,
                                    values[0].first_name + " " + properSurname,
                                    "",
                                    "Regards, The SAPPHIRE Team",
                                  );
                                },
                              );
                            }
                          }
                        },
                      );
                    }
                    break;
                  case "4":
                    if (currType != "CVSL") {
                      revertBookingStatus(
                        bookingId,
                        bookingDate,
                        currType,
                        "Approved",
                        prevType,
                        "CVSL",
                        function (response) {
                          if (response == "ok") {
                            if (live_site === true) {
                              let email_body =
                                "A " +
                                currType +
                                " booking dated " +
                                selectedRow[0].start_date.slice(0, 10) +
                                " has been updated!";
                              let email_extra =
                                "Note: The sickness type has been changed from " +
                                currType +
                                " to an approved CVSL";
                              getEmployeeData(
                                selectedRow[0].service_no,
                                function (values) {
                                  let properSurname = toProperCase(
                                    values[0].surname,
                                  );
                                  sendEmail(
                                    "SAPPHIRE<<EMAIL>>",
                                    values[0].notifications_email,
                                    "RE: Sick Certificate",
                                    email_body,
                                    email_extra,
                                    values[0].first_name + " " + properSurname,
                                    "",
                                    "Regards, The SAPPHIRE Team",
                                  );
                                },
                              );
                            }
                          }
                        },
                      );
                    }
                    break;
                  case "5":
                    if (currType != "CVWR") {
                      revertBookingStatus(
                        bookingId,
                        bookingDate,
                        currType,
                        "Approved",
                        prevType,
                        "CVWR",
                        function (response) {
                          if (response == "ok") {
                            if (live_site === true) {
                              let email_body =
                                "A " +
                                currType +
                                " booking dated " +
                                selectedRow[0].start_date.slice(0, 10) +
                                " has been updated!";
                              let email_extra =
                                "Note: The sickness type has been changed from " +
                                currType +
                                " to an approved CVWR";
                              getEmployeeData(
                                selectedRow[0].service_no,
                                function (values) {
                                  let properSurname = toProperCase(
                                    values[0].surname,
                                  );
                                  sendEmail(
                                    "SAPPHIRE<<EMAIL>>",
                                    values[0].notifications_email,
                                    "RE: Sick Certificate",
                                    email_body,
                                    email_extra,
                                    values[0].first_name + " " + properSurname,
                                    "",
                                    "Regards, The SAPPHIRE Team",
                                  );
                                },
                              );
                            }
                          }
                        },
                      );
                    }
                    break;
                  case "6":
                    break;
                }
              });
          }
        }
      });
  }
  function filter_elapsed(t) {
    if (t.elapsed >= 14) {
      return t;
    }
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("bookings-page")
      .$$("sick_certificates")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  employees_subject.subscribe(function (data) {
    let select = $$("bookings-page")
      .$$("sick_certificates")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  function markEmailAsSent(bookingId, startDate, leaveType, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .put(
        server_url + "/bookings/mark_as_email_sent",
        {
          booking_id: bookingId,
          leave_type_code: leaveType,
          date_string: moment(startDate, "DD/MM/YYYY H:mm").format("YYYYMMDD"),
        },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            callback("ok");
          },
        },
      );
  }
  function revertBookingStatus(
    bookingId,
    bookingDate,
    prevType,
    newStatus,
    oldType,
    newType,
    callback,
  ) {
    if (newType == "") {
      newType = oldType;
    }
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .put(
        server_url + "/bookings/revert_sick_status",
        {
          booking_id: bookingId,
          leave_type: prevType,
          status: newStatus,
          date_string: bookingDate,
          approved_denied_by: user_logged_in,
          old_leave_type: oldType,
          new_leave_type: newType,
        },
        {
          error: function (err) {
            callback("error");
          },
          success: function (results) {
            loadSicknessBookings();
            webix.message({
              text: "Sickness booking status was changed successfully!",
              type: "success",
              expire: 2e3,
            });
            callback("ok");
          },
        },
      );
  }
  function updateSicknessStatus(
    bookingId,
    sickType,
    newStatus,
    comments,
    date_string,
    lastEntry,
    newType,
  ) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .put(
        server_url + "/bookings/change_sick_status",
        {
          booking_id: bookingId,
          leave_type: sickType,
          status: newStatus,
          date_string: date_string,
          approved_denied_by: user_logged_in,
          request_comments: comments,
          new_leave_type: newType,
        },
        {
          error: function (err) {},
          success: function (results) {
            if (lastEntry === true) {
              loadSicknessBookings();
              webix.message({
                text: "Sickness booking's status were updated successfully!",
                type: "success",
                expire: 3e3,
              });
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("selected_request")
                .define("template", "Selected Sickness Bookings: None");
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("selected_request")
                .refresh();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("btn_approve_sicm")
                .disable();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("btn_change_to_sic")
                .disable();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("btn_approve_cvil")
                .disable();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("btn_approve_cvfl")
                .disable();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("btn_approve_cvsl")
                .disable();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("btn_approve_cvwr")
                .disable();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("stat_dec_provided")
                .disable();
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("btn_deny")
                .disable();
              lastEmployeeName = "";
              $$("bookings-page")
                .$$("sick_certificates")
                .$$("request_comments")
                .setValue("");
            }
          },
        },
      );
  }
  function loadSicknessBookings() {
    let roster = $$("bookings-page")
      .$$("sick_certificates")
      .$$("roster_filter")
      .getText();
    let status = $$("bookings-page")
      .$$("sick_certificates")
      .$$("status_filter")
      .getText();
    let pay_id = $$("bookings-page")
      .$$("sick_certificates")
      .$$("employee_filter")
      .getValue();
    let grid = $$("bookings-page")
      .$$("sick_certificates")
      .$$("sickness_pending_grid");
    let grid_info = grid.getState();
    let sort_order = grid_info.sort;
    let approved_date_format = "";
    $$("loader-window").show();
    if (status == "Denied" || status == "Approved") {
      $$("bookings-page").$$("sick_certificates").$$("selected_request").hide();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_sicm").hide();
      $$("bookings-page")
        .$$("sick_certificates")
        .$$("btn_change_to_sic")
        .hide();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvil").hide();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvfl").hide();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvsl").hide();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvwr").hide();
      $$("bookings-page")
        .$$("sick_certificates")
        .$$("stat_dec_provided")
        .hide();
      $$("bookings-page").$$("sick_certificates").$$("btn_deny").hide();
      $$("bookings-page").$$("sick_certificates").$$("request_comments").hide();
    } else {
      $$("bookings-page").$$("sick_certificates").$$("selected_request").show();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_sicm").show();
      $$("bookings-page")
        .$$("sick_certificates")
        .$$("btn_change_to_sic")
        .show();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvil").show();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvfl").show();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvsl").show();
      $$("bookings-page").$$("sick_certificates").$$("btn_approve_cvwr").show();
      $$("bookings-page")
        .$$("sick_certificates")
        .$$("stat_dec_provided")
        .show();
      $$("bookings-page").$$("sick_certificates").$$("btn_deny").show();
      $$("bookings-page").$$("sick_certificates").$$("request_comments").show();
    }
    setTimeout(function () {
      grid.clearAll();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/bookings/get_sickness_bookings",
          { roster: roster, status: status, pay_id: pay_id },
          {
            error: function (err) {
              $$("loader-window").hide();
            },
            success: function (results) {
              let values = JSON.parse(results);
              let status_icon = "";
              let middleName = "";
              let comments = "";
              let emailed = "";
              let work_cover = "";
              values.forEach(function (result) {
                if (
                  result.sick_certificate == null &&
                  result.statutory_declaration == null
                ) {
                  status_icon =
                    "<span class = 'approved_pending webix_icon fas fa-clock'></span>";
                } else if (
                  result.sick_certificate == true ||
                  result.statutory_declaration == true
                ) {
                  status_icon =
                    "<span class = 'approved_true webix_icon fas fa-thumbs-up'></span>";
                } else if (
                  result.sick_certificate == false &&
                  result.statutory_declaration == null
                ) {
                  status_icon =
                    "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>";
                } else if (
                  result.sick_certificate == null &&
                  result.statutory_declaration == false
                ) {
                  status_icon =
                    "<span class = 'approved_false webix_icon fas fa-thumbs-down'></span>";
                }
                if (result.middle_name === null) {
                  middleName = "";
                } else {
                  middleName = result.middle_name;
                }
                if (result.approved_denied_date == "Invalid date") {
                  approved_date_format = "";
                } else {
                  approved_date_format = result.approved_denied_date;
                }
                if (
                  result.request_comments == null ||
                  result.request_comments == ""
                ) {
                  comments = result.comments;
                } else {
                  comments = result.request_comments;
                }
                if (result.sick_email_sent == null) {
                  emailed =
                    "<span class = 'webix_icon fas fa-times' style='color: red'></span>";
                } else {
                  emailed =
                    "<span class = 'webix_icon fas fa-check' style='color: green'></span>";
                }
                if (result.work_cover == null || result.work_cover == 0) {
                  work_cover = "No";
                } else {
                  work_cover = "Yes";
                }
                grid.add({
                  booking_id: result.booking_id,
                  service_no: result.pay_id,
                  employee:
                    result.surname +
                    ", " +
                    result.first_name +
                    " " +
                    middleName,
                  rank: result.rank,
                  roster: result.roster,
                  shift: result.shift,
                  location: result.location,
                  start_date: result.start_date,
                  date_string: result.date_string,
                  bk_period: result.bk_period,
                  elapsed: result.elapsed,
                  type: result.leave_type_code,
                  status: status_icon,
                  approved_date: approved_date_format,
                  approved_by: result.approved_denied_by,
                  comments: comments,
                  emailed: emailed,
                  work_cover: work_cover,
                  email_address: result.notifications_email,
                });
              });
              grid.eachRow(function (row) {
                let record = grid.getItem(row);
                if (
                  record.status ==
                  "<span class = 'approved_pending webix_icon fas fa-clock'></span>"
                ) {
                  if (record.elapsed > 14) {
                    grid.addCellCss(
                      row,
                      "elapsed",
                      "sickness_report_row_highlight",
                    );
                  }
                }
              });
              grid.adjustColumn("employee", "data");
              grid.adjustColumn("comments", "data");
              if (status != "Pending") {
                grid.hideColumn("select");
                grid.showColumn("edit");
                grid.define("height", dtableHeight + 120);
                grid.resize();
              } else {
                grid.showColumn("select");
                grid.hideColumn("edit");
                grid.define("height", dtableHeight);
                grid.resize();
              }
              if (sort_order != undefined) {
                if (
                  sort_order.id == "start_date" ||
                  sort_order.id == "approved_date"
                ) {
                  grid.sort(sort_order.id, sort_order.dir, "date");
                } else if (
                  sort_order.id == "elapsed" ||
                  sort_order.id == "service_no"
                ) {
                  grid.sort(sort_order.id, sort_order.dir, "int");
                } else {
                  grid.sort(sort_order.id, sort_order.dir, "string");
                }
              }
              if (
                user_permission_level == 3 ||
                user_permission_level == 4 ||
                user_permission_level == 5
              ) {
                grid.hideColumn("comments");
                grid.hideColumn("emailed");
                grid.hideColumn("email_address");
                grid.hideColumn("select");
                grid.hideColumn("edit");
              }
              $$("loader-window").hide();
            },
          },
        );
    }, 250);
  }
  function getStatDecCount(payId, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/get_stat_dec_count",
        { pay_id: payId },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let values = JSON.parse(results);
            callback(values);
          },
        },
      );
  }
  function getUserSickBookings(payId, leaveType, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/get_sick_bookings",
        { pay_id: payId, leave_type_code: leaveType },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let values = JSON.parse(results);
            callback(values);
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
