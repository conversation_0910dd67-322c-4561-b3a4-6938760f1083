let overtimeReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let customReportStyle2 = {
    0: { font: { name: "Arial", sz: 15, bold: true } },
  };
  let customReportStyle3 = {
    0: { font: { name: "Arial", sz: 12, bold: true } },
  };
  let exp_grid;
  let exp_form;
  let exp_roster = "";
  let exp_shift = "";
  let form_values;
  function initApplication() {
    eventHandlers();
    $$("reports-page").$$("reporting_overtime").$$("from").setValue(new Date());
    $$("reports-page").$$("reporting_overtime").$$("to").setValue(new Date());
    $$("reports-page").$$("reporting_overtime").$$("rosters").setValue("Metro");
    exp_grid = $$("reports-page").$$("reporting_overtime").$$("overtime_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].exportData.unshift(["OVERTIME SUMMARY"]);
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].exportData.unshift(["Page: " + "      " + "1"]);
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].exportData.unshift([
          "Time: " +
            "      " +
            form_values.overtime_export_time +
            "                                      " +
            "Duty Officer: " +
            "       " +
            form_values.overtime_export_duty_officer,
        ]);
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].exportData.unshift([
          "Date: " +
            "       " +
            moment(form_values.overtime_export_date, "YYYY-MM-DD H:mm").format(
              "DD/MM/YYYY",
            ) +
            "                            " +
            "Commander: " +
            "       " +
            form_values.overtime_export_commander,
        ]);
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].exportData.unshift([
          exp_shift.toUpperCase() + " - OVERTIME SUMMARY",
        ]);
        returnValue[0].styles.unshift(customReportStyle2);
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          exp_shift +
            " (" +
            exp_roster +
            ") " +
            moment(form_values.overtime_export_date, "YYYY-MM-DD H:mm").format(
              "DD/MM/YYYY",
            ),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["End Of Shift Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_overtime")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_overtime")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_overtime")
          .$$("to")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_overtime")
          .$$("rosters")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_overtime")
          .$$("shift")
          .getText();
        if (roster == "" || shift == "") {
          webix.alert(
            "You must select a Roster & Shift before you can Search!",
          );
        } else {
          generateOvertimeReport(fromDate, toDate, roster, shift);
        }
      });
    $$("reports-page")
      .$$("reporting_overtime")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        if (exp_grid.count() > 0) {
          $$("overtime_report-popup").show();
          $$("overtime_export_date").setValue(new Date());
          $$("overtime_export_duty_officer").setValue(user_logged_in_name);
          $$("overtime_export_time").setValue("08:00");
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("btn_overtime_report_close").attachEvent(
      "onItemClick",
      function (id, e) {
        $$("overtime_report-popup").hide();
      },
    );
    $$("reports-page")
      .$$("reporting_overtime")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv === "Metro") {
          $$("reports-page")
            .$$("reporting_overtime")
            .$$("include_otr")
            .enable();
        } else {
          $$("reports-page")
            .$$("reporting_overtime")
            .$$("include_otr")
            .disable();
        }
        if (newv === "Comms") {
          $$("reports-page")
            .$$("reporting_overtime")
            .$$("include_commcen")
            .enable();
        } else {
          $$("reports-page")
            .$$("reporting_overtime")
            .$$("include_commcen")
            .disable();
        }
        load_shifts(newv);
      });
    $$("btn_export_overtime_report").attachEvent(
      "onItemClick",
      function (id, e) {
        exp_form = $$("overtime_export_info");
        exp_grid = $$("reports-page")
          .$$("reporting_overtime")
          .$$("overtime_grid");
        exp_roster = $$("reports-page")
          .$$("reporting_overtime")
          .$$("rosters")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_overtime")
          .$$("shift")
          .getText();
        let filename = "";
        if ($$("overtime_export_info").validate()) {
          form_values = exp_form.getValues();
          filename =
            "WFR Rostering Report - OVERTIME (" +
            moment(form_values.overtime_export_date, "YYYY-MM-DD H:mm").format(
              "DD-MM-YYYY",
            ) +
            ")";
          webix.toExcel(exp_grid, { filename: filename, styles: true });
          exp_grid.clearAll();
          $$("overtime_export_info").clear();
          $$("overtime_report-popup").hide();
        }
      },
    );
    $$("btn_export_email_overtime_report").attachEvent(
      "onItemClick",
      function (id, e) {
        exp_form = $$("overtime_export_info");
        exp_grid = $$("reports-page")
          .$$("reporting_overtime")
          .$$("overtime_grid");
        exp_roster = $$("reports-page")
          .$$("reporting_overtime")
          .$$("rosters")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_overtime")
          .$$("shift")
          .getText();
        let filename = "";
        if ($$("overtime_export_info").validate()) {
          form_values = exp_form.getValues();
          if (form_values.overtime_export_send_email != "") {
            if (
              isEmailAddressValid(form_values.overtime_export_send_email) ===
              true
            ) {
              filename =
                "WFR Rostering Report - OVERTIME (" +
                moment(
                  form_values.overtime_export_date,
                  "YYYY-MM-DD H:mm",
                ).format("DD-MM-YYYY") +
                ")";
              webix
                .toExcel(exp_grid, { filename: filename, styles: true })
                .then(function (blob) {
                  const reader = new FileReader();
                  reader.readAsDataURL(blob);
                  reader.onloadend = function () {
                    let base64String = reader.result;
                    if (live_site === true) {
                      sendEmailWithAttachment(
                        "SAPPHIRE<<EMAIL>>",
                        form_values.overtime_export_send_email,
                        "Overtime Report - " +
                          moment(
                            form_values.overtime_export_date,
                            "YYYY-MM-DD H:mm",
                          ).format("DD/MM/YYYY"),
                        base64String,
                        filename + ".xlsx",
                        form_values.overtime_export_duty_officer,
                      );
                      exp_grid.clearAll();
                      $$("overtime_export_info").clear();
                      $$("overtime_report-popup").hide();
                    }
                  };
                });
            } else {
              webix.alert("You must enter a valid email address!");
            }
          } else {
            webix.alert("You must select an email address!");
          }
        }
      },
    );
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page").$$("reporting_overtime").$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                $$("reports-page")
                  .$$("reporting_overtime")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_overtime")
                  .$$("shift")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateOvertimeReport(fromDate, toDate, roster, shift) {
    let grid = $$("reports-page").$$("reporting_overtime").$$("overtime_grid");
    let include_otr = $$("reports-page")
      .$$("reporting_overtime")
      .$$("include_otr")
      .getValue();
    let include_commcen = $$("reports-page")
      .$$("reporting_overtime")
      .$$("include_commcen")
      .getValue();
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/overtime_report",
        {
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          include_otr: include_otr,
          include_commcen: include_commcen,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let overtimeType = "";
              let activity = "";
              let empRank = "";
              let fin_admin = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  let shift_start_time = moment();
                  let shift_end_time = moment();
                  let bk_start_time = moment();
                  let isBetweenShift = false;
                  overtimeType = "";
                  activity = "";
                  if (data[x].actup_rank == null) {
                    empRank = data[x].rank;
                  } else if (data[x].actup_rank != data[x].rank) {
                    empRank = data[x].actup_rank;
                  } else {
                    empRank = data[x].rank;
                  }
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].leave_type_code == "AU") {
                    overtimeType = "Acting Up";
                    activity = data[x].rank + " to " + data[x].actup_rank;
                  } else if (data[x].leave_type_code == "RC") {
                    overtimeType = "Overtime";
                    activity = "Recall";
                  } else if (data[x].leave_type_code == "HB") {
                    overtimeType = "Overtime";
                    activity = "Hold Back";
                  } else if (data[x].leave_type_code == "OTFC") {
                    overtimeType = "Overtime";
                    activity = "OTFC";
                  } else if (data[x].leave_type_code == "IA") {
                    overtimeType = "Overtime";
                    activity = "Inconvenience Allowance";
                  } else if (data[x].leave_type_code == "IRD") {
                    overtimeType = "Overtime";
                    activity = "Inter Deployment";
                  } else if (data[x].leave_type_code == "IAD") {
                    overtimeType = "Overtime";
                    activity = "Intra Deployment";
                  } else if (data[x].leave_type_code == "REL") {
                    overtimeType = "Overtime";
                    activity = "Rel. Off";
                  } else if (data[x].leave_type_code == "T/E") {
                    overtimeType = "Overtime";
                    activity = "Training/Exercise";
                  } else if (data[x].leave_type_code == "OCH") {
                    overtimeType = "Overtime";
                    activity = "On Call (Higher)";
                  } else if (data[x].leave_type_code == "OCL") {
                    overtimeType = "Overtime";
                    activity = "On Call (Lower)";
                  } else if (data[x].leave_type_code == "DRILL") {
                    overtimeType = "Overtime";
                    activity = "Drill Co.";
                  }
                  bk_start_time = moment(
                    data[x].start_date,
                    "DD/MM/YYYY HH:mm",
                  );
                  if (include_otr == 1 && data[x].period == "night") {
                    shift_start_time = moment(
                      data[x].start_date.slice(0, 8) + " 16:00",
                      "DD/MM/YYYY HH:mm",
                    );
                    shift_end_time = moment(
                      data[x].start_date.slice(0, 8) + " 20:00",
                      "DD/MM/YYYY HH:mm",
                    );
                    if (
                      bk_start_time.isBetween(shift_start_time, shift_end_time)
                    ) {
                      isBetweenShift = true;
                    }
                  } else if (include_otr == 1 && data[x].period == "day") {
                    shift_start_time = moment(
                      data[x].start_date.slice(0, 8) + " 06:00",
                      "DD/MM/YYYY HH:mm",
                    );
                    shift_end_time = moment(
                      data[x].start_date.slice(0, 8) + " 10:00",
                      "DD/MM/YYYY HH:mm",
                    );
                    if (
                      bk_start_time.isBetween(shift_start_time, shift_end_time)
                    ) {
                      isBetweenShift = true;
                    }
                  }
                  if (data[x].location === "MCO Central") {
                    fin_admin = "3-511";
                  } else if (data[x].location === "Adelaide") {
                    fin_admin = "3-520";
                  } else if (data[x].location === "Beulah Park") {
                    fin_admin = "3-521";
                  } else if (data[x].location === "Woodville") {
                    fin_admin = "3-524";
                  } else if (data[x].location === "Port Adelaide") {
                    fin_admin = "3-525";
                  } else if (data[x].location === "Marine") {
                    fin_admin = "3-527";
                  } else if (data[x].location === "Largs North") {
                    fin_admin = "3-528";
                  } else if (data[x].location === "Prospect") {
                    fin_admin = "3-537";
                  } else if (data[x].location === "MCO North") {
                    fin_admin = "3-507";
                  } else if (data[x].location === "Paradise") {
                    fin_admin = "3-522";
                  } else if (data[x].location === "Oakden") {
                    fin_admin = "3-530";
                  } else if (data[x].location === "Golden Grove") {
                    fin_admin = "3-531";
                  } else if (data[x].location === "Salisbury") {
                    fin_admin = "3-532";
                  } else if (data[x].location === "Elizabeth") {
                    fin_admin = "3-533";
                  } else if (data[x].location === "Gawler") {
                    fin_admin = "3-535";
                  } else if (data[x].location === "Angle Park") {
                    fin_admin = "3-536";
                  } else if (data[x].location === "MCO South") {
                    fin_admin = "3-509";
                  } else if (data[x].location === "St Marys") {
                    fin_admin = "3-540";
                  } else if (data[x].location === "Camden Park") {
                    fin_admin = "3-541";
                  } else if (data[x].location === "O'Halloran Hill") {
                    fin_admin = "3-542";
                  } else if (data[x].location === "Noarlunga") {
                    fin_admin = "3-543";
                  } else if (data[x].location === "Glen Osmond") {
                    fin_admin = "3-544";
                  } else if (data[x].location === "Brooklyn Park") {
                    fin_admin = "3-545";
                  } else if (data[x].location === "Seaford") {
                    fin_admin = "3-546";
                  } else if (data[x].location === "OTR Central") {
                    fin_admin = "3-513";
                  } else if (data[x].location === "OTR North") {
                    fin_admin = "3-513";
                  } else if (data[x].location === "OTR South") {
                    fin_admin = "3-513";
                  } else if (data[x].location === "Adelaide Comms") {
                    fin_admin = "3-453";
                  } else if (data[x].location === "Port Pirie") {
                    fin_admin = "3-650";
                  } else if (data[x].location === "Mt Gambier") {
                    fin_admin = "3-670";
                  } else {
                    fin_admin = "";
                  }
                  if (
                    include_otr == 1 &&
                    data[x].period == "day" &&
                    isBetweenShift === false &&
                    data[x].shift == "OTR"
                  ) {
                  } else if (
                    include_otr == 1 &&
                    data[x].period == "off" &&
                    data[x].shift == "OTR"
                  ) {
                  } else if (
                    include_otr == 1 &&
                    data[x].period == "night" &&
                    isBetweenShift === false &&
                    data[x].shift == "OTR"
                  ) {
                  } else {
                    bookings.push({
                      pay_id: data[x].pay_id,
                      employee: empName.toUpperCase(),
                      rank: empRank,
                      location: data[x].location,
                      shift: data[x].shift,
                      type: overtimeType,
                      activity: activity,
                      start_date: data[x].start_date.slice(0, 8),
                      start_time: data[x].start_date.slice(9, 14),
                      end_date: data[x].end_date.slice(0, 8),
                      end_time: data[x].end_date.slice(9, 14),
                      total_hours: data[x].total_hours,
                      comments: data[x].comments,
                      fin_admin: fin_admin,
                    });
                  }
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            $$("reports-page")
              .$$("reporting_overtime")
              .$$("records_count")
              .define("template", bookings.length + " bookings found!");
            $$("reports-page")
              .$$("reporting_overtime")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
