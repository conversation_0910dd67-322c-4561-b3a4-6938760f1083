let bookingErrorsReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate;
  function initApplication() {
    eventHandlers();
    exp_grid = $$("reports-page")
      .$$("reporting_booking_errors")
      .$$("booking_errors_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report for All Locations from date " +
            moment(exp_fromDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Booking Errors Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_booking_errors")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_booking_errors")
          .$$("from")
          .getValue();
        generateBookingErrorsReport(fromDate);
      });
    $$("reports-page")
      .$$("reporting_booking_errors")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_booking_errors")
          .$$("from")
          .getValue();
        let filename =
          "Booking Errors Report - (" +
          moment(exp_fromDate).format("DD-MM-YYYY") +
          ")";
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, { filename: filename, styles: true });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  function generateBookingErrorsReport(fromDate) {
    let grid = $$("reports-page")
      .$$("reporting_booking_errors")
      .$$("booking_errors_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/booking_errors_report",
        { from_date: fromDate },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName,
                    ra_rank: data[x].ra_rank,
                    leave_code: data[x].leave_type_code,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    start_date: data[x].start_date,
                    end_date: data[x].end_date,
                    ra_roster: data[x].ra_roster,
                    ra_shift: data[x].ra_shift,
                    ra_location: data[x].ra_location,
                    total_hours: data[x].hours,
                    comments: data[x].comments,
                    created_by: data[x].created_by,
                    created_date: data[x].created_date,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
