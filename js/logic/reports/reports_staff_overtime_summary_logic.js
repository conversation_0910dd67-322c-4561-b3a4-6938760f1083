let staffOvertimeSummaryReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
  };
  let grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster;
  let exp_shift;
  let exp_location;
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("to_date")
      .setValue(endOfMonth);
    $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    grid = $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("staff_overtime_summary_grid");
    let defaultHandler = grid.$exportView;
    grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Show total hours worked for Recalls by employee - " +
            "(" +
            exp_roster +
            " / " +
            exp_shift +
            " / " +
            exp_location +
            ")",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Overtime Staff Summary Report - (Recalls)",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("shift_filter")
          .setValue("");
        $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("location_filter")
          .setValue("");
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_staff_overtime_summary")
            .$$("shift_filter")
            .hide();
          $$("reports-page")
            .$$("reporting_staff_overtime_summary")
            .$$("location_filter")
            .hide();
        } else {
          $$("reports-page")
            .$$("reporting_staff_overtime_summary")
            .$$("shift_filter")
            .show();
          $$("reports-page")
            .$$("reporting_staff_overtime_summary")
            .$$("location_filter")
            .show();
          $$("reports-page")
            .$$("reporting_staff_overtime_summary")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_staff_overtime_summary")
            .$$("location_filter")
            .setValue("-- All Stations --");
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("roster_filter")
          .getValue();
        $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("location_filter")
          .setValue("");
        load_shifts(roster);
      });
    $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("from_date")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("to_date")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("roster_filter")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("shift_filter")
          .getText();
        let location = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("location_filter")
          .getText();
        if (roster != "-- All Rosters --") {
          if (location == "") {
            webix.alert("You must select a Location before you can Search!");
          } else {
            generateStaffOvertimeSummaryReport(
              fromDate,
              toDate,
              roster,
              shift,
              location,
            );
          }
        } else {
          generateStaffOvertimeSummaryReport(
            fromDate,
            toDate,
            roster,
            shift,
            location,
          );
        }
      });
    $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("from_date")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("to_date")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_staff_overtime_summary")
          .$$("location_filter")
          .getText();
        if (grid.count() > 0) {
          webix.toExcel(grid, {
            filename:
              "Report - STAFF OVERTIME SUMMARY (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let locationsArray = shiftList.data[0].locations.split(",");
                shiftArray.unshift("-- All Shifts --");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("reports-page")
                  .$$("reporting_staff_overtime_summary")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_staff_overtime_summary")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_staff_overtime_summary")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_staff_overtime_summary")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateStaffOvertimeSummaryReport(
    fromDate,
    toDate,
    roster,
    shift,
    location,
  ) {
    let red_fatigue = $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("red_fatigue_filter")
      .getValue();
    let grid = $$("reports-page")
      .$$("reporting_staff_overtime_summary")
      .$$("staff_overtime_summary_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/staff_overtime_summary_report",
        {
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          location: location,
          red_fatigue: red_fatigue,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let grand_total_hours = 0;
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  grand_total_hours = grand_total_hours + data[x].total_hours;
                  bookings.push({
                    pay_id: data[x].pay_id,
                    name: empName.toUpperCase(),
                    rank: data[x].rank,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    total_hours: data[x].total_hours,
                  });
                }
                if (red_fatigue == 1) {
                  bookings.push({
                    pay_id: "",
                    name: "",
                    rank: "",
                    roster: "",
                    shift: "",
                    location: "GRAND TOTAL HOURS",
                    total_hours: grand_total_hours,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            if (red_fatigue == 1) {
              let totals_row_id = grid.getLastId();
              grid.addRowCss(totals_row_id, "totals_row");
            }
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
