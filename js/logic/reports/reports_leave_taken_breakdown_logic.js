let leaveTakenBreakdownReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
    3: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster;
  let exp_shift;
  let exp_location;
  let exp_display;
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("shift_filter")
      .define("options", ["-- All Shifts --"]);
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("location_filter")
      .define("options", ["-- All Locations --"]);
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("to_date")
      .setValue(endOfMonth);
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("employee_filter")
      .setValue(1);
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("shift_filter")
      .setValue("-- All Shifts --");
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("location_filter")
      .setValue("-- All Locations --");
    exp_grid = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("leave_taken_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Results Format Type: " + exp_display,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report period " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Actual Leave Taken Report for " +
            exp_roster +
            " | " +
            exp_shift +
            " | " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("from_date")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("to_date")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("location_filter")
          .getText();
        exp_display = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("display_filter")
          .getValue();
        if (exp_grid.count() > 0) {
          $$("loader-window").show();
          webix.toExcel(exp_grid, {
            filename:
              "Actual Leave Taken Report (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
            heights: true,
          });
          $$("loader-window").hide();
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("shift_filter")
          .setValue("");
        $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("location_filter")
          .setValue("");
        load_shifts(newv);
        $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("shift_filter")
          .setValue("-- All Shifts --");
        $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("location_filter")
          .setValue("-- All Locations --");
      });
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("roster_filter")
          .getValue();
        let location = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("location_filter")
          .getValue();
        let leave_type = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("leave_filter")
          .getValue();
        if (roster == "-- All Rosters --") {
          if (leave_type == "") {
            webix.alert({
              text: "You must specify at least 1 'Leave Type'.",
              width: 450,
            });
          } else {
            getBookings();
          }
        } else {
          if (location == "") {
            webix.alert("You must specify a 'Location'.");
          } else {
            if (leave_type == "") {
              webix.alert({
                text: "You must specify at least 1 'Leave Type'.",
                width: 450,
              });
            } else {
              getBookings();
            }
          }
        }
      });
    $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_leave_taken_breakdown")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({
          id: value.id,
          value: value.value + " (" + value.id + ")",
        });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  leave_types_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("leave_filter");
    if (data) {
      let options = [];
      let leave_types = JSON.parse(data);
      leave_types.forEach(function (value) {
        options.push({ id: value.code, value: value.code });
      });
      select.define("options", options);
      select.refresh();
      select.getPopup().queryView({ labelRight: "Select all" }).show();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                shiftArray.unshift("-- All Shifts --");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Locations --");
                $$("reports-page")
                  .$$("reporting_leave_taken_breakdown")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_leave_taken_breakdown")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_leave_taken_breakdown")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_leave_taken_breakdown")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function getBookings() {
    let pay_id = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("employee_filter")
      .getValue();
    let fromDate = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("from_date")
      .getValue();
    let toDate = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("to_date")
      .getValue();
    let roster = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("roster_filter")
      .getText();
    let shift = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("shift_filter")
      .getText();
    let location = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("location_filter")
      .getText();
    let leave_type = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("leave_filter")
      .getValue();
    let display = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("display_filter")
      .getValue();
    let grid = $$("reports-page")
      .$$("reporting_leave_taken_breakdown")
      .$$("leave_taken_grid");
    grid.clearAll();
    $$("loader-window").show();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/get_leave_taken_breakdown_report",
        {
          pay_id: pay_id,
          start_date: moment(fromDate).format("YYYY-MM-DD 00:00:01"),
          end_date: moment(toDate).format("YYYY-MM-DD 23:59:59"),
          roster: roster,
          shift: shift,
          location: location,
          leave_type: leave_type,
          display: display,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let result = JSON.parse(results);
            let search_results = [];
            let location = "";
            let middleName = "";
            let comments = "";
            let booking_first_date = "";
            let booking_last_date = "";
            if (result.length > 0) {
              for (let x = 0; x < result.length; x++) {
                if (result[x].location === null) {
                  location = "";
                } else {
                  location = result[x].location;
                }
                if (result[x].middle_name === null) {
                  middleName = "";
                } else {
                  middleName = result[x].middle_name;
                }
                if (
                  result[x].request_comments == "" ||
                  result[x].request_comments == null
                ) {
                  comments = result[x].comments;
                } else {
                  comments = result[x].request_comments;
                }
                booking_first_date = moment(
                  result[x].booking_first_date,
                  "DD/MM/YYYY H:mm",
                ).toDate();
                booking_last_date = moment(
                  result[x].booking_last_date,
                  "DD/MM/YYYY H:mm",
                ).toDate();
                search_results.push({
                  booking_id: result[x].booking_id,
                  service_no: result[x].pay_id,
                  name:
                    result[x].surname +
                    ", " +
                    result[x].first_name +
                    " " +
                    middleName,
                  rank: result[x].rank,
                  roster: result[x].roster,
                  shift: result[x].shift,
                  location: location,
                  booking_first_date: booking_first_date,
                  booking_last_date: booking_last_date,
                  total_hours: result[x].total_hours,
                  total_days: result[x].total_days,
                  code: result[x].leave_type_code,
                  date_created: result[x].date_created,
                  created_by: result[x].created_by,
                  approved_denied_date: result[x].approved_denied_date,
                  approved_denied_by: result[x].approved_denied_by,
                  comments: comments,
                });
              }
              grid.define("data", search_results);
              grid.refresh();
            }
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
