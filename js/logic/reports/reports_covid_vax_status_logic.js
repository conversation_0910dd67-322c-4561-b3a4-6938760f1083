let covidVaxStatusReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let vax_status = "";
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("status_filter")
      .getPopup()
      .queryView({ labelRight: "Select all" })
      .show();
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("shift_filter")
      .setValue("-- All Shifts --");
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("location_filter")
      .setValue("-- All Stations --");
    exp_grid = $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("covid_vax_status_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results filtered by Roster: " +
            exp_roster +
            " / " +
            exp_shift +
            " / " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["COVID Vaccination Status Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("show_address")
      .attachEvent("onChange", function (newv, oldv) {
        let grid = $$("reports-page")
          .$$("reporting_covid_vax_status")
          .$$("covid_vax_status_grid");
        if (newv == 0) {
          grid.hideColumn("home_address");
        } else {
          grid.showColumn("home_address");
          grid.adjustColumn("home_address", "data");
        }
      });
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateVaxReport();
      });
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("reports-page")
          .$$("reporting_covid_vax_status")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_covid_vax_status")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_covid_vax_status")
          .$$("location_filter")
          .getText();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "COVID Vaccination Status Report - (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_covid_vax_status")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_covid_vax_status")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("reports-page")
            .$$("reporting_covid_vax_status")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_covid_vax_status")
            .$$("location_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_covid_vax_status")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_covid_vax_status")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_covid_vax_status")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("reports-page")
                  .$$("reporting_covid_vax_status")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_covid_vax_status")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_covid_vax_status")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_covid_vax_status")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateVaxReport() {
    let grid = $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("covid_vax_status_grid");
    let roster = $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("roster_filter")
      .getValue();
    let shift = $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("shift_filter")
      .getValue();
    let location = $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("location_filter")
      .getValue();
    vax_status = $$("reports-page")
      .$$("reporting_covid_vax_status")
      .$$("status_filter")
      .getText();
    let vax_array = vax_status.split(",");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/covid_vax_status_report",
        { roster: roster, shift: shift, location: location },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let first_dose_date = "";
              let second_dose_date = "";
              let booster_date = "";
              let verified = "";
              let digital_cert_received = "";
              let vax_form_received = "";
              let status = "";
              let exemption_date = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].first_dose_date == "Invalid date") {
                    first_dose_date = "";
                  } else {
                    first_dose_date = data[x].first_dose_date;
                  }
                  if (data[x].second_dose_date == "Invalid date") {
                    second_dose_date = "";
                  } else {
                    second_dose_date = data[x].second_dose_date;
                  }
                  if (data[x].booster_date == "Invalid date") {
                    booster_date = "";
                  } else {
                    booster_date = data[x].booster_date;
                  }
                  if (
                    data[x].verified == false ||
                    data[x].verified == null ||
                    data[x].verified == 0
                  ) {
                    verified = "No";
                  } else {
                    verified = "Yes";
                  }
                  if (
                    data[x].digital_cert_received == false ||
                    data[x].digital_cert_received == null ||
                    data[x].digital_cert_received == 0
                  ) {
                    digital_cert_received = "No";
                  } else {
                    digital_cert_received = "Yes";
                  }
                  if (
                    data[x].vax_form_received == false ||
                    data[x].vax_form_received == null ||
                    data[x].vax_form_received == 0
                  ) {
                    vax_form_received = "No";
                  } else {
                    vax_form_received = "Yes";
                  }
                  if (data[x].exemption_date == "Invalid date") {
                    exemption_date = "";
                  } else {
                    exemption_date = moment(
                      data[x].exemption_date,
                      "DD/MM/YYYY",
                    ).toDate();
                  }
                  let curr_date = moment();
                  let first_date = moment(
                    data[x].first_dose_date,
                    "DD/MM/YYYY",
                  );
                  let second_date = moment(
                    data[x].second_dose_date,
                    "DD/MM/YYYY",
                  );
                  let exempt_expiry_date = moment(
                    data[x].exemption_date + " 23:59",
                    "DD/MM/YYYY H:mm",
                  );
                  if (
                    data[x].status == "Medically Exempt" &&
                    exempt_expiry_date.isSameOrAfter(curr_date) === true
                  ) {
                    status =
                      "COMPLETE - All current vaccination requirements met";
                  } else {
                    if (data[x].first_dose_date == "Invalid date") {
                      if (
                        data[x].vax_form_received == false ||
                        data[x].vax_form_received == null
                      ) {
                        status =
                          "NOT COMPLETE - No vaccinations (Not reported)";
                      } else {
                        status = "NOT COMPLETE - No vaccinations (Reported)";
                      }
                    } else if (
                      data[x].first_dose_date != "Invalid date" &&
                      data[x].second_dose_date == "Invalid date"
                    ) {
                      if (curr_date.diff(first_date, "days", false) > 183) {
                        status = "NOT COMPLETE - 2nd vaccination not received";
                      } else {
                        status = "NOT COMPLETE - Waiting on 2nd vaccine";
                      }
                    } else if (
                      data[x].first_dose_date != "Invalid date" &&
                      data[x].second_dose_date != "Invalid date" &&
                      data[x].booster_date == "Invalid date"
                    ) {
                      if (curr_date.diff(second_date, "days", false) > 183) {
                        status =
                          "NOT COMPLETE - Booster vaccination not received";
                      } else {
                        if (
                          data[x].digital_cert_received == true &&
                          data[x].vax_form_received == true
                        ) {
                          status =
                            "COMPLETE - All current vaccination requirements met";
                        } else if (
                          data[x].digital_cert_received == true &&
                          (data[x].vax_form_received == false ||
                            data[x].vax_form_received == null)
                        ) {
                          status = "NOT COMPLETE - Waiting on vax form";
                        } else if (
                          (data[x].digital_cert_received == false ||
                            data[x].digital_cert_received == null) &&
                          data[x].vax_form_received == true
                        ) {
                          status = "NOT COMPLETE - Waiting on digital cert";
                        } else if (
                          (data[x].digital_cert_received == false ||
                            data[x].digital_cert_received == null) &&
                          (data[x].vax_form_received == false ||
                            data[x].vax_form_received == null)
                        ) {
                          status =
                            "NOT COMPLETE - Waiting on digital cert & vax form";
                        }
                      }
                    } else if (
                      data[x].first_dose_date != "Invalid date" &&
                      data[x].second_dose_date != "Invalid date" &&
                      data[x].booster_date != "Invalid date"
                    ) {
                      if (
                        data[x].digital_cert_received == true &&
                        data[x].vax_form_received == true
                      ) {
                        status =
                          "COMPLETE - All current vaccination requirements met";
                      } else if (
                        data[x].digital_cert_received == true &&
                        (data[x].vax_form_received == false ||
                          data[x].vax_form_received == null)
                      ) {
                        status = "NOT COMPLETE - Waiting on vax form";
                      } else if (
                        (data[x].digital_cert_received == false ||
                          data[x].digital_cert_received == null) &&
                        data[x].vax_form_received == true
                      ) {
                        status = "NOT COMPLETE - Waiting on digital cert";
                      } else if (
                        (data[x].digital_cert_received == false ||
                          data[x].digital_cert_received == null) &&
                        data[x].vax_form_receiveived == null
                      ) {
                        status =
                          "NOT COMPLETE - Waiting on digital cert & vax form";
                      }
                    }
                  }
                  reportData.push({
                    pay_id: data[x].pay_id,
                    mobile_no: data[x].personal_mobile_no,
                    rank: data[x].rank,
                    employee: empName,
                    home_address:
                      data[x].street +
                      ", " +
                      data[x].suburb +
                      " " +
                      data[x].state +
                      " " +
                      data[x].post_code,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    first_dose_date: first_dose_date,
                    second_dose_date: second_dose_date,
                    booster_date: booster_date,
                    status: status,
                    exemption_date: exemption_date,
                    cert_received: digital_cert_received,
                    form_received: vax_form_received,
                    verified: verified,
                  });
                }
                grid.define("data", reportData);
                grid.refresh();
                grid.eachRow(function (row) {
                  let record = grid.getItem(row);
                  if (record.verified == "Yes") {
                    grid.addCellCss(row, "verified", "approved_status");
                  } else {
                    grid.addCellCss(row, "verified", "denied_status");
                  }
                  if (
                    record.status.includes(
                      "Booster vaccination not received",
                    ) === true
                  ) {
                    grid.addCellCss(row, "status", "booster_status");
                  } else if (record.status.includes("NOT COMPLETE") === true) {
                    grid.addCellCss(row, "status", "denied_status");
                  } else {
                    grid.addCellCss(row, "status", "complete_status");
                  }
                });
                if (vax_array.length < 9 && vax_status != "") {
                  grid.filter((obj) => filter_status(obj));
                }
              }
            }
            $$("loader-window").hide();
          },
        },
      );
  }
  function filter_status(t) {
    if (vax_status.includes("&amp;") === true) {
      vax_status = vax_status.replace("&amp;", "&");
    }
    let vax_array = vax_status.split(",");
    if (vax_array.includes(t.status)) {
      return t;
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
