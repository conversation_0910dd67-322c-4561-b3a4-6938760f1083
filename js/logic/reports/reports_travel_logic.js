let travelReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate = "";
  let exp_toDate = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page").$$("reporting_travel").$$("from").setValue(startOfMonth);
    $$("reports-page").$$("reporting_travel").$$("to").setValue(endOfMonth);
    $$("reports-page").$$("reporting_travel").$$("status_filter").setValue(1);
    $$("reports-page").$$("reporting_travel").$$("employee_filter").setValue(1);
    exp_grid = $$("reports-page").$$("reporting_travel").$$("travel_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "All Locations" +
            "                                                      " +
            "Note: please process all payments in accordance to the information provided on this report!",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Travel Records Log by Approved Date" +
            "              " +
            "Selected Date Range: " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_travel")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_travel")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_travel")
          .$$("to")
          .getValue();
        generateTravelReport(fromDate, toDate);
      });
    $$("reports-page")
      .$$("reporting_travel")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_travel")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_travel")
          .$$("to")
          .getValue();
        let travel_export_array = [];
        let filename =
          "Shared Services Travel - " +
          moment(exp_fromDate).format("DD-MM-YYYY") +
          " to " +
          moment(exp_toDate).format("DD-MM-YYYY") +
          " created on " +
          moment().format("DD-MM-YYYY");
        let base64String = "";
        webix.confirm({
          title: "Select Export Option",
          ok: "Excel Export Only",
          cancel: "Export and Process",
          css: "sm_prompt_window",
          width: 600,
          text: "<strong>Export Only:</strong> this option will just generate the Excel file with entries</br></br><strong>Export & Process:</strong> this option will generate the Excel file and process all entries (Entries will be marked as Exported and a Travel Summary email will be sent to each travel claimant)",
          callback: function (result) {
            switch (result) {
              case false:
                if (exp_grid.count() > 0) {
                  $$("loader-window").show();
                  exp_grid.showColumn("cost_centre");
                  setTimeout(function () {
                    webix
                      .toExcel(exp_grid, {
                        filename: filename,
                        styles: true,
                        ignore: { exported: true, approved_by: true },
                        name: "All Locations",
                      })
                      .then(function (blob) {
                        const reader = new FileReader();
                        reader.readAsDataURL(blob);
                        reader.onloadend = function () {
                          base64String = reader.result;
                        };
                      });
                    exp_grid.hideColumn("cost_centre");
                    $$("loader-window").hide();
                  }, 250);
                  webix.confirm({
                    title: "Confirm Processing Travel Claims!",
                    ok: "Yes",
                    cancel: "No",
                    width: 650,
                    text:
                      "You are about to process " +
                      exp_grid.count() +
                      " travel claims.</br></br>Note: This process will send an email to all employees claiming travel with a detailed summary of each claim plus a copy of each email will be sent to '<EMAIL>'</br></br>This process can take several minutes to complete based on the number of claims.</br></br>Once all the entries are processed they will be marked as 'Exported'</br></br>Do you want to continue?</br>",
                    callback: function (result) {
                      switch (result) {
                        case true:
                          $$("loader-window").show();
                          exp_grid.eachRow(function (row) {
                            const travel_object = {};
                            let record = exp_grid.getItem(row);
                            travel_object.db_id = record.db_id;
                            travel_object.travel_id = record.travel_id;
                            travel_object.pay_id = record.pay_id;
                            travel_object.date_string = record.date_string;
                            travel_object.distance = record.distance;
                            travel_object.route = record.route;
                            travel_object.equipment = record.equipment;
                            travel_object.employee = record.employee;
                            travel_export_array.push(travel_object);
                          });
                          webix
                            .ajax()
                            .headers({ Authorization: "Bearer " + api_key })
                            .post(
                              server_url + "/admin/update_travel_log_status",
                              {
                                travel_export_array: travel_export_array,
                                date_range:
                                  moment(exp_fromDate).format("DD/MM/YYYY") +
                                  " - " +
                                  moment(exp_toDate).format("DD/MM/YYYY"),
                              },
                              {
                                error: function (err) {
                                  $$("loader-window").hide();
                                  webix.alert({
                                    text:
                                      "There was an error processing one or more entries!</br>Error: " +
                                      err,
                                    width: 550,
                                  });
                                },
                                success: function () {
                                  generateTravelReport(
                                    exp_fromDate,
                                    exp_toDate,
                                  );
                                  $$("loader-window").hide();
                                  webix.alert(
                                    "Processing completed successfully!",
                                  );
                                },
                              },
                            );
                      }
                    },
                  });
                } else {
                  webix.alert("No data to Export!");
                }
                break;
              case true:
                if (exp_grid.count() > 0) {
                  $$("loader-window").show();
                  exp_grid.showColumn("cost_centre");
                  setTimeout(function () {
                    webix.toExcel(exp_grid, {
                      filename: filename,
                      styles: true,
                      ignore: { exported: true, approved_by: true },
                      name: "All Locations",
                    });
                    exp_grid.hideColumn("cost_centre");
                    $$("loader-window").hide();
                  }, 250);
                } else {
                  webix.alert("No data to Export!");
                }
                break;
            }
          },
        });
      });
  }
  function generateTravelReport(fromDate, toDate) {
    let grid = $$("reports-page").$$("reporting_travel").$$("travel_grid");
    let show_exported = $$("reports-page")
      .$$("reporting_travel")
      .$$("show_exported")
      .getValue();
    let show_negative = $$("reports-page")
      .$$("reporting_travel")
      .$$("show_negative")
      .getValue();
    let status = $$("reports-page")
      .$$("reporting_travel")
      .$$("status_filter")
      .getText();
    let pay_id = $$("reports-page")
      .$$("reporting_travel")
      .$$("employee_filter")
      .getValue();
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/travel_report",
        {
          from_date: fromDate,
          to_date: toDate,
          show_exported: show_exported,
          status: status,
          pay_id: pay_id,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let travel_logs = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let withPPE = "";
              let time_allowance = "";
              let approval_date = "";
              let approved_by = "";
              let meals = "";
              let is_exported = "";
              let distance = 0;
              let cost_centre = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  distance = data[x].distance;
                  if (distance > 0 || show_negative == 1) {
                    if (data[x].middle_name == null) {
                      empName = data[x].surname + ", " + data[x].first_name;
                    } else {
                      empName =
                        data[x].surname +
                        ", " +
                        data[x].first_name +
                        " " +
                        data[x].middle_name;
                    }
                    if (data[x].with_ppe == true) {
                      withPPE = "Yes";
                    } else {
                      withPPE = "No";
                    }
                    if (
                      data[x].route_from == "Res" ||
                      data[x].route_to == "Res"
                    ) {
                      time_allowance = "Yes";
                    } else {
                      time_allowance = "No";
                    }
                    if (data[x].route_from == "Res" && data[x].route_to == 50) {
                      time_allowance = "No";
                    } else if (
                      data[x].route_from == 50 &&
                      data[x].route_to == "Res"
                    ) {
                      time_allowance = "No";
                    }
                    if (data[x].route_from == "Res" && data[x].route_to == 70) {
                      time_allowance = "No";
                    } else if (
                      data[x].route_from == 70 &&
                      data[x].route_to == "Res"
                    ) {
                      time_allowance = "No";
                    }
                    if (data[x].route_from == 50 || data[x].route_to == 50) {
                      if (
                        data[x].pp_travel_type === "Recall" ||
                        (data[x].bk_code != null &&
                          data[x].bk_code.includes("RC"))
                      ) {
                        time_allowance = "Yes";
                      }
                    }
                    if (data[x].status == "Pending") {
                      approval_date = "";
                      approved_by = "";
                    } else {
                      approval_date = data[x].approved_denied_date;
                      approved_by = data[x].approved_denied_by;
                    }
                    if (data[x].route_to == 50 && data[x].route_from == "Res") {
                      if (
                        data[x].pp_travel_type == "Recall" ||
                        data[x].pp_travel_type == "Other"
                      ) {
                        meals = "0";
                      } else {
                        meals = "11";
                      }
                    } else if (
                      data[x].route_to == 70 &&
                      data[x].route_from == "Res"
                    ) {
                      meals = "0";
                    } else if (
                      data[x].route_to == "Res" &&
                      data[x].route_from == 70
                    ) {
                      meals = "0";
                    } else {
                      meals = "";
                    }
                    if (data[x].exported == true) {
                      is_exported = "Yes";
                    } else {
                      is_exported = "No";
                    }
                    if (data[x].route_to == 50 || data[x].route_from == 50) {
                      cost_centre = "3-650";
                    } else {
                      cost_centre = "";
                    }
                    travel_logs.push({
                      db_id: data[x].id,
                      travel_id: data[x].travel_id,
                      pay_id: data[x].pay_id[0],
                      employee: empName.toUpperCase(),
                      rank: data[x].rank,
                      roster: data[x].roster,
                      shift: data[x].shift,
                      location: data[x].location,
                      route: data[x].route_from + "-" + data[x].route_to,
                      travel_date: data[x].travel_date,
                      date_string: data[x].date_string,
                      distance: distance,
                      time_allowance: time_allowance,
                      bk_code: data[x].bk_code,
                      bk_desc: data[x].bk_desc,
                      time: "",
                      meals: meals,
                      equipment: withPPE,
                      status: data[x].status,
                      approved_by: approved_by,
                      approved_denied_date: approval_date,
                      created_by: data[x].created_by,
                      created_date: data[x].created_date,
                      exported: is_exported,
                      cost_centre: cost_centre,
                    });
                  }
                }
              }
            }
            grid.define("data", travel_logs);
            grid.refresh();
            $$("reports-page")
              .$$("reporting_travel")
              .$$("records_count")
              .define("template", travel_logs.length + " travel logs found!");
            $$("reports-page")
              .$$("reporting_travel")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_travel")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
