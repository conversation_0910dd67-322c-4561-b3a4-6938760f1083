let rosterArrangementsSummaryReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("employee_filter")
      .setValue(1);
    $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    exp_grid = $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("roster_arrangement_summary_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results filtered by Roster: " +
            exp_roster +
            " | " +
            exp_shift +
            " | " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Roster Arrangements Summary Report",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let roster = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("roster_filter")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("shift_filter")
          .getText();
        let location = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("location_filter")
          .getText();
        let payId = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("employee_filter")
          .getValue();
        generateRASummaryReport(roster, shift, location, payId);
      });
    $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("location_filter")
          .getText();
        exp_roster = exp_roster.replaceAll("--", "");
        exp_shift = exp_shift.replaceAll("--", "");
        exp_location = exp_location.replaceAll("--", "");
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Roster Arrangements Summary Report - (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_ra_summary")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_ra_summary")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("reports-page")
            .$$("reporting_ra_summary")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_ra_summary")
            .$$("location_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_ra_summary")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_ra_summary")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_ra_summary")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("reports-page")
                  .$$("reporting_ra_summary")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_ra_summary")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_ra_summary")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_ra_summary")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateRASummaryReport(roster, shift, location, payId) {
    let grid = $$("reports-page")
      .$$("reporting_ra_summary")
      .$$("roster_arrangement_summary_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/roster_arrangements_summary_report",
        { roster: roster, shift: shift, location: location, pay_id: payId },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let ra_end_date = "";
              let days_period = "";
              let au_hd = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].acting_up === 1 || data[x].acting_up === true) {
                    au_hd = "Yes";
                  } else {
                    au_hd = "No";
                  }
                  reportData.push({
                    pay_id: data[x].pay_id,
                    name: empName.toUpperCase(),
                    rank: data[x].rank,
                    acting_up: au_hd,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    start_date: data[x].ra_start_date,
                    end_date: data[x].ra_end_date,
                    period: data[x].period,
                  });
                }
              }
            }
            grid.define("data", reportData);
            grid.refresh();
            let nowDate = moment().toDate();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (
                moment(nowDate).isBetween(
                  moment(record.start_date, "DD/MM/YYYY"),
                  moment(record.end_date, "DD/MM/YYYY"),
                )
              ) {
                grid.addRowCss(row, "ra_report_row_highlight");
              }
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
