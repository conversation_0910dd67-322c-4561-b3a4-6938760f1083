let sicknessReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate = "";
  let exp_toDate = "";
  let exp_roster = "";
  let exp_shift = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_sickness")
      .$$("from")
      .setValue(startOfMonth);
    $$("reports-page").$$("reporting_sickness").$$("to").setValue(endOfMonth);
    exp_grid = $$("reports-page").$$("reporting_sickness").$$("sickness_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Show Sickness Bookings For: " + exp_shift + " (" + exp_roster + ")",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Sickness by Current Location Report",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_sickness")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_sickness")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_sickness")
          .$$("to")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_sickness")
          .$$("rosters")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_sickness")
          .$$("shift")
          .getText();
        if (roster == "" || shift == "") {
          webix.alert(
            "You must select a Roster & Shift before you can Search!",
          );
        } else {
          generateSicknessReport(fromDate, toDate, roster, shift);
        }
      });
    $$("reports-page")
      .$$("reporting_sickness")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_sickness")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_sickness")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_sickness")
          .$$("to")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_sickness")
          .$$("rosters")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_sickness")
          .$$("shift")
          .getText();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "WFR Rostering Report - SICKNESS (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
            ignore: { rank: true },
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page").$$("reporting_sickness").$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                $$("reports-page")
                  .$$("reporting_sickness")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_sickness")
                  .$$("shift")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateSicknessReport(fromDate, toDate, roster, shift) {
    let grid = $$("reports-page").$$("reporting_sickness").$$("sickness_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/sickness_report",
        { from_date: fromDate, to_date: toDate, roster: roster, shift: shift },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName.toUpperCase(),
                    rank: data[x].rank,
                    shift: data[x].shift,
                    location: data[x].location,
                    start_date: data[x].start_date.slice(0, 10),
                    start_time: data[x].start_date.slice(11, 16),
                    end_date: data[x].end_date.slice(0, 10),
                    end_time: data[x].end_date.slice(11, 16),
                    total_hours: data[x].hours,
                    code: data[x].leave_type_code,
                    description: data[x].leave_type_description,
                    created_by: data[x].created_by,
                    created_date: data[x].created_date,
                    had_SB: data[x].had_SB,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (record.had_SB == 1) {
                grid.addRowCss(row, "report_row_highlight");
              }
            });
            $$("reports-page")
              .$$("reporting_sickness")
              .$$("records_count")
              .define("template", bookings.length + " bookings found!");
            $$("reports-page")
              .$$("reporting_sickness")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
