let staffMovementReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  let exp_fromDate = "";
  let exp_toDate = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("to_date")
      .setValue(endOfMonth);
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    exp_grid = $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("staff_movement_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Show Shift Adjustments for all active entries made at: " +
            exp_roster +
            " / " +
            exp_shift +
            " / " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Shift Adjustment Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("shift_filter")
          .setValue("");
        $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("location_filter")
          .setValue("");
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_staff_movement")
            .$$("shift_filter")
            .hide();
          $$("reports-page")
            .$$("reporting_staff_movement")
            .$$("location_filter")
            .hide();
        } else {
          $$("reports-page")
            .$$("reporting_staff_movement")
            .$$("shift_filter")
            .show();
          $$("reports-page")
            .$$("reporting_staff_movement")
            .$$("location_filter")
            .show();
          $$("reports-page")
            .$$("reporting_staff_movement")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_staff_movement")
            .$$("location_filter")
            .setValue("-- All Stations --");
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("roster_filter")
          .getValue();
        $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("location_filter")
          .setValue("");
        load_shifts(roster);
      });
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("from_date")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("to_date")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("roster_filter")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("shift_filter")
          .getText();
        let location = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("location_filter")
          .getText();
        if (roster != "-- All Rosters --") {
          if (location == "") {
            webix.alert("You must select a Location before you can Search!");
          } else {
            generateStaffMovementReport(
              fromDate,
              toDate,
              roster,
              shift,
              location,
            );
          }
        } else {
          generateStaffMovementReport(
            fromDate,
            toDate,
            roster,
            shift,
            location,
          );
        }
      });
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("from_date")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("to_date")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_staff_movement")
          .$$("location_filter")
          .getText();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "WFR Rostering Report - STAFF MOVEMENT (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let locationsArray = shiftList.data[0].locations.split(",");
                shiftArray.unshift("-- All Shifts --");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                if (
                  $$("reports-page")
                    .$$("reporting_staff_movement")
                    .$$("shift_filter")
                    .getValue() == "-- All Shifts --"
                ) {
                } else {
                  locationOptions.unshift("-- All Stations --");
                }
                $$("reports-page")
                  .$$("reporting_staff_movement")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_staff_movement")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_staff_movement")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_staff_movement")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateStaffMovementReport(
    fromDate,
    toDate,
    roster,
    shift,
    location,
  ) {
    let grid = $$("reports-page")
      .$$("reporting_staff_movement")
      .$$("staff_movement_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/staff_movement_report",
        {
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          location: location,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let fromLocation = "";
              let toLocation = "";
              let priorNotice = "";
              let withPPE = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].from_station_id == data[x].to_station_id) {
                  } else if (data[x].from_station_id == 19) {
                    if (data[x].to_station_id == 20) {
                      continue;
                    } else if (data[x].to_station_id == 29) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 20) {
                    if (data[x].to_station_id == 19) {
                      continue;
                    } else if (data[x].to_station_id == 29) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 29) {
                    if (data[x].to_station_id == 20) {
                      continue;
                    } else if (data[x].to_station_id == 29) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 27) {
                    if (data[x].to_station_id == 28) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 28) {
                    if (data[x].to_station_id == 27) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 32) {
                    if (data[x].to_station_id == 39) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 39) {
                    if (data[x].to_station_id == 32) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 46) {
                    if (data[x].to_station_id == 49) {
                      continue;
                    }
                  } else if (data[x].from_station_id == 49) {
                    if (data[x].to_station_id == 46) {
                      continue;
                    }
                  }
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  fromLocation = data[x].location + " (" + data[x].shift + ")";
                  if (data[x].moved_location == null) {
                    toLocation = "";
                  } else {
                    toLocation =
                      data[x].moved_location + " (" + data[x].moved_shift + ")";
                  }
                  if (data[x].sm_travel_notice == true) {
                    priorNotice = "Yes";
                  } else {
                    priorNotice = "No";
                  }
                  if (data[x].sm_travel_ppe == true) {
                    withPPE = "Yes";
                  } else {
                    withPPE = "No";
                  }
                  bookings.push({
                    pay_id: data[x].pay_id,
                    name: empName.toUpperCase(),
                    rank: data[x].rank,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    start_date: data[x].start_date,
                    end_date: data[x].end_date,
                    hours: data[x].hours,
                    type: data[x].leave_type_description,
                    from_station_id: data[x].from_station_id,
                    from: fromLocation,
                    to_station_id: data[x].to_station_id,
                    to: toLocation,
                    prior_notice: priorNotice,
                    with_ppe: withPPE,
                    comments: data[x].comments,
                    created_by: data[x].created_by,
                    created_date: data[x].created_date,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            $$("reports-page")
              .$$("reporting_staff_movement")
              .$$("records_count")
              .define("template", bookings.length + " records found!");
            $$("reports-page")
              .$$("reporting_staff_movement")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
