let shiftSummaryReport = (function () {
  let shiftArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster;
  let exp_shift;
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("to_date")
      .setValue(endOfMonth);
    grid = $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("shift_summary_grid");
    let defaultHandler = grid.$exportView;
    grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Showing shift totals for - " +
            "(" +
            exp_roster +
            " / " +
            exp_shift +
            ")",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Shift Summary Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("shift_filter")
          .setValue("");
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_shift_summary")
            .$$("shift_filter")
            .hide();
        } else {
          $$("reports-page")
            .$$("reporting_shift_summary")
            .$$("shift_filter")
            .show();
          $$("reports-page")
            .$$("reporting_shift_summary")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
    $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("from_date")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("to_date")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("roster_filter")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("shift_filter")
          .getText();
        if (roster == "" || shift == "") {
          webix.alert("You must select a 'Roster' and 'Shift'");
        } else {
          generateShiftSummaryReport(fromDate, toDate, roster, shift);
        }
      });
    $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("from_date")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("to_date")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_shift_summary")
          .$$("shift_filter")
          .getText();
        if (grid.count() > 0) {
          webix.toExcel(grid, {
            filename:
              "Report - SHIFT SUMMARY (" + moment().format("DD-MM-YYYY") + ")",
            styles: true,
            heights: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {
            callback();
          },
          success: function (results) {
            let shiftOptions = [];
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                $$("reports-page")
                  .$$("reporting_shift_summary")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_shift_summary")
                  .$$("shift_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateShiftSummaryReport(fromDate, toDate, roster, shift) {
    let grid = $$("reports-page")
      .$$("reporting_shift_summary")
      .$$("shift_summary_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/shift_summary_report",
        { from_date: fromDate, to_date: toDate, roster: roster, shift: shift },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            let total_avl = 0;
            let total_actups = 0;
            let total_recalls = 0;
            let total_sick = 0;
            let total_rrl = 0;
            let total_leave = 0;
            let total_other = 0;
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].icon == "ban") {
                  } else if (data[x].icon == "moon") {
                    total_avl =
                      data[x].total_ras -
                      data[x].night_period_bks -
                      data[x].both_period_bks;
                    total_actups = data[x].total_actups;
                    total_recalls = data[x].total_recalls;
                    total_sick = data[x].total_sick;
                    total_rrl = data[x].total_rrl;
                    total_leave = data[x].total_leave;
                    total_other = data[x].total_other;
                    bookings.push({
                      date: moment(data[x].date_string, "YYYYMMDD").format(
                        "DD/MM/YYYY",
                      ),
                      period: "Night",
                      total_avl: total_avl,
                      total_actups: total_actups,
                      total_recalls: total_recalls,
                      total_sick: total_sick,
                      total_rrl: total_rrl,
                      total_leave: total_leave,
                      total_other: total_other,
                    });
                  } else if (data[x].icon == "sun") {
                    total_avl =
                      data[x].total_ras -
                      data[x].day_period_bks -
                      data[x].both_period_bks;
                    total_actups = data[x].total_actups;
                    total_recalls = data[x].total_recalls;
                    total_sick = data[x].total_sick;
                    total_rrl = data[x].total_rrl;
                    total_leave = data[x].total_leave;
                    total_other = data[x].total_other;
                    bookings.push({
                      date: moment(data[x].date_string, "YYYYMMDD").format(
                        "DD/MM/YYYY",
                      ),
                      period: "Day",
                      total_avl: total_avl,
                      total_actups: total_actups,
                      total_recalls: total_recalls,
                      total_sick: total_sick,
                      total_rrl: total_rrl,
                      total_leave: total_leave,
                      total_other: total_other,
                    });
                  }
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
