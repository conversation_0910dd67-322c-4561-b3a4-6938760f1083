let reasonOvertimeSummaryReport = (function () {
  let shiftArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster;
  let exp_shift;
  let exp_type;
  let report_text = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("from_date")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("to_date")
      .setValue(endOfMonth);
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("rank_filter")
      .setValue("ALL");
    grid = $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("reason_overtime_summary_grid");
    let defaultHandler = grid.$exportView;
    grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, [options]);
      if (exp_type === "OTFC") {
        report_text =
          "Show total hours worked for " +
          exp_type +
          " by reason - " +
          "(" +
          exp_roster +
          " / " +
          exp_shift +
          ")";
      } else {
        report_text =
          "Show totals for " +
          exp_type +
          " by reason - " +
          "(" +
          exp_roster +
          " / " +
          exp_shift +
          ")";
      }
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([report_text]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Overtime Reason Summary Report - " + exp_type,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        let headerStyles = Object.values(returnValue[0].styles[5]);
        headerStyles.forEach((item) => {
          item.alignment.wrapText = true;
        });
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("type_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let grid = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("reason_overtime_summary_grid");
        let grid_columns = [];
        grid.clearAll();
        if (newv === "RC") {
          grid_columns = [
            { id: "date", header: "Date", width: 90 },
            {
              id: "rrl",
              header: { text: "RRL", css: { "text-align": "center" } },
              width: 65,
              css: { "text-align": "center" },
            },
            {
              id: "sick_leave",
              header: { text: "Sick\nLeave", css: "multiline" },
              width: 70,
              css: { "text-align": "center" },
            },
            {
              id: "booked_leave",
              header: { text: "Booked\nLeave", css: "multiline" },
              width: 80,
              css: { "text-align": "center" },
            },
            {
              id: "covid",
              header: { text: "Covid", css: { "text-align": "center" } },
              width: 80,
              css: { "text-align": "center" },
            },
            {
              id: "industrial_action",
              header: { text: "Industrial\nAction", css: "multiline" },
              width: 80,
              css: { "text-align": "center" },
            },
            {
              id: "other_leave",
              header: { text: "Other\nLeave", css: "multiline" },
              width: 80,
              css: { "text-align": "center" },
            },
            {
              id: "course_instructor",
              header: { text: "Course\nInstructor", css: "multiline" },
              width: 90,
              css: { "text-align": "center" },
            },
            {
              id: "course_participant",
              header: { text: "Course\nParticipant", css: "multiline" },
              width: 90,
              css: { "text-align": "center" },
            },
            {
              id: "deployment_interstate",
              header: { text: "Deployment\nInterstate", css: "multiline" },
              width: 90,
              css: { "text-align": "center" },
            },
            {
              id: "deployment_intrastate",
              header: { text: "Deployment\nIntrastate", css: "multiline" },
              width: 90,
              css: { "text-align": "center" },
            },
            {
              id: "out_duty",
              header: { text: "Out\nDuty", css: "multiline" },
              width: 60,
              css: { "text-align": "center" },
            },
            {
              id: "special_leave",
              header: { text: "Special\nLeave", css: "multiline" },
              width: 80,
              css: { "text-align": "center" },
            },
            {
              id: "unknown_reason",
              header: { text: "Unknown\nReason", css: "multiline" },
              width: 80,
              css: { "text-align": "center" },
            },
            {
              id: "day_total",
              header: { text: "TOTAL (Qty)", css: { "text-align": "center" } },
              width: 95,
              css: { "text-align": "center" },
            },
          ];
        } else {
          grid_columns = [
            { id: "date", header: "Date", width: 90 },
            {
              id: "rrl",
              header: { text: "Alarm\nCall", css: "multiline" },
              width: 85,
              css: { "text-align": "center" },
            },
            {
              id: "sick_leave",
              header: { text: "Assist ESO\nAgency", css: "multiline" },
              width: 90,
              css: { "text-align": "center" },
            },
            {
              id: "booked_leave",
              header: { text: "Structure\nFire", css: "multiline" },
              width: 95,
              css: { "text-align": "center" },
            },
            {
              id: "covid",
              header: { text: "Other\nFires", css: "multiline" },
              width: 80,
              css: { "text-align": "center" },
            },
            {
              id: "other_leave",
              header: { text: "Hazmat", css: { "text-align": "center" } },
              width: 65,
              css: { "text-align": "center" },
            },
            {
              id: "course_instructor",
              header: { text: "Change of\nQuarters", css: "multiline" },
              width: 100,
              css: { "text-align": "center" },
            },
            {
              id: "course_participant",
              header: { text: "Rescue", css: { "text-align": "center" } },
              width: 70,
              css: { "text-align": "center" },
            },
            {
              id: "deployment_interstate",
              header: { text: "Weather\nRelated", css: "multiline" },
              width: 90,
              css: { "text-align": "center" },
            },
            {
              id: "deployment_intrastate",
              header: { text: "MVA", css: { "text-align": "center" } },
              width: 50,
              css: { "text-align": "center" },
            },
            {
              id: "out_duty",
              header: { text: "Other\nTotal", css: "multiline" },
              width: 85,
              css: { "text-align": "center" },
            },
            {
              id: "day_total",
              header: { text: "TOTAL (Hrs)", css: { "text-align": "center" } },
              width: 95,
              css: { "text-align": "center" },
            },
          ];
        }
        grid.define("columns", grid_columns);
        grid.refresh();
        grid.refreshColumns();
      });
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("shift_filter")
          .setValue("");
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_reason_overtime_summary")
            .$$("shift_filter")
            .hide();
        } else {
          $$("reports-page")
            .$$("reporting_reason_overtime_summary")
            .$$("shift_filter")
            .show();
          $$("reports-page")
            .$$("reporting_reason_overtime_summary")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("from_date")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("to_date")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("roster_filter")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("shift_filter")
          .getText();
        let rank = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("rank_filter")
          .getValue();
        let type = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("type_filter")
          .getValue();
        generateReasonOvertimeSummaryReport(
          type,
          fromDate,
          toDate,
          roster,
          shift,
          rank,
        );
      });
    $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("from_date")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("to_date")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("shift_filter")
          .getText();
        exp_type = $$("reports-page")
          .$$("reporting_reason_overtime_summary")
          .$$("type_filter")
          .getValue();
        if (grid.count() > 0) {
          webix.toExcel(grid, {
            filename:
              "Report - " +
              exp_type +
              " - REASON OVERTIME SUMMARY (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
            heights: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            let shiftOptions = [];
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_reason_overtime_summary")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_reason_overtime_summary")
                  .$$("shift_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateReasonOvertimeSummaryReport(
    type,
    fromDate,
    toDate,
    roster,
    shift,
    rank,
  ) {
    let grid = $$("reports-page")
      .$$("reporting_reason_overtime_summary")
      .$$("reason_overtime_summary_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/reason_overtime_summary_report",
        {
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          rank: rank,
          type: type,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            let grand_total = 0;
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                if (type === "OTFC") {
                  let alarm_call_total = 0;
                  let assist_eso_agency_total = 0;
                  let structure_fire_total = 0;
                  let other_fires_total = 0;
                  let hazmat_total = 0;
                  let change_of_quarters_total = 0;
                  let rescue_total = 0;
                  let weather_related_total = 0;
                  let mva_total = 0;
                  let other_total = 0;
                  let alarm_call = 0;
                  let assist_eso_agency = 0;
                  let structure_fire = 0;
                  let other_fires = 0;
                  let hazmat = 0;
                  let change_of_quarters = 0;
                  let rescue = 0;
                  let weather_related = 0;
                  let mva = 0;
                  let other = 0;
                  for (let x = 0; x < data.length; x++) {
                    if (data[x].alarm_call_total == null) {
                      alarm_call_total = alarm_call_total + 0;
                    } else {
                      alarm_call_total =
                        alarm_call_total + data[x].alarm_call_total;
                    }
                    if (data[x].assist_eso_agency_total == null) {
                      assist_eso_agency_total = assist_eso_agency_total + 0;
                    } else {
                      assist_eso_agency_total =
                        assist_eso_agency_total +
                        data[x].assist_eso_agency_total;
                    }
                    if (data[x].structure_fire_total == null) {
                      structure_fire_total = structure_fire_total + 0;
                    } else {
                      structure_fire_total =
                        structure_fire_total + data[x].structure_fire_total;
                    }
                    if (data[x].other_fires_total == null) {
                      other_fires_total = other_fires_total + 0;
                    } else {
                      other_fires_total =
                        other_fires_total + data[x].other_fires_total;
                    }
                    if (data[x].hazmat_total == null) {
                      hazmat_total = hazmat_total + 0;
                    } else {
                      hazmat_total = hazmat_total + data[x].hazmat_total;
                    }
                    if (data[x].change_of_quarters_total == null) {
                      change_of_quarters_total = change_of_quarters_total + 0;
                    } else {
                      change_of_quarters_total =
                        change_of_quarters_total +
                        data[x].change_of_quarters_total;
                    }
                    if (data[x].rescue_total == null) {
                      rescue_total = rescue_total + 0;
                    } else {
                      rescue_total = rescue_total + data[x].rescue_total;
                    }
                    if (data[x].weather_related_total == null) {
                      weather_related_total = weather_related_total + 0;
                    } else {
                      weather_related_total =
                        weather_related_total + data[x].weather_related_total;
                    }
                    if (data[x].mva_total == null) {
                      mva_total = mva_total + 0;
                    } else {
                      mva_total = mva_total + data[x].mva_total;
                    }
                    if (data[x].other_total == null) {
                      other_total = other_total + 0;
                    } else {
                      other_total = other_total + data[x].other_total;
                    }
                    if (data[x].alarm_call_total == null) {
                      alarm_call = 0;
                    } else {
                      alarm_call = data[x].alarm_call_total;
                    }
                    if (data[x].assist_eso_agency_total == null) {
                      assist_eso_agency = 0;
                    } else {
                      assist_eso_agency = data[x].assist_eso_agency_total;
                    }
                    if (data[x].structure_fire_total == null) {
                      structure_fire = 0;
                    } else {
                      structure_fire =
                        structure_fire + data[x].structure_fire_total;
                    }
                    if (data[x].other_fires_total == null) {
                      other_fires = 0;
                    } else {
                      other_fires = other_fires + data[x].other_fires_total;
                    }
                    if (data[x].hazmat_total == null) {
                      hazmat = 0;
                    } else {
                      hazmat = hazmat + data[x].hazmat_total;
                    }
                    if (data[x].change_of_quarters_total == null) {
                      change_of_quarters = 0;
                    } else {
                      change_of_quarters =
                        change_of_quarters + data[x].change_of_quarters_total;
                    }
                    if (data[x].rescue_total == null) {
                      rescue = 0;
                    } else {
                      rescue = rescue + data[x].rescue_total;
                    }
                    if (data[x].weather_related_total == null) {
                      weather_related = 0;
                    } else {
                      weather_related =
                        weather_related + data[x].weather_related_total;
                    }
                    if (data[x].mva_total == null) {
                      mva = 0;
                    } else {
                      mva = mva + data[x].mva_total;
                    }
                    if (data[x].other == null) {
                      other = 0;
                    } else {
                      other = data[x].other_total;
                    }
                    bookings.push({
                      date: moment(data[x].date_string, "YYYYMMDD").format(
                        "DD/MM/YYYY",
                      ),
                      rrl: alarm_call,
                      sick_leave: assist_eso_agency,
                      booked_leave: structure_fire,
                      covid: other_fires,
                      other_leave: hazmat,
                      course_instructor: change_of_quarters,
                      course_participant: rescue,
                      deployment_interstate: weather_related,
                      deployment_intrastate: mva,
                      out_duty: other,
                      day_total:
                        alarm_call +
                        assist_eso_agency +
                        structure_fire +
                        other_fires +
                        hazmat +
                        change_of_quarters +
                        rescue +
                        weather_related +
                        mva +
                        other,
                    });
                  }
                  grand_total =
                    alarm_call_total +
                    assist_eso_agency_total +
                    structure_fire_total +
                    other_fires_total +
                    hazmat_total +
                    change_of_quarters_total +
                    rescue_total +
                    weather_related_total +
                    mva_total +
                    other_total;
                  bookings.push({
                    date: " TOTALS",
                    rrl: alarm_call_total,
                    sick_leave: assist_eso_agency_total,
                    booked_leave: structure_fire_total,
                    covid: other_fires_total,
                    other_leave: hazmat_total,
                    course_instructor: change_of_quarters_total,
                    course_participant: rescue_total,
                    deployment_interstate: weather_related_total,
                    deployment_intrastate: mva_total,
                    out_duty: other_total,
                    day_total: grand_total,
                  });
                  bookings.push({
                    date: " TOTALS (%)",
                    rrl:
                      ((alarm_call_total / grand_total) * 100).toFixed(2) + "%",
                    sick_leave:
                      ((assist_eso_agency_total / grand_total) * 100).toFixed(
                        2,
                      ) + "%",
                    booked_leave:
                      ((structure_fire_total / grand_total) * 100).toFixed(2) +
                      "%",
                    covid:
                      ((other_fires_total / grand_total) * 100).toFixed(2) +
                      "%",
                    other_leave:
                      ((hazmat_total / grand_total) * 100).toFixed(2) + "%",
                    course_instructor:
                      ((change_of_quarters_total / grand_total) * 100).toFixed(
                        2,
                      ) + "%",
                    course_participant:
                      ((rescue_total / grand_total) * 100).toFixed(2) + "%",
                    deployment_interstate:
                      ((weather_related_total / grand_total) * 100).toFixed(2) +
                      "%",
                    deployment_intrastate:
                      ((mva_total / grand_total) * 100).toFixed(2) + "%",
                    out_duty:
                      ((other_total / grand_total) * 100).toFixed(2) + "%",
                    day_total: "100%",
                  });
                  grid.define("data", bookings);
                  grid.refresh();
                  let totals_row_percent_id = grid.getLastId();
                  let totals_row_id = grid.getLastId() - 1;
                  grid.addRowCss(totals_row_id, "totals_row");
                  grid.addRowCss(totals_row_percent_id, "totals_row");
                  $$("loader-window").hide();
                } else {
                  let rrl_total = 0;
                  let sick_leave_total = 0;
                  let booked_leave_total = 0;
                  let covid_leave_total = 0;
                  let industrial_action_total = 0;
                  let other_leave_total = 0;
                  let course_instructor_total = 0;
                  let course_participant_total = 0;
                  let deployment_interstate_total = 0;
                  let deployment_intrastate_total = 0;
                  let out_duty_total = 0;
                  let special_leave_total = 0;
                  let unknown_reason_total = 0;
                  for (let x = 0; x < data.length; x++) {
                    rrl_total = rrl_total + data[x].total_rrl;
                    sick_leave_total =
                      sick_leave_total + data[x].total_sick_leave;
                    booked_leave_total =
                      booked_leave_total + data[x].total_booked_leave;
                    covid_leave_total = covid_leave_total + data[x].total_covid;
                    industrial_action_total =
                      industrial_action_total + data[x].total_industrial_action;
                    other_leave_total =
                      other_leave_total + data[x].total_other_leave;
                    course_instructor_total =
                      course_instructor_total + data[x].total_course_instructor;
                    course_participant_total =
                      course_participant_total +
                      data[x].total_course_participant;
                    deployment_interstate_total =
                      deployment_interstate_total +
                      data[x].total_deploy_interstate;
                    deployment_intrastate_total =
                      deployment_intrastate_total +
                      data[x].total_deploy_intrastate;
                    out_duty_total = out_duty_total + data[x].total_out_duty;
                    special_leave_total =
                      special_leave_total + data[x].total_special_leave;
                    unknown_reason_total =
                      unknown_reason_total + data[x].total_unknown_reason;
                    bookings.push({
                      date: moment(data[x].date_string, "YYYYMMDD").format(
                        "DD/MM/YYYY",
                      ),
                      rrl: data[x].total_rrl,
                      sick_leave: data[x].total_sick_leave,
                      booked_leave: data[x].total_booked_leave,
                      covid: data[x].total_covid,
                      industrial_action: data[x].total_industrial_action,
                      other_leave: data[x].total_other_leave,
                      course_instructor: data[x].total_course_instructor,
                      course_participant: data[x].total_course_participant,
                      deployment_interstate: data[x].total_deploy_interstate,
                      deployment_intrastate: data[x].total_deploy_intrastate,
                      out_duty: data[x].total_out_duty,
                      special_leave: data[x].total_special_leave,
                      unknown_reason: data[x].total_unknown_reason,
                      day_total:
                        data[x].total_rrl +
                        data[x].total_sick_leave +
                        data[x].total_booked_leave +
                        data[x].total_other_leave +
                        data[x].total_course_instructor +
                        data[x].total_course_participant +
                        data[x].total_deploy_interstate +
                        data[x].total_deploy_intrastate +
                        data[x].total_out_duty +
                        data[x].total_special_leave +
                        data[x].total_unknown_reason,
                    });
                  }
                  grand_total =
                    rrl_total +
                    sick_leave_total +
                    booked_leave_total +
                    covid_leave_total +
                    industrial_action_total +
                    other_leave_total +
                    course_instructor_total +
                    course_participant_total +
                    deployment_interstate_total +
                    deployment_intrastate_total +
                    out_duty_total +
                    special_leave_total +
                    unknown_reason_total;
                  bookings.push({
                    date: " TOTALS",
                    rrl: rrl_total,
                    sick_leave: sick_leave_total,
                    booked_leave: booked_leave_total,
                    covid: covid_leave_total,
                    industrial_action: industrial_action_total,
                    other_leave: other_leave_total,
                    course_instructor: course_instructor_total,
                    course_participant: course_participant_total,
                    deployment_interstate: deployment_interstate_total,
                    deployment_intrastate: deployment_intrastate_total,
                    out_duty: out_duty_total,
                    special_leave: special_leave_total,
                    unknown_reason: unknown_reason_total,
                    day_total: grand_total,
                  });
                  bookings.push({
                    date: " TOTALS (%)",
                    rrl: ((rrl_total / grand_total) * 100).toFixed(2) + "%",
                    sick_leave:
                      ((sick_leave_total / grand_total) * 100).toFixed(2) + "%",
                    booked_leave:
                      ((booked_leave_total / grand_total) * 100).toFixed(2) +
                      "%",
                    covid:
                      ((covid_leave_total / grand_total) * 100).toFixed(2) +
                      "%",
                    industrial_action:
                      ((industrial_action_total / grand_total) * 100).toFixed(
                        2,
                      ) + "%",
                    other_leave:
                      ((other_leave_total / grand_total) * 100).toFixed(2) +
                      "%",
                    course_instructor:
                      ((course_instructor_total / grand_total) * 100).toFixed(
                        2,
                      ) + "%",
                    course_participant:
                      ((course_participant_total / grand_total) * 100).toFixed(
                        2,
                      ) + "%",
                    deployment_interstate:
                      (
                        (deployment_interstate_total / grand_total) *
                        100
                      ).toFixed(2) + "%",
                    deployment_intrastate:
                      (
                        (deployment_intrastate_total / grand_total) *
                        100
                      ).toFixed(2) + "%",
                    out_duty:
                      ((out_duty_total / grand_total) * 100).toFixed(2) + "%",
                    special_leave:
                      ((special_leave_total / grand_total) * 100).toFixed(2) +
                      "%",
                    unknown_reason:
                      ((unknown_reason_total / grand_total) * 100).toFixed(2) +
                      "%",
                    day_total: "100%",
                  });
                  grid.define("data", bookings);
                  grid.refresh();
                  let totals_row_percent_id = grid.getLastId();
                  let totals_row_id = grid.getLastId() - 1;
                  grid.addRowCss(totals_row_id, "totals_row");
                  grid.addRowCss(totals_row_percent_id, "totals_row");
                  $$("loader-window").hide();
                }
              }
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
