let covidVaxSummaryReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("shift_filter")
      .setValue("-- All Shifts --");
    $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("location_filter")
      .setValue("-- All Stations --");
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateVaxSummaryReport();
      });
    $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_covid_vax_summary")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_covid_vax_summary")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("reports-page")
            .$$("reporting_covid_vax_summary")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_covid_vax_summary")
            .$$("location_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_covid_vax_summary")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_covid_vax_summary")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_covid_vax_summary")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("reports-page")
                  .$$("reporting_covid_vax_summary")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_covid_vax_summary")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_covid_vax_summary")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_covid_vax_summary")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateVaxSummaryReport() {
    let grid = $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("covid_vax_summary_chart");
    let roster = $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("roster_filter")
      .getValue();
    let shift = $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("shift_filter")
      .getValue();
    let location = $$("reports-page")
      .$$("reporting_covid_vax_summary")
      .$$("location_filter")
      .getValue();
    let not_reported_filter = 0;
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/covid_vax_summary_report",
        {
          roster: roster,
          shift: shift,
          location: location,
          not_reported_filter: not_reported_filter,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                let all_records_qty = 0;
                all_records_qty = data[0].all_records;
                $$("reports-page")
                  .$$("reporting_covid_vax_summary")
                  .$$("covid_vax_records")
                  .define(
                    "template",
                    "<div style='font-size: 17px; text-align: center; line-height: 22px'>Total Vaccine Records: " +
                      all_records_qty +
                      "</br>Total Forms Received: " +
                      data[0].vax_form_received +
                      "</br>Total Verified Records: " +
                      data[0].total_verified +
                      "</div>",
                  );
                $$("reports-page")
                  .$$("reporting_covid_vax_summary")
                  .$$("covid_vax_records")
                  .refresh();
                reportData.push({
                  status: "Unvaccinated",
                  qty:
                    data[0].Unvaccinated +
                    " - (" +
                    (100 * (data[0].Unvaccinated / all_records_qty)).toFixed(
                      1,
                    ) +
                    ")%",
                  colour: "#EE3639",
                });
                reportData.push({
                  status: "Vaccinated",
                  qty:
                    data[0].Fully_Vaccinated +
                    " - (" +
                    (
                      100 *
                      (data[0].Fully_Vaccinated / all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#22D425",
                });
                reportData.push({
                  status: "Medically Exempt",
                  qty:
                    data[0].Medically_Exempt +
                    " - (" +
                    (
                      100 *
                      (data[0].Medically_Exempt / all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#EEEA36",
                });
                reportData.push({
                  status: "Medically Exempt Pending",
                  qty:
                    data[0].Medically_Exempt_Pending +
                    " - (" +
                    (
                      100 *
                      (data[0].Medically_Exempt_Pending / all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#63635E",
                });
                grid.define("data", reportData);
                grid.refresh();
                $$("loader-window").hide();
              } else {
                $$("loader-window").hide();
                webix.alert("No data to report!");
              }
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
