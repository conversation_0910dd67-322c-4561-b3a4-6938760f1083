let bookingsReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate;
  let exp_toDate;
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_bookings")
      .$$("from")
      .setValue(startOfMonth);
    $$("reports-page").$$("reporting_bookings").$$("to").setValue(endOfMonth);
    exp_grid = $$("reports-page").$$("reporting_bookings").$$("bookings_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Audit Report for All Locations logged between dates " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Creation Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_bookings")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_bookings")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_bookings")
          .$$("to")
          .getValue();
        generateBookingsReport(fromDate, toDate);
      });
    $$("reports-page")
      .$$("reporting_bookings")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_bookings")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_bookings")
          .$$("to")
          .getValue();
        let filename =
          "WFR Rostering Report - CREATION (" +
          moment(exp_fromDate).format("DD-MM-YYYY") +
          ")";
        if (exp_grid.count() > 0) {
          webix
            .toExcel(exp_grid, { filename: filename, styles: true })
            .then(function (blob) {
              const reader = new FileReader();
              reader.readAsDataURL(blob);
              reader.onloadend = function () {
                let base64String = reader.result;
                if (live_site === true) {
                  sendEmailWithAttachment(
                    "SAPPHIRE<<EMAIL>>",
                    "<EMAIL>",
                    "Creation Report - " +
                      moment(exp_fromDate).format("DD/MM/YYYY"),
                    base64String,
                    filename + ".xlsx",
                    user_logged_in_name,
                  );
                }
              };
            });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  function generateBookingsReport(fromDate, toDate) {
    let grid = $$("reports-page").$$("reporting_bookings").$$("bookings_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/bookings_report",
        { from_date: fromDate, to_date: toDate },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let chris21code = "";
              let total_hours = "";
              let total_days = "";
              let comments = "";
              let date_number = 0;
              let epoch_date = moment("01/01/1900", "DD/MM/YYYY");
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  comments = "";
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (
                    data[x].leave_type_code == "ARL" ||
                    data[x].leave_type_code == "XARL" ||
                    data[x].leave_type_code == "ANN"
                  ) {
                    chris21code = "ANN";
                  } else if (
                    data[x].leave_type_code == "LSLS" ||
                    data[x].leave_type_code == "ULSL" ||
                    data[x].leave_type_code == "XLSL" ||
                    data[x].leave_type_code == "XLSS" ||
                    data[x].leave_type_code == "ULSS"
                  ) {
                    chris21code = "LSL";
                  } else if (
                    data[x].leave_type_code == "XRET" ||
                    data[x].leave_type_code == "URET"
                  ) {
                    chris21code = "RET";
                  } else if (
                    data[x].leave_type_code == "XPHL" ||
                    data[x].leave_type_code == "UPHL"
                  ) {
                    chris21code = "PHOL";
                  } else if (
                    data[x].leave_type_code == "SOIL" ||
                    data[x].leave_type_code == "XTOI"
                  ) {
                    chris21code = "TOIL";
                  } else {
                    chris21code = data[x].leave_type_code;
                  }
                  if (
                    data[x].leave_type_code == "RET" ||
                    data[x].leave_type_code == "XRET" ||
                    data[x].leave_type_code == "URET"
                  ) {
                    total_days = "";
                    total_hours = data[x].total_hours;
                  } else {
                    total_days = data[x].days;
                    total_hours = "";
                  }
                  if (
                    data[x].leave_type_code == "ARL" ||
                    data[x].leave_type_code == "ANN"
                  ) {
                    if (
                      data[x].shift == "OTR" ||
                      data[x].shift == "CommCen E1" ||
                      data[x].shift == "CommCen E2"
                    ) {
                    } else if (data[x].location == "Port Pirie") {
                      if (data[x].total_hours == 24) {
                        total_days = total_days * 4;
                      } else {
                        total_days = total_days * 2;
                      }
                    } else {
                      total_days = total_days * 2;
                    }
                  } else if (data[x].leave_type_code == "XARL") {
                    total_days = total_days * 2;
                  } else if (data[x].leave_type_code == "ULSL") {
                    total_days = total_days * 2;
                  } else if (data[x].leave_type_code == "XLSL") {
                    if (total_days <= 4) {
                      total_days = total_days * 2;
                    }
                  } else if (
                    data[x].leave_type_code == "LSLS" ||
                    data[x].leave_type_code == "ULSS" ||
                    data[x].leave_type_code == "XLSS"
                  ) {
                    if (data[x].location == "Port Pirie") {
                      if (data[x].total_hours == 24) {
                        total_days = total_days * 4;
                      } else {
                        total_days = total_days * 2;
                      }
                    } else if (
                      data[x].shift == "OTR" ||
                      data[x].shift == "CommCen E1" ||
                      data[x].shift == "CommCen E2"
                    ) {
                      total_days = total_days * 1.75;
                    } else {
                      total_days = total_days * 2;
                    }
                  }
                  if (data[x].location == "Port Pirie") {
                    if (chris21code == "PHOL") {
                      if (data[x].total_hours == 24) {
                        total_days = total_days * 2;
                      }
                    }
                  }
                  if (
                    data[x].request_comments === null ||
                    data[x].request_comments === ""
                  ) {
                    comments = data[x].comments;
                  } else {
                    comments = data[x].request_comments;
                  }
                  date_number =
                    moment(
                      moment(data[x].booking_first_date, "DD/MM/YYYY H:mm"),
                    ).diff(epoch_date, "days") + 2;
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName.toUpperCase(),
                    shift: data[x].shift,
                    rank: data[x].rank,
                    chris_21: chris21code,
                    start_date: data[x].booking_first_date.slice(0, 10),
                    date_number: date_number,
                    sequence_code:
                      data[x].pay_id + date_number.toString() + chris21code,
                    start_time: data[x].booking_first_date.slice(11, 16),
                    end_date: data[x].booking_last_date.slice(0, 10),
                    end_time: data[x].booking_last_date.slice(11, 16),
                    code: data[x].leave_type_code,
                    days: total_days,
                    total_hours: total_hours,
                    location: data[x].location,
                    comments: comments,
                    approved_by: data[x].approved_denied_by,
                    approved_date: data[x].approved_denied_date,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            grid.sort("chris_21", "asc", "string");
            $$("reports-page")
              .$$("reporting_bookings")
              .$$("records_count")
              .define("template", bookings.length + " bookings found!");
            $$("reports-page")
              .$$("reporting_bookings")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
