let overtimeFatigueDistanceReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 12, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
    3: { font: { name: "Arial", sz: 10, bold: true } },
    4: { font: { name: "Arial", sz: 10, bold: true } },
    5: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  let exp_codes = "";
  let exp_date = "";
  let exp_filter_info = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("date_filter")
      .setValue(new Date());
    exp_grid = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("overtime_fatigue_distance_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Skills: " + exp_codes]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Date: " + exp_date]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Roster: " + exp_filter_info]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Overtime & Fatigue - Station Shortage By Distance",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateOvertimeFatigueDistanceReport();
      });
    $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("reports-page")
          .$$("reporting_overtime_distance_fatigue")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_overtime_distance_fatigue")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_overtime_distance_fatigue")
          .$$("location_filter")
          .getText();
        exp_codes = $$("reports-page")
          .$$("reporting_overtime_distance_fatigue")
          .$$("code_filter")
          .getValue();
        exp_date = moment(
          $$("reports-page")
            .$$("reporting_overtime_distance_fatigue")
            .$$("date_filter")
            .getValue(),
        ).format("DD/MM/YYYY");
        exp_filter_info = exp_roster & " | " & exp_shift & " | " & exp_location;
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Fatigue & Overtime - Station Shortage by Distance Report (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_overtime_distance_fatigue")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_overtime_distance_fatigue")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("reports-page")
            .$$("reporting_overtime_distance_fatigue")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_overtime_distance_fatigue")
            .$$("location_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_overtime_distance_fatigue")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_overtime_distance_fatigue")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_overtime_distance_fatigue")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  skill_codes_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("code_filter");
    let results = JSON.parse(data);
    let skill_codes = [];
    for (let x = 0; x < results.length; x++) {
      skill_codes.push({ id: results[x].code, value: results[x].code });
    }
    select.define("options", skill_codes);
    select.refresh();
    select.getPopup().queryView({ labelRight: "Select all" }).show();
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                $$("reports-page")
                  .$$("reporting_overtime_distance_fatigue")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_overtime_distance_fatigue")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_overtime_distance_fatigue")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_overtime_distance_fatigue")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateOvertimeFatigueDistanceReport() {
    let grid = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("overtime_fatigue_distance_grid");
    let filter_date = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("date_filter")
      .getValue();
    let roster = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("roster_filter")
      .getText();
    let shift = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("shift_filter")
      .getText();
    let location = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("location_filter")
      .getText();
    let rank_group = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("rank_filter")
      .getValue();
    let skillCodes = $$("reports-page")
      .$$("reporting_overtime_distance_fatigue")
      .$$("code_filter")
      .getValue();
    if (roster == "" || shift == "" || location == "") {
      webix.alert({
        text: "You must select a Roster, Shift & Location!",
        width: 450,
      });
    } else {
      if (rank_group == "" || skillCodes == "") {
        webix.alert({
          text: "You must select at least one rank and one skill code!",
          width: 450,
        });
      } else {
        $$("loader-window").show();
        grid.clearAll();
        webix
          .ajax()
          .headers({ Authorization: "Bearer " + api_key })
          .get(
            server_url + "/admin/get_overtime_fatigue_distance_report",
            {
              rank_group: rank_group,
              roster: roster,
              shift: shift,
              location: location,
              filter_date: moment(filter_date, "YYYY-MM-DD HH:mm").format(
                "YYYYMMDD",
              ),
              skill_codes: skillCodes,
            },
            {
              error: function (err) {
                $$("loader-window").hide();
              },
              success: function (results) {
                let responses = [];
                if (results) {
                  let data = JSON.parse(results);
                  if (data.length > 0) {
                    let skillCodesArray = skillCodes.split(",");
                    let userSkillsArray = [];
                    let sc_found = false;
                    for (let x = 0; x < data.length; x++) {
                      if (data[x].no_of_bookings == 0) {
                        sc_found = false;
                        if (
                          data[x].all_skill_codes != null &&
                          data[x].all_skill_codes.includes(",")
                        ) {
                          userSkillsArray = data[x].all_skill_codes.split(",");
                          skillCodesArray.forEach(function (item) {
                            if (userSkillsArray.includes(item)) {
                              sc_found = true;
                            }
                          });
                        } else if (data[x].all_skill_codes != null) {
                          skillCodesArray.forEach(function (item) {
                            if (data[x].all_skill_codes.includes(item)) {
                              sc_found = true;
                            }
                          });
                        }
                        if (sc_found === true) {
                          responses.push({
                            pay_id: data[x].pay_id,
                            rank: data[x].rank,
                            name: data[x].surname + ", " + data[x].first_name,
                            skill_codes: data[x].all_skill_codes,
                            kms_home_station: data[x].res_to_hs_kms,
                            kms_rel_station: data[x].res_to_rel_kms,
                            kms_difference: (
                              data[x].res_to_rel_kms - data[x].res_to_hs_kms
                            ).toFixed(1),
                            current_station: data[x].curr_station,
                            home_suburb: data[x].suburb,
                            stat_1_pref: data[x].hs_pref_1,
                            stat_2_pref: data[x].hs_pref_2,
                            stat_3_pref: data[x].hs_pref_3,
                          });
                        }
                      }
                    }
                  }
                }
                grid.define("data", responses);
                grid.refresh();
                grid.sort("kms_difference", "asc", "int");
                $$("loader-window").hide();
              },
            },
          );
      }
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
