let deletionReport = (function () {
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate;
  let exp_toDate;
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_deletion")
      .$$("from")
      .setValue(startOfMonth);
    $$("reports-page").$$("reporting_deletion").$$("to").setValue(endOfMonth);
    exp_grid = $$("reports-page").$$("reporting_deletion").$$("deletion_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Audit Report for All Locations logged between dates " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Deletion Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_deletion")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_deletion")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_deletion")
          .$$("to")
          .getValue();
        generateDeletionReport(fromDate, toDate);
      });
    $$("reports-page")
      .$$("reporting_deletion")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_deletion")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_deletion")
          .$$("to")
          .getValue();
        let filename =
          "WFR Rostering Report - DELETION (" +
          moment(exp_fromDate).format("DD-MM-YYYY") +
          ")";
        if (exp_grid.count() > 0) {
          webix
            .toExcel(exp_grid, { filename: filename, styles: true })
            .then(function (blob) {
              const reader = new FileReader();
              reader.readAsDataURL(blob);
              reader.onloadend = function () {
                let base64String = reader.result;
                if (live_site === true) {
                  sendEmailWithAttachment(
                    "SAPPHIRE<<EMAIL>>",
                    "<EMAIL>",
                    "Deletion Report - " +
                      moment(exp_fromDate).format("DD/MM/YYYY"),
                    base64String,
                    filename + ".xlsx",
                    user_logged_in_name,
                  );
                }
              };
            });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  function generateDeletionReport(fromDate, toDate) {
    let grid = $$("reports-page").$$("reporting_deletion").$$("deletion_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/deletion_report",
        { from_date: fromDate, to_date: toDate },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let deleted_period = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].day_deleted == true && data[x].deleted == false) {
                    if (data[x].bk_deleted_day != null) {
                      deleted_period = moment(
                        data[x].bk_deleted_day,
                        "YYYYMMDD",
                      ).format("DD/MM/YYYY");
                    } else {
                      if (data[x].del_start == data[x].del_end) {
                        deleted_period = moment(
                          data[x].del_start,
                          "YYYYMMDD",
                        ).format("DD/MM/YYYY");
                      } else {
                        deleted_period =
                          moment(data[x].del_start, "YYYYMMDD").format(
                            "DD/MM/YYYY",
                          ) +
                          " ~ " +
                          moment(data[x].del_end, "YYYYMMDD").format(
                            "DD/MM/YYYY",
                          );
                      }
                    }
                  } else {
                    deleted_period = "ALL DAYS DELETED";
                  }
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName.toUpperCase(),
                    rank: data[x].rank,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    code:
                      data[x].leave_type_description +
                      " - " +
                      data[x].leave_type_code,
                    deleted_period: deleted_period,
                    total_hours: data[x].total_hours,
                    approved_start_date: data[x].booking_first_date,
                    approved_end_date: data[x].booking_last_date,
                    deleted_by: data[x].deleted_by,
                    deleted_date: data[x].deleted_date,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            $$("reports-page")
              .$$("reporting_deletion")
              .$$("records_count")
              .define("template", bookings.length + " bookings found!");
            $$("reports-page")
              .$$("reporting_deletion")
              .$$("records_count")
              .refresh();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (record.deleted_period != "ALL DAYS DELETED") {
                grid.addCellCss(row, "deleted_period", "deleted_date");
              }
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
