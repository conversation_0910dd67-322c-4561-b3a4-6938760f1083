let overtimeFatigueReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 12, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    3: { font: { name: "Arial", sz: 10, bold: true } },
    4: { font: { name: "Arial", sz: 10, bold: true } },
    5: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  let exp_codes = "";
  let exp_date = "";
  let exp_time = "";
  let exp_filter_info = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("date_filter")
      .setValue(new Date());
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("time_filter")
      .setValue("08:00");
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("employee_filter")
      .setValue(1);
    exp_grid = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("overtime_fatigue_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Skills: " + exp_codes]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Time: " + exp_time]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Date: " + exp_date]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Roster: " + exp_filter_info]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["RECALL REQUIREMENTS"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("overtime_fatigue_grid")
      .attachEvent("onItemClick", function (id, e, node) {
        let item = this.getItem(id);
        if (id.column === "logs") {
          getOvertimeBreakdown(item.pay_id, item.name);
          $$("overtime-breakdown-window").show();
        }
      });
    $$("btn_overtime-breakdown_close").attachEvent(
      "onItemClick",
      function (id, e, node) {
        $$("overtime-breakdown-window").hide();
      },
    );
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("employee_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv != 1) {
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("roster_filter")
            .setValue("");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("shift_filter")
            .setValue("");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("location_filter")
            .setValue("");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("rank_filter")
            .setValue("");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("code_filter")
            .setValue("");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("roster_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("location_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("rank_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("code_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("roster_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("location_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("rank_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("code_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("roster_filter")
            .setValue("-- All Rosters --");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("rank_filter")
            .setValue("All");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("code_filter")
            .setValue("");
        }
      });
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateOvertimeFatigueReport();
      });
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("reports-page")
          .$$("reporting_overtime_fatigue")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_overtime_fatigue")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_overtime_fatigue")
          .$$("location_filter")
          .getText();
        exp_codes = $$("reports-page")
          .$$("reporting_overtime_fatigue")
          .$$("code_filter")
          .getValue();
        exp_date = moment(
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("date_filter")
            .getValue(),
        ).format("DD/MM/YYYY");
        exp_time = $$("reports-page")
          .$$("reporting_overtime_fatigue")
          .$$("time_filter")
          .getValue();
        if (exp_roster == "-- All Rosters --") {
          exp_filter_info = "All Rosters";
        } else {
          if (exp_shift == "-- All Shifts --") {
            if (exp_location == "-- All Stations --") {
              exp_filter_info = exp_roster + " / All Shifts / All Locations";
            } else {
              exp_filter_info = exp_roster + " / All Shifts / " + exp_location;
            }
          } else {
            if (exp_location == "-- All Stations --") {
              exp_filter_info =
                exp_roster + " / " + exp_shift + " / All Locations";
            } else {
              exp_filter_info =
                exp_roster + " / " + exp_shift + " / " + exp_location;
            }
          }
        }
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Fatigue & Overtime Report (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("location_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_overtime_fatigue")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_overtime_fatigue")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  skill_codes_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("code_filter");
    let results = JSON.parse(data);
    let skill_codes = [];
    for (let x = 0; x < results.length; x++) {
      skill_codes.push({ id: results[x].code, value: results[x].code });
    }
    select.define("options", skill_codes);
    select.refresh();
    select.getPopup().queryView({ labelRight: "Select all" }).show();
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("reports-page")
                  .$$("reporting_overtime_fatigue")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_overtime_fatigue")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_overtime_fatigue")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_overtime_fatigue")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateOvertimeFatigueReport() {
    let grid = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("overtime_fatigue_grid");
    let filter_date = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("date_filter")
      .getValue();
    let filter_time = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("time_filter")
      .getValue();
    let roster = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("roster_filter")
      .getText();
    let shift = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("shift_filter")
      .getText();
    let location = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("location_filter")
      .getText();
    let payId = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("employee_filter")
      .getValue();
    let rank_group = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("rank_filter")
      .getValue();
    let skillCode = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("code_filter")
      .getValue();
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/get_overtime_fatigue_report",
        {
          pay_id: payId,
          rank_group: rank_group,
          roster: roster,
          shift: shift,
          location: location,
          filter_date: moment(filter_date, "YYYY-MM-DD HH:mm").format(
            "YYYYMMDD",
          ),
          filter_time: filter_time,
          skill_codes: skillCode,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let responses = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let all_skills = "";
              let fatigueHours;
              let overtimeHours;
              let fatigue_date = filter_date.slice(0, 10) + " " + filter_time;
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].all_skill_codes !== null) {
                    all_skills = data[x].all_skill_codes.replaceAll("'", "");
                  }
                  getFatigueHours(
                    data[x].pay_id,
                    fatigue_date,
                    data[x].shift_type,
                    function (result) {
                      fatigueHours = result;
                    },
                  );
                  if (data[x].overtime_hours !== null) {
                    overtimeHours = data[x].overtime_hours;
                  } else {
                    overtimeHours = 0;
                  }
                  responses.push({
                    pay_id: data[x].pay_id,
                    mobile_no: data[x].personal_mobile_no,
                    rank: data[x].rank,
                    name: empName,
                    overtime_hours: overtimeHours,
                    fatigue_hours: fatigueHours,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    skill_codes: all_skills,
                  });
                }
              }
            }
            grid.define("data", responses);
            grid.refresh();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (filter_time == "08:00") {
                if (record.fatigue_hours > 76) {
                  grid.addCellCss(row, "fatigue_hours", "fatigue_red");
                } else if (record.fatigue_hours + 10 < 76) {
                  grid.addCellCss(row, "fatigue_hours", "fatigue_green");
                } else if (record.fatigue_hours + 10 >= 76) {
                  grid.addCellCss(row, "fatigue_hours", "fatigue_orange");
                }
              } else if (filter_time == "18:00") {
                if (record.fatigue_hours > 76) {
                  grid.addCellCss(row, "fatigue_hours", "fatigue_red");
                } else if (record.fatigue_hours + 14 < 76) {
                  grid.addCellCss(row, "fatigue_hours", "fatigue_green");
                } else if (record.fatigue_hours + 14 >= 76) {
                  grid.addCellCss(row, "fatigue_hours", "fatigue_orange");
                }
              }
            });
            grid.sort([
              { by: "fatigue_hours", dir: "asc", as: "int" },
              { by: "overtime_hours", dir: "asc", as: "int" },
            ]);
            $$("loader-window").hide();
          },
        },
      );
  }
  function getFatigueHours(payId, rspDate, shift_type, callback) {
    let firstDate = moment(rspDate, "YYYY-MM-DD HH:mm").format("YYYYMMDD");
    let totalHours = 0;
    let startDate = "";
    let finishDate = "";
    let todayHours = 0;
    if (shift_type != null) {
      let currHH = moment(rspDate, "DD/MM/YYYY HH:mm").format("HH:mm");
      let currDec = moment.duration(currHH).asHours();
      if (shift_type == "off") {
        finishDate = moment(moment(firstDate).subtract(1, "days")).format(
          "YYYYMMDD",
        );
        startDate = moment(moment(firstDate).subtract(8, "days")).format(
          "YYYYMMDD",
        );
      } else if (shift_type == "day") {
        if (currDec >= 8) {
          todayHours = currDec - 8;
          todayHours = parseFloat(todayHours.toFixed(2));
          finishDate = moment(moment(firstDate).subtract(1, "days")).format(
            "YYYYMMDD",
          );
          startDate = moment(moment(firstDate).subtract(7, "days")).format(
            "YYYYMMDD",
          );
        } else {
          finishDate = moment(moment(firstDate).subtract(1, "days")).format(
            "YYYYMMDD",
          );
          startDate = moment(moment(firstDate).subtract(8, "days")).format(
            "YYYYMMDD",
          );
        }
      } else if (shift_type == "night") {
        if (currDec >= 18) {
          todayHours = currDec - 18;
          todayHours = parseFloat(todayHours.toFixed(2));
          finishDate = firstDate;
          startDate = moment(moment(firstDate).subtract(7, "days")).format(
            "YYYYMMDD",
          );
        } else {
          finishDate = moment(moment(firstDate).subtract(1, "days")).format(
            "YYYYMMDD",
          );
          startDate = moment(moment(firstDate).subtract(8, "days")).format(
            "YYYYMMDD",
          );
        }
      }
    } else {
      finishDate = moment(moment(firstDate).subtract(1, "days")).format(
        "YYYYMMDD",
      );
      startDate = moment(moment(firstDate).subtract(8, "days")).format(
        "YYYYMMDD",
      );
    }
    getRAandBookings(payId, startDate, finishDate, function (results) {
      if (results.length > 0) {
        results.forEach(function (value) {
          if (
            value.booking_type == "sick_leave" ||
            value.booking_type == "leave_request" ||
            value.booking_type == "standby"
          ) {
          } else if (value.booking_type == "overtime") {
            if (value.roster === "Port Pirie") {
              if (value.shift_type == "day" || value.shift_type == "night") {
                totalHours = totalHours + 24;
              }
            } else {
              if (value.shift_type === "day") {
                totalHours = totalHours + 10;
              } else if (value.shift_type === "night") {
                totalHours = totalHours + 14;
              }
            }
            if (
              value.leave_type_code == "DRILL" ||
              value.leave_type_code == "REL"
            ) {
            } else {
              totalHours = totalHours + value.hours;
            }
          } else if (value.booking_type == "standby_link") {
            totalHours = totalHours + value.hours;
          } else {
            if (value.roster === "Port Pirie") {
              if (value.shift_type == "day" || value.shift_type == "night") {
                totalHours = totalHours + 24;
              }
            } else {
              if (value.shift_type === "day") {
                totalHours = totalHours + 10;
              } else if (value.shift_type === "night") {
                totalHours = totalHours + 14;
              }
            }
          }
          if (value.bk_period2 != value.bk_period) {
            if (
              value.booking_type2 == "sick_leave" ||
              value.booking_type2 == "leave_request" ||
              value.booking_type2 == "standby"
            ) {
            } else if (value.booking_type2 == "overtime") {
              if (
                value.leave_type_code2 != "DRILL" ||
                value.leave_type_code2 != "REL"
              ) {
                totalHours = totalHours + value.hours2;
              }
            } else if (value.booking_type2 == "standby_link") {
              totalHours = totalHours + value.hours2;
            } else {
              if (value.roster === "Port Pirie") {
                if (value.bk_period2 == "day" || value.bk_period2 == "night") {
                  totalHours = totalHours + 24;
                }
              } else {
                if (value.bk_period2 === "day") {
                  totalHours = totalHours + 10;
                } else if (value.bk_period2 === "night") {
                  totalHours = totalHours + 14;
                }
              }
            }
          }
        });
      }
      callback(totalHours + todayHours);
    });
  }
  function getRAandBookings(payId, startDate, finishDate, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/bookings/get_ra_bookings",
        {
          pay_id: payId,
          start_date_string: startDate,
          end_date_string: finishDate,
        },
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let value = JSON.parse(results);
            if (value) {
              callback(value);
            } else {
              callback([]);
            }
          },
        },
      );
  }
  function getOvertimeBreakdown(payId, employeeName) {
    let filter_date = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("date_filter")
      .getValue();
    let filter_time = $$("reports-page")
      .$$("reporting_overtime_fatigue")
      .$$("time_filter")
      .getValue();
    let grid = $$("overtime-breakdown_grid");
    grid.clearAll();
    let bookingInfo = "";
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/get_overtime_breakdown",
        {
          pay_id: payId,
          filter_date: moment(filter_date, "YYYY-MM-DD HH:mm").format(
            "YYYYMMDD",
          ),
          filter_time: filter_time,
        },
        {
          error: function (err) {},
          success: function (results) {
            let result = JSON.parse(results);
            let breakdown_results = [];
            if (result.length > 0) {
              bookingInfo =
                employeeName +
                " (" +
                payId +
                ")" +
                "</br>" +
                "Total Overtime Bookings: " +
                result.length;
              for (let x = 0; x < result.length; x++) {
                breakdown_results.push({
                  code: result[x].leave_type_code,
                  start_date: result[x].start_date,
                  roster: result[x].roster,
                  shift: result[x].shift,
                  location: result[x].location,
                  period: toProperCase(result[x].bk_period),
                  hours: result[x].hours,
                  created_by: result[x].created_by,
                  created_date: result[x].created_date,
                });
              }
              grid.define("data", breakdown_results);
              grid.refresh();
            } else {
              bookingInfo =
                employeeName +
                " (" +
                payId +
                ")" +
                "</br>" +
                "Total Overtime Bookings: " +
                0;
            }
            $$("overtime-breakdown_details").define("template", bookingInfo);
            $$("overtime-breakdown_details").refresh();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
