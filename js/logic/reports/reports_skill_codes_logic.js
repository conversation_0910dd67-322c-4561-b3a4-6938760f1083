let skillCodesReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
    3: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  let exp_codes = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("employee_filter")
      .setValue(1);
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("shift_filter")
      .setValue("-- All Shifts --");
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("location_filter")
      .setValue("-- All Stations --");
    exp_grid = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("skill_codes_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results filtered by Roster: " +
            exp_roster +
            " / " +
            exp_shift +
            " / " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Employees with skills: " + exp_codes,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Skill Codes Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateSkillCodesReport();
      });
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("reports-page")
          .$$("reporting_skill_codes")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_skill_codes")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_skill_codes")
          .$$("location_filter")
          .getText();
        exp_codes = $$("reports-page")
          .$$("reporting_skill_codes")
          .$$("code_filter")
          .getValue();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Skill Codes Report (" + moment().format("DD-MM-YYYY") + ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_skill_codes")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_skill_codes")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("reports-page")
            .$$("reporting_skill_codes")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_skill_codes")
            .$$("location_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_skill_codes")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_skill_codes")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_skill_codes")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  skill_codes_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("code_filter");
    let results = JSON.parse(data);
    let skill_codes = [];
    for (let x = 0; x < results.length; x++) {
      skill_codes.push({ id: results[x].code, value: results[x].code });
    }
    select.define("options", skill_codes);
    select.refresh();
    select.getPopup().queryView({ labelRight: "Select all" }).show();
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("reports-page")
                  .$$("reporting_skill_codes")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_skill_codes")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_skill_codes")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_skill_codes")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateSkillCodesReport() {
    let grid = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("skill_codes_grid");
    let roster = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("roster_filter")
      .getText();
    let shift = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("shift_filter")
      .getText();
    let location = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("location_filter")
      .getText();
    let payId = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("employee_filter")
      .getValue();
    let skillCode = $$("reports-page")
      .$$("reporting_skill_codes")
      .$$("code_filter")
      .getValue();
    if (skillCode == "") {
      webix.alert("You must select at least 1 skill code!");
    } else {
      $$("loader-window").show();
      grid.clearAll();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/admin/skill_codes_report",
          {
            pay_id: payId,
            skill_code: skillCode,
            roster: roster,
            shift: shift,
            location: location,
          },
          {
            error: function (err) {
              $$("loader-window").hide();
            },
            success: function (results) {
              let reportData = [];
              if (results) {
                let data = JSON.parse(results);
                let empName = "";
                let expiryDate = "";
                if (data.length > 0) {
                  for (let x = 0; x < data.length; x++) {
                    if (data[x].middle_name == null) {
                      empName = data[x].surname + ", " + data[x].first_name;
                    } else {
                      empName =
                        data[x].surname +
                        ", " +
                        data[x].first_name +
                        " " +
                        data[x].middle_name;
                    }
                    if ((data[x].expire_date = "01/01/2100")) {
                      expiryDate = "Never";
                    } else {
                      expiryDate = data[x].expire_date;
                    }
                    reportData.push({
                      pay_id: data[x].pay_id,
                      name: empName.toUpperCase(),
                      rank: data[x].rank,
                      roster: data[x].roster,
                      shift: data[x].shift,
                      location: data[x].location,
                      code: data[x].code,
                      description: data[x].description,
                      group: data[x].group,
                      expiry_date: expiryDate,
                    });
                  }
                }
              }
              grid.define("data", reportData);
              grid.refresh();
              $$("loader-window").hide();
            },
          },
        );
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
