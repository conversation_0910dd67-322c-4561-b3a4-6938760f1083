let sicknessSummaryReport = (function () {
  let shiftArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let grid_log;
  let exp_fromDate = "";
  let exp_toDate = "";
  let exp_roster = "";
  let exp_shift = "";
  let selEmployee = "";
  let date_period = "";
  let empName = "";
  let employee = [];
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("from")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("to")
      .setValue(endOfMonth);
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("employee_filter")
      .setValue(1);
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("rosters")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("shift")
      .setValue("-- All Shifts --");
    exp_grid = $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("sickness_summary_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Show Sickness Totals For: " + exp_roster + " | " + exp_shift,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Sickness Summary Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
    grid_log = $$("sickness-breakdown_grid");
    let defaultHandler2 = grid_log.$exportView;
    grid_log.$exportView = function (options) {
      let returnValue = defaultHandler2.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Sick Leave taken between " + exp_fromDate + " - " + exp_toDate,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Sick Bookings Log for " + selEmployee,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("btn_export_excel").attachEvent("onItemClick", function (id, e) {
      let annToDate = $$("reports-page")
        .$$("reporting_sickness_summary")
        .$$("use_anniversary_date")
        .getValue();
      if (annToDate === 1) {
        exp_fromDate = date_period.substring(0, 10);
        exp_toDate = date_period.substring(13, 23);
      } else {
        exp_fromDate = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("to")
          .getValue();
        exp_fromDate = moment(exp_fromDate).format("DD/MM/YYYY");
        exp_toDate = moment(exp_toDate).format("DD/MM/YYYY");
      }
      selEmployee = $$("reports-page")
        .$$("reporting_sickness_summary")
        .$$("employee_filter")
        .getText();
      let filename = "Sick Bookings Log (" + selEmployee + ")";
      if (grid_log.count() > 0) {
        webix.toExcel(grid_log, {
          filename: filename,
          styles: true,
          heights: true,
        });
      } else {
        webix.alert("No data to Export!");
      }
    });
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("sickness_summary_grid")
      .attachEvent("onItemClick", function (id, e, node) {
        if (id.column == "total_ALL") {
          let selectedRow = $$("reports-page")
            .$$("reporting_sickness_summary")
            .$$("sickness_summary_grid")
            .getSelectedItem(id);
          let roster = selectedRow[0].roster;
          let shift = selectedRow[0].shift;
          let location = selectedRow[0].location;
          empName = selectedRow[0].employee;
          employee = empName.split(", ");
          $$("sickness-breakdown-window").show();
          let period = selectedRow[0].period;
          let dateArray = period.split(" - ");
          getSickBookingDays(
            selectedRow[0].pay_id,
            dateArray[0],
            dateArray[1],
            roster,
            shift,
            location,
          );
        }
      });
    $$("btn_sickness-breakdown_close").attachEvent(
      "onItemClick",
      function (id, e) {
        $$("sickness-breakdown_details").define("template", "");
        $$("sickness-breakdown_details").refresh();
        $$("sickness-breakdown-window").hide();
      },
    );
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("use_anniversary_date")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == 1) {
          $$("reports-page")
            .$$("reporting_sickness_summary")
            .$$("from")
            .disable();
          $$("reports-page")
            .$$("reporting_sickness_summary")
            .$$("to")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_sickness_summary")
            .$$("from")
            .enable();
          $$("reports-page").$$("reporting_sickness_summary").$$("to").enable();
        }
      });
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("to")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("rosters")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("shift")
          .getText();
        let payId = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("employee_filter")
          .getValue();
        if (roster == "" || shift == "") {
          webix.alert(
            "You must select a Roster & Shift before you can Search!",
          );
        } else {
          if (
            user_permission_level == 4 ||
            user_permission_level == 5 ||
            user_permission_level == 6
          ) {
            getSkillCodes(user_logged_in, function (response) {
              if (response.some((response) => response.code == "ESC")) {
                generateSicknessSummaryReport(
                  fromDate,
                  toDate,
                  roster,
                  shift,
                  payId,
                );
              } else {
                if (payId == user_logged_in) {
                  generateSicknessSummaryReport(
                    fromDate,
                    toDate,
                    roster,
                    shift,
                    payId,
                  );
                } else {
                  webix.alert({
                    text: "You only have permission to view your own results for this report.</br>Select your name from the 'Employee' drop down list above",
                    width: 600,
                  });
                }
              }
            });
          } else {
            generateSicknessSummaryReport(
              fromDate,
              toDate,
              roster,
              shift,
              payId,
            );
          }
        }
      });
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv, function (callback) {
          if (callback == "ok") {
            if (newv == "-- All Rosters --") {
              $$("reports-page")
                .$$("reporting_sickness_summary")
                .$$("shift")
                .setValue("-- All Shifts --");
              $$("reports-page")
                .$$("reporting_sickness_summary")
                .$$("shift")
                .disable();
            } else {
              $$("reports-page")
                .$$("reporting_sickness_summary")
                .$$("shift")
                .enable();
            }
          }
        });
      });
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("shift")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("rosters")
          .getValue();
        load_shifts(roster, function (callback) {
          if (callback == "ok") {
          }
        });
      });
    $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("to")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("rosters")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_sickness_summary")
          .$$("shift")
          .getText();
        exp_shift = exp_shift.replaceAll("--", "");
        exp_roster = exp_roster.replaceAll("--", "");
        if (exp_grid.count() > 0) {
          exp_grid.clearSelection();
          webix.toExcel(exp_grid, {
            filename:
              "WFR Rostering Report - SICKNESS SUMMARY (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  function getSickBookingDays(
    payId,
    fromDate,
    toDate,
    roster,
    shift,
    location,
  ) {
    let grid = $$("sickness-breakdown_grid");
    grid.clearAll();
    let bookingInfo = "";
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/bookings/get_sickness_breakdown",
        {
          pay_id: payId,
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          location: location,
        },
        {
          error: function (err) {},
          success: function (results) {
            let result = JSON.parse(results);
            let breakdown_results = [];
            if (result.length > 0) {
              bookingInfo =
                employee[0] +
                ", " +
                toProperCase(employee[1]) +
                " (" +
                payId +
                ")" +
                "</br>" +
                "Total Sick Bookings: " +
                result.length;
              let sick_type = "";
              for (let x = 0; x < result.length; x++) {
                if (
                  result[x].leave_type_code == "SICM" ||
                  result[x].leave_type_code == "FAML"
                ) {
                  if (
                    result[x].sick_certificate === true ||
                    result[x].statutory_declaration === true
                  ) {
                    sick_type = result[x].leave_type_code;
                  } else {
                    sick_type = "(" + result[x].leave_type_code + ")";
                  }
                } else {
                  sick_type = result[x].leave_type_code;
                }
                breakdown_results.push({
                  booking_id: result[x].booking_id,
                  code: sick_type,
                  start_date: result[x].start_date,
                  roster: result[x].roster,
                  shift: result[x].shift,
                  location: result[x].location,
                  period: toProperCase(result[x].bk_period),
                  hours: result[x].hours,
                  created_by: result[x].created_by,
                  created_date: result[x].created_date,
                  stat_dec_used: result[x].statutory_declaration,
                });
              }
              grid.define("data", breakdown_results);
              grid.refresh();
              grid.eachRow(function (row) {
                let record = grid.getItem(row);
                if (record.code == "SICM" || record.code == "FAML") {
                  if (record.stat_dec_used == true) {
                    grid.addCellCss(
                      row,
                      "code",
                      "sickness_report_row_highlight",
                    );
                  }
                }
              });
            } else {
              bookingInfo =
                employee[0] +
                ", " +
                toProperCase(employee[1]) +
                " (" +
                payId +
                ")" +
                "</br>" +
                "Total Sick Bookings: " +
                0;
            }
            $$("sickness-breakdown_details").define("template", bookingInfo);
            $$("sickness-breakdown_details").refresh();
          },
        },
      );
  }
  function load_shifts(rosterName, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {
            callback();
          },
          success: function (results) {
            let shiftOptions = [];
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_sickness_summary")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_sickness_summary")
                  .$$("shift")
                  .refresh();
                callback("ok");
              } else {
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_sickness_summary")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_sickness_summary")
                  .$$("shift")
                  .refresh();
              }
            } else {
              callback();
            }
          },
        },
      );
  }
  function generateSicknessSummaryReport(
    fromDate,
    toDate,
    roster,
    shift,
    payId,
  ) {
    let grid = $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("sickness_summary_grid");
    let use_anniversary = $$("reports-page")
      .$$("reporting_sickness_summary")
      .$$("use_anniversary_date")
      .getValue();
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/sickness_summary_report",
        {
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          pay_id: payId,
          use_anniversary: use_anniversary,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let total_ALL = 0;
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (use_anniversary == 0) {
                    date_period =
                      moment(fromDate).format("DD/MM/YYYY") +
                      " - " +
                      moment(toDate).format("DD/MM/YYYY");
                  } else {
                    date_period =
                      moment(data[x].anniversary_date).format("DD/MM/YYYY") +
                      " - " +
                      moment(data[x].to_date).format("DD/MM/YYYY");
                  }
                  total_ALL =
                    data[x].total_BERE +
                    data[x].total_CVPL +
                    data[x].total_CVUL +
                    data[x].total_FAML +
                    data[x].total_SIC +
                    data[x].total_SICM +
                    data[x].total_SLUP +
                    data[x].total_SLUW +
                    data[x].total_WRSL +
                    data[x].total_CVSL +
                    data[x].total_CVFL +
                    data[x].total_CVIL +
                    data[x].total_CVWR;
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName.toUpperCase(),
                    period: date_period,
                    days: data[x].days_diff,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    total_BERE: data[x].total_BERE,
                    total_CVPL: data[x].total_CVPL,
                    total_CVUL: data[x].total_CVUL,
                    total_FAML: data[x].total_FAML,
                    total_SIC: data[x].total_SIC,
                    total_SICM: data[x].total_SICM,
                    total_SLUP: data[x].total_SLUP,
                    total_SLUW: data[x].total_SLUW,
                    total_WRSL: data[x].total_WRSL,
                    total_CVSL: data[x].total_CVSL,
                    total_CVFL: data[x].total_CVFL,
                    total_CVIL: data[x].total_CVIL,
                    total_CVWR: data[x].total_CVWR,
                    total_ALL: total_ALL,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (record.total_SIC > 5) {
                grid.addCellCss(row, "pay_id", "sickness_report_row_highlight");
                grid.addCellCss(
                  row,
                  "employee",
                  "sickness_report_row_highlight",
                );
                grid.addCellCss(row, "period", "sickness_report_row_highlight");
                grid.addCellCss(row, "days", "sickness_report_row_highlight");
                grid.addCellCss(row, "roster", "sickness_report_row_highlight");
                grid.addCellCss(row, "shift", "sickness_report_row_highlight");
                grid.addCellCss(
                  row,
                  "location",
                  "sickness_report_row_highlight",
                );
                grid.addCellCss(
                  row,
                  "total_SIC",
                  "sickness_report_row_highlight",
                );
              }
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
