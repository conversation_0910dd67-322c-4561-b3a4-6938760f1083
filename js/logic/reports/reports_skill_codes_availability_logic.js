let skillCodesAvailabilityReport = (function () {
  let customReportStyle = {
    0: { font: { name: "Arial", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_codes = "";
  let date_filter = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("date_filter")
      .setValue(new Date());
    $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("rosters")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("shift")
      .setValue("-- All Shifts --");
    exp_grid = $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("skill_codes_availability_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results filtered by Roster: " + exp_roster + " / " + exp_shift,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Employees with skills: " +
            exp_codes +
            " Available to work on " +
            moment(date_filter).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Skill Codes Availability Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let selected_date = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("date_filter")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("rosters")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("shift")
          .getText();
        let date_filter = moment(selected_date).format("YYYYMMDD");
        let skill_code_1 = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("code_filter_1")
          .getValue();
        let skill_code_2 = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("code_filter_2")
          .getValue();
        if (roster == "") {
          webix.alert("You must select a Roster before you can Search!");
        } else if (skill_code_1 == "" || skill_code_1 == 1) {
          webix.alert(
            "You must select at least one Skill Code before you can Search!",
          );
        } else {
          generateSkillCodesAvailabilityReport(
            date_filter,
            roster,
            shift,
            skill_code_1,
            skill_code_2,
          );
        }
      });
    $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("rosters")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("shift")
          .getText();
        exp_codes = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("code_filter")
          .getValue();
        date_filter = $$("reports-page")
          .$$("reporting_skill_codes_availability")
          .$$("date_filter")
          .getValue();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Skill Codes Availability Report (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv);
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_skill_codes_availability")
            .$$("shift")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_skill_codes_availability")
            .$$("shift")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_skill_codes_availability")
            .$$("shift")
            .enable();
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  skill_codes_subject.subscribe(function (data) {
    let select1 = $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("code_filter_1");
    let select2 = $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("code_filter_2");
    let skill_codes = [];
    let results = JSON.parse(data);
    for (let x = 0; x < results.length; x++) {
      skill_codes.push({ id: results[x].code, value: results[x].code });
    }
    select1.define("options", skill_codes);
    select1.refresh();
    select2.define("options", skill_codes);
    select2.refresh();
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_skill_codes_availability")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_skill_codes_availability")
                  .$$("shift")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateSkillCodesAvailabilityReport(
    date_filter,
    roster,
    shift,
    skill_code_1,
    skill_code_2,
  ) {
    let grid = $$("reports-page")
      .$$("reporting_skill_codes_availability")
      .$$("skill_codes_availability_grid");
    if (shift == "") {
      shift = "-- All Shifts --";
    }
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/skill_codes_availability_report",
        {
          date_filter: date_filter,
          roster: roster,
          shift: shift,
          skill_code_1: skill_code_1,
          skill_code_2: skill_code_2,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let temp_ra = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (
                    data[x].temp_roster == null ||
                    data[x].temp_roster == ""
                  ) {
                    temp_ra = "";
                  } else {
                    temp_ra =
                      data[x].temp_roster +
                      " / " +
                      data[x].temp_shift +
                      " / " +
                      data[x].temp_location;
                  }
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName.toUpperCase(),
                    rank: data[x].rank,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    skill_codes: data[x].skill_codes,
                    temp_ra: temp_ra,
                    shift_period: toProperCase(data[x].shift_type),
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            $$("reports-page")
              .$$("reporting_skill_codes_availability")
              .$$("records_count")
              .define("template", bookings.length + " employees found!");
            $$("reports-page")
              .$$("reporting_skill_codes_availability")
              .$$("records_count")
              .refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
