let stationPreferencesReport = (function () {
  let shiftArray = [];
  let locationsArray = [];
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    3: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_roster = "";
  let exp_shift = "";
  let exp_location = "";
  let pref_no = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("employee_filter")
      .setValue(1);
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("roster_filter")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("shift_filter")
      .setValue("-- All Shifts --");
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("location_filter")
      .setValue("-- All Stations --");
    exp_grid = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("station_preferences_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results filtered by Roster: " +
            exp_roster +
            " / " +
            exp_shift +
            " / " +
            exp_location,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Employees with Station Preferences: " + pref_no,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Station Preferences Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateStationPreferencesReport();
      });
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_roster = $$("reports-page")
          .$$("reporting_station_preference")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_station_preference")
          .$$("shift_filter")
          .getText();
        exp_location = $$("reports-page")
          .$$("reporting_station_preference")
          .$$("location_filter")
          .getText();
        pref_no = $$("reports-page")
          .$$("reporting_station_preference")
          .$$("pref_filter")
          .getValue();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Skill Codes Report (" + moment().format("DD-MM-YYYY") + ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        if (newv == "-- All Rosters --") {
          $$("reports-page")
            .$$("reporting_station_preference")
            .$$("shift_filter")
            .setValue("-- All Shifts --");
          $$("reports-page")
            .$$("reporting_station_preference")
            .$$("location_filter")
            .setValue("-- All Stations --");
          $$("reports-page")
            .$$("reporting_station_preference")
            .$$("shift_filter")
            .disable();
          $$("reports-page")
            .$$("reporting_station_preference")
            .$$("location_filter")
            .disable();
        } else {
          $$("reports-page")
            .$$("reporting_station_preference")
            .$$("shift_filter")
            .enable();
          $$("reports-page")
            .$$("reporting_station_preference")
            .$$("location_filter")
            .enable();
        }
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_station_preference")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_station_preference")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                locationsArray = shiftList.data[0].locations.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                let locationOptions = [];
                locationsArray.forEach(function (value) {
                  let locationFullName = value.split("-");
                  let locationName = locationFullName[1].trim();
                  locationOptions.push(locationName);
                });
                locationOptions.unshift("-- All Stations --");
                $$("reports-page")
                  .$$("reporting_station_preference")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_station_preference")
                  .$$("shift_filter")
                  .refresh();
                $$("reports-page")
                  .$$("reporting_station_preference")
                  .$$("location_filter")
                  .define("options", locationOptions);
                $$("reports-page")
                  .$$("reporting_station_preference")
                  .$$("location_filter")
                  .refresh();
              }
            }
          },
        },
      );
  }
  function generateStationPreferencesReport() {
    let roster = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("roster_filter")
      .getText();
    let shift = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("shift_filter")
      .getText();
    let location = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("location_filter")
      .getText();
    let payId = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("employee_filter")
      .getValue();
    let pref_filter = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("pref_filter")
      .getValue();
    let grid = $$("reports-page")
      .$$("reporting_station_preference")
      .$$("station_preferences_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/station_preferences_report",
        {
          pay_id: payId,
          pref_no: pref_filter,
          roster: roster,
          shift: shift,
          location: location,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            let empName = "";
            let prefNo = "";
            let permRank = "";
            let sortIndex = 0;
            let updatedDate = moment();
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  if (data[x].pref_no == 1) {
                    prefNo = "1st";
                  } else if (data[x].pref_no == 2) {
                    prefNo = "2nd";
                  } else if (data[x].pref_no == 3) {
                    prefNo = "3rd";
                  }
                  if (data[x].acting_up === 1 || data[x].acting_up === true) {
                    if (data[x].rank == "ACFO") {
                      permRank = "CMD";
                    } else if (data[x].rank == "CMD") {
                      permRank = "SO";
                    } else if (data[x].rank == "SO") {
                      permRank = "SFF";
                    } else if (data[x].rank == "MOFF") {
                      permRank = "SFF";
                    } else if (data[x].rank == "COFF") {
                      permRank = "SFF";
                    }
                  } else {
                    permRank = data[x].rank;
                  }
                  if (permRank === "CMD") {
                    sortIndex = 1;
                  } else if (permRank === "SO") {
                    sortIndex = 2;
                  } else if (permRank === "MOFF") {
                    sortIndex = 3;
                  } else if (permRank === "COFF") {
                    sortIndex = 4;
                  } else if (
                    permRank === "SFF" ||
                    permRank === "SCOP" ||
                    permRank === "COP" ||
                    permRank === "FF"
                  ) {
                    sortIndex = 5;
                  } else if (permRank === "ESFF" || permRank === "EFF") {
                    sortIndex = 6;
                  }
                  updatedDate = moment(
                    data[x].updated_date,
                    "DD/MM/YYYY HH:mm",
                  ).toDate();
                  reportData.push({
                    pay_id: data[x].pay_id,
                    name: empName,
                    rank: permRank,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    pref_no: prefNo,
                    updated_date: updatedDate,
                    sort_index: sortIndex,
                  });
                }
                grid.define("data", reportData);
                grid.refresh();
                grid.sort([
                  { by: "roster", dir: "asc", as: "string" },
                  { by: "shift", dir: "asc", as: "string" },
                  { by: "location", dir: "asc", as: "string" },
                  { by: "pref_no", dir: "asc", as: "string" },
                  { by: "sort_index", dir: "asc", as: "int" },
                  { by: "updated_date", dir: "asc", as: "date" },
                ]);
                $$("loader-window").hide();
              } else {
                $$("loader-window").hide();
                webix.alert("No data to report!");
              }
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
