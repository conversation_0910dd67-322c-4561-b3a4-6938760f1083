let supplementaryReport = (function () {
  let customReportStyle1 = {
    0: { font: { name: "Arial", sz: 14, bold: true } },
  };
  let customReportStyle2 = {
    0: { font: { name: "Aria<PERSON>", sz: 9, bold: true } },
  };
  let customReportStyle3 = {
    0: { font: { name: "Arial", sz: 9, bold: true } },
  };
  let customReportStyle4 = {
    0: { font: { name: "Arial", sz: 9, bold: true } },
  };
  let customReportStyle5 = {
    0: { font: { name: "Arial", sz: 9, bold: true } },
  };
  let customReportStyle6 = {
    0: { font: { name: "Arial", sz: 9, bold: true } },
  };
  let exp_grid;
  let supp_date = "";
  let supp_name = "";
  let supp_shift = "";
  function initApplication() {
    eventHandlers();
    generateSupplementaryReport();
    exp_grid = $$("reports-page")
      .$$("reporting_supplementary")
      .$$("supplementary_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].exportData.unshift([
          "             " +
            "DATE: " +
            "             " +
            moment(supp_date).format("DD/MM/YYYY"),
        ]);
        returnValue[0].exportData.unshift([
          "             " + "PRINT NAME: " + "    " + supp_name,
        ]);
        returnValue[0].exportData.unshift([
          "             " +
            "SIGNED " +
            "   " +
            "_________________________________",
        ]);
        returnValue[0].exportData.unshift([""]);
        returnValue[0].exportData.unshift([
          "             " +
            "SAFETY MANNING AT ALL STATIONS ON THE ABOVE DATE IN ACCORDANCE WITH INSTRUCTIONS",
        ]);
        returnValue[0].exportData.unshift([
          "             " +
            "I CERTIFY THAT THE STAFF BELOW WERE REQUIRED ON RELIEVING DUTIES TO MAINTAIN PROPER",
        ]);
        returnValue[0].exportData.unshift([""]);
        returnValue[0].exportData.unshift([
          "             " +
            supp_shift +
            "            " +
            "SUMMARY OF TRAVELLING, OVERTIME, RECALLS & SPECIAL PAYMENTS",
        ]);
        returnValue[0].styles.unshift({});
        returnValue[0].styles.unshift(customReportStyle6);
        returnValue[0].styles.unshift(customReportStyle5);
        returnValue[0].styles.unshift(customReportStyle4);
        returnValue[0].styles.unshift({});
        returnValue[0].styles.unshift(customReportStyle3);
        returnValue[0].styles.unshift(customReportStyle2);
        returnValue[0].styles.unshift({});
        returnValue[0].styles.unshift(customReportStyle1);
        returnValue[0].spans[0] = { e: { c: 12, r: 10 }, s: { c: 0, r: 10 } };
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_supplementary")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        $$("supplementary_report-popup").show();
        $$("supplementary_export_date").setValue(new Date());
      });
    $$("btn_supplementary_report_close").attachEvent(
      "onItemClick",
      function (id, e) {
        $$("supplementary_report-popup").hide();
      },
    );
    $$("btn_supplementary_generate").attachEvent(
      "onItemClick",
      function (id, e) {
        supp_date = $$("supplementary_export_date").getValue();
        supp_name = $$("supplementary_export_commander").getValue();
        supp_shift = $$("supplementary_export_shift").getText();
        if (supp_name == "" || supp_shift == "") {
          webix.alert({
            text: "You must enter a 'Commander' and select a 'Shift' before you can generate the report!",
            width: 500,
          });
        } else {
          if (exp_grid.count() > 0) {
            webix.toExcel(exp_grid, {
              filename:
                "WFR Rostering Report - SUPPLEMENTARY (" +
                moment().format("DD-MM-YYYY") +
                ")",
              heights: true,
              styles: true,
              footer: false,
            });
            $$("supplementary_export_commander").setValue("");
            $$("supplementary_export_shift").setValue("");
            $$("supplementary_report-popup").hide();
          } else {
            webix.alert("No data to Export!");
          }
        }
      },
    );
  }
  function generateSupplementaryReport() {
    let grid = $$("reports-page")
      .$$("reporting_supplementary")
      .$$("supplementary_grid");
    let suppObject = {};
    for (let x = 0; x <= 25; x++) {
      suppObject.id = x;
      suppObject.pay_id = "";
      suppObject.employee = "";
      suppObject.rank = "";
      suppObject.reason = "";
      suppObject.stn = "";
      suppObject.higher_duties = "";
      suppObject.left_from = "";
      suppObject.gear_taken = "";
      suppObject.booked_on = "";
      suppObject.on = "";
      suppObject.off = "";
      suppObject.total = "";
      suppObject.fin_ad = "";
      grid.parse(suppObject);
    }
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
