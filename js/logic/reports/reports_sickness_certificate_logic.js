let sicknessCertificateReport = (function () {
  let shiftArray = [];
  let customReportStyle = {
    0: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Aria<PERSON>", sz: 10, bold: true } },
    3: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate = "";
  let exp_toDate = "";
  let exp_roster = "";
  let exp_shift = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("from")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("to")
      .setValue(endOfMonth);
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("employee_filter")
      .setValue(1);
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("rosters")
      .setValue("-- All Rosters --");
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("shift")
      .setValue("-- All Shifts --");
    exp_grid = $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("sickness_certificate_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Show Sickness Totals For: " + exp_roster + " | " + exp_shift,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["Sickness Certificate Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("to")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("rosters")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("shift")
          .getText();
        let payId = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("employee_filter")
          .getValue();
        if (roster == "" || shift == "") {
          webix.alert(
            "You must select a Roster & Shift before you can Search!",
          );
        } else {
          generateSicknessCertificateReport(
            fromDate,
            toDate,
            roster,
            shift,
            payId,
          );
        }
      });
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv, function (callback) {
          if (callback == "ok") {
            if (newv == "-- All Rosters --") {
              $$("reports-page")
                .$$("reporting_sickness_certificate")
                .$$("shift")
                .setValue("-- All Shifts --");
              $$("reports-page")
                .$$("reporting_sickness_certificate")
                .$$("shift")
                .disable();
            } else {
              $$("reports-page")
                .$$("reporting_sickness_certificate")
                .$$("shift")
                .enable();
            }
          }
        });
      });
    $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("to")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("rosters")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_sickness_certificate")
          .$$("shift")
          .getText();
        exp_shift = exp_shift.replaceAll("--", "");
        exp_roster = exp_roster.replaceAll("--", "");
        if (exp_grid.count() > 0) {
          exp_grid.clearSelection();
          webix.toExcel(exp_grid, {
            filename:
              "Sickness Certificate Report (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
            heights: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      options.push("-- All Rosters --");
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  employees_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("employee_filter");
    let employee_list = [];
    if (data) {
      employee_list.push({ id: 1, value: "-- All Employees --" });
      data.forEach(function (value) {
        employee_list.push({ id: value.id, value: value.value });
      });
      select.define("options", employee_list);
      select.refresh();
    }
  });
  function load_shifts(rosterName, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {
            callback();
          },
          success: function (results) {
            let shiftOptions = [];
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_sickness_certificate")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_sickness_certificate")
                  .$$("shift")
                  .refresh();
                callback("ok");
              } else {
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_sickness_certificate")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_sickness_certificate")
                  .$$("shift")
                  .refresh();
              }
            } else {
              callback();
            }
          },
        },
      );
  }
  function generateSicknessCertificateReport(
    fromDate,
    toDate,
    roster,
    shift,
    payId,
  ) {
    let grid = $$("reports-page")
      .$$("reporting_sickness_certificate")
      .$$("sickness_certificate_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/sickness_certificate_report",
        {
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          pay_id: payId,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            let total_sicm = 0;
            let total_faml = 0;
            let total_slup = 0;
            let total_cert = 0;
            let total_stat_dec = 0;
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              let cert_provided = "";
              let stat_dec_provided = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  empName = data[x].surname + ", " + data[x].first_name;
                  if (data[x].sick_certificate == 1) {
                    cert_provided = "Yes";
                    total_cert += 1;
                  } else {
                    cert_provided = "-";
                  }
                  if (data[x].statutory_declaration == 1) {
                    stat_dec_provided = "Yes";
                    total_stat_dec += 1;
                  } else {
                    stat_dec_provided = "-";
                  }
                  if (
                    data[x].sick_certificate == null &&
                    data[x].statutory_declaration == null
                  ) {
                    cert_provided = "-";
                    stat_dec_provided = "-";
                  }
                  if (data[x].leave_type_code == "SICM") {
                    total_sicm += 1;
                  } else if (data[x].leave_type_code == "FAML") {
                    total_faml += 1;
                  } else if (data[x].leave_type_code == "SLUP") {
                    total_slup += 1;
                  }
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName,
                    rank: data[x].rank,
                    sic_date: data[x].start_date,
                    sic_type: data[x].leave_type_code,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    cert_provided: cert_provided,
                    stat_dec_provided: stat_dec_provided,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            grid.adjustColumn("employee");
            grid.adjustColumn("roster");
            grid.adjustColumn("shift");
            let total_records = total_faml + total_sicm + total_slup;
            let sicm_percent = 0;
            let faml_percent = 0;
            let slup_percent = 0;
            let cert_percent = 0;
            let stat_dec_percent = 0;
            if (total_records > 0) {
              sicm_percent = getPercentage(total_sicm, total_records);
              faml_percent = getPercentage(total_faml, total_records);
              slup_percent = getPercentage(total_slup, total_records);
              cert_percent = getPercentage(total_cert, total_records);
              stat_dec_percent = getPercentage(total_stat_dec, total_records);
            }
            grid.getColumnConfig("pay_id").footer[0].text =
              "Total Records = " + total_records;
            grid.getColumnConfig("rank").footer[0].text =
              "SICM Records = " + total_sicm + " (" + sicm_percent + "%)";
            grid.getColumnConfig("roster").footer[0].text =
              "FAML Records = " + total_faml + " (" + faml_percent + "%)";
            grid.getColumnConfig("location").footer[0].text =
              "SLUP Records = " + total_slup + " (" + slup_percent + "%)";
            grid.getColumnConfig("cert_provided").footer[0].text =
              cert_percent + "%";
            grid.getColumnConfig("stat_dec_provided").footer[0].text =
              stat_dec_percent + "%";
            grid.refreshColumns();
            $$("loader-window").hide();
          },
        },
      );
  }
  function getPercentage(partialValue, totalValue) {
    return ((100 * partialValue) / totalValue).toFixed(1);
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
