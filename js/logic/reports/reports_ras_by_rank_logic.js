let rosterArrangementsByRankReport = (function () {
  let shiftArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "Arial", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate;
  let exp_toDate;
  let exp_roster = "";
  let exp_shift = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("from")
      .setValue(startOfMonth);
    $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("to")
      .setValue(endOfMonth);
    $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("roster_filter")
      .setValue("");
    exp_grid = $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("roster_arrangement_by_rank_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Showing shift totals for: " + exp_roster + " | " + exp_shift,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Roster Arrangements Report By Rank",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("to")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("roster_filter")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("shift_filter")
          .getText();
        if (roster == "-- All Rosters --") {
          generateRAsByRankReport(fromDate, toDate, roster, shift);
        } else {
          if (roster == "" || shift == "") {
            webix.alert("You must select a 'Roster' and 'Shift'");
          } else {
            generateRAsByRankReport(fromDate, toDate, roster, shift);
          }
        }
      });
    $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("to")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("shift_filter")
          .getText();
        exp_roster = exp_roster.replaceAll("--", "");
        exp_shift = exp_shift.replaceAll("--", "");
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "Roster Arrangements By Rank Report - (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
            heights: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
    $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        $$("reports-page")
          .$$("reporting_ras_by_rank")
          .$$("shift_filter")
          .setValue("");
        load_shifts(newv);
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      options.unshift("-- All Rosters --");
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    if (rosterName == "-- All Rosters --") {
      $$("reports-page")
        .$$("reporting_ras_by_rank")
        .$$("shift_filter")
        .define("options", []);
      $$("reports-page")
        .$$("reporting_ras_by_rank")
        .$$("shift_filter")
        .refresh();
      $$("reports-page")
        .$$("reporting_ras_by_rank")
        .$$("shift_filter")
        .disable();
    } else {
      $$("reports-page")
        .$$("reporting_ras_by_rank")
        .$$("shift_filter")
        .enable();
      webix
        .ajax()
        .headers({ Authorization: "Bearer " + api_key })
        .get(
          server_url + "/schedule/shifts",
          { roster_name: rosterName },
          {
            error: function (err) {
              webix.alert(
                "The was an error loading the Shifts for this Roster. Please try again!",
              );
            },
            success: function (results) {
              if (results) {
                let shiftList = JSON.parse(results);
                if (shiftList.data.length > 0) {
                  shiftArray = shiftList.data[0].shifts.split(",");
                  let shiftOptions = [];
                  shiftArray.forEach(function (value) {
                    shiftOptions.push(value);
                  });
                  $$("reports-page")
                    .$$("reporting_ras_by_rank")
                    .$$("shift_filter")
                    .define("options", shiftOptions);
                  $$("reports-page")
                    .$$("reporting_ras_by_rank")
                    .$$("shift_filter")
                    .refresh();
                }
              }
            },
          },
        );
    }
  }
  function generateRAsByRankReport(fromDate, toDate, roster, shift) {
    let grid = $$("reports-page")
      .$$("reporting_ras_by_rank")
      .$$("roster_arrangement_by_rank_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/roster_arrangements_by_rank_report",
        { from_date: fromDate, to_date: toDate, roster: roster, shift: shift },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              let shift_period = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (roster == "-- All Rosters --") {
                    shift_period = "Mixed";
                  } else {
                    if (data[x].icon == "sun") {
                      shift_period = "Day";
                    } else if (data[x].icon == "moon") {
                      shift_period = "Night";
                    } else if (data[x].icon == "ban") {
                      shift_period = "Off";
                    } else {
                      shift_period = "-";
                    }
                  }
                  reportData.push({
                    date: moment(data[x].date_string, "YYYYMMDD").format(
                      "DD/MM/YYYY",
                    ),
                    period: shift_period,
                    total_ras: data[x].total_ras,
                    co_total: data[x].co_total,
                    act_co_total: data[x].act_co_total,
                    dco_total: data[x].dco_total,
                    act_dco_total: data[x].act_dco_total,
                    acfo_total: data[x].acfo_total,
                    act_acfo_total: data[x].act_acfo_total,
                    man_total: data[x].man_total,
                    act_man_total: data[x].act_man_total,
                    cmd_total: data[x].cmd_total,
                    act_cmd_total: data[x].act_cmd_total,
                    so_total: data[x].so_total,
                    act_so_total: data[x].act_so_total,
                    sff_ff_total: data[x].sff_ff_total,
                    moff_total: data[x].moff_total,
                    act_moff_total: data[x].act_moff_total,
                    coff_total: data[x].coff_total,
                    act_coff_total: data[x].act_coff_total,
                    scop_cop_total: data[x].scop_cop_total,
                  });
                }
              }
            }
            grid.define("data", reportData);
            grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
