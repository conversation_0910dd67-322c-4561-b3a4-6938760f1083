let overtimeSummaryReport = (function () {
  let shiftArray = [];
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate = "";
  let exp_toDate = "";
  let exp_roster = "";
  let exp_shift = "";
  let exp_reason = "";
  function initApplication() {
    eventHandlers();
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("from")
      .setValue(new Date());
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("to")
      .setValue(new Date());
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("shift_filter")
      .setValue("-- All Shifts --");
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("reason_filter")
      .setValue("All");
    exp_grid = $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("overtime_summary_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results filtered by Roster: " +
            exp_roster +
            " | Shift: " +
            exp_shift +
            " | Reason: " +
            exp_reason,
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Results for period: " +
            moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Overtime Summary Report (Recalls)",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      const data = defaultHandler.apply(this, [options]);
      if (
        options.export_mode == "excel" &&
        options.styles &&
        !options.dataOnly
      ) {
        const styles = data[0].styles;
        for (let row = 0; row < styles.length; row++)
          for (let col in styles[row])
            styles[row][col].alignment.wrapText = true;
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("avg_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let grid = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("overtime_summary_grid");
        let one_year_ago = moment().subtract(1, "years");
        one_year_ago = one_year_ago.add(1, "days");
        if (newv == 1) {
          grid.showColumn("cmd_hrs");
          grid.showColumn("avg_cmd");
          grid.showColumn("so_hrs");
          grid.showColumn("avg_so");
          grid.showColumn("sff_ff_hrs");
          grid.showColumn("avg_sff_ff");
          grid.showColumn("coff_hrs");
          grid.showColumn("avg_coff");
          grid.showColumn("scop_cop_hrs");
          grid.showColumn("avg_scop_cop");
          grid.showColumn("moff_hrs");
          grid.showColumn("avg_moff");
          $$("reports-page")
            .$$("reporting_overtime_summary")
            .$$("from")
            .setValue(moment(one_year_ago).toDate());
        } else {
          grid.hideColumn("cmd_hrs");
          grid.hideColumn("avg_cmd");
          grid.hideColumn("so_hrs");
          grid.hideColumn("avg_so");
          grid.hideColumn("sff_ff_hrs");
          grid.hideColumn("avg_sff_ff");
          grid.hideColumn("coff_hrs");
          grid.hideColumn("avg_coff");
          grid.hideColumn("scop_cop_hrs");
          grid.hideColumn("avg_scop_cop");
          grid.hideColumn("moff_hrs");
          grid.hideColumn("avg_moff");
          $$("reports-page")
            .$$("reporting_overtime_summary")
            .$$("from")
            .setValue(new Date());
        }
        grid.clearAll();
      });
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("to")
          .getValue();
        let roster = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("roster_filter")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("shift_filter")
          .getText();
        let reason = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("reason_filter")
          .getText();
        if (shift != "") {
          if (roster != "") {
            getLocationMinValues(function (minValuesArray) {
              generateOvertimeSummaryReport(
                fromDate,
                toDate,
                roster,
                shift,
                reason,
                minValuesArray,
              );
            });
          } else {
            webix.alert("You must select a Roster!");
          }
        } else {
          webix.alert("You must select a Shift!");
        }
      });
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        let filename =
          "OVERTIME SUMMARY REPORT (" + moment().format("DD-MM-YYYY") + ")";
        exp_fromDate = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("to")
          .getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("roster_filter")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("shift_filter")
          .getText();
        exp_reason = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("reason_filter")
          .getText();
        webix.toExcel(exp_grid, {
          filename: filename,
          styles: true,
          heights: true,
        });
      });
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("roster_filter")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv, function (callback) {
          if (callback == "ok") {
            $$("reports-page")
              .$$("reporting_overtime_summary")
              .$$("shift_filter")
              .setValue("");
            $$("reports-page")
              .$$("reporting_overtime_summary")
              .$$("shift_filter")
              .enable();
          }
        });
      });
    $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("shift_filter")
      .attachEvent("onChange", function (newv, oldv) {
        let roster = $$("reports-page")
          .$$("reporting_overtime_summary")
          .$$("roster_filter")
          .getValue();
        load_shifts(roster, function (callback) {
          if (callback == "ok") {
          }
        });
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("roster_filter");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName, callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {
            callback();
          },
          success: function (results) {
            let shiftOptions = [];
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                shiftArray = shiftList.data[0].shifts.split(",");
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_overtime_summary")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_overtime_summary")
                  .$$("shift_filter")
                  .refresh();
                callback("ok");
              } else {
                shiftOptions.unshift("-- All Shifts --");
                $$("reports-page")
                  .$$("reporting_overtime_summary")
                  .$$("shift_filter")
                  .define("options", shiftOptions);
                $$("reports-page")
                  .$$("reporting_overtime_summary")
                  .$$("shift_filter")
                  .refresh();
              }
            } else {
              callback();
            }
          },
        },
      );
  }
  function generateOvertimeSummaryReport(
    fromDate,
    toDate,
    roster,
    shift,
    reason,
    minValuesArray,
  ) {
    let grid = $$("reports-page")
      .$$("reporting_overtime_summary")
      .$$("overtime_summary_grid");
    $$("loader-window").show();
    grid.clearAll();
    grid.markSorting();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/overtime_summary_report",
        {
          from_date: fromDate,
          to_date: toDate,
          roster: roster,
          shift: shift,
          reason: reason,
        },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            let cmd_totals = 0;
            let so_totals = 0;
            let sff_ff_totals = 0;
            let coff_totals = 0;
            let scop_cop_totals = 0;
            let moff_totals = 0;
            let cmd_totals_hrs = 0;
            let so_totals_hrs = 0;
            let sff_ff_totals_hrs = 0;
            let coff_totals_hrs = 0;
            let scop_cop_totals_hrs = 0;
            let moff_totals_hrs = 0;
            let avg_cmd = 0;
            let avg_so = 0;
            let avg_sff_ff = 0;
            let avg_coff = 0;
            let avg_scop_cop = 0;
            let avg_moff = 0;
            let minCMD = 0;
            let minSO = 0;
            let minFF = 0;
            let minCOFF = 0;
            let minSCOP = 0;
            let minMOF = 0;
            let cmd_grand_total_hrs = 0;
            let so_grand_total_hrs = 0;
            let sff_ff_grand_total_hrs = 0;
            let coff_grand_total_hrs = 0;
            let scop_cop_grand_total_hrs = 0;
            let moff_grand_total_hrs = 0;
            let shiftDiv = 0;
            let isComms = false;
            let so_days = 0;
            let coff_days = 0;
            let sff_days = 0;
            let scop_days = 0;
            let cmd_rows = 0;
            let so_rows = 0;
            let sff_ff_rows = 0;
            let coff_rows = 0;
            let scop_cop_rows = 0;
            let moff_rows = 0;
            let cmd_grand_total_avg = 0;
            let so_grand_total_avg = 0;
            let sff_ff_grand_total_avg = 0;
            let coff_grand_total_avg = 0;
            let scop_cop_grand_total_avg = 0;
            let moff_grand_total_avg = 0;
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                if (shift == "-- All Shifts --") {
                  shiftDiv = 4;
                } else {
                  shiftDiv = 1;
                }
                for (let x = 0; x < data.length; x++) {
                  let min_values = {};
                  min_values = minValuesArray.find(
                    (obj) => obj.name === data[x].name,
                  );
                  if (data[x].name == "Adelaide Comms") {
                    isComms = true;
                  } else {
                    isComms = false;
                  }
                  cmd_totals = cmd_totals + data[x].cmd_total;
                  if (isComms === false) {
                    so_totals = so_totals + data[x].so_total;
                  }
                  if (isComms === false) {
                    sff_ff_totals = sff_ff_totals + data[x].sff_ff_total;
                  }
                  if (isComms === false) {
                    coff_totals = coff_totals + data[x].coff_total;
                  } else {
                    coff_totals =
                      coff_totals + data[x].coff_total + data[x].so_total;
                  }
                  if (isComms === false) {
                    scop_cop_totals = scop_cop_totals + data[x].scop_cop_total;
                  } else {
                    scop_cop_totals =
                      scop_cop_totals +
                      data[x].scop_cop_total +
                      data[x].sff_ff_total;
                  }
                  moff_totals = moff_totals + data[x].moff_total;
                  cmd_grand_total_hrs =
                    cmd_grand_total_hrs + data[x].cmd_total_hrs;
                  so_grand_total_hrs =
                    so_grand_total_hrs + data[x].so_total_hrs;
                  sff_ff_grand_total_hrs =
                    sff_ff_grand_total_hrs + data[x].sff_ff_total_hrs;
                  coff_grand_total_hrs =
                    coff_grand_total_hrs + data[x].coff_total_hrs;
                  scop_cop_grand_total_hrs =
                    scop_cop_grand_total_hrs + data[x].scop_cop_total_hrs;
                  moff_grand_total_hrs =
                    moff_grand_total_hrs + data[x].moff_total_hrs;
                  if (data[x].cmd_total_hrs != null) {
                    if (min_values.min_CMD == 0) {
                      minCMD = 1;
                    } else {
                      minCMD = min_values.min_CMD;
                    }
                    cmd_totals_hrs = data[x].cmd_total_hrs;
                    if (minCMD >= 1) {
                      avg_cmd = cmd_totals_hrs / (shiftDiv * minCMD);
                    } else {
                      avg_cmd = 0;
                    }
                  } else {
                    cmd_totals_hrs = 0;
                    avg_cmd = 0;
                  }
                  if (isComms === true) {
                    so_totals_hrs = 0;
                    avg_so = 0;
                    so_days = 0;
                  } else {
                    if (data[x].so_total_hrs != null) {
                      if (min_values.min_SO == 0) {
                        minSO = 1;
                      } else {
                        minSO = min_values.min_SO;
                      }
                      so_totals_hrs = data[x].so_total_hrs;
                      if (minSO >= 1) {
                        if (
                          data[x].name == "Mt Gambier" &&
                          shift == "-- All Shifts --"
                        ) {
                          avg_so = so_totals_hrs / 2;
                        } else if (
                          data[x].name == "OTR South" ||
                          data[x].name == "OTR North" ||
                          data[x].name == "OTR Central"
                        ) {
                          avg_so = so_totals_hrs / 1;
                        } else {
                          avg_so = so_totals_hrs / (shiftDiv * minSO);
                        }
                      } else {
                        avg_so = 0;
                      }
                      so_days = data[x].so_total;
                    } else {
                      so_totals_hrs = 0;
                      avg_so = 0;
                      so_days = 0;
                    }
                  }
                  if (isComms === true) {
                    if (data[x].scop_cop_total_hrs != null) {
                      if (min_values.min_SCOP + min_values.min_COP == 0) {
                        minSCOP = 1;
                      } else {
                        minSCOP = min_values.min_SCOP + min_values.min_COP;
                      }
                      if (data[x].sff_ff_total_hrs != null) {
                        scop_cop_totals_hrs =
                          data[x].scop_cop_total_hrs + data[x].sff_ff_total_hrs;
                      } else {
                        scop_cop_totals_hrs = data[x].scop_cop_total_hrs;
                      }
                      if (minSCOP >= 1) {
                        avg_scop_cop =
                          scop_cop_totals_hrs / (shiftDiv * minSCOP);
                      } else {
                        avg_scop_cop = 0;
                      }
                      scop_days = data[x].scop_cop_total + data[x].sff_ff_total;
                    } else {
                      scop_cop_totals_hrs = 0;
                      avg_scop_cop = 0;
                      scop_days = 0;
                    }
                  } else {
                    if (data[x].scop_cop_total_hrs != null) {
                      if (min_values.min_SCOP + min_values.min_COP == 0) {
                        minSCOP = 1;
                      } else {
                        minSCOP = min_values.min_SCOP + min_values.min_COP;
                      }
                      scop_cop_totals_hrs = data[x].scop_cop_total_hrs;
                      if (minSCOP >= 1) {
                        avg_scop_cop =
                          scop_cop_totals_hrs / (shiftDiv * minSCOP);
                      } else {
                        avg_scop_cop = 0;
                      }
                      scop_days = data[x].scop_cop_total;
                    } else {
                      scop_cop_totals_hrs = 0;
                      avg_scop_cop = 0;
                      scop_days = 0;
                    }
                  }
                  if (isComms === true) {
                    sff_ff_totals_hrs = 0;
                    avg_sff_ff = 0;
                    sff_days = 0;
                  } else {
                    if (data[x].sff_ff_total_hrs != null) {
                      if (min_values.min_SF + min_values.min_FF == 0) {
                        minFF = 1;
                      } else {
                        minFF = min_values.min_SF + min_values.min_FF;
                      }
                      sff_ff_totals_hrs =
                        data[x].sff_ff_total_hrs + data[x].scop_cop_total_hrs;
                      if (minFF >= 1) {
                        if (
                          data[x].name == "Mt Gambier" &&
                          shift == "-- All Shifts --"
                        ) {
                          avg_sff_ff = sff_ff_totals_hrs / 6;
                        } else if (
                          data[x].name == "Port Pirie" &&
                          shift == "-- All Shifts --"
                        ) {
                          avg_sff_ff = sff_ff_totals_hrs / 16;
                        } else if (
                          data[x].name == "Port Pirie" &&
                          shift != "-- All Shifts --"
                        ) {
                          avg_sff_ff = sff_ff_totals_hrs / 4;
                        } else if (
                          data[x].name == "OTR South" ||
                          data[x].name == "OTR North" ||
                          data[x].name == "OTR Central"
                        ) {
                          avg_sff_ff = sff_ff_totals_hrs / 3;
                        } else {
                          avg_sff_ff = sff_ff_totals_hrs / (shiftDiv * minFF);
                        }
                      } else {
                        avg_sff_ff = 0;
                      }
                      sff_days = data[x].sff_ff_total + data[x].scop_cop_total;
                      scop_cop_totals_hrs = 0;
                      avg_scop_cop = 0;
                      scop_days = 0;
                    } else {
                      sff_ff_totals_hrs = 0;
                      avg_sff_ff = 0;
                      sff_days = 0;
                    }
                  }
                  if (isComms === true) {
                    if (data[x].coff_total_hrs != null) {
                      if (min_values.min_COFF == 0) {
                        minCOFF = 1;
                      } else {
                        minCOFF = min_values.min_COFF;
                      }
                      if (data[x].so_total_hrs != null) {
                        coff_totals_hrs =
                          data[x].coff_total_hrs + data[x].so_total_hrs;
                      } else {
                        coff_totals_hrs = data[x].coff_total_hrs;
                      }
                      if (minCOFF >= 1) {
                        avg_coff = coff_totals_hrs / (shiftDiv * minCOFF);
                      } else {
                        avg_coff = 0;
                      }
                      coff_days = data[x].coff_total + data[x].so_total;
                    } else {
                      coff_totals_hrs = 0;
                      avg_coff = 0;
                      coff_days = 0;
                    }
                  } else {
                    if (data[x].coff_total_hrs != null) {
                      if (min_values.min_COFF == 0) {
                        minCOFF = 1;
                      } else {
                        minCOFF = min_values.min_COFF;
                      }
                      coff_totals_hrs = data[x].coff_total_hrs;
                      if (minCOFF >= 1) {
                        avg_coff = coff_totals_hrs / (shiftDiv * minCOFF);
                      } else {
                        avg_coff = 0;
                      }
                      coff_days = data[x].coff_total;
                    } else {
                      coff_totals_hrs = 0;
                      avg_coff = 0;
                      coff_days = 0;
                    }
                  }
                  if (data[x].moff_total_hrs != null) {
                    if (min_values.min_MOF == 0) {
                      minMOF = 1;
                    } else {
                      minMOF = min_values.min_MOF;
                    }
                    moff_totals_hrs = data[x].moff_total_hrs;
                    if (minMOF >= 1) {
                      avg_moff = moff_totals_hrs / (shiftDiv * minMOF);
                    } else {
                      avg_moff = 0;
                    }
                  } else {
                    moff_totals_hrs = 0;
                    avg_moff = 0;
                  }
                  if (data[x].cmd_total > 0) {
                    cmd_rows += 1;
                  }
                  if (so_days > 0) {
                    so_rows += 1;
                  }
                  if (sff_days > 0) {
                    sff_ff_rows += 1;
                  }
                  if (coff_days > 0) {
                    coff_rows += 1;
                  }
                  if (scop_days > 0) {
                    scop_cop_rows += 1;
                  }
                  if (data[x].moff_total > 0) {
                    moff_rows += 1;
                  }
                  cmd_grand_total_avg = cmd_grand_total_avg + avg_cmd;
                  so_grand_total_avg = so_grand_total_avg + avg_so;
                  sff_ff_grand_total_avg = sff_ff_grand_total_avg + avg_sff_ff;
                  scop_cop_grand_total_avg =
                    scop_cop_grand_total_avg + avg_scop_cop;
                  moff_grand_total_avg = moff_grand_total_avg + avg_moff;
                  coff_grand_total_avg = coff_grand_total_avg + avg_coff;
                  bookings.push({
                    location: data[x].name,
                    cmd: data[x].cmd_total,
                    cmd_hrs: cmd_totals_hrs,
                    avg_cmd: avg_cmd.toFixed(2),
                    so: so_days,
                    so_hrs: so_totals_hrs,
                    avg_so: +avg_so.toFixed(2),
                    sff_ff: sff_days,
                    sff_ff_hrs: sff_ff_totals_hrs,
                    avg_sff_ff: +avg_sff_ff.toFixed(2),
                    coff: coff_days,
                    coff_hrs: coff_totals_hrs,
                    avg_coff: avg_coff.toFixed(2),
                    scop_cop: scop_days,
                    scop_cop_hrs: scop_cop_totals_hrs,
                    avg_scop_cop: avg_scop_cop.toFixed(2),
                    moff: data[x].moff_total,
                    moff_hrs: moff_totals_hrs,
                    avg_moff: avg_moff.toFixed(2),
                    total:
                      data[x].cmd_total +
                      data[x].so_total +
                      data[x].sff_ff_total +
                      data[x].coff_total +
                      data[x].scop_cop_total +
                      data[x].moff_total,
                  });
                }
                bookings.push({
                  location: "GRAND TOTALS",
                  cmd: cmd_totals,
                  cmd_hrs: cmd_grand_total_hrs,
                  avg_cmd: cmd_grand_total_avg.toFixed(2),
                  so: so_totals,
                  so_hrs: so_grand_total_hrs,
                  avg_so: +so_grand_total_avg.toFixed(2),
                  sff_ff: sff_ff_totals,
                  sff_ff_hrs: sff_ff_grand_total_hrs,
                  avg_sff_ff: +sff_ff_grand_total_avg.toFixed(2),
                  coff: coff_totals,
                  coff_hrs: coff_grand_total_hrs,
                  avg_coff: coff_grand_total_avg.toFixed(2),
                  scop_cop: scop_cop_totals,
                  scop_cop_hrs: scop_cop_grand_total_hrs,
                  avg_scop_cop: scop_cop_grand_total_avg.toFixed(2),
                  moff: moff_totals,
                  moff_hrs: moff_grand_total_hrs,
                  avg_moff: moff_grand_total_avg.toFixed(2),
                  total:
                    cmd_totals +
                    so_totals +
                    sff_ff_totals +
                    coff_totals +
                    scop_cop_totals +
                    moff_totals,
                });
              }
            }
            let cmd_avg_value = cmd_grand_total_avg.toFixed(2) / cmd_rows;
            let so_avg_value = so_grand_total_avg.toFixed(2) / so_rows;
            let sff_ff_avg_value =
              sff_ff_grand_total_avg.toFixed(2) / sff_ff_rows;
            let coff_avg_value = coff_grand_total_avg.toFixed(2) / coff_rows;
            let moff_avg_value = moff_grand_total_avg.toFixed(2) / moff_rows;
            let scop_avg_value =
              scop_cop_grand_total_avg.toFixed(2) / scop_cop_rows;
            if (isNaN(cmd_avg_value)) {
              cmd_avg_value = 0;
            }
            if (isNaN(so_avg_value)) {
              so_avg_value = 0;
            }
            if (isNaN(sff_ff_avg_value)) {
              sff_ff_avg_value = 0;
            }
            if (isNaN(coff_avg_value)) {
              coff_avg_value = 0;
            }
            if (isNaN(moff_avg_value)) {
              moff_avg_value = 0;
            }
            if (isNaN(scop_avg_value)) {
              scop_avg_value = 0;
            }
            bookings.push({
              location: "AVERAGE VALUES",
              cmd: "",
              cmd_hrs: "",
              avg_cmd: cmd_avg_value.toFixed(2),
              so: "",
              so_hrs: "",
              avg_so: +so_avg_value.toFixed(2),
              sff_ff: "",
              sff_ff_hrs: "",
              avg_sff_ff: +sff_ff_avg_value.toFixed(2),
              coff: "",
              coff_hrs: "",
              avg_coff: coff_avg_value.toFixed(2),
              scop_cop: "",
              scop_cop_hrs: "",
              avg_scop_cop: scop_avg_value.toFixed(2),
              moff: "",
              moff_hrs: "",
              avg_moff: moff_avg_value.toFixed(2),
              total: "",
            });
            grid.define("data", bookings);
            grid.refresh();
            let totals_row_id = grid.getLastId();
            let averages_row_id = totals_row_id - 1;
            grid.addRowCss(totals_row_id, "totals_row");
            grid.addRowCss(averages_row_id, "totals_row");
            grid.eachRow(function (row) {
              let record = grid.getItem(row);
              if (
                record.location != "GRAND TOTALS" &&
                record.location != "AVERAGE VALUES"
              ) {
                if (record.avg_cmd > cmd_avg_value) {
                  grid.addCellCss(row, "avg_cmd", "fatigue_red");
                } else {
                  grid.addCellCss(row, "avg_cmd", "fatigue_green");
                }
                if (record.avg_so > so_avg_value) {
                  grid.addCellCss(row, "avg_so", "fatigue_red");
                } else {
                  grid.addCellCss(row, "avg_so", "fatigue_green");
                }
                if (record.avg_sff_ff > sff_ff_avg_value) {
                  grid.addCellCss(row, "avg_sff_ff", "fatigue_red");
                } else {
                  grid.addCellCss(row, "avg_sff_ff", "fatigue_green");
                }
                if (record.avg_coff > coff_avg_value) {
                  grid.addCellCss(row, "avg_coff", "fatigue_red");
                } else {
                  grid.addCellCss(row, "avg_coff", "fatigue_green");
                }
                if (record.avg_moff > moff_avg_value) {
                  grid.addCellCss(row, "avg_moff", "fatigue_red");
                } else {
                  grid.addCellCss(row, "avg_moff", "fatigue_green");
                }
                if (record.avg_scop_cop > scop_avg_value) {
                  grid.addCellCss(row, "avg_scop_cop", "fatigue_red");
                } else {
                  grid.addCellCss(row, "avg_scop_cop", "fatigue_green");
                }
              }
            });
            $$("loader-window").hide();
          },
        },
      );
  }
  function getLocationMinValues(callback) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .sync()
      .get(
        server_url + "/admin/get_location_min_values",
        {},
        {
          error: function (err) {
            callback([]);
          },
          success: function (results) {
            let values = JSON.parse(results);
            callback(values);
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
