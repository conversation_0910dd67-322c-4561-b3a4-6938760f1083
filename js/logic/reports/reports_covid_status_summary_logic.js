let covidStatusSummaryReport = (function () {
  function initApplication() {
    eventHandlers();
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_covid_status_summary")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        generateStatusSummaryReport();
      });
  }
  function generateStatusSummaryReport() {
    let grid = $$("reports-page")
      .$$("reporting_covid_status_summary")
      .$$("covid_status_summary_chart");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/covid_status_summary_report",
        {},
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let reportData = [];
            if (results) {
              let data = JSON.parse(results);
              if (data.length > 0) {
                let all_records_qty = data.length;
                let medExempt = 0;
                let NOT_COMPLETE_No_vaccinations_received_not_reported = 0;
                let NOT_COMPLETE_No_vaccinations_received_reported = 0;
                let NOT_COMPLETE_2nd_vaccination_not_received = 0;
                let NOT_COMPLETE_Waiting_on_2nd_vaccine = 0;
                let NOT_COMPLETE_Booster_vaccination_not_received = 0;
                let COMPLETE_You_have_met_all_current_vaccination_requirements = 0;
                let NOT_COMPLETE_Waiting_on_vaccination_form = 0;
                let NOT_COMPLETE_Waiting_on_digital_certificate = 0;
                let NOT_COMPLETE_Waiting_on_digital_certificate_vaccination_form = 0;
                data.forEach(function (records) {
                  let curr_date = moment();
                  let first_date = moment(
                    records.first_dose_date,
                    "DD/MM/YYYY",
                  );
                  let second_date = moment(
                    records.second_dose_date,
                    "DD/MM/YYYY",
                  );
                  let exempt_expiry_date = moment(
                    records.exemption_date + " 23:59",
                    "DD/MM/YYYY H:mm",
                  );
                  if (
                    records.status == "Medically Exempt" &&
                    exempt_expiry_date.isSameOrAfter(curr_date) === true
                  ) {
                    medExempt += 1;
                    COMPLETE_You_have_met_all_current_vaccination_requirements += 1;
                  } else {
                    if (records.first_dose_date == "Invalid date") {
                      if (
                        records.vax_form_received == false ||
                        records.vax_form_received == null
                      ) {
                        NOT_COMPLETE_No_vaccinations_received_not_reported += 1;
                      } else {
                        NOT_COMPLETE_No_vaccinations_received_reported += 1;
                      }
                    } else if (
                      records.first_dose_date != "Invalid date" &&
                      records.second_dose_date == "Invalid date"
                    ) {
                      if (curr_date.diff(first_date, "days", false) > 183) {
                        NOT_COMPLETE_2nd_vaccination_not_received += 1;
                      } else {
                        NOT_COMPLETE_Waiting_on_2nd_vaccine += 1;
                      }
                    } else if (
                      records.first_dose_date != "Invalid date" &&
                      records.second_dose_date != "Invalid date" &&
                      records.booster_date == "Invalid date"
                    ) {
                      if (curr_date.diff(second_date, "days", false) > 183) {
                        NOT_COMPLETE_Booster_vaccination_not_received += 1;
                      } else {
                        if (
                          records.digital_cert_received == true &&
                          records.vax_form_received == true
                        ) {
                          COMPLETE_You_have_met_all_current_vaccination_requirements += 1;
                        } else if (
                          records.digital_cert_received == true &&
                          (records.vax_form_received == false ||
                            records.vax_form_received == null)
                        ) {
                          NOT_COMPLETE_Waiting_on_vaccination_form += 1;
                        } else if (
                          (records.digital_cert_received == false ||
                            records.digital_cert_received == null) &&
                          records.vax_form_received == true
                        ) {
                          NOT_COMPLETE_Waiting_on_digital_certificate += 1;
                        } else if (
                          (records.digital_cert_received == false ||
                            records.digital_cert_received == null) &&
                          (records.vax_form_received == false ||
                            records.vax_form_received == null)
                        ) {
                          NOT_COMPLETE_Waiting_on_digital_certificate_vaccination_form += 1;
                        }
                      }
                    } else if (
                      records.first_dose_date != "Invalid date" &&
                      records.second_dose_date != "Invalid date" &&
                      records.booster_date != "Invalid date"
                    ) {
                      if (
                        records.digital_cert_received == true &&
                        records.vax_form_received == true
                      ) {
                        COMPLETE_You_have_met_all_current_vaccination_requirements += 1;
                      } else if (
                        records.digital_cert_received == true &&
                        (records.vax_form_received == false ||
                          records.vax_form_received == null)
                      ) {
                        NOT_COMPLETE_Waiting_on_vaccination_form += 1;
                      } else if (
                        (records.digital_cert_received == false ||
                          records.digital_cert_received == null) &&
                        records.vax_form_received == true
                      ) {
                        NOT_COMPLETE_Waiting_on_digital_certificate += 1;
                      } else if (
                        (records.digital_cert_received == false ||
                          records.digital_cert_received == null) &&
                        records.vax_form_receiveived == null
                      ) {
                        NOT_COMPLETE_Waiting_on_digital_certificate_vaccination_form += 1;
                      }
                    }
                  }
                });
                let total_not_complete =
                  NOT_COMPLETE_No_vaccinations_received_not_reported +
                  NOT_COMPLETE_No_vaccinations_received_reported +
                  NOT_COMPLETE_2nd_vaccination_not_received +
                  NOT_COMPLETE_Waiting_on_2nd_vaccine +
                  NOT_COMPLETE_Booster_vaccination_not_received +
                  NOT_COMPLETE_Waiting_on_vaccination_form +
                  NOT_COMPLETE_Waiting_on_digital_certificate +
                  NOT_COMPLETE_Waiting_on_digital_certificate_vaccination_form;
                let total_complete =
                  COMPLETE_You_have_met_all_current_vaccination_requirements;
                $$("reports-page")
                  .$$("reporting_covid_status_summary")
                  .$$("covid_status_records")
                  .define(
                    "template",
                    "<div style='font-size: 17px; text-align: center; line-height: 22px'>Total Vaccine Records: " +
                      all_records_qty +
                      "</br>Total 'COMPLETE' Records: " +
                      total_complete +
                      "<span style='font-size: 13px'> - (" +
                      medExempt +
                      " Med. Exempt)</span></br>Total 'NOT COMPLETE' Records: " +
                      total_not_complete +
                      "</div>",
                  );
                $$("reports-page")
                  .$$("reporting_covid_status_summary")
                  .$$("covid_status_records")
                  .refresh();
                reportData.push({
                  status: "COMPLETE - All current vax requirements met",
                  qty:
                    COMPLETE_You_have_met_all_current_vaccination_requirements +
                    " - (" +
                    (
                      100 *
                      (COMPLETE_You_have_met_all_current_vaccination_requirements /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#22D425",
                });
                reportData.push({
                  status: "NOT COMPLETE - No vaccinations (Reported)",
                  qty:
                    NOT_COMPLETE_No_vaccinations_received_reported +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_No_vaccinations_received_reported /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#EE3639",
                });
                reportData.push({
                  status: "NOT COMPLETE - No vaccinations (Not Reported)",
                  qty:
                    NOT_COMPLETE_No_vaccinations_received_not_reported +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_No_vaccinations_received_not_reported /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#EE3639",
                });
                reportData.push({
                  status: "NOT COMPLETE - 2nd vax not received",
                  qty:
                    NOT_COMPLETE_2nd_vaccination_not_received +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_2nd_vaccination_not_received /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#2471A3",
                });
                reportData.push({
                  status: "NOT COMPLETE - Waiting on 2nd vaccine",
                  qty:
                    NOT_COMPLETE_Waiting_on_2nd_vaccine +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_Waiting_on_2nd_vaccine / all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#EEEA36",
                });
                reportData.push({
                  status: "NOT COMPLETE - Booster vax not received",
                  qty:
                    NOT_COMPLETE_Booster_vaccination_not_received +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_Booster_vaccination_not_received /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#63635E",
                });
                reportData.push({
                  status: "NOT COMPLETE - Waiting on vax form",
                  qty:
                    NOT_COMPLETE_Waiting_on_vaccination_form +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_Waiting_on_vaccination_form /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#DC7633",
                });
                reportData.push({
                  status: "NOT COMPLETE - Waiting on digital cert",
                  qty:
                    NOT_COMPLETE_Waiting_on_digital_certificate +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_Waiting_on_digital_certificate /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#6C3483",
                });
                reportData.push({
                  status: "NOT COMPLETE - Waiting on digital cert & vax form",
                  qty:
                    NOT_COMPLETE_Waiting_on_digital_certificate_vaccination_form +
                    " - (" +
                    (
                      100 *
                      (NOT_COMPLETE_Waiting_on_digital_certificate_vaccination_form /
                        all_records_qty)
                    ).toFixed(1) +
                    "%)",
                  colour: "#D4AC0D",
                });
                grid.define("data", reportData);
                grid.refresh();
                $$("loader-window").hide();
              } else {
                $$("loader-window").hide();
                webix.alert("No data to report!");
              }
            }
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
