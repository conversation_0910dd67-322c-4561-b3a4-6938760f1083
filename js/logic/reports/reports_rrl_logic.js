let rrlReport = (function () {
  let customReportStyle = {
    0: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    1: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
    2: { font: { name: "<PERSON><PERSON>", sz: 10, bold: true } },
  };
  let exp_grid;
  let exp_fromDate = "";
  let exp_toDate = "";
  let exp_roster = "";
  let exp_shift = "";
  function initApplication() {
    eventHandlers();
    let startOfMonth = moment().startOf("month").format("YYYY-MM-DD");
    let endOfMonth = moment().endOf("month").format("YYYY-MM-DD");
    $$("reports-page").$$("reporting_rrl").$$("from").setValue(startOfMonth);
    $$("reports-page").$$("reporting_rrl").$$("to").setValue(endOfMonth);
    exp_grid = $$("reports-page").$$("reporting_rrl").$$("rrl_grid");
    let defaultHandler = exp_grid.$exportView;
    exp_grid.$exportView = function (options) {
      let returnValue = defaultHandler.apply(this, arguments);
      if (returnValue[0] && returnValue[0].exportData) {
        returnValue[0].exportData.unshift([""]);
        returnValue[0].styles.unshift({});
        returnValue[0].exportData.unshift([
          "Report print date: " + moment().format("DD/MM/YYYY HH:mm"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          moment(exp_fromDate).format("DD/MM/YYYY") +
            " - " +
            moment(exp_toDate).format("DD/MM/YYYY"),
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift([
          "Show RRL Bookings For: " + exp_shift + " (" + exp_roster + ")",
        ]);
        returnValue[0].styles.unshift(customReportStyle);
        returnValue[0].exportData.unshift(["RRL Report"]);
        returnValue[0].styles.unshift(customReportStyle);
      }
      return returnValue;
    };
  }
  function eventHandlers() {
    $$("reports-page")
      .$$("reporting_rrl")
      .$$("btn_search")
      .attachEvent("onItemClick", function (id, e) {
        let fromDate = $$("reports-page")
          .$$("reporting_rrl")
          .$$("from")
          .getValue();
        let toDate = $$("reports-page").$$("reporting_rrl").$$("to").getValue();
        let roster = $$("reports-page")
          .$$("reporting_rrl")
          .$$("rosters")
          .getText();
        let shift = $$("reports-page")
          .$$("reporting_rrl")
          .$$("shift")
          .getText();
        if (roster == "" || shift == "") {
          webix.alert(
            "You must select a Roster & Shift before you can Search!",
          );
        } else {
          generateRRLReport(fromDate, toDate, roster, shift);
        }
      });
    $$("reports-page")
      .$$("reporting_rrl")
      .$$("rosters")
      .attachEvent("onChange", function (newv, oldv) {
        load_shifts(newv);
      });
    $$("reports-page")
      .$$("reporting_rrl")
      .$$("btn_export_excel")
      .attachEvent("onItemClick", function (id, e) {
        exp_fromDate = $$("reports-page")
          .$$("reporting_rrl")
          .$$("from")
          .getValue();
        exp_toDate = $$("reports-page").$$("reporting_rrl").$$("to").getValue();
        exp_roster = $$("reports-page")
          .$$("reporting_rrl")
          .$$("rosters")
          .getText();
        exp_shift = $$("reports-page")
          .$$("reporting_rrl")
          .$$("shift")
          .getText();
        if (exp_grid.count() > 0) {
          webix.toExcel(exp_grid, {
            filename:
              "WFR Rostering Report - RRL (" +
              moment().format("DD-MM-YYYY") +
              ")",
            styles: true,
          });
        } else {
          webix.alert("No data to Export!");
        }
      });
  }
  rosters_subject.subscribe(function (data) {
    let select = $$("reports-page").$$("reporting_rrl").$$("rosters");
    if (data) {
      let options = [];
      let roster_names = JSON.parse(data);
      roster_names.forEach(function (value) {
        options.push(value.roster_name);
      });
      select.define("options", options);
      select.refresh();
    }
  });
  function load_shifts(rosterName) {
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/schedule/shifts",
        { roster_name: rosterName },
        {
          error: function (err) {},
          success: function (results) {
            if (results) {
              let shiftList = JSON.parse(results);
              if (shiftList.data.length > 0) {
                let shiftArray = shiftList.data[0].shifts.split(",");
                let shiftOptions = [];
                shiftArray.forEach(function (value) {
                  shiftOptions.push(value);
                });
                $$("reports-page")
                  .$$("reporting_rrl")
                  .$$("shift")
                  .define("options", shiftOptions);
                $$("reports-page").$$("reporting_rrl").$$("shift").refresh();
              }
            }
          },
        },
      );
  }
  function generateRRLReport(fromDate, toDate, roster, shift) {
    let grid = $$("reports-page").$$("reporting_rrl").$$("rrl_grid");
    $$("loader-window").show();
    grid.clearAll();
    webix
      .ajax()
      .headers({ Authorization: "Bearer " + api_key })
      .get(
        server_url + "/admin/rrl_report",
        { from_date: fromDate, to_date: toDate, roster: roster, shift: shift },
        {
          error: function (err) {
            $$("loader-window").hide();
          },
          success: function (results) {
            let bookings = [];
            if (results) {
              let data = JSON.parse(results);
              let empName = "";
              if (data.length > 0) {
                for (let x = 0; x < data.length; x++) {
                  if (data[x].middle_name == null) {
                    empName = data[x].surname + ", " + data[x].first_name;
                  } else {
                    empName =
                      data[x].surname +
                      ", " +
                      data[x].first_name +
                      " " +
                      data[x].middle_name;
                  }
                  bookings.push({
                    pay_id: data[x].pay_id,
                    employee: empName.toUpperCase(),
                    rank: data[x].rank,
                    leave_group: data[x].leave_group,
                    roster: data[x].roster,
                    shift: data[x].shift,
                    location: data[x].location,
                    start_date: data[x].booking_first_date,
                    end_date: data[x].booking_last_date,
                    total_days: data[x].days,
                    swap_pay_id: data[x].rrl_swap_pay_id,
                    created_by: data[x].created_by,
                    date_created: data[x].date_created,
                  });
                }
              }
            }
            grid.define("data", bookings);
            grid.refresh();
            $$("loader-window").hide();
          },
        },
      );
  }
  return {
    initialise: function () {
      initApplication();
    },
  };
})();
