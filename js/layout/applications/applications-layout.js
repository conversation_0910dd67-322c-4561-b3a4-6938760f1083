let applicationsLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "applications-page",
        isolate: true,
        rows: [
          {
            view: "toolbar",
            id: "header",
            css: "main_header",
            cols: [
              {
                view: "label",
                label:
                  "<span class='header_font'> Leave Applications & Requests</span>",
                align: "left",
              },
              {},
            ],
          },
          { height: 20 },
          {
            view: "tabbar",
            multiview: true,
            id: "applications_tabs",
            value: "1",
            options: [
              { id: "1", value: "Standby Requests" },
              { id: "2", value: "RRL Swap Requests" },
              { id: "3", value: "SP90 Form" },
            ],
          },
          {
            view: "multiview",
            cells: [
              applicationsStandbysLayout.render(),
              applicationsRRLsLayout.render(),
              applicationsSP90Layout.render(),
            ],
            animate: false,
          },
        ],
      };
    },
  };
})();
