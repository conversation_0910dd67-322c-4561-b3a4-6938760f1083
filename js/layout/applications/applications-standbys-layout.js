let applicationsStandbysLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "sb-requests-page",
        isolate: true,
        view: "scrollview",
        scroll: "xy",
        body: {
          rows: [
            {
              view: "form",
              id: "standby_request",
              width: 460,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "standbys_start_date",
                      name: "standbys_start_date",
                      width: 240,
                      label: "Start Date",
                      labelWidth: 85,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                    },
                    { width: 20 },
                    {
                      view: "datepicker",
                      id: "standbys_start_time",
                      name: "standbys_start_time",
                      type: "time",
                      width: 85,
                      label: "",
                      value: "01/01/2019 08:00",
                      format: "%H:%i",
                      stringResult: true,
                      disabled: true,
                    },
                    {
                      view: "checkbox",
                      id: "standbys_day_hours",
                      name: "standbys_day_hours",
                      labelRight: "Day Shift",
                      value: 1,
                      labelWidth: 20,
                      width: 120,
                    },
                    {
                      view: "checkbox",
                      id: "standbys_night_hours",
                      name: "standbys_night_hours",
                      labelRight: "Night Shift",
                      value: 0,
                      labelWidth: 20,
                      width: 130,
                    },
                    {
                      view: "checkbox",
                      id: "standbys_part_shift",
                      name: "standbys_part_shift",
                      labelRight: "Part Shift",
                      value: 0,
                      labelWidth: 20,
                      width: 120,
                    },
                    {
                      view: "checkbox",
                      id: "standbys_full_shift",
                      name: "standbys_full_shift",
                      labelRight: "Full Shift (24h)",
                      value: 0,
                      labelWidth: 20,
                      width: 140,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "standbys_end_date",
                      name: "standbys_end_date",
                      width: 240,
                      label: "End Date",
                      labelWidth: 85,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      disabled: true,
                    },
                    { width: 20 },
                    {
                      view: "datepicker",
                      id: "standbys_end_time",
                      name: "standbys_end_time",
                      type: "time",
                      width: 85,
                      label: "",
                      value: "01/01/2019 18:00",
                      format: "%H:%i",
                      stringResult: true,
                      disabled: true,
                    },
                    { width: 20 },
                    {
                      view: "label",
                      label:
                        "Note: you can only request Standbys individually per shift. For multiple days simply send multiple requests, one for each shift.",
                      css: "standby_request_note",
                    },
                  ],
                },
                {
                  cols: [
                    {
                      rows: [
                        {
                          view: "template",
                          template:
                            "--------------------- Applicant's Roster Shift Sequence ---------------------",
                          align: "center",
                          css: {
                            "font-size": "14px !important",
                            color: "#0133FE",
                          },
                          borderless: true,
                          autoheight: true,
                        },
                        {
                          view: "datatable",
                          id: "shift_sequence",
                          minColumnWidth: 52,
                          maxColumnWidth: 52,
                          width: 416,
                          height: 80,
                          scroll: false,
                          columns: [],
                          data: [],
                        },
                      ],
                    },
                    {},
                  ],
                },
                { height: 20 },
                {
                  cols: [
                    {
                      view: "combo",
                      id: "standbys_employee",
                      name: "standbys_employee",
                      label: "Substitute",
                      width: 360,
                      labelWidth: 90,
                      options: [],
                    },
                    { width: 20 },
                    {
                      view: "template",
                      id: "standbys_sub_info",
                      template: "Notice:",
                      css: { color: "black" },
                      width: 875,
                      borderless: true,
                      autoheight: true,
                    },
                  ],
                },
                {
                  view: "combo",
                  id: "standbys_approving_officer",
                  name: "standbys_approving_officer",
                  label: "Approving Officer to Notify",
                  width: 500,
                  labelWidth: 180,
                  options: [],
                },
                {
                  view: "text",
                  id: "standbys_comments",
                  name: "standbys_comments",
                  label: "Comment to Substitute",
                  width: 1100,
                  labelWidth: 160,
                },
                {
                  view: "button",
                  id: "standbys_btn_send",
                  value: "Send Request",
                  width: 140,
                  disabled: false,
                },
                { height: 10 },
                {
                  view: "template",
                  template:
                    "<hr style='border: 0;  height: 5px; width: 100%; background: black'/>",
                  autoheight: true,
                  borderless: true,
                },
                { height: 10 },
                {
                  cols: [
                    { width: 10 },
                    {
                      view: "datepicker",
                      id: "sb_search_date_from",
                      name: "sb_search_date_from",
                      width: 170,
                      label: "From",
                      labelWidth: 45,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                    },
                    { width: 20 },
                    {
                      view: "datepicker",
                      id: "sb_search_date_to",
                      name: "sb_search_date_to",
                      width: 170,
                      label: "To",
                      labelWidth: 25,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                    },
                    { width: 10 },
                    {
                      view: "button",
                      id: "btnSBSearchLog",
                      type: "icon",
                      icon: "fas fa-search",
                      label: "Search",
                      width: 100,
                    },
                    { width: 100 },
                    {
                      view: "template",
                      template:
                        "<div style='font-size: 20px'>Standby Requests Log</div>",
                      borderless: true,
                      autoheight: true,
                      width: 260,
                    },
                    {},
                    {
                      view: "checkbox",
                      id: "sb_show_all",
                      label: "Show All Requests",
                      value: 0,
                      width: 150,
                      labelWidth: 120,
                    },
                    { width: 20 },
                  ],
                },
                {
                  view: "datatable",
                  id: "grid_standby_requests",
                  height: 500,
                  columns: [
                    {
                      id: "request_id",
                      header: "Request ID",
                      width: 120,
                      css: { "text-align": "center" },
                      hidden: true,
                    },
                    {
                      id: "request_date",
                      header: "Request Date",
                      width: 130,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "requested_by",
                      header: "Requested By",
                      width: 200,
                      sort: "string",
                      adjust: true,
                    },
                    {
                      id: "roster",
                      header: "Roster",
                      adjust: true,
                      width: 160,
                    },
                    {
                      id: "shift",
                      header: "Shift",
                      width: 160,
                      adjust: true,
                      sort: "string",
                    },
                    {
                      id: "location",
                      header: "Location",
                      width: 160,
                      adjust: true,
                      sort: "string",
                    },
                    {
                      id: "substitute",
                      header: { text: "Substitute", css: "rrl_swap_header" },
                      width: 200,
                      adjust: true,
                      sort: "string",
                    },
                    {
                      id: "requested_shift",
                      header: {
                        text: "Requested SB Shift",
                        css: "rrl_swap_header",
                      },
                      width: 170,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "response",
                      header: { text: "Response", css: "rrl_swap_header" },
                      width: 120,
                      sort: "string",
                      css: { "text-align": "center" },
                    },
                    {
                      id: "so_pay_id",
                      header: {
                        text: "Approving Officer",
                        css: "rrl_swap_header",
                      },
                      width: 140,
                      sort: "int",
                      css: { "text-align": "center" },
                    },
                  ],
                  data: [],
                },
                { height: 10 },
              ],
            },
          ],
        },
      };
    },
  };
})();
