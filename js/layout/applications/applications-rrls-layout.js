let applicationsRRLsLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "rrl-requests-page",
        isolate: true,
        view: "scrollview",
        scroll: "xy",
        body: {
          rows: [
            {
              view: "form",
              id: "frmLeaveSwap",
              autoheight: true,
              elements: [
                {
                  cols: [
                    {
                      view: "text",
                      id: "from_employee",
                      name: "from_employee",
                      label: "Applicant",
                      width: 400,
                      labelWidth: 70,
                      disabled: true,
                    },
                    { width: 30 },
                    {
                      view: "text",
                      id: "curr_rank_from",
                      name: "curr_rank_from",
                      label: "Rank",
                      width: 140,
                      labelWidth: 45,
                      disabled: true,
                    },
                    { width: 30 },
                    {
                      view: "text",
                      id: "rrl_group_from",
                      name: "rrl_group_from",
                      label: "Leave Group",
                      width: 170,
                      labelWidth: 90,
                      disabled: true,
                    },
                  ],
                },
                { height: 20 },
                {
                  view: "datatable",
                  id: "from_dates_grid",
                  select: "row",
                  multiselect: false,
                  height: 200,
                  columns: [
                    { id: "booking_id", hidden: true },
                    { id: "group_colour", header: "", width: 5, css: "" },
                    {
                      id: "leave_type",
                      header: "Leave",
                      width: 65,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "start_date",
                      header: "Start Date",
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "return_date",
                      header: "Return Date",
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "week_no",
                      header: "Week #",
                      width: 75,
                      css: { "text-align": "center" },
                    },
                    { id: "roster", header: "Roster", minWidth: 80 },
                    { id: "shift", header: "Shift", minWidth: 80 },
                    { id: "location", header: "Location", minWidth: 80 },
                    {
                      id: "rrl_swapee",
                      header: { text: "Swapped With", css: "rrl_swap_header" },
                      minWidth: 120,
                    },
                    {
                      id: "swap_start_date",
                      header: { text: "Start Date", css: "rrl_swap_header" },
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "swap_return_date",
                      header: { text: "Return Date", css: "rrl_swap_header" },
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "swap_week_no",
                      header: { text: "Week #", css: "rrl_swap_header" },
                      width: 75,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "swap_roster",
                      header: { text: "Roster", css: "rrl_swap_header" },
                      minWidth: 80,
                    },
                    {
                      id: "swap_shift",
                      header: { text: "Shift", css: "rrl_swap_header" },
                      minWidth: 80,
                    },
                    {
                      id: "swap_location",
                      header: { text: "Location", css: "rrl_swap_header" },
                      minWidth: 80,
                    },
                    { fillspace: true },
                  ],
                  data: [],
                },
                { height: 20 },
                {
                  cols: [
                    {
                      view: "combo",
                      id: "swap_employee",
                      name: "swap_employee",
                      label: "Substitute",
                      width: 400,
                      labelWidth: 80,
                      options: [],
                    },
                    { width: 30 },
                    {
                      view: "text",
                      id: "curr_rank_swap",
                      name: "curr_rank_swap",
                      label: "Rank",
                      width: 140,
                      labelWidth: 45,
                    },
                    { width: 30 },
                    {
                      view: "text",
                      id: "rrl_group_swap",
                      name: "rrl_group_swap",
                      label: "Leave Group",
                      width: 170,
                      labelWidth: 90,
                    },
                  ],
                },
                { height: 20 },
                {
                  view: "datatable",
                  id: "swap_dates_grid",
                  select: "row",
                  multiselect: false,
                  height: 200,
                  columns: [
                    { id: "booking_id", hidden: true },
                    { id: "group_colour", header: "", width: 5, css: "" },
                    {
                      id: "leave_type",
                      header: "Leave",
                      width: 65,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "start_date",
                      header: "Start Date",
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "return_date",
                      header: "Return Date",
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "week_no",
                      header: "Week #",
                      width: 75,
                      css: { "text-align": "center" },
                    },
                    { id: "roster", header: "Roster", minWidth: 80 },
                    { id: "shift", header: "Shift", minWidth: 80 },
                    { id: "location", header: "Location", minWidth: 80 },
                    {
                      id: "rrl_swapee",
                      header: { text: "Swapped With", css: "rrl_swap_header" },
                      minWidth: 120,
                    },
                    {
                      id: "swap_start_date",
                      header: { text: "Start Date", css: "rrl_swap_header" },
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "swap_return_date",
                      header: { text: "Return Date", css: "rrl_swap_header" },
                      width: 105,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "swap_week_no",
                      header: { text: "Week #", css: "rrl_swap_header" },
                      width: 75,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "swap_roster",
                      header: { text: "Roster", css: "rrl_swap_header" },
                      minWidth: 80,
                    },
                    {
                      id: "swap_shift",
                      header: { text: "Shift", css: "rrl_swap_header" },
                      minWidth: 80,
                    },
                    {
                      id: "swap_location",
                      header: { text: "Location", css: "rrl_swap_header" },
                      minWidth: 80,
                    },
                    { fillspace: true },
                  ],
                  data: [],
                },
                { height: 20 },
                {
                  cols: [
                    { width: 10 },
                    {
                      view: "text",
                      id: "swap_comments",
                      name: "swap_comments",
                      label: "Comment to Substitute",
                      labelWidth: 160,
                      width: 1160,
                    },
                    { width: 10 },
                    {
                      view: "button",
                      id: "btn_send_rrl_request",
                      label: "Send Request",
                      width: 120,
                    },
                  ],
                },
                { height: 10 },
                {
                  view: "template",
                  template:
                    "<hr style='border: 0;  height: 5px; width: 100%; background: black'/>",
                  autoheight: true,
                  borderless: true,
                },
                { height: 10 },
                {
                  cols: [
                    { width: 10 },
                    {
                      view: "datepicker",
                      id: "rrl_search_date_from",
                      name: "rrl_search_date_from",
                      width: 170,
                      label: "From",
                      labelWidth: 45,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                    },
                    { width: 20 },
                    {
                      view: "datepicker",
                      id: "rrl_search_date_to",
                      name: "rrl_search_date_to",
                      width: 170,
                      label: "To",
                      labelWidth: 25,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                    },
                    { width: 10 },
                    {
                      view: "button",
                      id: "btnRRLSearchLog",
                      type: "icon",
                      icon: "fas fa-search",
                      label: "Search",
                      width: 100,
                    },
                    { width: 100 },
                    {
                      view: "template",
                      template:
                        "<div style='font-size: 20px'>RRL Swap Requests Log</div>",
                      borderless: true,
                      autoheight: true,
                      width: 280,
                    },
                    {},
                    {
                      view: "checkbox",
                      id: "rrl_show_all",
                      label: "Show All Requests",
                      value: 0,
                      width: 150,
                      labelWidth: 120,
                    },
                    { width: 20 },
                  ],
                },
                {
                  view: "datatable",
                  id: "grid_rrl_requests",
                  height: 400,
                  columns: [
                    {
                      id: "request_id",
                      header: "Request ID",
                      width: 120,
                      css: { "text-align": "center" },
                      hidden: true,
                    },
                    {
                      id: "request_date",
                      header: "Request Date",
                      width: 130,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "requested_by",
                      header: "Requested By",
                      width: 200,
                      sort: "string",
                      adjust: true,
                    },
                    { id: "rrl_start_date", header: "Start Date", width: 100 },
                    {
                      id: "rrl_return_date",
                      header: "Return Date",
                      width: 100,
                    },
                    {
                      id: "rrl_roster",
                      header: "Roster",
                      adjust: true,
                      width: 160,
                    },
                    {
                      id: "rrl_shift",
                      header: "Shift",
                      width: 160,
                      adjust: true,
                      sort: "string",
                    },
                    {
                      id: "rrl_location",
                      header: "Location",
                      width: 160,
                      adjust: true,
                      sort: "string",
                    },
                    {
                      id: "substitute",
                      header: { text: "Substitute", css: "rrl_swap_header" },
                      width: 200,
                      adjust: true,
                      sort: "string",
                    },
                    {
                      id: "swap_start_date",
                      header: { text: "Start Date", css: "rrl_swap_header" },
                      width: 100,
                    },
                    {
                      id: "swap_return_date",
                      header: { text: "Return Date", css: "rrl_swap_header" },
                      width: 100,
                    },
                    {
                      id: "swap_roster",
                      header: { text: "Swap Roster", css: "rrl_swap_header" },
                      adjust: true,
                      minWidth: 120,
                      sort: "string",
                    },
                    {
                      id: "swap_shift",
                      header: { text: "Swap Shift", css: "rrl_swap_header" },
                      adjust: true,
                      minWidth: 120,
                      sort: "string",
                    },
                    {
                      id: "swap_location",
                      header: { text: "Swap Location", css: "rrl_swap_header" },
                      adjust: true,
                      minWidth: 120,
                      sort: "string",
                    },
                    {
                      id: "response",
                      header: { text: "Response", css: "rrl_swap_header" },
                      width: 120,
                      sort: "string",
                      css: { "text-align": "center" },
                    },
                  ],
                  data: [],
                },
                { height: 10 },
              ],
            },
          ],
        },
      };
    },
  };
})();
