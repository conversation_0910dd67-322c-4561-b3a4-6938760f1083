function createFuncWindow() {
  webix
      .ui({
        view: "window",
        id: "functions-popup",
        modal: false,
        headHeight: 35,
        css: {"background-color": "dimgrey !important"},
        position: function (state) {
          state.left = 2;
          state.top = state.maxHeight - 284;
          state.width = state.maxWidth - 4;
          state.height = 282;
          toolbarWidth = state.width;
        },
        head: {
          view: "toolbar",
          css: "functions_toolbar",
          height: 35,
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "functions-popup-label",
                  label: "",
                  css: "header_font",
                  align: "left",
                },
                {},
                {
                  view: "button",
                  id: "btn_functions_popup_close",
                  label: "X",
                  width: 40,
                  css: "webix_danger",
                  height: 32,
                },
              ],
            },
          ],
        },
        body: {
          cols: [
            {
              view: "list",
              id: "main_functions_list",
              select: true,
              scroll: false,
              css: "func_popup_toolbar",
              type: {height: 34.8, width: 160},
              data: [
                {
                  id: "staff_movement",
                  icon: "fas fa-route",
                  value: " Staff Movement",
                },
                {id: "leave", icon: "fas fa-file-alt", value: " Leave"},
                {
                  id: "sickness",
                  icon: "fas fa-notes-medical",
                  value: " Sickness",
                },
                {id: "overtime", icon: "fas fa-clock", value: " Overtime"},
                {id: "standbys", icon: "fas fa-walking", value: " Standbys"},
                {id: "actups", icon: "fas fa-sort-alpha-up", value: " Act-Ups"},
                {
                  id: "day_work",
                  icon: "fas fa-sun",
                  value: " Day Work",
                  disabled: true,
                },
              ],
            },
            {
              view: "form",
              id: "booking_info",
              width: 520,
              css: "functions_form",
              elements: [
                {
                  cols: [
                    {
                      view: "label",
                      id: "bookings_shift_times",
                      label: "",
                      height: 32,
                      align: "left",
                    },
                    {
                      view: "button",
                      id: "btnAddBooking",
                      label: "Add New",
                      width: 100,
                      height: 32,
                      align: "right",
                    },
                  ],
                },
                {
                  view: "label",
                  id: "ARL_leave_counts",
                  label: "",
                  css: "leave_count_labels",
                  height: 15,
                  hidden: true,
                },
                {
                  view: "label",
                  id: "ARL_leave_counts_pp",
                  label: "",
                  css: "leave_count_labels",
                  height: 15,
                  hidden: true,
                },
                {
                  view: "label",
                  id: "LSL_leave_counts",
                  label: "",
                  css: "leave_count_labels",
                  height: 15,
                  hidden: true,
                },
                {
                  view: "label",
                  id: "no_bookings",
                  label: "NO BOOKINGS",
                  css: "header_font",
                  height: 32,
                },
                {
                  view: "scrollview",
                  id: "logs_scrollview",
                  scroll: "y",
                  height: 140,
                  css: "leave_logs",
                  body: {
                    id: "movement_logs_scrollview",
                    autoheight: true,
                    rows: [],
                  },
                },
              ],
            },
            {
              view: "form",
              id: "bookings_form",
              width: 700,
              css: "functions_form",
              margin: -3,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "bookings_start_date",
                      name: "bookings_start_date",
                      width: 250,
                      label: "Start Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                      disabled: true,
                    },
                    {width: 35},
                    {
                      view: "bookings_start_time",
                      id: "bookings_start_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("bookings_start_time"),
                      },
                    },
                    {
                      view: "checkbox",
                      id: "bookings_day_hours",
                      labelRight: "Day Shift",
                      value: 1,
                      css: "popup_radio",
                      labelWidth: 20,
                      width: 140,
                      hidden: true,
                    },
                    {
                      view: "checkbox",
                      id: "bookings_both_hours",
                      labelRight: "Full Shift",
                      value: 0,
                      css: "popup_radio",
                      labelWidth: 20,
                      width: 140,
                      hidden: true,
                    },
                    {
                      view: "label",
                      id: "lsl_days_label",
                      label: "Select Leave Period",
                      align: "center",
                      hidden: false,
                      inputHeight: 32,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "bookings_end_date",
                      name: "bookings_end_date",
                      width: 250,
                      label: "End Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                      disabled: true,
                    },
                    {width: 5},
                    {
                      view: "button",
                      id: "btn_unlock_end_date",
                      type: "icon",
                      icon: "fas fa-unlock",
                      css: "unlock_button",
                      width: 25,
                    },
                    {width: 7},
                    {
                      view: "bookings_end_time",
                      id: "bookings_end_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("bookings_end_time"),
                      },
                    },
                    {
                      view: "checkbox",
                      id: "bookings_night_hours",
                      labelRight: "Night Shift",
                      value: 0,
                      css: "popup_radio",
                      labelWidth: 20,
                      width: 140,
                      hidden: true,
                    },
                    {
                      view: "radio",
                      id: "lsl_days_radio",
                      label: "Days",
                      css: "popup_radio",
                      labelWidth: 60,
                      labelAlign: "right",
                      options: ["8", "16", "24", "32"],
                      align: "center",
                      value: "8",
                      hidden: true,
                      inputHeight: 32,
                      width: 320,
                    },
                    {
                      view: "combo",
                      id: "pp_lsl_days",
                      label: "LSL Days",
                      options: ["8", "16", "24", "32"],
                      align: "right",
                      labelWidth: 75,
                      labelAlign: "right",
                      value: "8",
                      hidden: true,
                      inputHeight: 32,
                      width: 140,
                    },
                    {
                      view: "radio",
                      id: "leave_days_radio",
                      label: "Days",
                      css: "popup_radio",
                      labelWidth: 60,
                      labelAlign: "right",
                      options: ["1", "2", "3", "4"],
                      align: "center",
                      value: "1",
                      hidden: false,
                      inputHeight: 32,
                      width: 330,
                    },
                  ],
                },
                {
                  view: "combo",
                  id: "bookings_leave_type",
                  name: "bookings_leave_type",
                  label: "Leave Type",
                  labelWidth: 100,
                  width: 590,
                  options: [],
                  inputHeight: 32,
                  on: {
                    onChange: function (newValue, oldValue, config) {
                      functionsPopup.bookingTypeSelect(newValue);
                    },
                  },
                },
                {
                  view: "combo",
                  id: "day_work_supervisor",
                  name: "day_work_supervisor",
                  label: "Supervisor",
                  width: 400,
                  labelWidth: 100,
                  options: [],
                  inputHeight: 32,
                  hidden: true,
                },
                {
                  view: "richselect",
                  id: "sickness_relationship",
                  name: "sickness_relationship",
                  label: "Relationship",
                  labelWidth: 100,
                  width: 300,
                  options: [],
                  inputHeight: 32,
                  hidden: true,
                },
                {
                  view: "textarea",
                  id: "bookings_comments",
                  name: "bookings_comments",
                  label: "Comments",
                  height: 50,
                  labelWidth: 100,
                  width: 590,
                },
                {height: 10},
                {
                  cols: [
                    {width: 100},
                    {
                      view: "button",
                      id: "bookings_btn_schedule",
                      label: "Work Schedule",
                      width: 120,
                      align: "left",
                      height: 32,
                    },
                    {},
                    {
                      view: "checkbox",
                      id: "work_cover",
                      name: "work_cover",
                      css: "popup_radio",
                      label: "Work Related",
                      value: 0,
                      labelWidth: 90,
                      width: 120,
                      hidden: true,
                    },
                    {},
                    {
                      view: "button",
                      id: "bookings_btn_save",
                      label: "Save",
                      width: 100,
                      align: "right",
                      height: 32,
                    },
                    {width: 76},
                  ],
                },
                {},
              ],
            },
            {
              view: "form",
              id: "overtime_form",
              width: 385,
              css: "functions_form",
              margin: -7,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "overtime_start_date",
                      name: "overtime_start_date",
                      width: 230,
                      label: "Start Date",
                      labelWidth: 80,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                      disabled: true,
                    },
                    {width: 35},
                    {
                      view: "overtime_start_time",
                      width: 86,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("overtime_start_time"),
                      },
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "overtime_end_date",
                      name: "overtime_end_date",
                      width: 230,
                      label: "End Date",
                      labelWidth: 80,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                      disabled: true,
                    },
                    {width: 5},
                    {
                      view: "button",
                      id: "overtime_btn_unlock_end_date",
                      type: "icon",
                      icon: "fas fa-unlock",
                      css: "unlock_button",
                      width: 25,
                    },
                    {width: 7},
                    {
                      view: "overtime_end_time",
                      width: 86,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("overtime_end_time"),
                      },
                    },
                  ],
                },
                {
                  view: "combo",
                  id: "overtime_roster",
                  name: "overtime_roster",
                  label: "Roster",
                  labelWidth: 80,
                  options: [],
                  inputHeight: 32,
                  disabled: true,
                },
                {
                  view: "combo",
                  id: "overtime_shift",
                  name: "overtime_shift",
                  label: "Shift",
                  labelWidth: 80,
                  options: [],
                  inputHeight: 32,
                  disabled: true,
                },
                {
                  view: "combo",
                  id: "overtime_location",
                  name: "overtime_location",
                  label: "Location",
                  labelWidth: 80,
                  options: [],
                  inputHeight: 32,
                  disabled: true,
                },
                {
                  view: "combo",
                  id: "overtime_activity",
                  name: "overtime_activity",
                  label: "Activity",
                  labelWidth: 80,
                  options: [],
                  inputHeight: 32,
                },
                {
                  view: "textarea",
                  id: "overtime_comments",
                  name: "overtime_comments",
                  label: "Comments",
                  height: 50,
                  labelWidth: 80,
                },
              ],
              rules: {
                overtime_roster: webix.rules.isNotEmpty,
                overtime_shift: webix.rules.isNotEmpty,
                overtime_location: webix.rules.isNotEmpty,
              },
            },
            {
              view: "form",
              id: "overtime_form_cont",
              css: "functions_form",
              width: 1155,
              elements: [
                {
                  cols: [
                    {
                      view: "button",
                      id: "btn_add_row",
                      type: "icon",
                      icon: "fas fa-plus",
                      label: "",
                      width: 34,
                      height: 32,
                    },
                    {width: 300},
                    {
                      view: "template",
                      id: "overtime_dt_count",
                      template: "1 employee(s) selected",
                      borderless: true,
                      css: "overtime_dt_count",
                    },
                    {},
                    {
                      view: "checkbox",
                      id: "overtime_fatigued",
                      name: "overtime_fatigued",
                      label: "Employee is at RED Fatigue Level",
                      width: 245,
                      labelWidth: 215,
                      css: "popup_radio",
                      value: 0,
                      height: 32,
                      hidden: true,
                    },
                    {width: 50},
                    {
                      view: "button",
                      id: "overtime_btn_save",
                      value: "Save",
                      width: 100,
                      align: "right",
                      height: 32,
                    },
                  ],
                },
                {
                  view: "datatable",
                  id: "overtime_employees",
                  select: "row",
                  scroll: "xy",
                  height: 180,
                  rowHeight: 30,
                  css: "overtime_employees",
                  editable: true,
                  columns: [],
                  data: [
                    {
                      overtime_employee: "",
                      overtime_acting_rank: "",
                      overtime_reason: "",
                      overtime_roster: "",
                      overtime_shift: "",
                      overtime_location: "",
                    },
                  ],
                },
              ],
            },
            {
              view: "form",
              id: "standbys_form",
              width: 460,
              css: "functions_form",
              margin: 2,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "standbys_start_date",
                      name: "standbys_start_date",
                      width: 250,
                      label: "Start Date",
                      labelWidth: 90,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      height: 32,
                    },
                    {width: 30},
                    {
                      view: "standbys_start_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("standbys_start_time"),
                      },
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "standbys_end_date",
                      name: "standbys_end_date",
                      width: 250,
                      label: "End Date",
                      labelWidth: 90,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      height: 32,
                    },
                    {width: 30},
                    {
                      view: "standbys_end_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("standbys_end_time"),
                      },
                    },
                  ],
                },
                {
                  view: "combo",
                  id: "standbys_employee",
                  name: "standbys_employee",
                  label: "Substitute",
                  width: 450,
                  labelWidth: 90,
                  options: [],
                  height: 32,
                },
                {
                  view: "text",
                  id: "standbys_roster",
                  name: "standbys_roster",
                  label: "Roster",
                  hidden: true,
                  value: "",
                  height: 32,
                },
                {
                  view: "text",
                  id: "standbys_shift",
                  name: "standbys_shift",
                  label: "Shift",
                  hidden: true,
                  value: "",
                  height: 32,
                },
                {
                  view: "text",
                  id: "standbys_location",
                  name: "standbys_location",
                  label: "Location",
                  hidden: true,
                  value: "",
                  height: 32,
                },
                {
                  view: "text",
                  id: "standbys_rank",
                  name: "standbys_rank",
                  label: "Rank",
                  labelWidth: 90,
                  width: 220,
                  hidden: true,
                  value: "",
                  height: 32,
                },
                {
                  view: "textarea",
                  id: "standbys_comments",
                  name: "standbys_comments",
                  label: "Comments",
                  width: 450,
                  labelWidth: 90,
                  height: 50,
                },
                {
                  cols: [
                    {
                      view: "template",
                      id: "standbys_sub_info",
                      template: "Notice:",
                      css: "standby_notice",
                      width: 330,
                      borderless: true,
                      autoheight: true,
                    },
                    {
                      rows: [
                        {
                          view: "button",
                          id: "standbys_btn_save",
                          value: "Save",
                          width: 100,
                          align: "right",
                          height: 32,
                        },
                        {},
                      ],
                    },
                  ],
                },
                {},
              ],
            },
            {
              view: "form",
              id: "staff_movement_form",
              width: 740,
              css: "functions_form",
              margin: -3,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "shift_adjustment_start_date",
                      name: "shift_adjustment_start_date",
                      width: 250,
                      label: "Start Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                      disabled: true,
                    },
                    {width: 20},
                    {
                      view: "shift_adjustment_start_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker(
                            "shift_adjustment_start_time",
                        ),
                      },
                    },
                    {
                      view: "label",
                      id: "shift_adjustment_days_label",
                      label: "Select Leave Period",
                      align: "center",
                      hidden: false,
                      inputHeight: 32,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "shift_adjustment_end_date",
                      name: "shift_adjustment_end_date",
                      width: 250,
                      label: "End Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                      disabled: true,
                    },
                    {width: 20},
                    {
                      view: "shift_adjustment_end_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker(
                            "shift_adjustment_end_time",
                        ),
                      },
                    },
                    {width: 30},
                    {
                      view: "radio",
                      id: "shift_adjustment_days_radio",
                      label: "Days",
                      css: "popup_radio",
                      labelWidth: 60,
                      labelAlign: "right",
                      options: ["1", "2", "3", "4", "NR"],
                      align: "center",
                      value: "1",
                      hidden: false,
                      inputHeight: 32,
                      width: 360,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "combo",
                      id: "shift_adjustment_type",
                      name: "shift_adjustment_type",
                      label: "Adjustment",
                      labelWidth: 100,
                      width: 360,
                      options: [],
                      inputHeight: 32,
                    },
                    {},
                    {
                      view: "combo",
                      id: "shift_adjustment_roster",
                      name: "shift_adjustment_roster",
                      label: "Roster",
                      labelWidth: 70,
                      width: 340,
                      options: [],
                      inputHeight: 32,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "combo",
                      id: "shift_adjustment_shift",
                      name: "shift_adjustment_shift",
                      label: "Shift",
                      labelWidth: 100,
                      width: 360,
                      options: [],
                      inputHeight: 32,
                    },
                    {},
                    {
                      view: "combo",
                      id: "shift_adjustment_location",
                      name: "shift_adjustment_location",
                      label: "Location",
                      labelWidth: 70,
                      width: 340,
                      options: [],
                      inputHeight: 32,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "textarea",
                      id: "shift_adjustment_comments",
                      name: "shift_adjustment_comments",
                      label: "Comments",
                      labelWidth: 100,
                      width: 360,
                    },
                    {
                      view: "checkbox",
                      id: "shift_adjustment_notice",
                      name: "shift_adjustment_notice",
                      label: "Prior Notice Given",
                      value: 0,
                      labelWidth: 140,
                      css: "popup_radio",
                      width: 180,
                      height: 35,
                      hidden: true,
                    },
                    {},
                    {
                      rows: [
                        {},
                        {
                          view: "button",
                          id: "shift_adjustment_btn_save",
                          value: "Save",
                          width: 100,
                          align: "right",
                          height: 32,
                        },
                      ],
                    },
                  ],
                },
                {
                  cols: [
                    {width: 55},
                    {
                      view: "checkbox",
                      id: "shift_adjustment_travel_claim",
                      name: "shift_adjustment_travel_claim",
                      label: "This SM Does Not Qualify For A Travel Claim",
                      value: 0,
                      labelWidth: 280,
                      css: "popup_radio",
                      width: 310,
                      height: 35,
                    },
                  ],
                },
              ],
            },
            {
              view: "form",
              id: "actups_form",
              width: 680,
              css: "functions_form",
              margin: 0,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "actups_start_date",
                      name: "actups_start_date",
                      width: 250,
                      label: "Start Date",
                      labelWidth: 90,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      height: 32,
                    },
                    {width: 30},
                    {
                      view: "actups_start_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("actups_start_time"),
                      },
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "actups_end_date",
                      name: "actups_end_date",
                      width: 250,
                      label: "End Date",
                      labelWidth: 90,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      height: 32,
                    },
                    {width: 30},
                    {
                      view: "actups_end_time",
                      width: 87,
                      css: "mobiscroll_timepicker",
                      on: {
                        onBeforeRender: renderMSTimePicker("actups_end_time"),
                      },
                    },
                  ],
                },
                {
                  view: "combo",
                  id: "actups_rank",
                  name: "actups_rank",
                  label: "Acting Rank",
                  labelWidth: 90,
                  width: 180,
                  height: 32,
                  options: rank_options,
                },
                {height: 5},
                {
                  view: "fieldset",
                  label:
                      "Staff Movement Location Details (Leave blank if NOT applicable)",
                  css: "actup_fieldset",
                  body: {
                    margin: -6,
                    rows: [
                      {
                        cols: [
                          {
                            view: "combo",
                            id: "actup_adjustment_type",
                            name: "actup_adjustment_type",
                            label: "Adjustment",
                            labelWidth: 90,
                            width: 340,
                            options: [],
                            inputHeight: 32,
                          },
                          {width: 25},
                          {
                            view: "combo",
                            id: "actups_roster",
                            name: "actups_roster",
                            label: "Roster",
                            labelWidth: 65,
                            options: [],
                            inputHeight: 32,
                          },
                        ],
                      },
                      {
                        cols: [
                          {
                            view: "combo",
                            id: "actups_shift",
                            name: "actups_shift",
                            label: "Shift",
                            labelWidth: 90,
                            width: 340,
                            options: [],
                            inputHeight: 32,
                          },
                          {width: 25},
                          {
                            view: "combo",
                            id: "actups_location",
                            name: "actups_location",
                            label: "Location",
                            labelWidth: 65,
                            options: [],
                            inputHeight: 32,
                          },
                        ],
                      },
                    ],
                  },
                },
                {
                  margin: -5,
                  cols: [
                    {
                      view: "text",
                      id: "actups_comments",
                      name: "actups_comments",
                      label: "Comments",
                      height: 32,
                      labelWidth: 80,
                      width: 460,
                    },
                    {},
                    {
                      view: "button",
                      id: "actups_btn_save",
                      value: "Save",
                      width: 100,
                      align: "right",
                      height: 32,
                    },
                  ],
                },
              ],
            },
            {
              view: "form",
              id: "day_work_form",
              width: 460,
              css: "functions_form",
              margin: -3,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "day_work_start_date",
                      name: "day_work_start_date",
                      width: 250,
                      label: "Start Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "day_work_end_date",
                      name: "day_work_end_date",
                      width: 250,
                      label: "End Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      inputHeight: 32,
                    },
                  ],
                },
                {
                  view: "combo",
                  id: "day_work_booking_type",
                  name: "day_work_booking_type",
                  label: "Work Type",
                  labelWidth: 100,
                  width: 400,
                  options: [],
                  inputHeight: 32,
                },
                {
                  view: "textarea",
                  id: "day_work_comments",
                  name: "day_work_comments",
                  label: "Comments",
                  height: 50,
                  labelWidth: 100,
                  width: 400,
                },
                {height: 10},
                {
                  cols: [
                    {width: 100},
                    {
                      view: "button",
                      id: "day_work_btn_schedule",
                      label: "Day Work Schedule",
                      width: 140,
                      align: "left",
                      height: 32,
                    },
                    {width: 62},
                    {
                      view: "button",
                      id: "day_work_btn_save",
                      label: "Save",
                      width: 100,
                      align: "right",
                      height: 32,
                    },
                  ],
                },
                {},
              ],
            },
            {},
          ],
        },
      })
      .hide();
  webix
      .ui({
        view: "window",
        id: "bookings-window",
        modal: true,
        position: "center",
        head: {
          view: "toolbar",
          autoheight: true,
          elements: [
            {
              cols: [
                {},
                {
                  view: "template",
                  id: "bookings-window-label",
                  template: "",
                  autoheight: true,
                  width: 660,
                  borderless: true,
                  css: {
                    "font-size": "14px !important",
                    "text-align": "center !important",
                  },
                },
                {},
                {
                  rows: [
                    {
                      view: "button",
                      id: "btn_bookings-window_close",
                      label: "X",
                      width: 40,
                      css: "webix_danger",
                      height: 35,
                    },
                    {},
                  ],
                },
              ],
            },
          ],
        },
        body: {
          rows: [
            {
              view: "datatable",
              id: "bookings-window_grid",
              select: "row",
              rowHeight: 35,
              autowidth: true,
              height: 200,
              scroll: "y",
              columns: [
                {id: "booking_id", hidden: true},
                {
                  id: "type",
                  header: "Type",
                  width: 65,
                  css: {"text-align": "center"},
                },
                {id: "bk_date", header: "Date", width: 125},
                {
                  id: "hours",
                  header: "Hours",
                  width: 60,
                  css: {"text-align": "center"},
                },
                {id: "roster", header: "Roster", adjust: true},
                {id: "shift", header: "Shift", adjust: true, sort: "string"},
                {
                  id: "location",
                  header: "Location",
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "status",
                  header: "Status",
                  width: 80,
                  css: {"text-align": "center"},
                },
                {id: "comments", header: "Comments", adjust: true},
                {
                  id: "select",
                  header: "<span class='webix_icon fas fa-trash-alt'></span>",
                  template: "{common.checkbox()}",
                  css: {"text-align": "center"},
                  width: 45,
                },
              ],
              data: [],
            },
            {height: 10},
            {
              cols: [
                {},
                {
                  view: "button",
                  id: "btn_delete_bookings",
                  label: "Delete",
                  width: 100,
                  height: 35,
                },
                {width: 15},
              ],
            },
            {height: 10},
          ],
        },
      })
      .hide();
  webix
      .ui({
        view: "window",
        id: "recurrence-window",
        modal: true,
        position: "center",
        width: 450,
        head: {
          cols: [
            {width: 10},
            {
              view: "label",
              id: "recurrence-window-label",
              label: "<span class='header_font'>Leave Booking Schedule</span>",
              align: "left",
              width: 190,
            },
            {},
            {
              view: "button",
              id: "btn_recurrence-window_close",
              label: "X",
              align: "right",
              css: "webix_danger",
              width: 40,
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              cols: [
                {},
                {
                  view: "label",
                  label: "Leave Booking Recurrence Options",
                  align: "center",
                  width: 240,
                },
                {},
              ],
            },
            {height: 10},
            {
              cols: [
                {
                  view: "datepicker",
                  id: "recurrence-window_start_date",
                  name: "recurrence-window_start_date",
                  width: 240,
                  label: "Starting On",
                  labelWidth: 120,
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                  labelAlign: "right",
                  readonly: true,
                },
                {},
              ],
            },
            {height: 10},
            {
              cols: [
                {
                  view: "counter",
                  id: "recurrence-window_days",
                  label: "Repeat every",
                  labelWidth: 120,
                  min: 1,
                  max: 14,
                  value: 1,
                  width: 230,
                  labelAlign: "right",
                },
                {view: "label", label: "day(s)", width: 70},
                {},
              ],
            },
            {height: 10},
            {cols: [{width: 40}, {view: "label", label: "Repeat"}, {}]},
            {
              cols: [
                {width: 60},
                {
                  view: "radio",
                  id: "recurrence-window_radio",
                  name: "recurrence-window_radio",
                  vertical: true,
                  css: "radio_spacing",
                  options: [
                    {id: 1, value: "for"},
                    {id: 2, value: "until"},
                    {id: 3, value: "to end of RA"},
                  ],
                  value: 1,
                  width: 120,
                },
                {
                  rows: [
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "recurrence-window_repeat",
                          min: 1,
                          max: 50,
                          value: 1,
                          width: 115,
                        },
                        {view: "label", label: "occurrences", width: 80},
                        {},
                      ],
                    },
                    {
                      view: "datepicker",
                      id: "recurrence-window_end_date",
                      name: "recurrence-window_end_date",
                      width: 120,
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      disabled: true,
                    },
                    {
                      view: "datepicker",
                      id: "recurrence-window_ra_end_date",
                      name: "recurrence-window_ra_end_date",
                      width: 120,
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      readonly: true,
                      disabled: true,
                    },
                  ],
                },
                {},
              ],
            },
            {height: 10},
            {
              cols: [
                {},
                {
                  view: "button",
                  id: "btn_recurrence-window_create",
                  label: "Create",
                  width: 100,
                  align: "right",
                },
                {width: 10},
              ],
            },
            {height: 10},
          ],
        },
      })
      .hide();
}
