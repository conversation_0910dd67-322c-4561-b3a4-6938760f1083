function createARWindow() {
  webix.ui({
    view: "window",
    id: "availability_report_window",
    modal: false,
    move: true,
    position: "center",
    head: {
      view: "toolbar",
      elements: [
        {
          cols: [
            {
              view: "label",
              id: "availability_report_label",
              label: "<span class='header_font'>Availability Report</span>",
              align: "left",
            },
            {
              view: "button",
              id: "btn_availability_report_close",
              label: "X",
              align: "right",
              css: "webix_danger",
              width: 40,
            },
          ],
        },
      ],
      css: "main_header",
    },
    body: {
      view: "datatable",
      id: "grid_availability_report",
      autowidth: true,
      rowHeight: 30,
      headerRowHeight: 30,
      css: "availability_report",
      columns: [
        {id: "locations", header: "Locations", width: 150},
        {
          id: "cmd_req",
          header: [
            {text: "CMD", colspan: 3, css: {"text-align": "center"}},
            "REQ",
          ],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "cmd_act",
          header: [null, "ACT"],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "cmd_diff",
          header: [null, "DIFF"],
          width: 40,
          cssFormat: reportDifference,
        },
        {
          id: "so_req",
          header: [
            {text: "SO", colspan: 3, css: {"text-align": "center"}},
            "REQ",
          ],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "so_act",
          header: [null, "ACT"],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "so_diff",
          header: [null, "DIFF"],
          width: 40,
          sort: "int",
          cssFormat: reportDifference,
        },
        {
          id: "sff_req",
          header: [
            {text: "SFF / FF", colspan: 3, css: {"text-align": "center"}},
            "REQ",
          ],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "sff_act",
          header: [null, "ACT"],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "sff_diff",
          header: [null, "DIFF"],
          width: 40,
          sort: "int",
          cssFormat: reportDifference,
        },
        {
          id: "moff_req",
          header: [
            {text: "MOFF", colspan: 3, css: {"text-align": "center"}},
            "REQ",
          ],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "moff_act",
          header: [null, "ACT"],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "moff_diff",
          header: [null, "DIFF"],
          width: 40,
          cssFormat: reportDifference,
        },
        {
          id: "total_req",
          header: [
            {text: "TOTAL", colspan: 3, css: {"text-align": "center"}},
            "REQ",
          ],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "total_act",
          header: [null, "ACT"],
          width: 40,
          css: {"text-align": "center"},
        },
        {
          id: "total_diff",
          header: [null, "DIFF"],
          width: 40,
          cssFormat: reportDifference,
        },
        {id: "list_index", header: "", sort: "int", hidden: true},
        {id: "list_index2", header: "", sort: "int", hidden: true},
        {id: "list_index3", header: "", sort: "int", hidden: true},
      ],
      data: [],
    },
  });
}


function createSCIWindow() {
  webix
      .ui({
        view: "popup",
        id: "skill_code_info_popup",
        modal: false,
        height: 200,
        width: 490,
        body: {
          rows: [
            {
              view: "template",
              id: "sel_ar_value",
              css: "crew_info_popup",
              template: "",
            },
          ],
        },
      })
      .hide();
}
