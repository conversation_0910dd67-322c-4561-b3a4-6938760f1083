let scheduleLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "schedule-page",
        isolate: true,
        rows: [
          {
            view: "toolbar",
            id: "main_toolbar",
            css: "toolbar",
            margin: 1,
            cols: [
              {
                view: "button",
                id: "date_prev",
                type: "icon",
                label: "",
                width: 32,
                icon: "fas fa-step-backward",
              },
              {
                view: "datepicker",
                id: "schedule_date",
                height: 38,
                width: 120,
                format: "%d/%m/%Y",
                value: new Date(),
              },
              {
                view: "button",
                id: "date_next",
                type: "icon",
                label: "",
                width: 32,
                icon: "fas fa-step-forward",
              },
              { width: 15 },
              {
                view: "combo",
                id: "schedule_rosters",
                height: 38,
                width: 230,
                value: "Metro",
                options: [],
              },
              {
                view: "button",
                id: "refresh",
                type: "icon",
                label: "",
                width: 32,
                icon: "fas fa-sync-alt",
              },
              {
                view: "combo",
                id: "schedule_shifts",
                height: 38,
                width: 275,
                value: "",
                options: [],
              },
              {
                view: "combo",
                id: "schedule_locations",
                width: 265,
                height: 38,
                value: "",
                options: [],
              },
              {
                view: "checkbox",
                id: "payNo_filter",
                labelRight: "PayID",
                width: 78,
                labelWidth: 10,
                value: 0,
              },
              {
                view: "checkbox",
                id: "rank_filter",
                labelRight: "Rank",
                width: 72,
                labelWidth: 10,
                value: 0,
              },
              {
                view: "checkbox",
                id: "skill_filter",
                labelRight: "Skills",
                width: 75,
                labelWidth: 10,
                value: 0,
              },
              {
                view: "checkbox",
                id: "covid_filter",
                labelRight: "Covid",
                width: 80,
                labelWidth: 10,
                value: 0,
                hidden: true,
              },
              {},
              {
                view: "richselect",
                id: "schedule_display_type",
                height: 38,
                value: "32 Days",
                options: ["Day", "8 Days", "16 Days", "32 Days"],
                width: 95,
              },
              { width: 10 },
            ],
          },
          {
            cols: [
              {
                view: "scrollview",
                id: "roster_scroll_view",
                scroll: "y",
                body: { id: "roster_grid_layout", rows: [] },
              },
              {
                view: "layout",
                id: "ro_view_layout",
                hidden: true,
                rows: [
                  {
                    view: "toolbar",
                    id: "ro_view_toolbar",
                    css: "main_header",
                    elements: [
                      {
                        view: "label",
                        id: "ro_view_label",
                        label:
                          "<span class='header_font'>Availability Report</span>",
                        align: "left",
                      },
                    ],
                  },
                  {
                    view: "datatable",
                    id: "grid_ro_view_report",
                    rowHeight: 30,
                    headerRowHeight: 30,
                    css: "availability_report",
                    width: 770,
                    scroll: "y",
                    columns: [
                      { id: "locations", header: "Locations", width: 150 },
                      {
                        id: "cmd_req",
                        header: [
                          {
                            text: "CMD",
                            colspan: 3,
                            css: { "text-align": "center" },
                          },
                          "REQ",
                        ],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "cmd_act",
                        header: [null, "ACT"],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "cmd_diff",
                        header: [null, "DIFF"],
                        width: 40,
                        cssFormat: reportDifference,
                      },
                      {
                        id: "so_req",
                        header: [
                          {
                            text: "SO",
                            colspan: 3,
                            css: { "text-align": "center" },
                          },
                          "REQ",
                        ],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "so_act",
                        header: [null, "ACT"],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "so_diff",
                        header: [null, "DIFF"],
                        width: 40,
                        sort: "int",
                        cssFormat: reportDifference,
                      },
                      {
                        id: "sff_req",
                        header: [
                          {
                            text: "SFF / FF",
                            colspan: 3,
                            css: { "text-align": "center" },
                          },
                          "REQ",
                        ],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "sff_act",
                        header: [null, "ACT"],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "sff_diff",
                        header: [null, "DIFF"],
                        width: 40,
                        sort: "int",
                        cssFormat: reportDifference,
                      },
                      {
                        id: "moff_req",
                        header: [
                          {
                            text: "MOFF",
                            colspan: 3,
                            css: { "text-align": "center" },
                          },
                          "REQ",
                        ],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "moff_act",
                        header: [null, "ACT"],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "moff_diff",
                        header: [null, "DIFF"],
                        width: 40,
                        cssFormat: reportDifference,
                      },
                      {
                        id: "total_req",
                        header: [
                          {
                            text: "TOTAL",
                            colspan: 3,
                            css: { "text-align": "center" },
                          },
                          "REQ",
                        ],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "total_act",
                        header: [null, "ACT"],
                        width: 40,
                        css: { "text-align": "center" },
                      },
                      {
                        id: "total_diff",
                        header: [null, "DIFF"],
                        width: 40,
                        cssFormat: reportDifference,
                      },
                      {
                        id: "list_index",
                        header: "",
                        sort: "int",
                        hidden: true,
                      },
                      {
                        id: "list_index2",
                        header: "",
                        sort: "int",
                        hidden: true,
                      },
                      {
                        id: "list_index3",
                        header: "",
                        sort: "int",
                        hidden: true,
                      },
                    ],
                    data: [],
                  },
                ],
              },
            ],
          },
        ],
      };
    },
  };
})();
