let messagingLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "messaging-page",
        isolate: true,
        rows: [
          {
            view: "toolbar",
            id: "header",
            css: "main_header",
            cols: [
              {
                view: "label",
                label:
                  "<span class='header_font'> Crew Messaging Facility</span>",
                align: "left",
              },
              {},
            ],
          },
          { height: 20 },
          {
            view: "tabview",
            id: "msg_tabs",
            cells: [
              {
                header: "New Message",
                body: {
                  view: "form",
                  id: "msg_send_form",
                  elements: [
                    {
                      cols: [
                        { width: 30 },
                        {
                          view: "datepicker",
                          id: "msg_date",
                          name: "msg_date",
                          width: 170,
                          label: "Date",
                          labelWidth: 45,
                          labelAlign: "left",
                          value: new Date(),
                          format: "%d/%m/%Y",
                          stringResult: true,
                          timepicker: false,
                        },
                        { width: 30 },
                        {
                          view: "multicombo",
                          id: "roster_filter",
                          name: "roster_filter",
                          label: "Rosters",
                          labelWidth: 55,
                          width: 300,
                          placeholder: "Select rosters",
                          tagMode: false,
                          tagTemplate: function (values) {
                            return values.length
                              ? values.length + " roster(s) selected"
                              : "";
                          },
                          options: { data: [] },
                        },
                        { width: 30 },
                        {
                          view: "multicombo",
                          id: "shift_filter",
                          name: "shift_filter",
                          label: "Shifts",
                          labelWidth: 50,
                          width: 300,
                          placeholder: "Select shifts",
                          tagMode: false,
                          tagTemplate: function (values) {
                            return values.length
                              ? values.length + " shift(s) selected"
                              : "";
                          },
                          options: { data: [] },
                          disabled: true,
                        },
                        { width: 30 },
                        {
                          view: "multicombo",
                          id: "location_filter",
                          name: "location_filter",
                          label: "Locations",
                          labelWidth: 70,
                          width: 340,
                          placeholder: "Select locations",
                          tagMode: false,
                          tagTemplate: function (values) {
                            return values.length
                              ? values.length + " location(s) selected"
                              : "";
                          },
                          options: { data: [] },
                          disabled: true,
                        },
                        { width: 5 },
                        {
                          view: "radio",
                          id: "station_filters",
                          value: "",
                          options: ["Central", "Southern", "Northern"],
                          width: 600,
                          hidden: true,
                        },
                        {},
                      ],
                    },
                    {
                      cols: [
                        { width: 30 },
                        {
                          view: "multicombo",
                          id: "rank_filter",
                          name: "rank_filter",
                          label: "Rank",
                          labelWidth: 45,
                          width: 220,
                          placeholder: "Select rank(s)",
                          tagMode: false,
                          tagTemplate: function (values) {
                            return values.length
                              ? values.length + " rank(s) selected"
                              : "";
                          },
                          options: { data: [] },
                        },
                        { width: 30 },
                        {
                          view: "combo",
                          id: "skill_filter_1",
                          name: "skill_filter_1",
                          label: "Skill Code(s)",
                          labelWidth: 85,
                          width: 170,
                          options: [],
                        },
                        {
                          view: "combo",
                          id: "skill_filter_2",
                          name: "skill_filter_2",
                          label: "",
                          width: 85,
                          options: [],
                        },
                        { width: 30 },
                        {
                          view: "checkbox",
                          id: "leave_filter",
                          label: "Inc. Crew on RRL/LSL",
                          value: 0,
                          labelWidth: 140,
                          width: 180,
                        },
                        {
                          view: "radio",
                          id: "shift_req_filter",
                          label: "For Shift Period",
                          value: "",
                          labelWidth: 105,
                          width: 260,
                          options: ["Day", "Night"],
                          hidden: true,
                        },
                        { width: 20 },
                        {
                          view: "button",
                          id: "btnSearchCrew",
                          type: "icon",
                          icon: "fas fa-search",
                          label: "Search",
                          width: 100,
                        },
                      ],
                    },
                    {
                      cols: [
                        {},
                        {
                          view: "radio",
                          id: "classification_filter",
                          label: "Classification Filter",
                          value: "Show All",
                          labelWidth: 130,
                          width: 970,
                          options: ["Green Only", "Green & Orange", "Show All"],
                          hidden: true,
                        },
                        {},
                        {
                          view: "button",
                          id: "btnDayWork",
                          type: "icon",
                          icon: "fas fa-sun",
                          label: "Day Work",
                          width: 110,
                          hidden: true,
                        },
                        { width: 30 },
                        {
                          view: "button",
                          id: "btnOvertimeFatigueMode",
                          type: "icon",
                          icon: "fas fa-toggle-off",
                          label: " Overtime & Fatigue Mode",
                          width: 240,
                          css: "orange_button",
                        },
                        { width: 5 },
                      ],
                    },
                    {
                      view: "scrollview",
                      id: "msg_scroller",
                      scroll: "y",
                      body: {
                        rows: [
                          {
                            view: "datatable",
                            id: "grid_recipients",
                            checkboxRefresh: true,
                            height: 400,
                            columns: [
                              {
                                id: "pay_id",
                                header: "Service No.",
                                width: 100,
                                css: { "text-align": "center" },
                                sort: "int",
                              },
                              {
                                id: "name",
                                header: "Employee",
                                width: 300,
                                sort: "string",
                              },
                              {
                                id: "rank",
                                header: "Rank",
                                width: 80,
                                css: { "text-align": "center" },
                                sort: "string",
                              },
                              {
                                id: "roster",
                                header: "Roster",
                                minWidth: 100,
                                adjust: true,
                              },
                              {
                                id: "shift",
                                header: "Shift",
                                adjust: true,
                                minWidth: 100,
                                sort: "string",
                              },
                              {
                                id: "location",
                                header: "Location",
                                adjust: true,
                                minWidth: 100,
                                sort: "string",
                              },
                              {
                                id: "skills",
                                header: "Skill Codes",
                                minWidth: 220,
                                adjust: true,
                              },
                              {
                                id: "fatigue_hours",
                                header: {
                                  text: "Fatigue\nHours",
                                  css: "multiline",
                                },
                                sort: "int",
                                width: 90,
                                css: { "text-align": "center" },
                                hidden: true,
                              },
                              {
                                id: "fatigue_hours_shift",
                                header: {
                                  text: "Fatigue\nHours + Shift",
                                  css: "multiline",
                                },
                                sort: "int",
                                width: 100,
                                css: { "text-align": "center" },
                                hidden: true,
                              },
                              {
                                id: "sm_and_date",
                                header: "SM (past 8 days)",
                                width: 140,
                                hidden: true,
                              },
                              {
                                id: "phone",
                                header: "Mobile No.",
                                width: 120,
                                css: { "text-align": "center" },
                                sort: "int",
                              },
                              {
                                id: "hs_1_pref",
                                header: "1st Preference",
                                adjust: true,
                                minWidth: 120,
                                css: { "text-align": "center" },
                                hidden: true,
                              },
                              {
                                id: "select",
                                header: {
                                  content: "masterCheckbox",
                                  contentId: "master_select",
                                },
                                template: "{common.checkbox()}",
                                css: { "text-align": "center" },
                                width: 50,
                              },
                            ],
                            data: [],
                          },
                          {
                            view: "template",
                            id: "records_count",
                            template: "0 matching employee records found!",
                            borderless: true,
                            css: { "font-style": "italic" },
                            height: 17,
                          },
                          {
                            cols: [
                              { width: 420 },
                              {
                                view: "radio",
                                id: "preset_messages",
                                label: "Preset Messages",
                                options: ["Custom", "Recall", "Operations"],
                                value: "Custom",
                                labelWidth: 120,
                                width: 500,
                              },
                              {
                                view: "template",
                                template:
                                  '<div style="font-size: 12px; font-style: italic; color: black">Note: where applicable the <strong>Date: </strong> section in the message text below must be formatted as <strong>Date: dd/mm/yyyy </strong>to ensure that the fatigue/overtime hours<br>are calculated correctly for respondents. Typing in <strong>Date: TONIGHT </strong> or <strong>Date: NOW </strong> or <strong>Date: 2-7-24 </strong>or <strong>Date: 2/7/2024 </strong> or <strong>Date: 2nd July 2024 </strong>, etc. <u>will not</u> work!</div>',
                                autowidth: true,
                                autoheight: true,
                                borderless: true,
                              },
                            ],
                          },
                          {
                            cols: [
                              {
                                rows: [
                                  {
                                    cols: [
                                      { width: 320 },
                                      {
                                        view: "textarea",
                                        id: "msg_text",
                                        height: 125,
                                        width: 1100,
                                        label: "Message Text",
                                        labelWidth: 100,
                                        keyPressTimeout: 100,
                                        css: "msg_text_style",
                                        attributes: { maxlength: 210 },
                                      },
                                      { width: 10 },
                                      {
                                        rows: [
                                          {
                                            view: "checkbox",
                                            id: "no_response",
                                            label: "No Response Required",
                                            value: 0,
                                            labelWidth: 150,
                                            width: 190,
                                          },
                                          {},
                                          {
                                            view: "button",
                                            id: "btnSendMsg",
                                            type: "icon",
                                            label: " Send Message",
                                            icon: "fas fa-sms",
                                            width: 190,
                                            height: 55,
                                            disabled: true,
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                  {
                                    cols: [
                                      { width: 420 },
                                      {
                                        view: "template",
                                        template:
                                          "<div class='msg_count'><strong>'Response Required'</strong> messages have a limit of <strong>210</strong> characters!</br><strong>'No Response Required'</strong> messages have a limit of <strong>305</strong> characters!</div>",
                                        align: "left",
                                        autoheight: true,
                                        borderless: true,
                                        width: 500,
                                      },
                                      { width: 280 },
                                      {
                                        view: "label",
                                        id: "char_count",
                                        label: "0 / 210 characters",
                                        align: "right",
                                        css: "msg_count",
                                      },
                                      {},
                                    ],
                                  },
                                  { height: 20 },
                                ],
                              },
                              {},
                            ],
                          },
                          { height: 20 },
                        ],
                      },
                    },
                  ],
                },
              },
              {
                header: "Sent Messages",
                body: {
                  view: "form",
                  id: "sent_msg_form",
                  elements: [
                    {
                      cols: [
                        { width: 30 },
                        {
                          view: "datepicker",
                          id: "msg_search_date",
                          name: "msg_search_date",
                          width: 170,
                          label: "Date",
                          labelWidth: 45,
                          labelAlign: "left",
                          value: new Date(),
                          format: "%d/%m/%Y",
                          stringResult: true,
                          timepicker: false,
                          disabled: false,
                        },
                        { width: 30 },
                        {
                          view: "checkbox",
                          id: "adv_msg_filters",
                          label: "Msg. Text Filters",
                          value: 0,
                          width: 140,
                          labelWidth: 110,
                        },
                        { width: 20 },
                        {
                          view: "datepicker",
                          id: "adv_msg_search_date",
                          name: "adv_msg_search_date",
                          width: 170,
                          label: "Date",
                          labelWidth: 45,
                          labelAlign: "left",
                          value: new Date(),
                          format: "%d/%m/%Y",
                          stringResult: true,
                          timepicker: false,
                          hidden: true,
                        },
                        { width: 20 },
                        {
                          view: "combo",
                          id: "adv_msg_search_time",
                          name: "adv_msg_search_time",
                          label: "Time",
                          labelWidth: 40,
                          width: 120,
                          options: ["", "0800", "1800"],
                          hidden: true,
                        },
                        { width: 20 },
                        {
                          view: "combo",
                          id: "adv_msg_search_station",
                          name: "adv_msg_search_station",
                          label: "Station",
                          width: 210,
                          labelWidth: 55,
                          options: [],
                          hidden: true,
                        },
                        { width: 20 },
                        {
                          view: "button",
                          id: "btnSearchLog",
                          type: "icon",
                          icon: "fas fa-search",
                          label: "Search",
                          width: 100,
                        },
                      ],
                    },
                    { height: 10 },
                    {
                      view: "scrollview",
                      id: "msg_scroller",
                      scroll: "y",
                      body: {
                        rows: [
                          {
                            view: "template",
                            template:
                              "<div style='font-size: 20px'>Messages</div>",
                            borderless: true,
                            autoheight: true,
                          },
                          {
                            view: "datatable",
                            id: "grid_messages",
                            select: "row",
                            css: "grid_messages",
                            height: 320,
                            fixedRowHeight: false,
                            columns: [
                              {
                                id: "message_id",
                                header: "Message ID",
                                hidden: true,
                              },
                              {
                                id: "msg_date",
                                header: "Filter Date",
                                hidden: true,
                              },
                              {
                                id: "sent_date",
                                header: "Sent Date",
                                width: 140,
                                css: { "text-align": "center" },
                              },
                              {
                                id: "msg_text",
                                header: "Message Text",
                                fillspace: true,
                              },
                              {
                                id: "msg_response_req",
                                header: "Response Required",
                                css: { "text-align": "center" },
                                width: 150,
                              },
                              {
                                id: "msg_sender",
                                header: "Sender",
                                css: { "text-align": "center" },
                                width: 110,
                              },
                              {
                                id: "msg_send_count",
                                header: "# of Recipients",
                                css: { "text-align": "center" },
                                width: 120,
                              },
                            ],
                            data: [],
                          },
                          { height: 20 },
                          {
                            cols: [
                              {
                                view: "template",
                                template:
                                  "<div style='font-size: 20px'>Responses</div>",
                                borderless: true,
                                autoheight: true,
                                width: 120,
                              },
                              {},
                              {
                                view: "checkbox",
                                id: "auto_refresh",
                                label: "Auto Reload Responses",
                                value: 0,
                                width: 185,
                                labelWidth: 155,
                              },
                              { width: 40 },
                              {
                                view: "checkbox",
                                id: "show_fatigue",
                                label: "Show Fatigue Info",
                                value: 0,
                                width: 150,
                                labelWidth: 120,
                              },
                              { width: 40 },
                              {
                                view: "checkbox",
                                id: "hide_no_responses",
                                label: "Hide 'No' Responses",
                                value: 1,
                                width: 160,
                                labelWidth: 130,
                              },
                              {},
                              {
                                view: "button",
                                id: "btn_export_excel",
                                label:
                                  "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
                                width: 120,
                                align: "right",
                              },
                            ],
                          },
                          {
                            view: "datatable",
                            id: "grid_responses",
                            height: 320,
                            columns: [
                              {
                                id: "message_id",
                                header: "Message ID",
                                hidden: true,
                              },
                              {
                                id: "pay_id",
                                header: "Service No.",
                                width: 100,
                                css: { "text-align": "center" },
                                sort: "int",
                              },
                              {
                                id: "mobile_phone",
                                header: "Mobile No.",
                                width: 100,
                                css: { "text-align": "center" },
                                sort: "int",
                              },
                              {
                                id: "name",
                                header: "Employee",
                                fillspace: true,
                                sort: "string",
                              },
                              {
                                id: "overtime_hours",
                                header: {
                                  text: "Overtime\nHours",
                                  css: "multiline",
                                },
                                sort: "int",
                                css: { "text-align": "center" },
                                width: 90,
                                hidden: true,
                              },
                              {
                                id: "fatigue_hours",
                                header: {
                                  text: "Fatigue\nHours",
                                  css: "multiline",
                                },
                                sort: "int",
                                width: 90,
                                css: { "text-align": "center" },
                                hidden: true,
                              },
                              {
                                id: "rank",
                                header: "Rank",
                                width: 80,
                                css: { "text-align": "center" },
                                sort: "string",
                              },
                              {
                                id: "roster",
                                header: "Roster",
                                css: { "text-align": "center" },
                                adjust: true,
                              },
                              {
                                id: "shift",
                                header: "Shift",
                                css: { "text-align": "center" },
                                adjust: true,
                                sort: "string",
                              },
                              {
                                id: "location",
                                header: "Location",
                                css: { "text-align": "center" },
                                adjust: true,
                                sort: "string",
                              },
                              {
                                id: "skills",
                                header: "Skill Codes",
                                css: { "text-align": "center" },
                                adjust: true,
                              },
                              {
                                id: "response_date",
                                header: "Response Date",
                                css: { "text-align": "center" },
                                width: 150,
                              },
                              {
                                id: "response",
                                header: "Response",
                                css: { "text-align": "center" },
                                width: 90,
                              },
                              {
                                id: "hs_1_pref",
                                header: "1st Preference",
                                adjust: true,
                                minWidth: 120,
                                css: { "text-align": "center" },
                                hidden: true,
                              },
                              {
                                id: "select",
                                header: {
                                  content: "masterCheckbox",
                                  contentId: "master_select",
                                },
                                template: "{common.checkbox()}",
                                css: { "text-align": "center" },
                                width: 50,
                              },
                              {
                                id: "responder_phone",
                                header: "Mobile No.",
                                width: 120,
                                css: { "text-align": "center" },
                                sort: "int",
                                hidden: true,
                              },
                            ],
                            data: [],
                          },
                          {
                            cols: [
                              { width: 220 },
                              {
                                view: "radio",
                                id: "preset_responder_messages",
                                label: "Preset Messages",
                                options: ["Custom", "Operations", "Recall"],
                                value: "Custom",
                                labelWidth: 120,
                                width: 420,
                              },
                            ],
                          },
                          {
                            cols: [
                              {
                                view: "textarea",
                                id: "responders_message_text",
                                label: "Message to Responders",
                                labelWidth: 160,
                                height: 110,
                                keyPressTimeout: 100,
                                css: "msg_text_style",
                                attributes: { maxlength: 305 },
                              },
                              { width: 10 },
                              {
                                rows: [
                                  {},
                                  {
                                    view: "button",
                                    id: "btnSendMsgToResponders",
                                    type: "icon",
                                    label: " Send Message",
                                    icon: "fas fa-sms",
                                    width: 190,
                                    height: 55,
                                    disabled: true,
                                  },
                                ],
                              },
                            ],
                          },
                          {
                            cols: [
                              { width: 170 },
                              {
                                view: "label",
                                label:
                                  "<div><strong>Note:</strong> messages have a limit of 305 characters!</div>",
                                align: "left",
                                css: "msg_count",
                              },
                              {
                                view: "label",
                                id: "responders_char_count",
                                label: "0 / 305 characters",
                                align: "right",
                                css: "msg_count",
                              },
                              { width: 190 },
                            ],
                          },
                          { height: 10 },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
          },
        ],
      };
    },
  };
})();
