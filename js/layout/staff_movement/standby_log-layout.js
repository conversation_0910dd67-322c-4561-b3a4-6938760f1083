let standbyLogLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Standby Logs</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        hidden: true,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        hidden: true,
        options: [],
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 15 },
      {
        view: "richselect",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 55,
        width: 170,
        options: [
          { id: 1, value: "Active" },
          { id: 2, value: "Deleted" },
          { id: 3, value: "All" },
        ],
      },
      { width: 15 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 50,
        width: 180,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 30,
        width: 160,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "standby_logs_grid",
    select: "row",
    columns: [
      { id: "booking_id", hidden: true },
      {
        id: "service_no",
        header: "Service No.",
        adjust: "header",
        sort: "int",
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      { id: "rank", header: "Rank", adjust: true, sort: "string" },
      { id: "roster", header: "Roster", adjust: true },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "booking_first_date",
        header: "Start Date",
        width: 160,
        sort: "date",
      },
      { id: "booking_last_date", header: "End Date", width: 160, sort: "date" },
      {
        id: "total_hours",
        header: "Total Hours",
        width: 105,
        css: { "text-align": "center" },
        sort: "int",
      },
      { id: "from", header: "From", adjust: true, sort: "string" },
      { id: "to", header: "To", adjust: true, sort: "string" },
      { id: "swap_with", header: "Swap With", adjust: true, sort: "string" },
      {
        id: "deleted",
        header: "Deleted",
        width: 70,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];
