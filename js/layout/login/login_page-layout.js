loginLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "login-page",
        isolate: true,
        name: "login-page",
        type: "clean",
        rows: [
          { height: 40 },
          {
            cols: [
              {},
              {
                view: "template",
                id: "splash",
                borderless: true,
                height: 360,
                width: 360,
                template:
                  "<img src='./resources/images/Sapphire_splash_logo.jpg'/>",
              },
              {},
            ],
          },
          {
            view: "template",
            id: "dev_text",
            borderless: true,
            height: 35,
            autowidth: true,
            template: "<strong>SAPPHIRE</strong> Rostering & Availability",
            css: { "font-size": "24px !important", "text-align": "center" },
          },
          {
            view: "template",
            id: "ver_text",
            borderless: true,
            height: 20,
            autowidth: true,
            template: "<i>( Version 1.7.2 )</i>",
            css: { "font-size": "12px !important", "text-align": "center" },
          },
          { height: 20 },
          {
            cols: [
              {},
              {
                view: "button",
                id: "btnLogin",
                type: "icon",
                icon: "fas fa-sign-in-alt",
                label: " Login",
                width: 100,
                height: 45,
                css: "login_button",
              },
              {},
            ],
          },
          { height: 5 },
          {
            view: "template",
            id: "internet_status",
            borderless: true,
            height: 20,
            autowidth: true,
            template:
              "<i>The SAPPHIRE server is offline! Please try again later.</i>",
            css: {
              "font-size": "15px !important",
              "text-align": "center",
              color: "red",
            },
            hidden: true,
          },
          { height: 20 },
          {
            cols: [
              {},
              {
                view: "textarea",
                id: "login_message",
                label: "Message From SAMFS",
                labelPosition: "top",
                labelWidth: 110,
                css: "login_page_message",
                width: 700,
                height: 140,
              },
              {},
            ],
          },
        ],
      };
    },
  };
})();
