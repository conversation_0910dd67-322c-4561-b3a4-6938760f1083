let incidentLocationSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Incident Location Summary Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "combo",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 65,
        width: 235,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      {},
      {
        view: "template",
        id: "incident_records",
        template:
          "<div style='font-size: 17px; text-align: center'>Total Incidents: 0</div>",
        borderless: true,
        autoheight: true,
        width: 300,
      },
      {},
    ],
  },
  { height: 15 },
  {
    view: "datatable",
    id: "incident_location_type_grid",
    css: "r52_location_report",
    leftSplit: 1,
    columns: [],
    data: [],
  },
  { height: 10 },
];
