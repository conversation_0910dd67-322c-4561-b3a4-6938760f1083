let incidentInfoSearchReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Incident Info Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "date_filter",
        name: "date_filter",
        width: 210,
        label: "Incident Date",
        labelWidth: 90,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 40 },
      {
        view: "text",
        id: "incident_no_filter",
        name: "incident_no_filter",
        label: "Incident #",
        labelWidth: 70,
        width: 140,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "incident_info_search_report_grid",
    columns: [
      {
        id: "incident_date",
        header: { text: "Incident Date", css: { "text-align": "center" } },
        width: 130,
        css: { "text-align": "center" },
      },
      {
        id: "incident_no",
        header: { text: "Incident #", css: { "text-align": "center" } },
        width: 110,
        css: { "text-align": "center" },
      },
      {
        id: "incident_type",
        header: { text: "Incident Type", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee", css: { "text-align": "center" } },
        minWidth: 200,
        adjust: true,
      },
      {
        id: "station_id",
        header: { text: "Station", css: { "text-align": "center" } },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "truck_id",
        header: { text: "Vehicle", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "position",
        header: { text: "Position", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];
