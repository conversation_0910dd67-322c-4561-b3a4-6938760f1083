let ridingPositionLocationReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Riding Positions by Location Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      {
        rows: [
          {
            view: "checkbox",
            id: "chk_r52_auto_refresh",
            label: "Auto Refresh Data",
            width: 150,
            labelWidth: 120,
            value: 0,
            align: "center",
          },
          {
            view: "unitlist",
            id: "r52_station_list",
            width: 220,
            css: "r52_station_list",
            data: [
              { id: "04", icon: "fas fa-warehouse", value: " 04 - Comms" },
              { id: "20", icon: "fas fa-warehouse", value: " 20 - Adelaide" },
              {
                id: "21",
                icon: "fas fa-warehouse",
                value: " 21 - Beulah Park",
              },
              { id: "24", icon: "fas fa-warehouse", value: " 24 - Woodville" },
              {
                id: "25",
                icon: "fas fa-warehouse",
                value: " 25 - Port Adelaide",
              },
              { id: "27", icon: "fas fa-warehouse", value: " 27 - Marine" },
              {
                id: "28",
                icon: "fas fa-warehouse",
                value: " 28 - Largs North",
              },
              { id: "37", icon: "fas fa-warehouse", value: " 37 - Prospect" },
              { id: "22", icon: "fas fa-warehouse", value: " 22 - Paradise" },
              { id: "30", icon: "fas fa-warehouse", value: " 30 - Oakden" },
              {
                id: "31",
                icon: "fas fa-warehouse",
                value: " 31 - Golden Grove",
              },
              { id: "32", icon: "fas fa-warehouse", value: " 32 - Salisbury" },
              { id: "33", icon: "fas fa-warehouse", value: " 33 - Elizabeth" },
              { id: "34", icon: "fas fa-warehouse", value: " 34 - USAR" },
              { id: "35", icon: "fas fa-warehouse", value: " 35 - Gawler" },
              { id: "36", icon: "fas fa-warehouse", value: " 36 - Angle Park" },
              { id: "38", icon: "fas fa-warehouse", value: " 38 - APTC" },
              { id: "40", icon: "fas fa-warehouse", value: " 40 - St Marys" },
              {
                id: "41",
                icon: "fas fa-warehouse",
                value: " 41 - Camden Park",
              },
              {
                id: "42",
                icon: "fas fa-warehouse",
                value: " 42 - O'Halloran Hill",
              },
              { id: "43", icon: "fas fa-warehouse", value: " 43 - Noarlunga" },
              {
                id: "44",
                icon: "fas fa-warehouse",
                value: " 44 - Glen Osmond",
              },
              {
                id: "45",
                icon: "fas fa-warehouse",
                value: " 45 - Brooklyn Park",
              },
              { id: "46", icon: "fas fa-warehouse", value: " 46 - Seaford" },
              { id: "50", icon: "fas fa-warehouse", value: " 50 - Port Pirie" },
              {
                id: "51",
                icon: "fas fa-warehouse",
                value: " 51 - Port Augusta",
              },
              { id: "52", icon: "fas fa-warehouse", value: " 52 - Whyalla" },
              {
                id: "54",
                icon: "fas fa-warehouse",
                value: " 54 - Port Lincoln",
              },
              {
                id: "55",
                icon: "fas fa-warehouse",
                value: " 55 - Peterborough",
              },
              { id: "60", icon: "fas fa-warehouse", value: " 60 - Berri" },
              { id: "61", icon: "fas fa-warehouse", value: " 61 - Renmark" },
              { id: "62", icon: "fas fa-warehouse", value: " 62 - Loxton" },
              { id: "63", icon: "fas fa-warehouse", value: " 63 - Tanunda" },
              { id: "64", icon: "fas fa-warehouse", value: " 64 - Kapunda" },
              { id: "66", icon: "fas fa-warehouse", value: " 66 - Kadina" },
              { id: "67", icon: "fas fa-warehouse", value: " 67 - Wallaroo" },
              { id: "68", icon: "fas fa-warehouse", value: " 68 - Moonta" },
              { id: "70", icon: "fas fa-warehouse", value: " 70 - Mt Gambier" },
              {
                id: "71",
                icon: "fas fa-warehouse",
                value: " 71 - Victor Harbor",
              },
              {
                id: "72",
                icon: "fas fa-warehouse",
                value: " 72 - Murray Bridge",
              },
              {
                id: "73",
                icon: "fas fa-warehouse",
                value: " 73 - Mount Barker",
              },
            ],
            select: true,
            uniteBy: function (obj) {
              if (parseInt(obj.id) < 50) {
                if (obj.id != "04" && obj.id != "34" && obj.id != "38") {
                  return "--- METRO STATIONS ---";
                } else {
                  return "--- MISC LOCATIONS ---";
                }
              } else {
                return "--- REGIONAL STATIONS ---";
              }
            },
          },
        ],
      },
      { width: 20 },
      {
        view: "datatable",
        id: "r52_at_station",
        width: 240,
        rowHeight: 50,
        scroll: "y",
        columns: [
          { id: "at_station", header: "At Station", width: 210 },
          { id: "css_theme", header: "", width: 100, hidden: true },
        ],
        data: [],
      },
      { width: 20 },
      {
        view: "datatable",
        id: "r52_riding_positions",
        rowHeight: 50,
        scroll: "xy",
        columns: [
          { id: "callsign", header: "Vehicle", width: 130, sort: "string" },
          { id: "officer", header: "Officer", width: 180 },
          { id: "officer_css", header: "", width: 100, hidden: true },
          { id: "driver", header: "Driver", width: 180 },
          { id: "driver_css", header: "", width: 100, hidden: true },
          { id: "crew_1", header: "Crew 1", width: 180 },
          { id: "crew_1_css", header: "", width: 100, hidden: true },
          { id: "crew_2", header: "Crew 2", width: 180 },
          { id: "crew_2_css", header: "", width: 100, hidden: true },
          { id: "crew_3", header: "Crew 3", width: 180 },
          { id: "crew_3_css", header: "", width: 100, hidden: true },
          { id: "crew_4", header: "Crew 4", width: 180, hidden: true },
          { id: "crew_4_css", header: "", width: 100, hidden: true },
          { id: "crew_5", header: "Crew 5", width: 180, hidden: true },
          { id: "crew_5_css", header: "", width: 100, hidden: true },
        ],
        data: [],
      },
    ],
  },
  { height: 5 },
];
