let respond52Layout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "respond52-page",
        isolate: true,
        cols: [
          {
            view: "sidebar",
            id: "respond52_sidebar",
            width: 270,
            scroll: "y",
            data: [],
            on: {
              onAfterSelect: function (id) {
                routes.navigate(id, { trigger: true });
              },
            },
          },
          {
            view: "multiview",
            cells: [
              {
                id: "respond52_incident",
                isolate: true,
                rows: incidentReportLayout,
              },
              {
                id: "respond52_incident_type_summary",
                isolate: true,
                rows: incidentTypeSummaryReportLayout,
              },
              {
                id: "respond52_incident_location_summary",
                isolate: true,
                rows: incidentLocationSummaryReportLayout,
              },
              {
                id: "respond52_riding_position_location",
                isolate: true,
                rows: ridingPositionLocationReportLayout,
              },
              {
                id: "respond52_incident_info_search",
                isolate: true,
                rows: incidentInfoSearchReportLayout,
              },
            ],
            animate: false,
          },
        ],
      };
    },
  };
})();
