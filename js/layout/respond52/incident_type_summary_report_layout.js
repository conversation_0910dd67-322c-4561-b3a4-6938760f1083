let incidentTypeSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Incident Type Summary Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      {},
      {
        view: "template",
        id: "incident_records",
        template:
          "<div style='font-size: 17px; text-align: center'>Total Incidents: 0</div>",
        borderless: true,
        autoheight: true,
        width: 300,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      { width: 40 },
      {
        view: "chart",
        id: "incident_type_summary_chart",
        type: "barH",
        value: "#qty#",
        color: function (obj) {
          if (obj.qty > 0 && obj.qty <= 5) return "#DC7633";
          else if (obj.qty > 5 && obj.qty <= 10) return "#fbc02d";
          else if (obj.qty > 10 && obj.qty <= 25) return "#0ab6f6";
          else if (obj.qty > 25 && obj.qty <= 50) return "#0ba120";
          else if (obj.qty > 50 && obj.qty <= 100) return "#21618C";
          else if (obj.qty > 100 && obj.qty <= 200) return "#f06292";
          else if (obj.qty > 200 && obj.qty <= 500) return "#af7ac5";
          else return "#CB4335";
        },
        label: "#qty#",
        minHeight: 400,
        data: [],
        radius: 2,
        yAxis: { template: "#type#" },
        padding: { left: 190, right: 50 },
      },
      { width: 40 },
    ],
  },
  { height: 10 },
];
