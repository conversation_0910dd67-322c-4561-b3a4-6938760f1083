let incidentReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Individual Incident Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 320,
        options: [],
      },
      {},
    ],
  },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "incident_grid",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        sort: "int",
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: "Employee Name",
        minWidth: 140,
        sort: "string",
        adjust: true,
      },
      {
        id: "roster",
        header: "Curr Roster",
        minWidth: 120,
        sort: "string",
        adjust: true,
      },
      {
        id: "shift",
        header: "Curr Shift",
        minWidth: 120,
        sort: "string",
        adjust: true,
      },
      {
        id: "location",
        header: "Curr Location",
        minWidth: 120,
        sort: "string",
        adjust: true,
      },
      {
        id: "incident_date",
        header: "Incident Date",
        width: 130,
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "incident_no",
        header: "Incident #",
        width: 90,
        sort: "int",
        css: { "text-align": "center" },
      },
      {
        id: "incident_type",
        header: "Incident Type",
        adjust: true,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "station_id",
        header: "Station",
        width: 80,
        sort: "int",
        css: { "text-align": "center" },
      },
      {
        id: "truck_id",
        header: "Appliance",
        sort: "string",
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "position",
        header: "Position",
        width: 90,
        sort: "string",
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
