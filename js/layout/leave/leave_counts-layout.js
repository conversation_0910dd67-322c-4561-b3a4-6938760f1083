let LeaveCountsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Leave Counts</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "rosters",
        label: "Roster",
        width: 280,
        labelWidth: 50,
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift",
        label: "Shift",
        width: 320,
        labelWidth: 45,
        options: [],
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 15 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "leave_counts_grid",
    spans: true,
    select: "row",
    columns: [
      { id: "date", header: "Date", width: 140 },
      { id: "ranks", header: "Ranks", width: 300 },
      { id: "leave_types", header: "Leave Types", width: 420 },
      {
        id: "booked",
        header: "Booked",
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "pending",
        header: "Pending",
        width: 80,
        css: { "text-align": "center" },
      },
    ],
    data: { data: [], spans: [] },
  },
  { height: 10 },
];
