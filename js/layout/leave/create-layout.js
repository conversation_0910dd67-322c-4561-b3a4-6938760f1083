let createBookingLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Create Bookings</span>",
        align: "left",
      },
      { view: "button", id: "btn_reset_form", value: "Reset Form", width: 100 },
    ],
  },
  {
    cols: [
      {
        view: "form",
        id: "create_booking",
        margin: 1,
        scroll: "y",
        elements: [
          {
            cols: [
              {
                view: "datepicker",
                id: "start_date",
                name: "start_date",
                width: 260,
                label: "Start Date",
                labelWidth: 85,
                labelAlign: "left",
                value: new Date(),
                format: "%D, %d/%m/%Y",
                stringResult: true,
                timepicker: false,
              },
              { width: 20 },
              {
                view: "datepicker",
                id: "start_time",
                name: "start_time",
                type: "time",
                width: 85,
                label: "",
                value: "01/01/2019 08:00",
                format: "%H:%i",
                stringResult: true,
                disabled: true,
              },
              {
                view: "checkbox",
                id: "day_hours",
                name: "day_hours",
                labelRight: "Day Shift",
                value: 1,
                labelWidth: 20,
                width: 140,
              },
            ],
          },
          {
            cols: [
              {
                view: "datepicker",
                id: "end_date",
                name: "end_date",
                width: 260,
                label: "End Date",
                labelWidth: 85,
                labelAlign: "left",
                value: new Date(),
                format: "%D, %d/%m/%Y",
                stringResult: true,
                timepicker: false,
                disabled: true,
              },
              { width: 20 },
              {
                view: "datepicker",
                id: "end_time",
                name: "end_time",
                type: "time",
                width: 85,
                label: "",
                value: "01/01/2019 18:00",
                format: "%H:%i",
                stringResult: true,
                disabled: true,
              },
              {
                view: "checkbox",
                id: "night_hours",
                name: "night_hours",
                labelRight: "Night Shift",
                value: 0,
                labelWidth: 20,
                width: 140,
              },
            ],
          },
          {
            view: "combo",
            id: "leave_type",
            name: "leave_type",
            label: "Leave Type",
            labelWidth: 85,
            width: 600,
            options: [],
            labelAlign: "left",
          },
          {
            cols: [
              {
                view: "combo",
                id: "roster_name",
                name: "roster_name",
                label: "Roster",
                labelWidth: 50,
                width: 280,
                options: [],
                labelAlign: "left",
              },
              { width: 15 },
              {
                view: "combo",
                id: "shift_type",
                name: "shift_type",
                label: "Shift",
                labelWidth: 45,
                width: 320,
                options: [],
                labelAlign: "left",
              },
              { width: 15 },
              {
                view: "combo",
                id: "locations",
                name: "locations",
                label: "Location",
                labelWidth: 70,
                width: 330,
                options: [],
                labelAlign: "left",
              },
            ],
          },
          { height: 20 },
          {
            cols: [
              {},
              {
                rows: [
                  {
                    view: "template",
                    template:
                      "------------------------------ Roster Shift Sequence ------------------------------",
                    align: "center",
                    css: { "font-size": "14px !important", color: "#0133FE" },
                    borderless: true,
                    autoheight: true,
                  },
                  {
                    view: "datatable",
                    id: "shift_sequence",
                    minColumnWidth: 52,
                    maxColumnWidth: 52,
                    width: 416,
                    height: 80,
                    scroll: false,
                    columns: [],
                    data: [],
                  },
                ],
              },
              {},
            ],
          },
          { height: 20 },
          {
            view: "fieldset",
            label: "Search Employees",
            body: {
              rows: [
                {
                  cols: [
                    {
                      view: "text",
                      id: "search_text",
                      label: "Employee / Service No",
                      labelWidth: 160,
                      width: 420,
                      labelAlign: "left",
                    },
                    { width: 5 },
                    {
                      view: "button",
                      id: "btn_search",
                      type: "icon",
                      icon: "fas fa-search",
                      label: "Search",
                      width: 100,
                    },
                    {},
                  ],
                },
                {
                  view: "dbllist",
                  id: "search_results",
                  list: { height: 200, width: 260 },
                  labelLeft: "Employee Search Results",
                  labelRight: "Selected Employee(s)",
                  data: [],
                },
                { height: 5 },
                {
                  cols: [
                    {
                      view: "template",
                      id: "search_count",
                      template: "0 records found",
                      borderless: true,
                      height: 25,
                      align: "left",
                      css: { "font-style": "italic" },
                    },
                    { width: 140 },
                    {
                      view: "template",
                      id: "selected_count",
                      template: "0 records selected",
                      borderless: true,
                      height: 25,
                      align: "right",
                      css: { "font-style": "italic" },
                    },
                  ],
                },
              ],
            },
          },
          { height: 10 },
          {
            cols: [
              {
                view: "textarea",
                id: "comments",
                name: "comments",
                label: "Comments",
                height: 55,
                width: 400,
                labelWidth: 85,
              },
              { width: 15 },
              {
                rows: [
                  {},
                  {
                    view: "button",
                    id: "btn_save",
                    value: "Save",
                    width: 100,
                    align: "center",
                  },
                ],
              },
            ],
          },
          { height: 10 },
        ],
        rules: {
          leave_type: webix.rules.isNotEmpty,
          shift_type: webix.rules.isNotEmpty,
          roster_name: webix.rules.isNotEmpty,
        },
      },
      {},
    ],
  },
  { height: 10 },
  {
    view: "toolbar",
    id: "import_toolbar",
    css: "main_header",
    hidden: true,
    cols: [
      {
        view: "uploader",
        id: "import_uploader",
        width: 120,
        css: "webix_secondary",
        value: "Load Excel File",
        accept: "application/vnd.ms-excel",
        multiple: false,
      },
      { view: "button", id: "clear_sheet", label: "Clear", width: 60 },
      { width: 20 },
      {
        view: "label",
        id: "file_name",
        label: "Import Booking Reports from Gartan",
        css: "header_font",
        on: {
          onBeforeRender: function (config) {
            config.labelWidth = webix.html.getTextSize(config.label).width;
          },
        },
      },
      { width: 10 },
      {
        view: "radio",
        id: "import_type",
        label: "",
        css: "ra_import",
        options: ["Leave", "RRL"],
        value: "Leave",
        width: 160,
      },
      {
        view: "datepicker",
        id: "import_start_date",
        value: new Date(),
        label: "Start Date",
        labelWidth: 80,
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
        css: "ra_import",
        width: 200,
      },
      { width: 20 },
      {
        view: "button",
        id: "import_sheet",
        label: "Import Bookings",
        width: 150,
        disabled: true,
      },
    ],
  },
  {
    view: "excelviewer",
    id: "excel_import",
    excelHeader: false,
    header: false,
    scroll: true,
    hidden: true,
  },
];
