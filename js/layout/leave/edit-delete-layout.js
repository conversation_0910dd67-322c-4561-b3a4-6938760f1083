let editDeleteBookingsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Search Bookings</span>",
        align: "left",
      },
      {},
      {
        view: "button",
        id: "btn_delete",
        value: "Delete Whole Booking",
        width: 160,
      },
      {
        view: "button",
        id: "btn_delete_days",
        value: "Delete Booking Day",
        width: 150,
      },
      { width: 5 },
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        rows: [
          {
            cols: [
              {
                view: "combo",
                id: "rosters",
                label: "Select Roster",
                width: 370,
                labelWidth: 120,
                disabled: true,
                options: [],
              },
              { width: 10 },
              {
                view: "checkbox",
                id: "all_rosters",
                label: "All Rosters",
                value: 1,
                width: 200,
              },
            ],
          },
          {
            cols: [
              {
                view: "datepicker",
                id: "from",
                name: "from",
                width: 170,
                label: "From",
                labelWidth: 45,
                labelAlign: "left",
                value: new Date(),
                format: "%d/%m/%Y",
                stringResult: true,
                timepicker: false,
              },
              { width: 40 },
              {
                view: "datepicker",
                id: "to",
                name: "to",
                width: 150,
                label: "To",
                labelWidth: 25,
                labelAlign: "left",
                value: new Date(),
                format: "%d/%m/%Y",
                stringResult: true,
                timepicker: false,
              },
              { width: 10 },
              {
                view: "button",
                id: "btn_search",
                type: "icon",
                icon: "fas fa-search",
                label: "Search",
                width: 100,
              },
            ],
          },
        ],
      },
      {},
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "search_results",
    select: "row",
    scroll: "xy",
    columns: [
      { id: "booking_id", hidden: true },
      {
        id: "service_no",
        header: "Service No.",
        adjust: "header",
        sort: "int",
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      { id: "rank", header: "Rank", adjust: true, sort: "string" },
      { id: "roster", header: "Roster", adjust: true },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "booking_first_date",
        header: "Start Date",
        width: 160,
        sort: "date",
      },
      { id: "booking_last_date", header: "End Date", width: 160, sort: "date" },
      {
        id: "total_hours",
        header: "Total Hours",
        adjust: "header",
        sort: "int",
      },
      { id: "code", header: "Code", width: 60, sort: "string" },
      { id: "date_created", header: "Date Created", width: 160, sort: "date" },
    ],
    data: [],
  },
  {
    view: "template",
    id: "search_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];



function createSBookWindow() {
  webix
      .ui({
        view: "window",
        id: "search-bookings-window",
        modal: true,
        position: "center",
        fullscreen: false,
        width: 590,
        height: 740,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "search-booking-label",
                  label: "<span class='header_font'>Booking Details</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_search-booking_days_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              view: "template",
              id: "search-booking_details",
              template: "",
              autoheight: true,
              borderless: true,
              css: {
                "text-align": "center",
                color: "black",
                "font-size": "17px !important",
              },
            },
            {height: 15},
            {
              view: "datatable",
              id: "search-booking_days",
              select: "row",
              scheme: {
                $change: function (item) {
                  if (item.deleted == true) item.$css = "temp_ra_line";
                },
              },
              height: 500,
              columns: [
                {id: "booking_id", hidden: true},
                {id: "code", header: "Code", width: 90, sort: "string"},
                {
                  id: "start_date",
                  header: "Start Date & Time",
                  width: 160,
                  sort: "date",
                },
                {
                  id: "end_date",
                  header: "End Date & Time",
                  width: 160,
                  sort: "date",
                },
                {
                  id: "shift_hours",
                  header: "Shift Hours",
                  width: 100,
                  css: {"text-align": "center"},
                  sort: "int",
                },
                {
                  id: "day_delete",
                  header: "",
                  css: {"text-align": "center"},
                  width: 60,
                },
                {id: "status", hidden: true},
                {id: "deleted", hidden: true},
              ],
              data: [],
            },
            {height: 5},
            {
              view: "template",
              id: "search-days_count",
              template: "No. of booking days: 0",
              borderless: true,
              height: 35,
              css: {"font-style": "italic"},
            },
          ],
        },
      })
      .hide();
}

