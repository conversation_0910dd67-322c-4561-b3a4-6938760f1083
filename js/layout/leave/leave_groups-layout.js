let leaveGroupsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Leave Groups</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    view: "scrollview",
    id: "RRL_dates_sv",
    scroll: "y",
    body: {
      rows: [
        {
          cols: [
            { width: 10 },
            {
              view: "combo",
              id: "leave_group_list",
              label: "Leave Group",
              width: 190,
              labelWidth: 90,
              options: [],
            },
            { width: 20 },
            {
              view: "datepicker",
              id: "from_date",
              label: "From",
              timepicker: false,
              labelWidth: 50,
              width: 180,
              format: "%d/%m/%Y",
              value: new Date(),
              stringResult: true,
            },
            { width: 20 },
            {
              view: "datepicker",
              id: "to_date",
              label: "To",
              timepicker: false,
              labelWidth: 30,
              width: 160,
              format: "%d/%m/%Y",
              value: new Date(),
              stringResult: true,
            },
            { width: 20 },
            {
              view: "button",
              id: "btn_search",
              type: "icon",
              icon: "fas fa-search",
              label: "Search",
              width: 100,
            },
            {},
          ],
        },
        {
          cols: [
            {
              rows: [
                { height: 40 },
                {
                  view: "datatable",
                  id: "leave_dates_grid",
                  select: "row",
                  scroll: "y",
                  height: 300,
                  width: 370,
                  columns: [
                    { id: "id", hidden: true },
                    { id: "leave_type", header: "Leave Type", width: 100 },
                    { id: "start_date", header: "Start Date", width: 125 },
                    { id: "return_date", header: "Return Date", width: 125 },
                  ],
                  data: [],
                },
                { height: 10 },
              ],
            },
            { width: 55 },
            {
              rows: [
                {
                  view: "button",
                  id: "btn_export_excel",
                  label:
                    "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
                  width: 120,
                  align: "right",
                },
                {
                  view: "datatable",
                  id: "leave_groups_grid",
                  select: "row",
                  scroll: "y",
                  height: 300,
                  width: 820,
                  columns: [
                    { id: "booking_id", hidden: true },
                    {
                      id: "service_no",
                      header: "Pay ID",
                      width: 75,
                      sort: "int",
                    },
                    {
                      id: "employee",
                      header: "Employee",
                      fillspace: true,
                      sort: "string",
                    },
                    {
                      id: "rank",
                      header: "Rank",
                      width: 90,
                      css: { "text-align": "center" },
                      sort: "string",
                    },
                    {
                      id: "week_no",
                      header: "Week #",
                      css: { "text-align": "center" },
                      width: 70,
                    },
                    {
                      id: "start_date",
                      header: "Start Date",
                      css: { "text-align": "center" },
                      width: 100,
                    },
                    {
                      id: "return_date",
                      header: "Return Date",
                      css: { "text-align": "center" },
                      width: 110,
                    },
                    {
                      id: "days",
                      header: "Days",
                      css: { "text-align": "center" },
                      width: 60,
                    },
                  ],
                  data: [],
                },
                {
                  view: "template",
                  id: "search_count",
                  template: "",
                  borderless: true,
                  autoheight: true,
                  css: { "font-style": "italic" },
                },
              ],
            },
            {},
          ],
        },
        { height: 10 },
        {
          cols: [
            {
              view: "form",
              id: "frmCreateGroup",
              elements: [
                {
                  view: "label",
                  label: "Create Leave Group",
                  align: "center",
                  css: "form_label",
                },
                { height: 5 },
                {
                  view: "text",
                  id: "group_name",
                  name: "group_name",
                  label: "Group Name",
                  width: 240,
                  labelWidth: 120,
                  align: "center",
                },
                {
                  view: "datepicker",
                  id: "group_start_date",
                  name: "group_start_date",
                  width: 240,
                  label: "Start Date",
                  labelWidth: 100,
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                  align: "center",
                },
                {
                  view: "datepicker",
                  id: "group_return_date",
                  name: "group_return_date",
                  width: 240,
                  label: "Return Date",
                  labelWidth: 100,
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                  align: "center",
                },
                {
                  cols: [
                    {},
                    {
                      view: "checkbox",
                      id: "auto_generate_group",
                      name: "auto_generate_group",
                      label: "Auto Generate",
                      width: 140,
                      labelWidth: 110,
                      value: 0,
                      align: "center",
                    },
                    { width: 15 },
                    {
                      view: "combo",
                      id: "period_years",
                      name: "period_years",
                      label: "Years",
                      width: 110,
                      labelWidth: 45,
                      value: 1,
                      align: "center",
                      options: [
                        "1",
                        "2",
                        "3",
                        "4",
                        "5",
                        "6",
                        "7",
                        "8",
                        "9",
                        "10",
                        "11",
                        "12",
                        "13",
                        "14",
                        "15",
                        "16",
                        "17",
                        "18",
                        "19",
                        "20",
                      ],
                      disabled: true,
                    },
                    {},
                  ],
                },
                {
                  view: "button",
                  id: "btn_save_group",
                  value: "Save",
                  width: 100,
                  align: "right",
                },
                { height: 10 },
              ],
              rules: { group_name: webix.rules.isNotEmpty },
            },
            { width: 30 },
            {
              view: "form",
              id: "frmLeaveGroups",
              width: 570,
              elements: [
                {
                  view: "label",
                  label: "Assign Employee to a Leave Group",
                  align: "center",
                  css: "form_label",
                },
                { height: 5 },
                {
                  view: "combo",
                  id: "search_employee",
                  name: "search_employee",
                  label: "Employee",
                  width: 460,
                  labelWidth: 80,
                  options: [],
                },
                {
                  cols: [
                    {
                      view: "template",
                      id: "current_rank",
                      name: "current_rank",
                      template: "Current Rank: ",
                      borderless: true,
                      autowidth: true,
                      autoheight: true,
                    },
                    {
                      view: "template",
                      id: "curr_leave_group",
                      name: "curr_leave_group",
                      template: "Current Leave Group: ",
                      borderless: true,
                      autowidth: true,
                      autoheight: true,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "combo",
                      id: "select_group",
                      name: "select_group",
                      label: "Assign To Group",
                      width: 230,
                      labelWidth: 130,
                      options: [],
                      labelAlign: "right",
                    },
                    { width: 20 },
                    {
                      view: "text",
                      id: "curr_rank",
                      name: "curr_rank",
                      label: "Rank",
                      labelWidth: 50,
                      width: 140,
                      hidden: true,
                    },
                    {
                      view: "text",
                      id: "current_leave_group",
                      name: "current_leave_group",
                      label: "Current Leave Group",
                      labelWidth: 100,
                      width: 300,
                      hidden: true,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "RRL_start_date",
                      name: "RRL_start_date",
                      width: 250,
                      label: "Official Start Date",
                      labelWidth: 130,
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      labelAlign: "right",
                    },
                    {
                      view: "template",
                      template:
                        "<span style='font-size: 12px; line-height: 15px; font-style: italic;'>the date this employee will be assigned to the new RRL leave group</span>",
                      width: 300,
                      borderless: true,
                      height: 38,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "RRL_effective_date",
                      name: "RRL_effective_date",
                      width: 250,
                      label: "Effective Start Date",
                      labelWidth: 130,
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      labelAlign: "right",
                    },
                    {
                      view: "template",
                      template:
                        "<span style='font-size: 12px; line-height: 15px; font-style: italic;'>the date from which this employee will be assigned the new RRL leave bookings</span>",
                      width: 300,
                      borderless: true,
                      height: 38,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "RRL_end_date",
                      name: "RRL_end_date",
                      width: 250,
                      label: "End Date",
                      labelWidth: 130,
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      labelAlign: "right",
                    },
                    {},
                    {
                      view: "button",
                      id: "btn_save",
                      value: "Save",
                      width: 100,
                      align: "right",
                    },
                  ],
                },
                {
                  view: "text",
                  id: "curr_roster",
                  name: "curr_roster",
                  label: "Roster",
                  labelWidth: 100,
                  width: 300,
                  hidden: true,
                },
                {
                  view: "text",
                  id: "curr_shift",
                  name: "curr_shift",
                  label: "Shift",
                  labelWidth: 100,
                  width: 300,
                  hidden: true,
                },
                {
                  view: "text",
                  id: "curr_location",
                  name: "curr_location",
                  label: "Location",
                  labelWidth: 100,
                  width: 300,
                  hidden: true,
                },
                { height: 10 },
              ],
              rules: {
                search_employee: webix.rules.isNotEmpty,
                select_group: webix.rules.isNotEmpty,
              },
            },
            { width: 30 },
            {
              view: "datatable",
              id: "RRL_dates_grid",
              select: "row",
              scroll: "y",
              height: 360,
              width: 320,
              columns: [
                { id: "id", hidden: true },
                { id: "leave_type", header: "Type", width: 70 },
                { id: "start_date", header: "Start Date", width: 120 },
                { id: "return_date", header: "Return Date", width: 120 },
              ],
              data: [],
            },
            {},
          ],
        },
        { height: 10 },
        {
          cols: [
            {
              rows: [
                {
                  view: "label",
                  label: "Delete Employee's Existing RRL bookings",
                  align: "center",
                },
                {
                  view: "form",
                  id: "frmRRLDelete",
                  borderless: true,
                  elements: [
                    {
                      cols: [
                        {
                          view: "combo",
                          id: "delete_search_employee",
                          name: "delete_search_employee",
                          label: "Employee",
                          width: 395,
                          labelWidth: 70,
                          options: [],
                        },
                        {
                          view: "button",
                          id: "btn_delete_search",
                          value: "Search",
                          width: 80,
                          align: "right",
                        },
                      ],
                    },
                  ],
                  rules: { delete_search_employee: webix.rules.isNotEmpty },
                },
                {
                  view: "datatable",
                  id: "employee_rrl_bookings_grid",
                  select: "row",
                  scroll: "y",
                  height: 260,
                  columns: [
                    { id: "booking_id", hidden: true },
                    {
                      id: "type",
                      header: "Type",
                      width: 80,
                      css: { "text-align": "center" },
                      sort: "string",
                    },
                    {
                      id: "week_no",
                      header: "Week #",
                      css: { "text-align": "center" },
                      width: 80,
                    },
                    {
                      id: "start_date",
                      header: "Start Date",
                      css: { "text-align": "center" },
                      width: 110,
                    },
                    {
                      id: "return_date",
                      header: "Return Date",
                      css: { "text-align": "center" },
                      width: 110,
                    },
                    {
                      id: "days",
                      header: "Days",
                      css: { "text-align": "center" },
                      width: 60,
                    },
                    {
                      id: "select",
                      header:
                        "<span class='webix_icon fas fa-trash-alt'></span>",
                      template: "{common.checkbox()}",
                      css: { "text-align": "center" },
                      width: 50,
                    },
                  ],
                  data: [],
                },
                {
                  cols: [
                    { width: 5 },
                    {
                      view: "button",
                      id: "btn_delete_select_all",
                      value: "Select All",
                      width: 110,
                      height: 32,
                    },
                    {
                      view: "button",
                      id: "btn_delete_unselect_all",
                      value: "Unselect All",
                      width: 120,
                      height: 32,
                    },
                    {},
                    {
                      view: "button",
                      id: "btn_delete_rrls",
                      value: "Delete",
                      width: 100,
                      align: "right",
                    },
                  ],
                },
                { height: 10 },
              ],
            },
            { width: 80 },
            {
              rows: [
                {
                  view: "label",
                  label: "Employee's RRL Group Logs",
                  align: "center",
                },
                { height: 10 },
                {
                  view: "datatable",
                  id: "employee_group_logs",
                  select: "row",
                  scroll: "y",
                  height: 350,
                  width: 660,
                  columns: [
                    { id: "id", hidden: true },
                    {
                      id: "group",
                      header: "Group",
                      width: 70,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "official_date",
                      header: "Official Date",
                      width: 110,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "effective_date",
                      header: "Effective Date",
                      width: 110,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "end_date",
                      header: "End Date",
                      width: 110,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "created_by",
                      header: "Created By",
                      width: 110,
                      css: { "text-align": "center" },
                    },
                    {
                      id: "created_date",
                      header: "Created Date",
                      width: 130,
                      css: { "text-align": "center" },
                    },
                  ],
                  data: [],
                },
                {},
              ],
            },
            {},
          ],
        },
      ],
    },
  },
  { height: 10 },
  {
    view: "toolbar",
    id: "import_toolbar",
    css: "main_header",
    hidden: true,
    cols: [
      {
        view: "uploader",
        id: "import_uploader",
        width: 120,
        css: "webix_secondary",
        value: "Load Excel File",
        accept: ".xlsx",
        multiple: false,
      },
      { view: "button", id: "clear_sheet", label: "Clear", width: 60 },
      { width: 20 },
      {
        view: "label",
        id: "file_name",
        label: "Import RRL Group List",
        css: "header_font",
        on: {
          onBeforeRender: function (config) {
            config.labelWidth = webix.html.getTextSize(config.label).width;
          },
        },
      },
      { width: 20 },
      {
        view: "button",
        id: "import_sheet",
        label: "Import RRL Groups",
        width: 160,
        disabled: true,
      },
      {},
    ],
  },
  {
    view: "excelviewer",
    id: "excel_import",
    excelHeader: false,
    header: false,
    scroll: true,
    hidden: true,
  },
  { height: 10 },
  {
    view: "toolbar",
    id: "create_RRL_toolbar",
    css: "main_header",
    hidden: true,
    cols: [
      {
        view: "uploader",
        id: "create_RRL_uploader",
        width: 120,
        css: "webix_secondary",
        value: "Load Excel File",
        accept: ".xlsx",
        multiple: false,
      },
      {
        view: "button",
        id: "clear_employee_RRL_sheet",
        label: "Clear",
        width: 60,
      },
      { width: 20 },
      {
        view: "label",
        id: "RRL_file_name",
        label: "Create RRL Bookings",
        css: "header_font",
        on: {
          onBeforeRender: function (config) {
            config.labelWidth = webix.html.getTextSize(config.label).width;
          },
        },
      },
      { width: 20 },
      {
        view: "button",
        id: "import_RRL_sheet",
        label: "Create RRL Bookings",
        width: 160,
        disabled: true,
      },
      {},
    ],
  },
  {
    view: "excelviewer",
    id: "excel_RRL_bookings_import",
    excelHeader: false,
    header: false,
    scroll: true,
    hidden: true,
  },
];
