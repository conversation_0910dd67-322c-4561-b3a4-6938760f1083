let leaveLogsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Leave Logs</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 420,
        options: [],
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 50,
        width: 180,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 30,
        width: 160,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      {
        view: "checkbox",
        id: "inc_comms",
        name: "inc_comms",
        labelRight: "inc. Comms",
        width: 140,
        labelWidth: 20,
        value: 0,
        hidden: true,
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 40,
        width: 320,
        hidden: true,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        hidden: true,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 55,
        width: 170,
        options: [
          { id: 1, value: "Active" },
          { id: 2, value: "Deleted" },
          { id: 3, value: "All" },
        ],
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "leave_filter",
        name: "leave_filter",
        label: "Leave",
        labelWidth: 70,
        width: 550,
        options: [],
      },
      { width: 48 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "leave-logs_grid",
    select: "row",
    columns: [
      { id: "booking_id", hidden: true },
      {
        id: "service_no",
        header: "Service No",
        width: 95,
        sort: "int",
        css: { "text-align": "center" },
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      { id: "rank", header: "Rank", adjust: true, sort: "string" },
      { id: "roster", header: "Roster", adjust: true },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "booking_first_date",
        header: "Start Date",
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "booking_last_date",
        header: "End Date",
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "total_hours",
        header: "Hours",
        width: 65,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "days",
        header: "Days",
        width: 65,
        css: { "text-align": "center" },
        sort: "int",
        hidden: true,
      },
      {
        id: "code",
        header: "Code",
        width: 60,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "leave_group",
        header: { text: "Leave\nGroup", css: "multiline" },
        width: 70,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "date_created",
        header: "Created Date",
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "created_by",
        header: "Created By",
        width: 95,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "deleted",
        header: "Deleted",
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "deleted_period",
        header: { text: "Deleted Period", css: { "text-align": "center" } },
        sort: "string",
        css: { "text-align": "center" },
        adjust: true,
      },
      {
        id: "status",
        header: "Status",
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "approved_denied_date",
        header: { text: "Approved/Denied Date", css: "rrl_swap_header" },
        width: 160,
        css: { "text-align": "center" },
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "approved_denied_by",
        header: { text: "Approved/Denied By", css: "rrl_swap_header" },
        width: 150,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "comments",
        header: { text: "Comments", css: "rrl_swap_header" },
        minWidth: 320,
        adjust: true,
        sort: "string",
      },
      {
        id: "date_deleted",
        header: { text: "Deleted Date", css: "rrl_swap_header" },
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "deleted_by",
        header: { text: "Deleted By", css: "rrl_swap_header" },
        width: 90,
        sort: "int",
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
];
