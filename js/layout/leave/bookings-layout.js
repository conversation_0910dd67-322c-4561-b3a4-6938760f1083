let bookingsLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "bookings-page",
        isolate: true,
        cols: [
          {
            view: "sidebar",
            id: "bookings_sidebar",
            data: [],
            width: 200,
            on: {
              onAfterSelect: function (id) {
                routes.navigate(id, { trigger: true });
              },
            },
          },
          {
            view: "multiview",
            cells: [
              {
                id: "booking_create",
                isolate: true,
                rows: createBookingLayout,
              },
              {
                id: "booking_requests",
                isolate: true,
                rows: leaveRequestsLayout,
              },
              {
                id: "booking_edit",
                isolate: true,
                rows: editDeleteBookingsLayout,
              },
              {
                id: "booking_leave_counts",
                isolate: true,
                rows: LeaveCountsLayout,
              },
              { id: "booking_log", isolate: true, rows: leaveLogsLayout },
              { id: "leave_swap", isolate: true, rows: leaveSwapLayout },
              {
                id: "leave_swap_list",
                isolate: true,
                rows: leaveSwapListLayout,
              },
              { id: "leave_groups", isolate: true, rows: leaveGroupsLayout },
              { id: "leave_log", isolate: true, rows: leaveGroupLogsLayout },
              { id: "acting_up_log", isolate: true, rows: actingUpLogLayout },
              { id: "standby_log", isolate: true, rows: standbyLogLayout },
              {
                id: "staff_movement_log",
                isolate: true,
                rows: staffMovementLogsLayout,
              },
              { id: "overtime_log", isolate: true, rows: overtimeLogsLayout },
              { id: "travel_create", isolate: true, rows: createTravelLayout },
              {
                id: "travel_requests",
                isolate: true,
                rows: travelRequestsLayout,
              },
              {
                id: "sick_certificates",
                isolate: true,
                rows: sicknessCertificatesLayout,
              },
              {
                id: "sick_bookings_log",
                isolate: true,
                rows: sickBookingsLogLayout,
              },
              {
                id: "soil_toil_balances",
                isolate: true,
                rows: soilToilBalancesLayout,
              },
              {
                id: "day_work_requests",
                isolate: true,
                rows: dayWorkRequestsLayout,
              },
            ],
            animate: false,
          },
        ],
      };
    },
  };
})();
