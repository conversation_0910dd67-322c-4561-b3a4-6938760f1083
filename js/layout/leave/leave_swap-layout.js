let leaveSwapLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Leave Swap</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    view: "scrollview",
    scroll: "xy",
    body: {
      rows: [
        {
          view: "form",
          id: "frmLeaveSwap",
          autoheight: true,
          elements: [
            {
              view: "combo",
              id: "from_employee",
              name: "from_employee",
              label: "Employee",
              width: 400,
              labelWidth: 100,
              options: [],
            },
            {
              view: "datatable",
              id: "from_dates_grid",
              select: "row",
              multiselect: false,
              height: 200,
              css: "rrl_swap_grid",
              columns: [
                { id: "booking_id", hidden: true },
                { id: "group_colour", header: "", width: 5, css: "" },
                {
                  id: "leave_type",
                  header: "Leave",
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "leave_group",
                  header: "Group",
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "rank",
                  header: "Rank",
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "start_date",
                  header: "Start Date",
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "return_date",
                  header: "Return Date",
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "week_no",
                  header: "Week #",
                  width: 75,
                  css: { "text-align": "center" },
                },
                { id: "roster", header: "Roster", minWidth: 80 },
                { id: "shift", header: "Shift", minWidth: 80 },
                { id: "location", header: "Location", minWidth: 80 },
                {
                  id: "rrl_swapee",
                  header: { text: "Swapped With", css: "rrl_swap_header" },
                  minWidth: 120,
                  adjust: true,
                },
                {
                  id: "swap_group",
                  header: { text: "Group", css: "rrl_swap_header" },
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_start_date",
                  header: { text: "Start Date", css: "rrl_swap_header" },
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_return_date",
                  header: { text: "Return Date", css: "rrl_swap_header" },
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_week_no",
                  header: { text: "Week #", css: "rrl_swap_header" },
                  width: 75,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_roster",
                  header: { text: "Roster", css: "rrl_swap_header" },
                  minWidth: 80,
                },
                {
                  id: "swap_shift",
                  header: { text: "Shift", css: "rrl_swap_header" },
                  minWidth: 80,
                },
                {
                  id: "swap_location",
                  header: { text: "Location", css: "rrl_swap_header" },
                  minWidth: 80,
                },
                { fillspace: true },
              ],
              data: [],
            },
            { height: 20 },
            {
              view: "combo",
              id: "swap_employee",
              name: "swap_employee",
              label: "Swap With",
              width: 400,
              labelWidth: 100,
              options: [],
            },
            {
              view: "datatable",
              id: "swap_dates_grid",
              select: "row",
              multiselect: false,
              height: 200,
              css: "rrl_swap_grid",
              columns: [
                { id: "booking_id", hidden: true },
                { id: "group_colour", header: "", width: 5, css: "" },
                {
                  id: "leave_type",
                  header: "Leave",
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "leave_group",
                  header: "Group",
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "rank",
                  header: "Rank",
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "start_date",
                  header: "Start Date",
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "return_date",
                  header: "Return Date",
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "week_no",
                  header: "Week #",
                  width: 75,
                  css: { "text-align": "center" },
                },
                { id: "roster", header: "Roster", minWidth: 80 },
                { id: "shift", header: "Shift", minWidth: 80 },
                { id: "location", header: "Location", minWidth: 80 },
                {
                  id: "rrl_swapee",
                  header: { text: "Swapped With", css: "rrl_swap_header" },
                  minWidth: 120,
                  adjust: true,
                },
                {
                  id: "swap_group",
                  header: { text: "Group", css: "rrl_swap_header" },
                  width: 65,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_start_date",
                  header: { text: "Start Date", css: "rrl_swap_header" },
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_return_date",
                  header: { text: "Return Date", css: "rrl_swap_header" },
                  width: 105,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_week_no",
                  header: { text: "Week #", css: "rrl_swap_header" },
                  width: 75,
                  css: { "text-align": "center" },
                },
                {
                  id: "swap_roster",
                  header: { text: "Roster", css: "rrl_swap_header" },
                  minWidth: 80,
                },
                {
                  id: "swap_shift",
                  header: { text: "Shift", css: "rrl_swap_header" },
                  minWidth: 80,
                },
                {
                  id: "swap_location",
                  header: { text: "Location", css: "rrl_swap_header" },
                  minWidth: 80,
                },
                { fillspace: true },
              ],
              data: [],
            },
          ],
        },
        { height: 20 },
        {
          cols: [
            { width: 10 },
            {
              view: "textarea",
              id: "swap_comments",
              name: "swap_comments",
              label: "Comments",
              height: 50,
              labelWidth: 90,
              width: 500,
            },
            { width: 10 },
            {
              rows: [
                {},
                {
                  view: "button",
                  id: "swap_btn_save",
                  label: "Save",
                  width: 100,
                  align: "right",
                  height: 32,
                },
              ],
            },
          ],
        },
        { height: 10 },
      ],
    },
  },
];
