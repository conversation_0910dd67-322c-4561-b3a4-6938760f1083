let leaveSwapListLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Leave Swap List</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "combo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 60,
        width: 170,
        options: [
          { id: 1, value: "Approved" },
          { id: 2, value: "Pending" },
          { id: 3, value: "Denied" },
          { id: 4, value: "All Swaps" },
        ],
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 50,
        width: 180,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 30,
        width: 160,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 30 },
  {
    view: "datatable",
    id: "leave-swap-list_grid",
    select: "row",
    scroll: "xy",
    columns: [
      { id: "id", hidden: true },
      {
        id: "service_no",
        header: "Service No.",
        width: 100,
        sort: "int",
        css: { "text-align": "center" },
      },
      { id: "employee", header: "Employee", width: 240, sort: "string" },
      {
        id: "original_start_date",
        header: "Start Date",
        width: 110,
        css: { "text-align": "center" },
      },
      {
        id: "original_end_date",
        header: "Return Date",
        width: 110,
        css: { "text-align": "center" },
      },
      {
        id: "week_no",
        header: "Week #",
        width: 70,
        css: { "text-align": "center" },
      },
      { id: "roster", header: "Roster", width: 120 },
      { id: "shift", header: "Shift", width: 140 },
      { id: "location", header: "Location", width: 160 },
      {
        id: "swap_employee",
        header: { text: "Swapped With", css: "rrl_swap_header" },
        width: 240,
        sort: "string",
      },
      {
        id: "swap_start_date",
        header: { text: "Start Date", css: "rrl_swap_header" },
        width: 110,
        css: { "text-align": "center" },
      },
      {
        id: "swap_end_date",
        header: { text: "Return Date", css: "rrl_swap_header" },
        width: 110,
        css: { "text-align": "center" },
      },
      {
        id: "swap_week_no",
        header: { text: "Week #", css: "rrl_swap_header" },
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "swap_roster",
        header: { text: "Swap Roster", css: "rrl_swap_header" },
        width: 120,
      },
      {
        id: "swap_shift",
        header: { text: "Swap Shift", css: "rrl_swap_header" },
        width: 140,
      },
      {
        id: "swap_location",
        header: { text: "Swap Location", css: "rrl_swap_header" },
        width: 160,
      },
      { id: "status", header: "Status", width: 70 },
    ],
    data: [],
  },
  { height: 10 },
];
