let soilToilBalancesLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Leave Balances (SOIL / TOIL / PHOL)</span>",
        align: "left",
      },
    ],
  },
  { height: 5 },
  {
    cols: [
      { width: 10 },
      {
        view: "search",
        id: "pay_id_search_field",
        label: "Service No. Filter",
        width: 220,
        labelWidth: 115,
      },
      { width: 30 },
      {
        view: "search",
        id: "name_search_field",
        label: "Surname Filter",
        width: 300,
        labelWidth: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
      { view: "button", id: "import_phol", label: "Import PHOL", width: 130 },
      { width: 10 },
    ],
  },
  { height: 5 },
  {
    view: "datatable",
    id: "grid-soil_toil_balances",
    select: "row",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        sort: "int",
        adjust: "header",
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: "Employee Name",
        sort: "string",
        minWidth: 140,
        adjust: true,
      },
      {
        id: "roster",
        header: "Roster",
        minWidth: 100,
        adjust: true,
        sort: "string",
      },
      {
        id: "shift",
        header: "Shift",
        minWidth: 100,
        adjust: true,
        sort: "string",
      },
      {
        id: "location",
        header: "Location",
        minWidth: 100,
        adjust: true,
        sort: "string",
      },
      {
        id: "soil_balance",
        header: "SOIL Hours",
        sort: "int",
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "toil_balance",
        header: "TOIL Hours",
        sort: "int",
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "phol_balance",
        header: "PHOL Hours",
        sort: "int",
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "edit",
        header: "",
        css: { "text-align": "center" },
        template: "<span class = 'webix_icon fas fa-edit'></span>",
        width: 50,
      },
    ],
    data: [],
  },
  { height: 10 },
  {
    view: "toolbar",
    id: "import_toolbar",
    css: "main_header",
    hidden: true,
    cols: [
      {
        view: "uploader",
        id: "import_uploader",
        width: 120,
        css: "webix_secondary",
        value: "Load Excel File",
        accept:
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        multiple: false,
      },
      { view: "button", id: "clear_sheet", label: "Clear", width: 60 },
      { width: 40 },
      {
        view: "label",
        label: "Import SOIL / TOIL records from WFR",
        css: "header_font",
        width: 240,
      },
      {},
      {
        view: "radio",
        id: "soil_toil_radio",
        label: "Type",
        labelWidth: 40,
        width: 200,
        css: "ra_import",
        options: ["SOIL", "TOIL"],
        value: "SOIL",
      },
      { width: 40 },
      {
        view: "button",
        id: "import_sheet",
        label: "Import Records",
        width: 140,
        disabled: true,
      },
    ],
  },
  {
    view: "excelviewer",
    id: "excel_import",
    excelHeader: false,
    header: false,
    scroll: true,
    hidden: true,
  },
  { height: 5 },
];


function createSoilToilWindow() {
  webix
      .ui({
        view: "window",
        id: "soil_toil-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "st_record_label",
                  label: "",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_soil_toil_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 10},
            {
              cols: [
                {width: 10},
                {
                  view: "label",
                  id: "st_employee_record_label",
                  label: "",
                  align: "left",
                  autowidth: true,
                },
                {},
                {
                  view: "radio",
                  id: "soil_toil_radio",
                  label: "Select Type",
                  labelWidth: 80,
                  width: 320,
                  options: ["SOIL", "TOIL", "PHOL"],
                  value: "",
                },
                {width: 10},
              ],
            },
            {height: 20},
            {
              cols: [
                {},
                {
                  view: "label",
                  id: "st_employee_balance",
                  label:
                      "<span style='font-size: 17px; color: black'>Current Balance = <strong>0 hours</strong></span>",
                  align: "center",
                  css: "header_font",
                  autowidth: true,
                },
                {},
                {
                  view: "button",
                  id: "btn_edit_soil_toil_entry",
                  type: "icon",
                  icon: "fas fa-edit",
                  label: "Edit Log Entry",
                  align: "right",
                  width: 135,
                },
                {width: 10},
                {
                  view: "button",
                  id: "btn_delete_soil_toil_entry",
                  type: "icon",
                  icon: "fas fa-trash",
                  label: "Delete Log Entry",
                  align: "right",
                  width: 140,
                },
                {width: 5},
              ],
            },
            {
              view: "datatable",
              id: "grid-soil_toil_logs",
              autowidth: true,
              height: 360,
              headerRowHeight: 45,
              select: "row",
              columns: [
                {id: "id", hidden: true},
                {
                  id: "leave_type",
                  header: {text: "Leave</br>Type", css: "multiline"},
                  width: 70,
                  css: {"text-align": "center"},
                },
                {
                  id: "adjustment_type",
                  header: {text: "Adjustment</br>Type", css: "multiline"},
                  width: 110,
                  css: {"text-align": "center"},
                },
                {
                  id: "accumulated_date",
                  header: {text: "Accum / Redeem</br>Date", css: "multiline"},
                  width: 120,
                  css: {"text-align": "center"},
                },
                {
                  id: "actual_hours",
                  header: {text: "Actual</br>Hours", css: "multiline"},
                  width: 80,
                  css: {"text-align": "center"},
                },
                {
                  id: "accum_hours",
                  header: {text: "Accum</br>Hours", css: "multiline"},
                  width: 80,
                  css: {"text-align": "center"},
                },
                {
                  id: "total_hours",
                  header: {text: "Total</br>Hours", css: "multiline"},
                  width: 80,
                  css: {"text-align": "center"},
                },
                {
                  id: "adjustment_date",
                  header: "Entered On",
                  width: 130,
                  css: {"text-align": "center"},
                },
                {
                  id: "updated_by",
                  header: "Entered By",
                  width: 100,
                  css: {"text-align": "center"},
                },
                {
                  id: "comments",
                  header: "Comments",
                  minWidth: 320,
                  adjust: true,
                  editor: "text",
                },
              ],
              data: [],
            },
            {height: 5},
            {
              cols: [
                {width: 20},
                {
                  view: "label",
                  id: "st_new_record_label",
                  label: "",
                  align: "left",
                },
              ],
            },
            {
              view: "form",
              id: "frm_soil_toil_adjustment",
              borderless: true,
              elements: [
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "soil_toil_accum_date",
                      name: "soil_toil_accum_date",
                      width: 245,
                      label: "Accumulated Date",
                      labelWidth: 120,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                    },
                    {width: 40},
                    {
                      view: "text",
                      id: "soil_toil_qty_actual",
                      name: "soil_toil_qty_actual",
                      type: "number",
                      format: "1.00",
                      label: "Actual Hours",
                      labelWidth: 90,
                      width: 160,
                    },
                    {width: 40},
                    {
                      view: "text",
                      id: "soil_toil_qty_accum",
                      name: "soil_toil_qty_accum",
                      type: "number",
                      format: "1.00",
                      label: "Accumulated Hours",
                      labelWidth: 130,
                      width: 200,
                    },
                    {width: 40},
                    {
                      view: "text",
                      id: "soil_toil_qty_total",
                      name: "soil_toil_qty_total",
                      type: "number",
                      format: "1.00",
                      label: "Total Hours",
                      labelWidth: 80,
                      width: 150,
                      hidden: true,
                    },
                    {
                      view: "text",
                      id: "soil_toil_id",
                      name: "soil_toil_id",
                      label: "ID",
                      labelWidth: 30,
                      width: 120,
                      hidden: true,
                    },
                  ],
                },
                {
                  view: "text",
                  id: "soil_toil_adjustment_comments",
                  name: "soil_toil_adjustment_comments",
                  label: "Comments",
                  labelWidth: 80,
                  width: 680,
                  attributes: {maxlength: 500},
                },
                {height: 10},
                {
                  view: "button",
                  id: "btn_soil_toil_adjustment_save",
                  value: "Save",
                  width: 100,
                  align: "center",
                },
              ],
              rules: {
                soil_toil_qty_actual: webix.rules.isNumber,
                soil_toil_qty_accum: webix.rules.isNumber,
              },
            },
          ],
        },
      })
      .hide();
}
