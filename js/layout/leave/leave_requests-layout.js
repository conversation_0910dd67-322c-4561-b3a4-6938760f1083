let leaveRequestsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Booking Requests</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 30 },
      {
        view: "combo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 55,
        width: 160,
        options: [
          { id: 1, value: "All" },
          { id: 2, value: "Pending" },
          { id: 3, value: "Denied" },
          { id: 4, value: "Approved" },
        ],
      },
      { width: 30 },
      {
        view: "combo",
        id: "type_filter",
        name: "type_filter",
        label: "Type",
        labelWidth: 40,
        width: 130,
        options: [
          { id: 1, value: "All" },
          { id: 2, value: "Create" },
          { id: 3, value: "Delete" },
        ],
      },
      { width: 30 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "booking-requests_grid",
    select: "row",
    on: {
      onAfterSort: function (by, dir, as, state) {
        this.$lastSort = state;
      },
    },
    columns: [
      { id: "booking_id", hidden: true },
      {
        id: "service_no",
        header: "Service No.",
        adjust: "header",
        sort: "int",
      },
      { id: "employee", header: "Employee", width: 240, sort: "string" },
      { id: "rank", header: "Rank", width: 100, sort: "string" },
      { id: "roster", header: "Roster", adjust: true, sort: "string" },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "start_date",
        header: "Start Date",
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "end_date",
        header: "End Date",
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "hours",
        header: { text: "Total<br/>Hours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      { id: "type", header: "Type", width: 70, sort: "string" },
      {
        id: "status",
        header: "Status",
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "deleted",
        header: "Deleted",
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "requested_date",
        header: { text: "Date<br/>Requested", css: "multiline" },
        width: 130,
        css: { "text-align": "center" },
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "approved_date",
        header: { text: "Approved / Denied<br/>Date", css: "multiline" },
        width: 140,
        css: { "text-align": "center" },
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "approved_by",
        header: { text: "Approved / Denied<br/>By", css: "multiline" },
        width: 140,
        css: { "text-align": "center" },
        sort: "int",
      },
    ],
    data: [],
  },
  {
    cols: [
      {
        view: "button",
        id: "btn_prev_page",
        type: "icon",
        icon: "fas fa-step-backward",
        width: 50,
      },
      {
        view: "label",
        id: "page_no",
        label: "Page 1/1",
        width: 120,
        align: "center",
      },
      {
        view: "button",
        id: "btn_next_page",
        type: "icon",
        icon: "fas fa-step-forward",
        width: 50,
      },
      {},
      {
        view: "template",
        id: "search_count",
        template: "",
        borderless: true,
        autoheight: true,
        width: 320,
        css: { "font-style": "italic" },
      },
    ],
  },
  { height: 15 },
  {
    view: "template",
    id: "selected_request",
    template: "Selected Request: None",
    borderless: true,
    css: "selected_employee",
    autoheight: true,
  },
  { view: "text", id: "request_id", label: "", hidden: true },
  {
    cols: [
      { width: 30 },
      {
        view: "text",
        id: "request_comments",
        label: "Comments",
        labelWidth: 90,
        width: 700,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_approve",
        label: "Approve",
        css: "green_button",
        width: 100,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_deny",
        label: "Deny",
        css: "webix_danger",
        width: 100,
      },
      {},
    ],
  },
  { height: 50 },
];
