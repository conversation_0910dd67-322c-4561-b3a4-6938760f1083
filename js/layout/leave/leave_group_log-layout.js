let leaveGroupLogsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Leave Group Logs</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "combo",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
        hidden: true,
      },
      { width: 20 },
      {
        view: "combo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 55,
        width: 145,
        options: [
          { id: 1, value: "Active" },
          { id: 2, value: "Deleted" },
        ],
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 40,
        width: 160,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 25,
        width: 145,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "leave-logs_grid",
    select: "row",
    columns: [
      { id: "booking_id", hidden: true },
      {
        id: "type",
        header: "Type",
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "service_no",
        header: "Service No.",
        width: 90,
        css: { "text-align": "center" },
        sort: "int",
      },
      { id: "employee", header: "Employee", adjust: true, sort: "string" },
      {
        id: "rank",
        header: "Rank",
        width: 65,
        css: { "text-align": "center" },
        sort: "string",
      },
      { id: "roster", header: "Roster", adjust: true, sort: "string" },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "start_date",
        header: "Start Date",
        width: 125,
        css: { "text-align": "center" },
      },
      {
        id: "end_date",
        header: "End Date",
        width: 125,
        css: { "text-align": "center" },
      },
      {
        id: "week_no",
        header: "Week #",
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "length",
        header: "Days",
        width: 65,
        css: { "text-align": "center" },
      },
      {
        id: "created_date",
        header: "Created Date",
        width: 140,
        css: { "text-align": "center" },
      },
      {
        id: "deleted_by",
        header: "Deleted By",
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "deleted_date",
        header: "Deleted Date",
        width: 140,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 5 },
];
