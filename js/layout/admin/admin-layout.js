let adminLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "admin-page",
        isolate: true,
        cols: [
          {
            view: "sidebar",
            id: "admin_sidebar",
            width: 220,
            scroll: "y",
            data: [],
            on: {
              onAfterSelect: function (id) {
                routes.navigate(id, { trigger: true });
              },
            },
          },
          {
            view: "multiview",
            cells: [
              { id: "admin_search", isolate: true, rows: adminSearchLayout },
              { id: "locations", isolate: true, rows: locationsLayout },
              {
                id: "location_groups",
                isolate: true,
                rows: locationsGroupsLayout,
              },
              { id: "rosters", isolate: true, rows: rostersLayout },
              {
                id: "roster_arrangement",
                isolate: true,
                rows: rosterArrangementLayout,
              },
              { id: "shifts", isolate: true, rows: shiftsLayout },
              { id: "shift_groups", isolate: true, rows: shiftsGroupsLayout },
              { id: "leave_types", isolate: true, rows: leaveTypesLayout },
              { id: "shift_types", isolate: true, rows: shiftTypesLayout },
              { id: "skill_codes", isolate: true, rows: skillCodesLayout },
              {
                id: "activity_types",
                isolate: true,
                rows: activityTypesLayout,
              },
              {
                id: "covid_vaccines",
                isolate: true,
                rows: covidVaccinesLayout,
              },
              {
                id: "tools_login_message",
                isolate: true,
                rows: loginMessageLayout,
              },
              {
                id: "tools_public_holidays",
                isolate: true,
                rows: publicHolidaysLayout,
              },
              {
                id: "tools_admin_settings",
                isolate: true,
                rows: adminSettingsLayout,
              },
            ],
            animate: false,
          },
        ],
      };
    },
  };
})();
