let publicHolidaysLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Public Holidays</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-publicHolidays",
    select: "row",
    editable: true,
    columns: [
      { id: "id", hidden: true },
      { id: "date", header: "Date", sort: "string", width: 100 },
      { id: "weekday", header: "Week Day", sort: "string", width: 120 },
      { id: "name", header: "Name", sort: "string", width: 260 },
      { id: "state", header: "State", sort: "string", width: 60 },
    ],
    fixedRowHeight: false,
    rowLineHeight: 35,
    rowHeight: 35,
    data: [],
  },
  { height: 10 },
];


function createPubHWindow() {
  webix
      .ui({
        view: "window",
        id: "publicHolidays-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "publicHolidays-label",
                  label: "<span class='header_font'>Add Public Holiday</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_publicHolidays_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formPublicHolidays",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "public_holiday_id",
              name: "public_holiday_id",
              label: "",
              hidden: true,
            },
            {
              view: "datepicker",
              id: "public_holiday_date",
              name: "public_holiday_date",
              label: "Date",
              width: 220,
              labelWidth: 60,
              value: new Date(),
              format: "%D, %d/%m/%Y",
              stringResult: true,
              timepicker: false,
            },
            {
              view: "text",
              id: "public_holiday_name",
              name: "public_holiday_name",
              label: "Name",
              width: 340,
              labelWidth: 60,
              suggest: [
                {id: 1, value: "New Year's Day"},
                {id: 2, value: "Australia Day"},
                {id: 3, value: "Australia Day (Additional)"},
                {id: 4, value: "Adelaide Cup Day"},
                {id: 5, value: "Good Friday"},
                {id: 6, value: "Easter Saturday"},
                {id: 7, value: "Easter Monday"},
                {id: 8, value: "Anzac Day"},
                {id: 9, value: "Anzac Day (Additional)"},
                {id: 10, value: "Queen's Birthday"},
                {id: 11, value: "Labour Day"},
                {id: 12, value: "Christmas Day"},
                {id: 13, value: "Boxing Day/Proclamation Day"},
              ],
            },
            {
              view: "combo",
              id: "public_holiday_state",
              name: "public_holiday_state",
              label: "State",
              value: "SA",
              width: 140,
              labelWidth: 60,
              options: australian_states,
            },
            {height: 20},
            {
              view: "button",
              id: "btn_public_holiday_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {
            public_holiday_date: webix.rules.isNotEmpty,
            public_holiday_name: webix.rules.isNotEmpty,
            public_holiday_state: webix.rules.isNotEmpty,
          },
        },
      })
      .hide();
}
