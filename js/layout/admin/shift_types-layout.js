let shiftTypesLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Relieving Codes</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-shifttypes",
    select: "row",
    columns: [
      { id: "id", hidden: true },
      { id: "code", header: "Code", sort: "string" },
      {
        id: "description",
        header: "Description",
        sort: "string",
        adjust: true,
      },
    ],
    data: [],
  },
  { height: 10 },
];

function createSTWindow() {
  webix
      .ui({
        view: "window",
        id: "shiftTypes-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "shiftType-label",
                  label: "<span class='header_font'>Add Relieving Code</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_shiftType_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formShiftTypes",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "shiftType_id",
              name: "shiftType_id",
              label: "",
              hidden: true,
            },
            {
              view: "text",
              id: "shiftType_code",
              name: "shiftType_code",
              label: "Code",
              width: 180,
              labelWidth: 90,
            },
            {
              view: "text",
              id: "shiftType_description",
              name: "shiftType_description",
              label: "Description",
              width: 400,
              labelWidth: 90,
            },
            {height: 20},
            {
              view: "button",
              id: "btn_shiftType_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {
            shiftType_code: webix.rules.isNotEmpty,
            shiftType_description: webix.rules.isNotEmpty,
          },
        },
      })
      .hide();
}
