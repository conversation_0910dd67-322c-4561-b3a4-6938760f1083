let adminSettingsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Admin Settings</span>",
        align: "left",
        width: 120,
      },
      {
        view: "label",
        label:
          "<span style='color: white; font-style: italic; font-size: 14px'>(Only Available to Users with Permission Level 1)</span>",
        align: "left",
        width: 340,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      { width: 10 },
      { view: "label", label: "Home Station Preferences BOP", width: 200 },
      {
        view: "toggle",
        id: "setting_hs_pref_bop",
        type: "icon",
        offIcon: "fas fa-toggle-off",
        onIcon: "fas fa-toggle-on",
        offLabel: "Disabled",
        onLabel: "Enabled",
        width: 110,
      },
      {},
    ],
  },
  {},
];
