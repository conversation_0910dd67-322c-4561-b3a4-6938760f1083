let activityTypesLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Overtime Types</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-activitytypes",
    scroll: "y",
    select: "row",
    columns: [
      { id: "id", hidden: true },
      { id: "code", header: "Code", sort: "string", adjust: true },
      { id: "overtime_name", header: "Name", sort: "string", adjust: true },
    ],
    data: [],
  },
  { height: 10 },
];


function createATWindow() {
  webix
      .ui({
        view: "window",
        id: "activityTypes-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "activityType-label",
                  label: "<span class='header_font'>Add Overtime Type</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_activityType_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formActivityTypes",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "activityType_id",
              name: "activityType_id",
              label: "",
              hidden: true,
            },
            {
              view: "text",
              id: "activityType_code",
              name: "activityType_code",
              label: "Code",
              width: 180,
              labelWidth: 70,
            },
            {
              view: "text",
              id: "activityType_name",
              name: "activityType_name",
              label: "Name",
              width: 300,
              labelWidth: 70,
            },
            {height: 20},
            {
              view: "button",
              id: "btn_activityType_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {
            activityType_code: webix.rules.isNotEmpty,
            activityType_name: webix.rules.isNotEmpty,
          },
        },
      })
      .hide();
}

