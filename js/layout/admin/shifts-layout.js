let shiftsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Shifts</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-shifts",
    select: "row",
    columns: [
      { id: "id", hidden: true },
      {
        id: "shift_name",
        header: "Shift Name",
        width: 250,
        sort: "string",
        adjust: "data",
      },
      {
        id: "roster_name",
        header: "Roster",
        width: 200,
        sort: "string",
        adjust: "data",
      },
      { id: "roster_id", hidden: true },
      {
        id: "colour",
        header: "Colour",
        template: gridColour,
        css: { "text-align": "center" },
        adjust: "header",
      },
      {
        id: "seq_start_date",
        header: "Start Date",
        css: { "text-align": "center" },
        adjust: "data",
      },
      {
        id: "sequence_days",
        header: "Sequence Days",
        width: 140,
        css: { "text-align": "center" },
      },
      {
        id: "duration_days",
        header: "Shift Duration",
        width: 140,
        css: { "text-align": "center" },
      },
      { fillspace: true },
    ],
    data: [],
  },
  { height: 10 },
];

function createShiftsWindow() {
  webix
      .ui({
        view: "window",
        id: "shifts-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "shifts-label",
                  label: "<span class='header_font'>Add Shift</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_shift_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formShifts",
          borderless: true,
          margin: 1,
          elements: [
            {
              view: "text",
              id: "shifts_id",
              name: "shifts_id",
              label: "",
              hidden: true,
            },
            {
              view: "combo",
              id: "shift_roster_name",
              name: "shift_roster_name",
              label: "Roster",
              labelWidth: 100,
              options: [],
              labelAlign: "left",
            },
            {
              view: "text",
              id: "shift_name",
              name: "shift_name",
              label: "Shift Name",
              labelWidth: 100,
              attributes: {maxlength: 40},
            },
            {
              view: "colorpicker",
              id: "shift_colour",
              name: "shift_colour",
              label: "Colour",
              value: "#00DDDC",
              labelWidth: 100,
              width: 270,
            },
            {
              view: "select",
              id: "shift_sequence_days",
              name: "shift_sequence_days",
              label: "Sequence Days",
              labelWidth: 150,
              width: 200,
              value: 8,
              options: [
                {id: 1, value: "1"},
                {id: 2, value: "2"},
                {id: 3, value: "3"},
                {id: 4, value: "4"},
                {id: 5, value: "5"},
                {id: 6, value: "6"},
                {id: 7, value: "7"},
                {id: 8, value: "8"},
                {id: 9, value: "9"},
                {id: 10, value: "10"},
                {id: 11, value: "11"},
                {id: 12, value: "12"},
                {id: 13, value: "13"},
                {id: 14, value: "14"},
                {id: 15, value: "15"},
                {id: 16, value: "16"},
              ],
              labelAlign: "left",
            },
            {
              view: "datepicker",
              type: "date",
              id: "shift_sequence_start_date",
              name: "shift_sequence_start_date",
              label: "Sequence Start Date",
              labelWidth: 150,
              width: 320,
              value: "",
              format: "%D, %d/%m/%Y",
              stringResult: true,
              timepicker: false,
            },
            {
              view: "counter",
              id: "shift_duration",
              name: "shift_duration",
              label: "Duration (years)",
              labelWidth: 150,
              value: 1,
              min: 1,
              max: 20,
            },
            {
              view: "datatable",
              id: "grid-shift_days",
              scroll: "y",
              height: 335,
              width: 450,
              select: "row",
              editable: true,
              checkboxRefresh: true,
              columns: [
                {id: "id", hidden: true},
                {
                  id: "day",
                  header: {text: "Day #", css: {"text-align": "center"}},
                  css: {"text-align": "center"},
                  adjust: "header",
                },
                {
                  id: "start_time",
                  header: {text: "Start Time", css: {"text-align": "center"}},
                  editor: "date",
                  css: {"text-align": "center"},
                  format: webix.Date.dateToStr("%H:%i"),
                  width: 110,
                },
                {
                  id: "end_time",
                  header: {text: "End Time", css: {"text-align": "center"}},
                  editor: "date",
                  css: {"text-align": "center"},
                  format: webix.Date.dateToStr("%H:%i"),
                  width: 110,
                },
                {
                  id: "day_off",
                  header: {text: "Day Off", css: {"text-align": "center"}},
                  width: 80,
                  template: "{common.checkbox()}",
                  css: {"text-align": "center"},
                },
                {
                  id: "icon_type",
                  header: {text: "Icon", css: {"text-align": "center"}},
                  width: 80,
                  css: {"text-align": "center"},
                  editor: "richselect",
                  collection: scheduleIcons,
                },
              ],
              data: [],
            },
            {height: 5},
            {
              view: "button",
              id: "btn_shift_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {
            shift_roster_name: webix.rules.isNotEmpty,
            shift_name: webix.rules.isNotEmpty,
            shift_sequence_days: webix.rules.isNumber,
            shift_sequence_start_date: webix.rules.isNotEmpty,
            shift_duration: function (value) {
              return value > 0;
            },
          },
        },
      })
      .hide();
}
