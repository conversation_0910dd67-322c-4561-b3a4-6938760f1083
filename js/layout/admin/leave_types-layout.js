let leaveTypesLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Leave Types</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-leavetypes",
    select: "row",
    columns: [
      { id: "id", hidden: true },
      { id: "code", header: "Code", sort: "string", adjust: "data" },
      {
        id: "description",
        header: "Description",
        sort: "string",
        adjust: true,
      },
      { id: "type", header: "Type", sort: "string", width: 120 },
    ],
    data: [],
  },
  { height: 10 },
];

function createLTWindow() {
  webix
      .ui({
        view: "window",
        id: "leaveTypes-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "leaveType-label",
                  label: "<span class='header_font'>Add Leave Type</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_leaveType_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formLeaveTypes",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "leaveType_id",
              name: "leaveType_id",
              label: "",
              hidden: true,
            },
            {
              view: "text",
              id: "leaveType_code",
              name: "leaveType_code",
              label: "Leave Code",
              width: 180,
              labelWidth: 90,
            },
            {
              view: "text",
              id: "leaveType_description",
              name: "leaveType_description",
              label: "Description",
              width: 400,
              labelWidth: 90,
            },
            {
              view: "richselect",
              id: "leaveType_type",
              name: "leaveType_type",
              label: "Type",
              width: 300,
              labelWidth: 90,
              options: [
                "Booked Leave",
                "Sickness",
                "Rostered Rec Leave",
                "Work",
                "Other",
              ],
            },
            {height: 20},
            {
              view: "button",
              id: "btn_leaveType_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {
            leaveType_code: webix.rules.isNotEmpty,
            leaveType_description: webix.rules.isNotEmpty,
            leaveType_type: webix.rules.isNotEmpty,
          },
        },
      })
      .hide();
}

