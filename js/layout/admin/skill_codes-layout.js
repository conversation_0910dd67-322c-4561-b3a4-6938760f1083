let skillCodesLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Skill Codes</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-skillCodes",
    select: "row",
    editable: true,
    columns: [
      { id: "id", hidden: true },
      { id: "code", header: "Code", sort: "string", width: 120 },
      { id: "description", header: "Description", sort: "string", width: 320 },
      { id: "group", header: "Group", sort: "string", width: 160 },
      {
        id: "linked_codes",
        header: "Linked Codes",
        optionslist: true,
        options: [],
        editor: "multiselect",
        optionWidth: 100,
        fillspace: true,
      },
    ],
    fixedRowHeight: false,
    rowLineHeight: 35,
    rowHeight: 35,
    data: [],
  },
  { height: 10 },
];


function createSkillCWindow() {
  webix
      .ui({
        view: "window",
        id: "skillCodes-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "skillCode-label",
                  label: "<span class='header_font'>Add Skill Code</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_skillCode_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formSkillCodes",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "skillCode_id",
              name: "skillCode_id",
              label: "",
              hidden: true,
            },
            {
              view: "text",
              id: "skillCode_code",
              name: "skillCode_code",
              label: "Code",
              width: 180,
              labelWidth: 90,
            },
            {
              view: "text",
              id: "skillCode_description",
              name: "skillCode_description",
              label: "Description",
              width: 400,
              labelWidth: 90,
            },
            {
              view: "combo",
              id: "skillCode_group",
              name: "skillCode_group",
              label: "Group",
              width: 400,
              labelWidth: 90,
              options: [
                {id: "Operations", value: "Operations"},
                {id: "Communications", value: "Communications"},
              ],
            },
            {height: 20},
            {
              view: "button",
              id: "btn_skillCode_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {
            skillCode_code: webix.rules.isNotEmpty,
            skillCode_description: webix.rules.isNotEmpty,
            skillCode_group: webix.rules.isNotEmpty,
          },
        },
      })
      .hide();
}