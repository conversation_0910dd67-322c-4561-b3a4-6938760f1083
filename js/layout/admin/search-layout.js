let adminSearchLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Search Employee</span>",
        align: "left",
      },
      {
        view: "button",
        id: "btn_terminate",
        value: "Switch Employment Status",
        width: 200,
      },
      { width: 5 },
      {
        view: "button",
        id: "btn_delete",
        value: "Delete",
        width: 100,
        hidden: true,
      },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        rows: [
          {
            cols: [
              {
                view: "combo",
                id: "rosters",
                label: "Select Roster",
                width: 400,
                labelWidth: 160,
                disabled: true,
                options: [],
              },
              { width: 10 },
              {
                view: "checkbox",
                id: "all_rosters",
                label: "All Rosters",
                value: 1,
                width: 200,
              },
            ],
          },
          {
            cols: [
              {
                view: "text",
                id: "search_field",
                name: "search_field",
                label: "Employee / Service No",
                value: "",
                width: 400,
                labelWidth: 160,
              },
              {
                view: "button",
                id: "btn_search",
                type: "icon",
                icon: "fas fa-search",
                label: "Search",
                width: 100,
              },
            ],
          },
        ],
      },
      {},
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "search_results",
    select: "row",
    editable: true,
    columns: [
      { id: "id", hidden: true },
      {
        id: "pay_id",
        header: "Pay ID",
        width: 70,
        sort: "int",
        css: { "text-align": "center" },
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      {
        id: "rank",
        header: "Current Rank",
        width: 110,
        sort: "string",
        css: { "text-align": "center" },
      },
      { id: "roster", header: "Current Roster", adjust: true },
      { id: "shift", header: "Current Shift", adjust: true, sort: "string" },
      {
        id: "location",
        header: "Current Location",
        adjust: true,
        sort: "string",
      },
      {
        id: "skill_codes",
        header: "Skill Codes",
        optionslist: true,
        options: [],
        editor: "multiselect",
        optionWidth: 40,
      },
      {
        id: "emp_status",
        header: "Employment Status",
        width: 160,
        sort: "string",
        css: { "text-align": "center" },
      },
    ],
    fixedRowHeight: false,
    rowLineHeight: 35,
    rowHeight: 35,
    data: [],
  },
  {
    view: "template",
    id: "search_count",
    template: "",
    borderless: true,
    height: 15,
    css: { "font-style": "italic" },
  },
  { height: 5 },
  {
    view: "toolbar",
    id: "import_toolbar",
    css: "main_header",
    hidden: true,
    cols: [
      {
        view: "uploader",
        id: "import_uploader",
        width: 120,
        css: "webix_secondary",
        value: "Load CSV File",
        accept: ".csv",
      },
      { view: "button", id: "clear_sheet", label: "Clear", width: 60 },
      { width: 20 },
      {
        view: "label",
        id: "file_name",
        label: "Import Employees from TASEMP",
        css: "header_font",
        width: 280,
      },
      {},
      {
        view: "button",
        id: "import_sheet",
        label: "Import Employees",
        width: 160,
        disabled: true,
      },
    ],
  },
  {
    view: "datatable",
    id: "excel_import",
    height: 400,
    resizeColumn: true,
    scroll: "xy",
    hidden: true,
    columns: [
      { id: "id", header: "ID", hidden: true },
      { id: "DET NUMBER", header: "Pay ID", adjust: true, sort: "int" },
      { id: "DET G1 NAME1", header: "First Name", adjust: true },
      { id: "DET G1 NAME2", header: "Middle Name", adjust: true },
      { id: "DET SURNAME", header: "Surname", adjust: true, sort: "string" },
      { id: "DET BIR DATE", header: "Birth Date", adjust: true },
      { id: "DET SEX", header: "Gender", adjust: true },
      { id: "ADR TYPE", header: "Address Type", hidden: true },
      { id: "ADR LINE 1", header: "Address Line 1", adjust: true },
      { id: "ADR LINE 3", header: "Suburb", adjust: true },
      { id: "ADR PST CODE", header: "Postcode", adjust: true },
      { id: "ADR PHONE", header: "Phone Number", adjust: true },
      { id: "ADR MOBILE", header: "Mobile", adjust: true },
      { id: "ADR EMAIL", header: "Personal Email", adjust: true },
      { id: "SMN CLASS", header: "Current Class", adjust: true },
      { id: "DET EMAIL AD", header: "Work Email", adjust: true },
      { id: "ZPS CRT STAT", header: "Current Station", adjust: true },
    ],
    data: [],
  },
  {
    view: "template",
    id: "csv_search_count",
    template: "",
    borderless: true,
    height: 20,
    css: { "font-style": "italic" },
    hidden: true,
  },
  { height: 10 },
];

function createEmpWindow() {
  webix
      .ui({
        view: "window",
        id: "employee-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "employee-label",
                  label: "<span class='header_font'>Add Employee</span>",
                  align: "left",
                  width: 720,
                },
                {},
                {
                  view: "button",
                  id: "btn_employee_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 10},
            {
              view: "fieldset",
              label: "Employee Profile Details",
              css: "new_fieldset_label",
              body: {
                view: "form",
                borderless: true,
                id: "formEmployee",
                elements: [
                  {
                    cols: [
                      {width: 10},
                      {
                        rows: [
                          {
                            cols: [
                              {
                                view: "text",
                                id: "employee_pay_id",
                                name: "employee_pay_id",
                                label: "Pay/ESO ID",
                                labelWidth: 120,
                                type: "number",
                                width: 200,
                                labelAlign: "right",
                              },
                              {width: 20},
                              {
                                view: "button",
                                id: "btn_emerald_sync",
                                type: "icon",
                                label: "Emerald Sync",
                                icon: "fas fa-gem",
                                width: 130,
                                css: "emerald_button",
                              },
                            ],
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_first_name",
                            name: "employee_first_name",
                            label: "First Name",
                            width: 350,
                            labelWidth: 120,
                            labelAlign: "right",
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_middle_name",
                            name: "employee_middle_name",
                            label: "Middle Name",
                            width: 350,
                            labelWidth: 120,
                            labelAlign: "right",
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_surname",
                            name: "employee_surname",
                            label: "Surname",
                            width: 350,
                            labelWidth: 120,
                            labelAlign: "right",
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "richselect",
                                id: "employee_rank",
                                name: "employee_rank",
                                label: "Current Rank",
                                labelWidth: 120,
                                width: 220,
                                options: all_rank_types,
                                labelAlign: "right",
                              },
                              {width: 10},
                              {
                                view: "richselect",
                                id: "employee_rank_class",
                                name: "employee_rank_class",
                                label: "Class",
                                labelWidth: 45,
                                width: 145,
                                options: all_rank_classes,
                                labelAlign: "right",
                                hidden: true,
                              },
                              {
                                view: "richselect",
                                id: "employee_gender",
                                name: "employee_gender",
                                label: "Gender",
                                labelWidth: 60,
                                width: 120,
                                options: [
                                  {id: "F", value: "F"},
                                  {id: "M", value: "M"},
                                ],
                                labelAlign: "right",
                              },
                            ],
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_station_name",
                            name: "employee_station_name",
                            label: "Home Station",
                            labelWidth: 120,
                            width: 350,
                            labelAlign: "right",
                            readonly: true,
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_rrl_group",
                            name: "employee_rrl_group",
                            label: "Current Leave Group (RRL)",
                            labelWidth: 170,
                            width: 240,
                            labelAlign: "right",
                            readonly: true,
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "text",
                                id: "employee_new_rrl_group",
                                name: "employee_new_rrl_group",
                                label: "New Leave Group (RRL)",
                                labelWidth: 170,
                                width: 240,
                                labelAlign: "right",
                                readonly: true,
                              },
                              {
                                view: "label",
                                label: " from ",
                                width: 40,
                                align: "center",
                              },
                              {
                                view: "text",
                                id: "employee_new_rrl_group_date",
                                name: "employee_new_rrl_group_date",
                                label: "",
                                labelWidth: 0,
                                width: 100,
                                readonly: true,
                              },
                            ],
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "text",
                                id: "employee_station_id",
                                name: "employee_station_id",
                                label: "Home Station ID",
                                labelWidth: 120,
                                width: 175,
                                labelAlign: "right",
                                readonly: true,
                              },
                              {width: 16},
                              {
                                view: "datepicker",
                                type: "date",
                                id: "employee_birth_date",
                                name: "employee_birth_date",
                                label: "DOB",
                                labelWidth: 40,
                                width: 160,
                                value: new Date(),
                                format: "%d/%m/%Y",
                                stringResult: true,
                                timepicker: false,
                                labelAlign: "right",
                                readonly: true,
                              },
                            ],
                          },
                          {height: 5},
                          {
                            view: "datepicker",
                            type: "date",
                            id: "samfs_commencement_date",
                            name: "samfs_commencement_date",
                            label: "SAMFS Commencement Date",
                            labelWidth: 205,
                            width: 325,
                            value: new Date(),
                            format: "%d/%m/%Y",
                            stringResult: true,
                            timepicker: false,
                            labelAlign: "right",
                            readonly: true,
                          },
                          {
                            view: "datepicker",
                            type: "date",
                            id: "govt_commencement_date",
                            name: "govt_commencement_date",
                            label: "GOVT Commencement Date",
                            labelWidth: 205,
                            width: 325,
                            value: new Date(),
                            format: "%d/%m/%Y",
                            stringResult: true,
                            timepicker: false,
                            labelAlign: "right",
                            readonly: true,
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_res_to_hs",
                            name: "employee_res_to_hs",
                            label: "Distance - Residence to Home Station (Kms)",
                            labelWidth: 280,
                            width: 355,
                            inputAlign: "center",
                            readonly: true,
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "counter",
                                id: "employee_permission_level",
                                name: "employee_permission_level",
                                label: "Permission Level",
                                labelWidth: 115,
                                width: 220,
                                value: 1,
                                min: 1,
                                max: 6,
                              },
                              {
                                view: "text",
                                id: "employee_position_number",
                                name: "employee_position_number",
                                label: "POS #",
                                labelWidth: 65,
                                width: 145,
                                labelAlign: "right",
                                readonly: true,
                              },
                            ],
                          },
                          {height: 10},
                          {
                            view: "fieldset",
                            label: "Recruit Overtime Hours Balance",
                            css: "new_fieldset_label",
                            body: {
                              rows: [
                                {
                                  cols: [
                                    {
                                      view: "text",
                                      id: "overtime_hrs_balance",
                                      type: "number",
                                      label: "Hours",
                                      width: 125,
                                      labelWidth: 50,
                                      disabled: true,
                                    },
                                    {width: 15},
                                    {
                                      view: "datepicker",
                                      id: "overtime_hrs_date",
                                      width: 160,
                                      label: "Date",
                                      labelWidth: 40,
                                      labelAlign: "left",
                                      value: new Date(),
                                      format: "%d/%m/%Y",
                                      stringResult: true,
                                      timepicker: false,
                                      disabled: true,
                                    },
                                    {width: 15},
                                    {
                                      view: "button",
                                      id: "overtime_hrs_save",
                                      type: "icon",
                                      label: "",
                                      icon: "fas fa-save",
                                      width: 40,
                                      disabled: true,
                                    },
                                  ],
                                },
                              ],
                            },
                          },
                        ],
                      },
                      {width: 60},
                      {
                        rows: [
                          {
                            view: "text",
                            id: "employee_home_street",
                            name: "employee_home_street",
                            label: "Street Name",
                            labelWidth: 120,
                            width: 340,
                            labelAlign: "right",
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_home_suburb",
                            name: "employee_home_suburb",
                            label: "Suburb",
                            labelWidth: 120,
                            width: 340,
                            labelAlign: "right",
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "richselect",
                                id: "employee_home_state",
                                name: "employee_home_state",
                                label: "State",
                                labelWidth: 120,
                                width: 200,
                                options: australian_states,
                                labelAlign: "right",
                              },
                              {
                                view: "text",
                                id: "employee_home_postcode",
                                name: "employee_home_postcode",
                                label: "Post Code",
                                labelWidth: 80,
                                type: "number",
                                width: 140,
                                labelAlign: "right",
                              },
                            ],
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "text",
                                id: "employee_personal_mobile",
                                name: "employee_personal_mobile",
                                label: "Mobile (Personal)",
                                labelWidth: 120,
                                attributes: {maxlength: 10},
                                width: 260,
                                labelAlign: "right",
                              },
                              {width: 5},
                              {
                                view: "checkbox",
                                id: "hide_personal_mobile",
                                labelRight: "Hide on Roster",
                                width: 200,
                                labelWidth: 0,
                              },
                            ],
                          },
                          {height: 5},
                          {
                            view: "text",
                            id: "employee_work_mobile",
                            name: "employee_work_mobile",
                            label: "Mobile (Work)",
                            labelWidth: 120,
                            width: 260,
                            attributes: {maxlength: 10},
                            labelAlign: "right",
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "text",
                                id: "employee_personal_email",
                                name: "employee_personal_email",
                                label: "Email (Personal)",
                                labelWidth: 120,
                                width: 400,
                                labelAlign: "right",
                              },
                              {width: 10},
                              {
                                view: "checkbox",
                                id: "default_email_personal",
                                label: "",
                                width: 30,
                                align: "right",
                              },
                            ],
                          },
                          {height: 5},
                          {
                            cols: [
                              {
                                view: "text",
                                id: "employee_work_email",
                                name: "employee_work_email",
                                label: "Email (Work/ESO)",
                                labelWidth: 120,
                                width: 400,
                                labelAlign: "right",
                              },
                              {width: 10},
                              {
                                view: "checkbox",
                                id: "default_email_work",
                                label: "",
                                width: 30,
                                align: "right",
                              },
                            ],
                          },
                          {height: 5},
                          {height: 10},
                          {
                            view: "fieldset",
                            label: "Respond 52 Use Only",
                            css: "new_fieldset_label",
                            body: {
                              rows: [
                                {
                                  cols: [
                                    {
                                      view: "text",
                                      id: "r52_exists",
                                      label: "User Found In R52",
                                      value: "No",
                                      width: 115,
                                      align: "center",
                                      inputAlign: "center",
                                      labelPosition: "top",
                                      readonly: true,
                                    },
                                    {width: 20},
                                    {
                                      rows: [
                                        {
                                          cols: [
                                            {width: 5},
                                            {
                                              view: "button",
                                              id: "btn_link_nfc",
                                              value: "Link NFC Tag",
                                              css: "webix_primary",
                                              width: 125,
                                              disabled: true,
                                            },
                                            {width: 30},
                                            {
                                              view: "button",
                                              id: "btn_clear_nfc",
                                              value: "Clear NFC Tag",
                                              css: "webix_danger",
                                              width: 125,
                                              disabled: true,
                                            },
                                            {width: 5},
                                          ],
                                        },
                                        {
                                          view: "template",
                                          template:
                                              "Note: If a user is not found in Respond 52, a profile will be automatically created for them!",
                                          borderless: true,
                                          autoheight: true,
                                        },
                                      ],
                                    },
                                  ],
                                },
                                {
                                  view: "combo",
                                  id: "employee_new_station",
                                  name: "employee_new_station",
                                  label: "New Station in R52",
                                  labelWidth: 125,
                                  width: 375,
                                  options: [],
                                  labelAlign: "right",
                                  disabled: true,
                                },
                                {
                                  cols: [
                                    {
                                      view: "combo",
                                      id: "employee_new_shift",
                                      name: "employee_new_shift",
                                      label: "New Shift in R52",
                                      labelWidth: 125,
                                      width: 375,
                                      options: [],
                                      labelAlign: "right",
                                      disabled: true,
                                    },
                                    {width: 5},
                                    {
                                      view: "button",
                                      id: "btn_r52_change",
                                      value: "Change",
                                      width: 70,
                                      css: "green_button",
                                      disabled: true,
                                    },
                                  ],
                                },
                              ],
                            },
                          },
                          {height: 10},
                          {
                            cols: [
                              {
                                view: "text",
                                id: "employee_app_pin",
                                name: "employee_app_pin",
                                label: "Sapphire Mobile App Login PIN code",
                                type: "password",
                                attributes: {maxlength: 4},
                                pattern: {mask: "####", allow: /[0-9]/g},
                                width: 300,
                                labelWidth: 230,
                                inputAlign: "center",
                                css: "pin_code",
                              },
                              {
                                view: "button",
                                id: "show_hide_pin",
                                type: "icon",
                                icon: "wxi-eye",
                                width: 40,
                              },
                            ],
                          },
                          {height: 10},
                          {
                            view: "button",
                            id: "btn_employee_station_distances",
                            type: "icon",
                            label: "View Travel Distances To Other Stations",
                            icon: "fas fa-list",
                            css: "webix_primary",
                            width: 300,
                            align: "right",
                          },
                          {height: 5},
                        ],
                      },
                      {width: 10},
                    ],
                  },
                  {
                    view: "button",
                    id: "btn_employee_save",
                    value: "Save",
                    width: 120,
                    css: "webix_primary",
                    align: "center",
                  },
                  {
                    cols: [
                      {},
                      {
                        view: "template",
                        id: "employee_details_notice",
                        template:
                            "<span style='font-size: 13px; color: red; font-style: italic; font-weight: 500'>Some or all of the contact details have been imported from Emerald and can no longer be modified here.</br>If any of these details are incorrect you will need to change them in HR21 --\x3e <a href='https://selfservice.hrms.sa.gov.au/PROD1' target='_blank'>https://selfservice.hrms.sa.gov.au/PROD1</a></br>Note: Any HR21 changes will be updated here within 24 hours!</span>",
                        width: 900,
                        autoheight: true,
                        borderless: true,
                        css: {"text-align": "center"},
                        hidden: true,
                      },
                      {},
                    ],
                  },
                ],
                rules: {
                  employee_pay_id: webix.rules.isNotEmpty,
                  employee_first_name: webix.rules.isNotEmpty,
                  employee_surname: webix.rules.isNotEmpty,
                  employee_work_email: webix.rules.isEmail,
                  employee_personal_email: webix.rules.isEmail,
                  employee_home_unit_no: webix.rules.isNotEmpty,
                  employee_home_street: webix.rules.isNotEmpty,
                  employee_home_suburb: webix.rules.isNotEmpty,
                  employee_home_postcode: webix.rules.isNumber,
                  employee_personal_mobile: webix.rules.isNotEmpty,
                  employee_station_id: webix.rules.isNumber,
                },
              },
            },
            {height: 5},
            {
              view: "fieldset",
              label: "Home Station Preferences",
              css: "new_fieldset_label",
              body: {
                rows: [
                  {
                    cols: [
                      {width: 130},
                      {
                        view: "label",
                        label:
                            "Roster                                                          Shift                                                         Location                                   Rank",
                      },
                    ],
                  },
                  {
                    cols: [
                      {view: "label", label: "1st", width: 50, align: "right"},
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_roster_pref_1",
                        name: "employee_roster_pref_1",
                        width: 210,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_shift_pref_1",
                        name: "employee_shift_pref_1",
                        width: 240,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_location_pref_1",
                        name: "employee_location_pref_1",
                        width: 230,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_rank_pref_1",
                        name: "employee_rank_pref_1",
                        width: 85,
                        options: all_rank_types,
                      },
                      {width: 5},
                      {
                        view: "button",
                        id: "pref_1_save",
                        type: "icon",
                        label: "Save",
                        icon: "fas fa-save",
                        width: 80,
                      },
                    ],
                  },
                  {
                    cols: [
                      {view: "label", label: "2nd", width: 50, align: "right"},
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_roster_pref_2",
                        name: "employee_roster_pref_2",
                        width: 210,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_shift_pref_2",
                        name: "employee_shift_pref_2",
                        width: 240,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_location_pref_2",
                        name: "employee_location_pref_2",
                        width: 230,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_rank_pref_2",
                        name: "employee_rank_pref_2",
                        width: 85,
                        options: all_rank_types,
                      },
                      {width: 5},
                      {
                        view: "button",
                        id: "pref_2_save",
                        type: "icon",
                        label: "Save",
                        icon: "fas fa-save",
                        width: 80,
                      },
                    ],
                  },
                  {
                    cols: [
                      {view: "label", label: "3rd", width: 50, align: "right"},
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_roster_pref_3",
                        name: "employee_roster_pref_3",
                        width: 210,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_shift_pref_3",
                        name: "employee_shift_pref_3",
                        width: 240,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_location_pref_3",
                        name: "employee_location_pref_3",
                        width: 230,
                        options: [],
                      },
                      {width: 5},
                      {
                        view: "combo",
                        id: "employee_rank_pref_3",
                        name: "employee_rank_pref_3",
                        width: 85,
                        options: all_rank_types,
                      },
                      {width: 5},
                      {
                        view: "button",
                        id: "pref_3_save",
                        type: "icon",
                        label: "Save",
                        icon: "fas fa-save",
                        width: 80,
                      },
                    ],
                  },
                  {
                    cols: [
                      {width: 200},
                      {
                        view: "label",
                        id: "employee_pref_notice",
                        label:
                            "<span style='font-size: 14px; color: red; font-style: italic'>Note: Changes to the Home Station Preferences is currently not available!</span>",
                        width: 500,
                        align: "center",
                      },
                      {},
                      {
                        view: "button",
                        id: "employee_pref_logs",
                        type: "icon",
                        label: "View Change Logs",
                        icon: "fas fa-list",
                        css: "webix_primary",
                        width: 160,
                        align: "right",
                      },
                      {width: 15},
                    ],
                  },
                ],
              },
            },
            {height: 15},
          ],
        },
      })
      .hide();
}

function createNFCWindow() {
  webix
      .ui({
        view: "window",
        id: "nfc_tag-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "nfc_tag-label",
                  label: "<span class='header_font'>Assign NFC Tag</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_nfc_tag_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formNFCtag",
          elements: [
            {
              view: "template",
              template:
                  "<strong>Instructions:</br>1. Plug the NFC tag reader into a USB port and wait until Windows has identified it.</br>2. Once installed scan the tag and the ID should be automatically displayed in the 'NFC Tag Code' field below.</br>3. Now click on 'Save' to assign it to the selected employee!</strong>",
              autoheight: true,
              borderless: true,
              width: 680,
            },
            {
              view: "text",
              id: "nfc_tag_string",
              name: "nfc_tag_string",
              label: "NFC Tag Code",
              labelWidth: 120,
              width: 540,
              labelAlign: "right",
            },
            {
              view: "button",
              id: "btn_assign_nfc_tag",
              name: "btn_assign_nfc_tag",
              label: "Save",
              align: "right",
              width: 100,
            },
          ],
          rules: {nfc_tag_string: webix.rules.isNotEmpty},
        },
      })
      .hide();
}

function createLBWindow() {
  webix
      .ui({
        view: "window",
        id: "leave_balances-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "leave_balances-label",
                  label: "<span class='header_font'>Leave Balances</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_leave_balances_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 15},
            {
              view: "datatable",
              id: "leave_balances_grid",
              autowidth: true,
              columns: [
                {
                  id: "type",
                  header: "Type",
                  width: 80,
                  sort: "string",
                  css: {"text-align": "center"},
                },
                {
                  id: "description",
                  header: "Description",
                  width: 160,
                  sort: "string",
                  css: {"text-align": "center"},
                },
                {
                  id: "total_hours",
                  header: "Total Hours",
                  width: 110,
                  css: {"text-align": "center"},
                },
                {
                  id: "day_shifts",
                  header: "Day Shifts",
                  width: 110,
                  css: {"text-align": "center"},
                },
              ],
              data: [],
            },
          ],
        },
      })
      .hide();
}

function createTEWindow() {
  webix
      .ui({
        view: "window",
        id: "terminate_employee-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "terminate_employee-label",
                  label: "<span class='header_font'>Terminate Employment</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_terminate_employee_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 15},
            {
              view: "template",
              template:
                  "Select a 'Termination Date' below to set as the end date of the employee's current Roster Arrangement.</br></br>Note: All Roster Arrangement data and any linked bookings found in Sapphire after the selected 'Termination Date' will be deleted however historical data in reports and on roster will still be available!",
              borderless: true,
              autoheight: true,
              width: 700,
              align: "center",
              css: {
                "font-size": "14px !important",
                color: "#000000",
                "text-align": "center",
                "font-weight": "500",
              },
            },
            {height: 15},
            {
              view: "datepicker",
              id: "terminate_employee_date",
              width: 240,
              label: "Termination Date",
              labelWidth: 120,
              labelAlign: "left",
              value: new Date(),
              format: "%d/%m/%Y",
              stringResult: true,
              timepicker: false,
              align: "center",
            },
            {height: 15},
            {
              view: "template",
              template:
                  "Click on the 'Proceed' button below to continue.</br>Note: The employee will no longer have access to Sapphire once made Inactive!",
              borderless: true,
              autoheight: true,
              align: "center",
              css: {
                "font-size": "14px !important",
                color: "#000000",
                "text-align": "center",
                "font-weight": "500",
              },
            },
            {height: 30},
            {
              view: "button",
              id: "btn_terminate_employee",
              value: "Proceed",
              width: 100,
              align: "center",
            },
            {height: 10},
          ],
        },
      })
      .hide();
}

function createHSWindow() {
  webix
      .ui({
        view: "window",
        id: "hs_preferences-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "hs_preferences-label",
                  label:
                      "<span class='header_font'>Home Station Preferences Change Log</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_hs_preferences_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 10},
            {
              view: "datatable",
              id: "hs_preferences_grid",
              autowidth: true,
              height: 600,
              columns: [
                {
                  id: "pref_no",
                  header: "Pref.",
                  width: 70,
                  sort: "string",
                  css: {"text-align": "center"},
                },
                {id: "roster", header: "Roster", width: 200, sort: "string"},
                {id: "shift", header: "Shift", width: 200, sort: "string"},
                {
                  id: "location",
                  header: "Location",
                  width: 200,
                  sort: "string",
                },
                {
                  id: "rank",
                  header: "Rank",
                  width: 80,
                  sort: "string",
                  css: {"text-align": "center"},
                },
                {
                  id: "updated_date",
                  header: "Updated On",
                  width: 125,
                  css: {"text-align": "center"},
                },
                {
                  id: "updated_by",
                  header: "Updated By",
                  width: 100,
                  sort: "int",
                  css: {"text-align": "center"},
                },
              ],
              data: [],
            },
            {height: 10},
          ],
        },
      })
      .hide();
}

function createTDWindow() {
  webix
      .ui({
        view: "window",
        id: "travel_distances-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "travel_distances-label",
                  label:
                      "<span class='header_font'>Travel Distances From Home To Other Stations - (in kilometres)</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_travel_distances_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              view: "button",
              id: "btn_calculate_travel_distances",
              type: "icon",
              icon: "fas fa-sync",
              label: "Get Station Travel Distances",
              width: 220,
              align: "right",
            },
            {
              view: "datatable",
              id: "travel_distances_grid",
              autowidth: true,
              height: 600,
              columns: [
                {
                  id: "station_id",
                  header: "Station #",
                  width: 85,
                  css: {"text-align": "center"},
                },
                {
                  id: "station_name",
                  header: "Station Name",
                  minWidth: 140,
                  adjust: true,
                },
                {
                  id: "distance",
                  header: "Distance",
                  width: 80,
                  css: {"text-align": "center"},
                },
                {
                  id: "updated_date",
                  header: "Last Updated Date",
                  width: 140,
                  css: {"text-align": "center"},
                },
              ],
              data: [],
            },
            {height: 10},
          ],
        },
      })
      .hide();
}
