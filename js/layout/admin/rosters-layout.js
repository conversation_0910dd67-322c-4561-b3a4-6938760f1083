let rostersLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Rosters</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-rosters",
    select: "row",
    editable: true,
    columns: [
      { id: "id", hidden: true },
      { id: "roster_name", adjust: true, header: "Name", sort: "string" },
      {
        id: "shifts",
        header: "Shifts",
        optionslist: true,
        fillspace: 2,
        options: [],
        editor: "multiselect",
      },
      {
        id: "locations",
        header: "Locations",
        optionslist: true,
        fillspace: 1,
        options: [],
        editor: "multiselect",
      },
    ],
    fixedRowHeight: false,
    rowLineHeight: 35,
    rowHeight: 35,
    data: [],
  },
  { height: 10 },
];

function createRosWindow() {
  webix
      .ui({
        view: "window",
        id: "rosters-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "rosters-label",
                  label: "<span class='header_font'>Add Roster</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_roster_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formRosters",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "rosters_id",
              name: "id",
              label: "",
              hidden: true,
            },
            {
              view: "text",
              id: "roster_name",
              name: "roster_name",
              label: "Rosters",
              width: 400,
              labelWidth: 80,
              attributes: {maxlength: 40},
            },
            {height: 20},
            {
              view: "button",
              id: "btn_roster_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {roster_name: webix.rules.isNotEmpty},
        },
      })
      .hide();
}

