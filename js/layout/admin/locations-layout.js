let locationsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Locations</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid-locations",
    select: "row",
    columns: [
      { id: "id", hidden: true },
      {
        id: "name",
        header: [
          {
            text: " Station Details",
            colspan: 5,
            css: { "font-size": "15px" },
          },
          { text: "Name" },
        ],
        sort: "string",
        adjust: "data",
      },
      {
        id: "station_id",
        header: [null, "Station #"],
        sort: "string",
        adjust: "header",
        css: { "text-align": "center" },
      },
      { id: "address", header: [null, "Address"], sort: "string" },
      {
        id: "latitude",
        header: [null, "Latitude"],
        css: { "text-align": "center" },
        adjust: true,
      },
      {
        id: "longitude",
        header: [null, "Longitude"],
        css: { "text-align": "center" },
        adjust: true,
      },
      {
        id: "min_SO",
        header: [
          {
            text: " Rank Requirements",
            colspan: 9,
            css: { "font-size": "15px" },
          },
          { text: "Min<br/>SO", css: "multiline" },
        ],
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_SF",
        header: [null, { text: "Min<br/>SFF", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_FF",
        header: [null, { text: "Min<br/>FF", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_MOP",
        header: [null, { text: "Min<br/>MOP", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_MOF",
        header: [null, { text: "Min<br/>MOF", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_COFF",
        header: [null, { text: "Min<br/>COFF", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_SCOP",
        header: [null, { text: "Min<br/>SCOP", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_COP",
        header: [null, { text: "Min<br/>COP", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_CMD",
        header: [null, { text: "Min<br/>CMD", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_R9O",
        header: [
          {
            text: " Skill Requirements",
            colspan: 20,
            css: { "font-size": "15px" },
          },
          { text: "Min<br/>R9O", css: "multiline" },
        ],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_R9",
        header: [null, { text: "Min<br/>R9", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_A5O",
        header: [null, { text: "Min<br/>A5O", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_A5",
        header: [null, { text: "Min<br/>A5", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_H6O",
        header: [null, { text: "Min<br/>H6O", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_H6",
        header: [null, { text: "Min<br/>H6", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_C3O",
        header: [null, { text: "Min<br/>C3O", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_C3",
        header: [null, { text: "Min<br/>C3", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_R4O",
        header: [null, { text: "Min<br/>R4O", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_R4",
        header: [null, { text: "Min<br/>R4", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_GPO",
        header: [null, { text: "Min<br/>GPO", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_GP",
        header: [null, { text: "Min<br/>GP", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_MCO",
        header: [null, { text: "Min<br/>MCO", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_DRO",
        header: [null, { text: "Min<br/>DRO", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_ICV",
        header: [null, { text: "Min<br/>ICV", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_HL",
        header: [null, { text: "Min<br/>HL", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_CCO",
        header: [null, { text: "Min<br/>CCO", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_CCP",
        header: [null, { text: "Min<br/>CCP", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_MVE",
        header: [null, { text: "Min<br/>MVE", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
      {
        id: "min_MVO",
        header: [null, { text: "Min<br/>MVO", css: "multiline" }],
        sort: "int",
        width: 50,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];

function createLocWindow() {
  webix
      .ui({
        view: "window",
        id: "locations-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "locations-label",
                  label: "<span class='header_font'>Add Location</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_loc_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formLocation",
          width: 900,
          borderless: true,
          elements: [
            {
              cols: [
                {width: 15},
                {
                  rows: [
                    {height: 10},
                    {
                      view: "text",
                      id: "location_id",
                      name: "location_id",
                      label: "id",
                      hidden: true,
                    },
                    {
                      view: "text",
                      id: "location_name",
                      name: "location_name",
                      label: "Location",
                      width: 340,
                      labelWidth: 80,
                      attributes: {maxlength: 40},
                    },
                    {
                      view: "text",
                      id: "location_station_id",
                      name: "location_station_id",
                      label: "Station #",
                      type: "number",
                      width: 140,
                      labelWidth: 80,
                    },
                    {
                      view: "text",
                      id: "location_address",
                      name: "location_address",
                      label: "Address",
                      width: 400,
                      labelWidth: 80,
                    },
                    {
                      cols: [
                        {
                          view: "text",
                          id: "location_latitude",
                          name: "location_latitude",
                          label: "Latitude",
                          type: "number",
                          width: 200,
                          labelWidth: 80,
                        },
                        {},
                        {
                          view: "text",
                          id: "location_longitude",
                          name: "location_longitude",
                          label: "Longitude",
                          type: "number",
                          width: 200,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      view: "combo",
                      id: "location_roster",
                      name: "location_roster",
                      label: "Roster",
                      labelWidth: 80,
                      options: [],
                      labelAlign: "left",
                    },
                    {height: 20},
                    {template: "Rank Requirements", type: "section"},
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_SO",
                          name: "min_SO",
                          label: "Min SO",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_SF",
                          name: "min_SF",
                          label: "Min SFF",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_FF",
                          name: "min_FF",
                          label: "Min FF",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_MOP",
                          name: "min_MOP",
                          label: "Min MOP",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_MOF",
                          name: "min_MOF",
                          label: "Min MOF",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_COFF",
                          name: "min_COFF",
                          label: "Min COFF",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_SCOP",
                          name: "min_SCOP",
                          label: "Min SCOP",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_COP",
                          name: "min_COP",
                          label: "Min COP",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_CMD",
                          name: "min_CMD",
                          label: "Min CMD",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                  ],
                },
                {width: 50},
                {
                  rows: [
                    {template: "Skill Requirements", type: "section"},
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_R9O",
                          name: "min_R9O",
                          label: "Min R9O",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_R9",
                          name: "min_R9",
                          label: "Min R9",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_A5O",
                          name: "min_A5O",
                          label: "Min A5O",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_A5",
                          name: "min_A5",
                          label: "Min A5",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_H6O",
                          name: "min_H6O",
                          label: "Min H6O",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_H6",
                          name: "min_H6",
                          label: "Min H6",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_C3O",
                          name: "min_C3O",
                          label: "Min C3O",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_C3",
                          name: "min_C3",
                          label: "Min C3",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_R4O",
                          name: "min_R4O",
                          label: "Min R4O",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_R4",
                          name: "min_R4",
                          label: "Min R4",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_GPO",
                          name: "min_GPO",
                          label: "Min GPO",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_GP",
                          name: "min_GP",
                          label: "Min GP",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_MCO",
                          name: "min_MCO",
                          label: "Min MCO",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_DRO",
                          name: "min_DRO",
                          label: "Min DRO",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_ICV",
                          name: "min_ICV",
                          label: "Min ICV",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_HL",
                          name: "min_HL",
                          label: "Min HL",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_CCO",
                          name: "min_CCO",
                          label: "Min CCO",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_CCP",
                          name: "min_CCP",
                          label: "Min CCP",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                    {
                      cols: [
                        {
                          view: "counter",
                          id: "min_MVE",
                          name: "min_MVE",
                          label: "Min MVE",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                        {
                          view: "counter",
                          id: "min_MVO",
                          name: "min_MVO",
                          label: "Min MVO",
                          value: 0,
                          min: 0,
                          max: 999,
                          labelWidth: 80,
                        },
                      ],
                    },
                  ],
                },
                {width: 15},
              ],
            },
            {height: 20},
            {
              view: "button",
              id: "btn_loc_save",
              value: "Save",
              width: 100,
              align: "right",
            },
          ],
          rules: {
            location_name: webix.rules.isNotEmpty,
            location_roster: webix.rules.isNotEmpty,
            min_SO: webix.rules.isNumber,
            min_SF: webix.rules.isNumber,
            min_FF: webix.rules.isNumber,
            min_MOP: webix.rules.isNumber,
            min_MOF: webix.rules.isNumber,
            min_COFF: webix.rules.isNumber,
            min_SCOP: webix.rules.isNumber,
            min_COP: webix.rules.isNumber,
            min_CMD: webix.rules.isNumber,
            min_R9O: webix.rules.isNumber,
            min_R9: webix.rules.isNumber,
            min_A5O: webix.rules.isNumber,
            min_A5: webix.rules.isNumber,
            min_H6O: webix.rules.isNumber,
            min_H6: webix.rules.isNumber,
            min_C3O: webix.rules.isNumber,
            min_C3: webix.rules.isNumber,
            min_R4O: webix.rules.isNumber,
            min_R4: webix.rules.isNumber,
            min_GPO: webix.rules.isNumber,
            min_GP: webix.rules.isNumber,
            min_MCO: webix.rules.isNumber,
            min_DRO: webix.rules.isNumber,
            min_ICV: webix.rules.isNumber,
            min_HL: webix.rules.isNumber,
            min_CCO: webix.rules.isNumber,
            min_CCP: webix.rules.isNumber,
            min_MVE: webix.rules.isNumber,
            min_MVO: webix.rules.isNumber,
          },
        },
      })
      .hide();
}

