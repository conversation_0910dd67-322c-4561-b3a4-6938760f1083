let covidVaccinesLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>COVID Vaccines Register</span>",
        width: 260,
        align: "left",
      },
      { width: 300 },
      {
        view: "checkbox",
        id: "booster_filter",
        labelRight: "Booster Filter",
        value: 0,
        css: "temp_ra",
        labelWidth: 5,
        width: 140,
        hidden: true,
      },
      {},
      {
        view: "button",
        id: "btn_send_sms_to_all",
        value: "Send SMS to All",
        width: 130,
      },
      {},
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
      { width: 5 },
    ],
  },
  { height: 5 },
  {
    cols: [
      { width: 10 },
      {
        rows: [
          {
            view: "search",
            id: "pay_id_search_field",
            label: "Service No. Filter",
            width: 220,
            labelWidth: 115,
          },
          {},
        ],
      },
      { width: 20 },
      {
        rows: [
          {
            view: "search",
            id: "name_search_field",
            label: "Surname Filter",
            width: 300,
            labelWidth: 100,
          },
          {},
        ],
      },
      { width: 50 },
      {
        view: "radio",
        id: "sms_filter",
        css: "sms_filter_radio",
        label:
          "SMS Filter (Outstanding)</br>SMS Filter (Expired)</br>SMS Filter (Warning)",
        options: [
          "All Records",
          "Booster Shot",
          "Vax Form",
          "Digital Certificate",
          "Vax Form & Digital Certificate",
          "Booster Required",
          "2nd Dose Expired",
          "Booster Expired",
          "2nd Dose Final Notice",
          "Booster Final Notice",
          "Booster Required in 7 days",
          "Booster Expiring in 7 days",
          "Exemption Expiring in 7 days",
        ],
        value: "All Records",
        labelWidth: 180,
        width: 1e3,
        height: 105,
      },
      {},
    ],
  },
  { height: 5 },
  {
    view: "datatable",
    id: "grid-covidVaccines",
    select: "row",
    columns: [
      { id: "id", hidden: true },
      {
        id: "pay_id",
        header: "Service No.",
        sort: "int",
        adjust: "header",
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: "Employee Name",
        sort: "string",
        minWidth: 120,
        adjust: true,
      },
      { id: "roster", header: "Roster", adjust: true, sort: "string" },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "future_booking",
        header: "Future Booking",
        width: 140,
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "first_dose_date",
        header: "1st Dose Date",
        width: 120,
        format: webix.Date.dateToStr("%d/%m/%Y"),
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "first_dose_type",
        header: "1st Dose Type",
        width: 200,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "second_dose_date",
        header: "2nd Dose Date",
        width: 120,
        format: webix.Date.dateToStr("%d/%m/%Y"),
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "second_dose_type",
        header: "2nd Dose Type",
        width: 200,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "booster_date",
        header: "Booster Date",
        width: 120,
        format: webix.Date.dateToStr("%d/%m/%Y"),
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "booster_type",
        header: "Booster Type",
        width: 200,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "booster_date_2",
        header: "Booster Date 2",
        width: 120,
        format: webix.Date.dateToStr("%d/%m/%Y"),
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "booster_type_2",
        header: "Booster Type 2",
        width: 200,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "vax_status",
        header: "Vax Status",
        width: 160,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "exemption_date",
        header: "Exempt Expiry",
        width: 120,
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "compliance_status",
        header: "Compliance Status",
        adjust: true,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "updated_by",
        header: "Updated By",
        sort: "int",
        adjust: "header",
        css: { "text-align": "center" },
      },
      {
        id: "updated_date",
        header: "Updated On",
        width: 110,
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "cert_received",
        header: "Cert. Received",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "form_received",
        header: "Form Received",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "verified",
        header: "Verified",
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "verified_by",
        header: "Verified By",
        sort: "int",
        adjust: "header",
        css: { "text-align": "center" },
      },
      {
        id: "verified_date",
        header: "Verified On",
        width: 110,
        sort: "date",
        css: { "text-align": "center" },
      },
      {
        id: "phone_number",
        header: "Mobile Number",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "second_dose_expired_sms_sent",
        header: "SMS - 2nd Dose Expired",
        width: 170,
        css: { "text-align": "center" },
      },
      {
        id: "booster_dose_expired_sms_sent",
        header: "SMS - Booster Expired",
        width: 160,
        css: { "text-align": "center" },
      },
      {
        id: "second_dose_final_sms_sent",
        header: "SMS - 2nd Dose Final",
        width: 160,
        css: { "text-align": "center" },
      },
      {
        id: "booster_dose_final_sms_sent",
        header: "SMS - Booster Final",
        width: 150,
        css: { "text-align": "center" },
      },
      {
        id: "booster_required_sms_sent",
        header: "SMS - Booster Required",
        width: 170,
        css: { "text-align": "center" },
      },
      {
        id: "booster_required_warning_sms_sent",
        header: "SMS - Booster Req Warning",
        width: 200,
        css: { "text-align": "center" },
      },
      {
        id: "booster_expiring_warning_sms_sent",
        header: "SMS - Booster Exp Warning",
        width: 200,
        css: { "text-align": "center" },
      },
      {
        id: "exemption_expiring_warning_sms_sent",
        header: "SMS - Exemption Exp Warning",
        width: 210,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];


function createCVWindow() {
  webix
      .ui({
        view: "window",
        id: "covid_vaccines-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "vax_record_label",
                  label:
                      "<span class='header_font'>Add COVID Vaccine Record</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_covid_vaccines_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formCovidVaccines",
          borderless: true,
          width: 620,
          height: 760,
          elements: [
            {
              view: "text",
              id: "covid_vaccines_id",
              name: "covid_vaccines_id",
              label: "",
              hidden: true,
            },
            {
              view: "combo",
              id: "covid_vaccines_employee",
              name: "covid_vaccines_employee",
              label: "Employee",
              width: 460,
              labelWidth: 70,
              options: [],
            },
            {
              cols: [
                {},
                {
                  view: "template",
                  id: "current_ra",
                  name: "current_ra",
                  template: "",
                  borderless: true,
                  autoheight: true,
                  width: 580,
                },
                {},
              ],
            },
            {height: 15},
            {
              cols: [
                {
                  view: "checkbox",
                  id: "future_booking",
                  name: "future_booking",
                  labelRight:
                      "I have not yet received a vaccination but have a future booking on",
                  width: 440,
                  labelWidth: 5,
                  value: 0,
                },
                {
                  view: "datepicker",
                  id: "future_booking_date",
                  name: "future_booking_date",
                  width: 120,
                  label: "",
                  labelWidth: 0,
                  labelAlign: "left",
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                },
                {},
              ],
            },
            {height: 20},
            {
              cols: [
                {
                  view: "richselect",
                  id: "first_dose_type",
                  name: "first_dose_type",
                  label: "1st Dose Type",
                  width: 320,
                  labelWidth: 100,
                  options: [
                    " ",
                    "Vaxzevria (AstraZeneca)",
                    "Comirnaty (Pfizer)",
                    "Spikevax (Moderna)",
                    "Nuvaxovid (Novavax)",
                  ],
                },
                {width: 30},
                {
                  view: "datepicker",
                  id: "first_dose_date",
                  name: "first_dose_date",
                  width: 220,
                  label: "1st Dose Date",
                  labelWidth: 100,
                  labelAlign: "left",
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                },
              ],
            },
            {
              cols: [
                {
                  view: "richselect",
                  id: "second_dose_type",
                  name: "second_dose_type",
                  label: "2nd Dose Type",
                  width: 320,
                  labelWidth: 100,
                  options: [
                    " ",
                    "Vaxzevria (AstraZeneca)",
                    "Comirnaty (Pfizer)",
                    "Spikevax (Moderna)",
                    "Nuvaxovid (Novavax)",
                  ],
                },
                {width: 30},
                {
                  view: "datepicker",
                  id: "second_dose_date",
                  name: "second_dose_date",
                  width: 220,
                  label: "2nd Dose Date",
                  labelWidth: 100,
                  labelAlign: "left",
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                },
              ],
            },
            {
              cols: [
                {
                  view: "richselect",
                  id: "booster_type",
                  name: "booster_type",
                  label: "Booster Type",
                  width: 320,
                  labelWidth: 100,
                  options: [
                    " ",
                    "Vaxzevria (AstraZeneca)",
                    "Comirnaty (Pfizer)",
                    "Spikevax (Moderna)",
                    "Nuvaxovid (Novavax)",
                  ],
                },
                {width: 30},
                {
                  view: "datepicker",
                  id: "booster_date",
                  name: "booster_date",
                  width: 220,
                  label: "Booster Date",
                  labelWidth: 100,
                  labelAlign: "left",
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                },
              ],
            },
            {
              cols: [
                {
                  view: "richselect",
                  id: "booster_type_2",
                  name: "booster_type_2",
                  label: "Booster Type 2",
                  width: 320,
                  labelWidth: 100,
                  options: [
                    " ",
                    "Vaxzevria (AstraZeneca)",
                    "Comirnaty (Pfizer)",
                    "Spikevax (Moderna)",
                    "Nuvaxovid (Novavax)",
                  ],
                },
                {width: 30},
                {
                  view: "datepicker",
                  id: "booster_date_2",
                  name: "booster_date_2",
                  width: 220,
                  label: "Booster Date 2",
                  labelWidth: 100,
                  labelAlign: "left",
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                },
              ],
            },
            {
              cols: [
                {
                  view: "richselect",
                  id: "vax_status",
                  name: "vax_status",
                  label: "Vaccine Status",
                  width: 320,
                  labelWidth: 110,
                  css: "",
                  hidden: true,
                  options: [
                    "Unvaccinated",
                    "Vaccinated",
                    "Medically Exempt",
                    "Medically Exempt Pending",
                  ],
                },
                {width: 30},
                {
                  view: "datepicker",
                  id: "exemption_date",
                  name: "exemption_date",
                  width: 220,
                  label: "Expiry Date",
                  labelWidth: 100,
                  labelAlign: "left",
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                  hidden: true,
                },
              ],
            },
            {
              view: "textarea",
              id: "vax_comments",
              name: "vax_comments",
              label: "Comments",
              labelWidth: 85,
              width: 560,
              height: 80,
              attributes: {maxlength: 500},
            },
            {height: 5},
            {
              cols: [
                {
                  view: "checkbox",
                  id: "digital_cert_received",
                  name: "digital_cert_received",
                  label: "Digital COVID-19 Certificate received",
                  width: 270,
                  labelWidth: 230,
                  align: "left",
                  value: 0,
                },
                {},
                {
                  view: "checkbox",
                  id: "vax_form_received",
                  name: "vax_form_received",
                  label: "COVID Vaccination Form received",
                  width: 255,
                  labelWidth: 215,
                  align: "right",
                  value: 0,
                },
              ],
            },
            {
              view: "checkbox",
              id: "verified_record",
              name: "verified_record",
              label: "This COVID vaccine record has been verified!",
              width: 320,
              labelWidth: 280,
              align: "center",
              value: 0,
            },
            {height: 5},
            {
              view: "template",
              id: "compliance_status",
              template:
                  "<div style='font-size: 15px; font-weight: 500; color: red; text-align: center'>IMPORTANT NOTICE:</br>Vaccination compliance data for all employees is currently being processed.</br>Once completed your compliance status will be updated on this form!</div>",
              align: "center",
              autoheight: true,
              borderless: true,
            },
            {height: 5},
            {
              view: "button",
              id: "btn_covid_vaccines_save",
              value: "Save",
              width: 100,
              align: "right",
            },
          ],
          rules: {
            shift_group_roster: webix.rules.isNotEmpty,
            shift_group_shift: webix.rules.isNotEmpty,
          },
        },
      })
      .hide();
}

