shiftsGroupsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Shift Groups</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid_shift_groups",
    scroll: "y",
    select: "row",
    editable: true,
    columns: [
      { id: "id", hidden: true },
      { id: "roster", width: 300, header: "Roster", sort: "string" },
      { id: "shift", width: 300, header: "Shift", sort: "string" },
      {
        id: "locations",
        header: "Locations",
        fillspace: true,
        optionslist: true,
        options: [],
        editor: "multiselect",
      },
    ],
    fixedRowHeight: false,
    rowLineHeight: 35,
    rowHeight: 35,
    data: [],
  },
  { height: 10 },
];


function createSGWindow() {
  webix
      .ui({
        view: "window",
        id: "shift_groups-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  label: "<span class='header_font'>Add Shift Group</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_shift_grp_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formShiftGroups",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "shift_group_id",
              name: "id",
              label: "",
              hidden: true,
            },
            {
              view: "richselect",
              id: "shift_group_roster",
              name: "shift_group_roster",
              label: "Roster",
              width: 330,
              labelWidth: 60,
              options: [],
            },
            {
              view: "richselect",
              id: "shift_group_shift",
              name: "shift_group_shift",
              label: "Shift",
              width: 330,
              labelWidth: 60,
              options: [],
            },
            {height: 20},
            {
              view: "button",
              id: "btn_shift_grp_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {
            shift_group_roster: webix.rules.isNotEmpty,
            shift_group_shift: webix.rules.isNotEmpty,
          },
        },
      })
      .hide();
}
