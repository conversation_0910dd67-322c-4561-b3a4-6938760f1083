let rosterArrangementLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Roster Arrangement</span>",
        align: "left",
        width: 200,
      },
      {},
      {
        view: "checkbox",
        id: "show_temp_ra",
        labelRight: "Show Temporary",
        value: 0,
        css: "temp_ra",
        labelWidth: 140,
        width: 280,
      },
      { width: 20 },
      { view: "button", id: "btn_edit", value: "Edit RA", width: 110 },
      { width: 20 },
      { view: "button", id: "btn_delete", value: "Delete RA", width: 110 },
    ],
  },
  { height: 10 },
  {
    view: "scrollview",
    id: "ra_scroller",
    scroll: "y",
    body: {
      rows: [
        {
          cols: [
            {
              view: "form",
              id: "formArrangement",
              width: 540,
              autoheight: true,
              margin: 1,
              elements: [
                {
                  view: "text",
                  id: "arrangement_id",
                  name: "arrangement_id",
                  hidden: true,
                },
                {
                  view: "combo",
                  id: "employees",
                  name: "employees",
                  label: "Employee",
                  width: 460,
                  labelWidth: 100,
                  options: [],
                },
                {
                  view: "combo",
                  id: "rosters",
                  name: "rosters",
                  label: "Roster",
                  width: 380,
                  labelWidth: 100,
                  options: [],
                },
                {
                  view: "combo",
                  id: "shifts",
                  name: "shifts",
                  label: "Shift",
                  width: 380,
                  labelWidth: 100,
                  options: [],
                },
                {
                  view: "combo",
                  id: "locations",
                  name: "locations",
                  label: "Location",
                  width: 380,
                  labelWidth: 100,
                  options: [],
                },
                {
                  cols: [
                    {
                      view: "combo",
                      id: "rank",
                      name: "rank",
                      label: "Select Rank",
                      width: 260,
                      labelWidth: 100,
                      value: "",
                      options: all_rank_types,
                    },
                    { width: 30 },
                    {
                      view: "checkbox",
                      id: "acting_up",
                      name: "acting_up",
                      label: "Acting Up/Higher Duties",
                      value: 0,
                      labelWidth: 155,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "start_date",
                      name: "start_date",
                      width: 260,
                      label: "Start Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                    },
                    { width: 20 },
                    {
                      view: "datepicker",
                      id: "start_time",
                      name: "start_time",
                      type: "time",
                      width: 100,
                      label: "",
                      value: new Date("01/01/2019 00:00"),
                      format: "%H:%i",
                      stringResult: true,
                      disabled: true,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "end_date",
                      name: "end_date",
                      width: 260,
                      label: "End Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      disabled: true,
                    },
                    { width: 20 },
                    {
                      view: "datepicker",
                      id: "end_time",
                      name: "end_time",
                      type: "time",
                      width: 100,
                      label: "",
                      value: new Date("01/01/2019 23:59"),
                      format: "%H:%i",
                      stringResult: true,
                      disabled: true,
                    },
                  ],
                },
                {
                  view: "checkbox",
                  id: "no_end_date",
                  name: "no_end_date",
                  label: "No End Date",
                  value: 1,
                  labelWidth: 100,
                },
                { height: 5 },
                {
                  view: "button",
                  id: "btn_roster_save",
                  value: "Save",
                  width: 100,
                  align: "center",
                },
                { height: 1 },
              ],
              rules: {
                employees: webix.rules.isNotEmpty,
                rosters: webix.rules.isNotEmpty,
                shifts: webix.rules.isNotEmpty,
                locations: webix.rules.isNotEmpty,
                rank: webix.rules.isNotEmpty,
              },
            },
            { width: 10 },
            {
              view: "datatable",
              id: "roster_arrangements",
              select: "row",
              height: 260,
              scheme: {
                $change: function (item) {
                  if (item.type == "Temporary") item.$css = "temp_ra_line";
                },
              },
              columns: [
                { id: "id", hidden: true },
                { id: "roster_arrangement_id", hidden: true },
                { id: "roster", header: "Roster", adjust: true },
                { id: "shift", header: "Shift", adjust: true, sort: "string" },
                {
                  id: "location",
                  header: "Location",
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "rank",
                  header: "Rank",
                  width: 60,
                  sort: "string",
                  css: { "text-align": "center" },
                },
                {
                  id: "acting_up",
                  header: "AU/HD",
                  width: 70,
                  sort: "string",
                  css: { "text-align": "center" },
                },
                {
                  id: "start_date",
                  header: "Start Date",
                  width: 90,
                  sort: "date",
                  css: { "text-align": "center" },
                },
                {
                  id: "end_date",
                  header: "End Date",
                  width: 90,
                  sort: "date",
                  css: { "text-align": "center" },
                },
                { id: "no_end_date", header: "", hidden: true },
                { id: "linked_booking_id", header: "", hidden: true },
                {
                  id: "type",
                  header: "Type",
                  width: 100,
                  css: { "text-align": "center" },
                },
                {
                  id: "created_by",
                  header: "Created By",
                  width: 100,
                  css: { "text-align": "center" },
                },
                {
                  id: "created_date",
                  header: "Created Date",
                  width: 125,
                  sort: "date",
                  css: { "text-align": "center" },
                },
              ],
              data: [],
            },
          ],
        },
        { height: 10 },
        {
          view: "toolbar",
          id: "timeline_toolbar",
          css: "main_header",
          cols: [
            {
              view: "label",
              label:
                "<span class='header_font'>Roster Arrangements Timeline</span>",
              align: "left",
            },
          ],
        },
        {
          view: "dhx-gantt",
          id: "ra_scheduler",
          height: 260,
          init: function (gantt_obj) {
            gantt_obj.config.csp = true;
            gantt_obj.config.readonly = true;
            gantt_obj.config.scale_height = 50;
            gantt_obj.config.date_scale = "%M";
            gantt_obj.config.scale_unit = "month";
            gantt_obj.config.subscales = [
              { unit: "year", step: 1, date: "%Y" },
            ];
            gantt_obj.config.columns = [
              { name: "roster", label: "Roster", width: 300, tree: false },
            ];
            gantt_obj.config.min_column_width = 30;
            gantt_obj.config.date_grid = "%d/%m/%Y";
            gantt_obj.config.row_height = 40;
          },
        },
        { height: 10 },
        {
          view: "toolbar",
          id: "import_toolbar",
          css: "main_header",
          hidden: true,
          cols: [
            {
              view: "uploader",
              id: "import_uploader",
              width: 120,
              css: "webix_secondary",
              value: "Load Excel File",
              accept: "application/vnd.ms-excel",
              multiple: false,
            },
            { view: "button", id: "clear_sheet", label: "Clear", width: 60 },
            { width: 20 },
            {
              view: "label",
              id: "file_name",
              label: "Import Roster Arrangements from Gartan",
              css: "header_font",
              on: {
                onBeforeRender: function (config) {
                  config.labelWidth = webix.html.getTextSize(
                    config.label,
                  ).width;
                },
              },
            },
            { width: 10 },
            {
              view: "datepicker",
              id: "import_start_date",
              value: new Date(),
              label: "Start Date",
              labelWidth: 80,
              format: "%d/%m/%Y",
              stringResult: true,
              timepicker: false,
              css: "ra_import",
              width: 200,
            },
            { width: 20 },
            {
              view: "counter",
              id: "import_duration",
              name: "import_duration",
              label: "Period",
              css: "ra_import",
              labelWidth: 60,
              width: 160,
              value: 1,
              min: 1,
              max: 20,
            },
            { width: 20 },
            {
              view: "button",
              id: "import_sheet",
              label: "Import Roster Arrangement",
              width: 190,
              disabled: true,
            },
          ],
        },
        {
          view: "excelviewer",
          id: "excel_import",
          autoheight: true,
          excelHeader: false,
          header: false,
          scroll: true,
          hidden: false,
        },
        { height: 5 },
      ],
    },
  },
];

function createRABWindow() {
  webix
      .ui({
        view: "window",
        id: "ra_bookings-window",
        modal: true,
        position: "center",
        head: {
          view: "toolbar",
          autoheight: true,
          elements: [
            {
              cols: [
                {
                  view: "template",
                  id: "ra_bookings-window-label",
                  template: "",
                  autoheight: true,
                  width: 660,
                  borderless: true,
                  css: {
                    "font-size": "14px !important",
                    "text-align": "center !important",
                  },
                },
                {},
                {
                  rows: [
                    {
                      view: "button",
                      id: "btn_ra_bookings-window_close",
                      label: "X",
                      width: 40,
                      css: "webix_danger",
                      height: 35,
                    },
                    {},
                  ],
                },
              ],
            },
          ],
        },
        body: {
          rows: [
            {
              view: "datatable",
              id: "ra_bookings-window_grid",
              select: "row",
              rowHeight: 35,
              autowidth: true,
              height: 300,
              scroll: "y",
              columns: [
                {id: "bk_id", hidden: true},
                {id: "booking_id", hidden: true},
                {id: "type", header: "Type", width: 60},
                {id: "start_date", header: "Start Date", width: 120},
                {id: "end_date", header: "End Date", width: 120},
                {
                  id: "hours",
                  header: "Hours",
                  width: 60,
                  css: {"text-align": "center"},
                },
                {id: "roster", header: "Roster", adjust: true},
                {id: "shift", header: "Shift", adjust: true, sort: "string"},
                {
                  id: "location",
                  header: "Location",
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "status",
                  header: "Status",
                  width: 70,
                  css: {"text-align": "center"},
                },
                {
                  id: "select",
                  header: "<span class='webix_icon fas fa-trash-alt'></span>",
                  template: "{common.checkbox()}",
                  css: {"text-align": "center"},
                  width: 40,
                },
              ],
              data: [],
            },
            {height: 10},
            {
              cols: [
                {
                  view: "text",
                  id: "move_ra_bookings_roster",
                  label: "Roster",
                  labelWidth: 10,
                  hidden: true,
                },
                {
                  view: "text",
                  id: "move_ra_bookings_shift",
                  label: "Shift",
                  labelWidth: 10,
                  hidden: true,
                },
                {
                  view: "text",
                  id: "move_ra_bookings_location",
                  label: "Location",
                  labelWidth: 10,
                  hidden: true,
                },
                {width: 5},
                {
                  view: "button",
                  id: "btn_ra_select_all_bookings",
                  value: "Select All",
                  width: 110,
                  height: 32,
                },
                {
                  view: "button",
                  id: "btn_ra_unselect_all_bookings",
                  value: "Unselect All",
                  width: 120,
                  height: 32,
                },
                {},
                {
                  view: "button",
                  id: "btn_ra_confirm_bookings",
                  value: "Delete",
                  width: 100,
                  height: 32,
                },
                {width: 5},
              ],
            },
            {height: 10},
          ],
        },
      })
      .hide();
}

function createRARWindow() {
  webix
      .ui({
        view: "window",
        id: "ra_recurrence-window",
        modal: true,
        position: "center",
        width: 460,
        head: {
          cols: [
            {width: 10},
            {
              view: "label",
              id: "ra_recurrence-window-label",
              label: "<span class='header_font'>Day Work Booking Schedule</span>",
              align: "left",
              width: 190,
            },
            {},
            {
              view: "button",
              id: "btn_ra_recurrence-window_close",
              label: "X",
              align: "right",
              css: "webix_danger",
              width: 40,
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              cols: [
                {},
                {
                  view: "label",
                  label: "Select Day Work Booking Options",
                  align: "center",
                  width: 240,
                },
                {},
              ],
            },
            {height: 10},
            {
              cols: [
                {
                  view: "combo",
                  id: "ra_recurrence-window_pattern",
                  width: 320,
                  label: "Work Pattern",
                  labelWidth: 120,
                  labelAlign: "right",
                  options: [],
                  on: {
                    onAfterRender: function () {
                      $$(this).define("options", {
                        data: [
                          {
                            id: "DW1",
                            value: "DW1 - W2H08400N022",
                            info: "<strong>Week 1</strong> - Mon~Fri - <strong>8</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Fri - <strong>8</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW2",
                            value: "DW2 - W2H08400N001",
                            info: "<strong>Week 1</strong> - Mon~Fri - <strong>8.4</strong> hrs/day - Week 1 Hours = <strong>42</strong></br><strong>Week 2</strong> - Mon~Fri - <strong>8.4</strong> hrs/day - Week 2 Hours = <strong>42</strong></br>RDO: <strong>Yes</strong> - Total Fortnightly Hours = <strong>84</strong>",
                          },
                          {
                            id: "DW3",
                            value: "DW3 - W2H08000N014",
                            info: "<strong>Week 1</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW4",
                            value: "DW4 - W2H08000N021",
                            info: "<strong>Week 1</strong> - Mon~Thu - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Thu - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW5",
                            value: "DW5 - W2H08000N033",
                            info: "<strong>Week 1</strong> - Mon/Tue/Wed/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Tue/Wed/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW6",
                            value: "DW6 - W2H08000N015",
                            info: "<strong>Week 1</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW7",
                            value: "DW7 - W2H08000N031",
                            info: "<strong>Week 1</strong> - Mon~Fri - <strong>8.4</strong> hrs/day - Week 1 Hours = <strong>42</strong></br><strong>Week 2</strong> - Tue~Fri - <strong>9.5</strong> hrs/day - Week 2 Hours = <strong>38</strong></br>RDO: <strong>Yes</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW8",
                            value: "DW8 - W1H04000N007",
                            info: "<strong>Week 1</strong> - Mon/Wed/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Wed/Thu/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW9",
                            value: "DW9 - W2H08000N019",
                            info: "<strong>Week 1</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW10",
                            value: "DW10 - W2H08000N016",
                            info: "<strong>Week 1</strong> - Tue~Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Thu - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW11",
                            value: "DW11 - W2H08000N002",
                            info: "<strong>Week 1</strong> - Mon~Thu 8.5/Fri 8 - <strong>8.5/8</strong> hrs/day - Week 1 Hours = <strong>42</strong></br><strong>Week 2</strong> - Mon~Thu 8.5/Fri 8 - <strong>8.5/8</strong> hrs/day - Week 2 Hours = <strong>42</strong></br>RDO: <strong>Yes</strong> - Total Fortnightly Hours = <strong>84</strong>",
                          },
                          {
                            id: "DW12",
                            value: "DW12 - W1H04000N011",
                            info: "<strong>Week 1</strong> - Mon~Thu 8.1/Fri 7.6 - <strong>8.1/7.6</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Thu 8.1/Fri 7.6 - <strong>8.1/7.6</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW13",
                            value: "DW13 - W1H04000N014",
                            info: "<strong>Week 1</strong> - Mon~Wed 5/Thu 12/Fri 11.5 - <strong>5.5/12/11.5</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon~Wed 5.5/Thu 12/Fri 11.5 - <strong>5.5/12/11.5</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW14",
                            value: "DW14 - W2H08000N028",
                            info: "<strong>Week 1</strong> - Mon~Thu - <strong>9</strong> hrs/day - Week 1 Hours = <strong>36</strong></br><strong>Week 2</strong> - Mon~Thu 9/Fri 8 - <strong>9/8</strong> hrs/day - Week 2 Hours = <strong>44</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW15",
                            value: "DW15 - W2H08000N054",
                            info: "<strong>Week 1</strong> - Mon~Fri - <strong>8.9</strong> hrs/day - Week 1 Hours = <strong>44.5</strong></br><strong>Week 2</strong> - Tue~Thu 8.9/Fri 8.8 - <strong>8.4</strong> hrs/day - Week 2 Hours = <strong>35.5</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW16",
                            value: "DW16 - W2H08000N055",
                            info: "<strong>Week 1</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 1 Hours = <strong>40</strong></br><strong>Week 2</strong> - Mon/Tue/Thu/Fri - <strong>10</strong> hrs/day - Week 2 Hours = <strong>40</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                          {
                            id: "DW17",
                            value: "DW17 - W2H08000N056",
                            info: "<strong>Week 1</strong> - Tue~Thu 8.9/Fri 8.8 - <strong>8.9/8.8</strong> hrs/day - Week 1 Hours = <strong>35.5</strong></br><strong>Week 2</strong> - Mon~Fri - <strong>8.9</strong> hrs/day - Week 2 Hours = <strong>44.5</strong></br>RDO: <strong>No</strong> - Total Fortnightly Hours = <strong>80</strong>",
                          },
                        ],
                        body: {
                          tooltip: function (obj) {
                            return obj.info;
                          },
                        },
                      });
                    },
                  },
                },
                {},
              ],
            },
            {height: 10},
            {
              cols: [
                {},
                {
                  view: "template",
                  id: "ra_recurrence-window_shift_info",
                  template: "",
                  css: "day_work_schedule",
                  align: "center",
                  autoheight: true,
                  width: 460,
                  borderless: true,
                },
                {},
              ],
            },
            {height: 20},
            {
              cols: [
                {
                  view: "datepicker",
                  id: "ra_recurrence-window_start_date",
                  width: 240,
                  label: "Starting On",
                  labelWidth: 120,
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                  labelAlign: "right",
                  readonly: true,
                },
                {},
              ],
            },
            {height: 10},
            {
              cols: [
                {
                  view: "datepicker",
                  id: "ra_recurrence-window_end_date",
                  width: 240,
                  label: "Ending On",
                  labelWidth: 120,
                  value: new Date(),
                  format: "%d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                  labelAlign: "right",
                  readonly: true,
                },
                {},
                {
                  view: "checkbox",
                  id: "ra_recurrence-window_ra_end_date",
                  labelRight: "to end of RA",
                  value: 0,
                  width: 200,
                  labelWidth: 0,
                  hidden: true,
                },
              ],
            },
            {height: 10},
            {
              view: "text",
              id: "ra_recurrence-window_roster",
              label: "Roster",
              width: 320,
              labelWidth: 120,
              labelAlign: "right",
              readonly: true,
            },
            {height: 10},
            {
              view: "text",
              id: "ra_recurrence-window_shift",
              label: "Shift",
              width: 320,
              labelWidth: 120,
              labelAlign: "right",
              readonly: true,
            },
            {height: 10},
            {
              view: "text",
              id: "ra_recurrence-window_location",
              label: "Location",
              width: 320,
              labelWidth: 120,
              labelAlign: "right",
              readonly: true,
            },
            {height: 20},
            {
              cols: [
                {},
                {
                  view: "button",
                  id: "btn_ra_recurrence-window_create",
                  label: "Create Day Work Bookings",
                  width: 180,
                  css: "webix_primary",
                  align: "center",
                },
                {},
              ],
            },
            {height: 15},
          ],
        },
      })
      .hide();
}

function createRAEWindow() {
  webix
      .ui({
        view: "window",
        id: "ra_edit-window",
        modal: true,
        position: "center",
        head: {
          cols: [
            {width: 10},
            {
              view: "label",
              id: "ra_edit-window-label",
              label:
                  "<span class='header_font'>Edit Roster Arrangement Details</span>",
              align: "left",
              width: 240,
            },
            {},
            {
              view: "button",
              id: "btn_ra_edit-window_close",
              label: "X",
              align: "right",
              css: "webix_danger",
              width: 40,
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              view: "template",
              template:
                  "<div style='font-size: 14px !important; color: black !important; text-align: center !important;'>You can edit any RA value below (except Roster, Shift & Location)</div><br><br><div style='font-size: 13px !important; color: black !important;'><i>This method will split the existing RA in to 2 periods. The new/edited RA period can be anytime between the existing selected RA's Start & End date.<br>NOTE: Because the Roster, Shift & Location remain the same all bookings in this period won't be affected (not moved or deleted).</i></div>",
              align: "center",
              width: 500,
              height: 120,
              borderless: true,
            },
            {height: 10},
            {
              view: "form",
              id: "ra_edit-form",
              width: 540,
              autoheight: true,
              borderless: true,
              elements: [
                {
                  view: "text",
                  id: "ra_edit_id",
                  name: "ra_edit_id",
                  label: "RA_ID",
                  width: 420,
                  labelWidth: 100,
                  disabled: true,
                },
                {
                  view: "text",
                  id: "ra_edit_rosters",
                  name: "ra_edit_rosters",
                  label: "Roster",
                  width: 380,
                  labelWidth: 100,
                  readonly: true,
                },
                {
                  view: "text",
                  id: "ra_edit_shifts",
                  name: "ra_edit_shifts",
                  label: "Shift",
                  width: 380,
                  labelWidth: 100,
                  readonly: true,
                },
                {
                  view: "text",
                  id: "ra_edit_locations",
                  name: "ra_edit_locations",
                  label: "Location",
                  width: 380,
                  labelWidth: 100,
                  readonly: true,
                },
                {
                  cols: [
                    {
                      view: "combo",
                      id: "ra_edit_rank",
                      name: "ra_edit_rank",
                      label: "Select Rank",
                      width: 260,
                      labelWidth: 100,
                      value: "",
                      options: all_rank_types,
                    },
                    {width: 30},
                    {
                      view: "checkbox",
                      id: "ra_edit_acting_up",
                      name: "ra_edit_acting_up",
                      label: "Acting Up/Higher Duties",
                      value: 0,
                      labelWidth: 155,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "ra_edit_start_date",
                      name: "ra_edit_start_date",
                      width: 260,
                      label: "Start Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      icons: false,
                    },
                    {width: 20},
                    {
                      view: "datepicker",
                      id: "ra_edit_start_time",
                      name: "ra_edit_start_time",
                      type: "time",
                      width: 100,
                      label: "",
                      value: new Date("01/01/2019 00:00"),
                      format: "%H:%i",
                      stringResult: true,
                      disabled: true,
                    },
                  ],
                },
                {
                  cols: [
                    {
                      view: "datepicker",
                      id: "ra_edit_end_date",
                      name: "ra_edit_end_date",
                      width: 260,
                      label: "End Date",
                      labelWidth: 100,
                      labelAlign: "left",
                      value: new Date(),
                      format: "%D, %d/%m/%Y",
                      stringResult: true,
                      timepicker: false,
                      icons: false,
                    },
                    {width: 20},
                    {
                      view: "datepicker",
                      id: "ra_edit_end_time",
                      name: "ra_edit_end_time",
                      type: "time",
                      width: 100,
                      label: "",
                      value: new Date("01/01/2019 23:59"),
                      format: "%H:%i",
                      stringResult: true,
                      disabled: true,
                    },
                  ],
                },
              ],
              rules: {
                ra_edit_id: webix.rules.isNotEmpty,
                ra_edit_rosters: webix.rules.isNotEmpty,
                ra_edit_shifts: webix.rules.isNotEmpty,
                ra_edit_locations: webix.rules.isNotEmpty,
                ra_edit_rank: webix.rules.isNotEmpty,
                ra_edit_start_date: webix.rules.isNotEmpty,
                ra_edit_end_date: webix.rules.isNotEmpty,
              },
            },
            {height: 10},
            {
              cols: [
                {},
                {
                  view: "button",
                  id: "btn_ra_edit_update",
                  label: "Update",
                  width: 100,
                  css: "webix_primary",
                  align: "center",
                },
                {},
              ],
            },
            {height: 30},
          ],
        },
      })
      .hide();
}

