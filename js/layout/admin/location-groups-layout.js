locationsGroupsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Location Groups</span>",
        align: "left",
      },
      { view: "button", id: "btn_delete", value: "Delete", width: 100 },
      { view: "button", id: "btn_add", value: "Add New", width: 100 },
    ],
  },
  {
    view: "datatable",
    id: "grid_groups",
    scroll: "y",
    select: "row",
    editable: true,
    columns: [
      { id: "id", hidden: true },
      { id: "group_name", width: 300, header: "Group Name", sort: "string" },
      {
        id: "locations",
        header: "Locations",
        fillspace: true,
        optionslist: true,
        options: [],
        editor: "multiselect",
      },
    ],
    fixedRowHeight: false,
    rowLineHeight: 35,
    rowHeight: 35,
    data: [],
  },
  { height: 10 },
];


function createLGWindow() {
  webix
      .ui({
        view: "window",
        id: "location_groups-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  label: "<span class='header_font'>Add Location Group</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_grp_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          view: "form",
          id: "formGroups",
          borderless: true,
          elements: [
            {
              view: "text",
              id: "location_group_id",
              name: "id",
              label: "",
              hidden: true,
            },
            {
              view: "text",
              id: "location_group_name",
              name: "group_name",
              label: "Group Name",
              width: 400,
              labelWidth: 100,
            },
            {height: 20},
            {
              view: "button",
              id: "btn_grp_save",
              value: "Save",
              width: 100,
              align: "center",
            },
          ],
          rules: {group_name: webix.rules.isNotEmpty},
        },
      })
      .hide();
}

