let loginMessageLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Login Page Message</span>",
        align: "left",
      },
    ],
  },
  { height: 20 },
  {
    cols: [
      { width: 60 },
      {
        view: "textarea",
        id: "login_message",
        label: "Message From SAMFS",
        labelPosition: "top",
        labelWidth: 110,
        css: "login_page_message",
        width: 700,
        height: 140,
      },
      {},
    ],
  },
  {
    view: "button",
    id: "btnSaveMessage",
    label: "Save",
    width: 100,
    align: "right",
  },
  {},
];
