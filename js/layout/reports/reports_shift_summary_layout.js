let shiftSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Shift Summary Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from_date",
        name: "from_date",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        name: "to_date",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    view: "datatable",
    id: "shift_summary_grid",
    rowHeight: 30,
    headerRowHeight: 45,
    css: "availability_report",
    columns: [
      { id: "date", header: "Date", width: 90 },
      {
        id: "period",
        header: { text: "Period", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_avl",
        header: { text: "Total\nAvailable", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_actups",
        header: { text: "Total\nAct-Ups", css: "multiline" },
        width: 65,
        css: { "text-align": "center" },
      },
      {
        id: "total_recalls",
        header: { text: "Total\nRecalls", css: "multiline" },
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "total_sick",
        header: { text: "Total\nSick", css: "multiline" },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "total_rrl",
        header: { text: "Total\nRRL", css: "multiline" },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "total_leave",
        header: { text: "Total\nLeave", css: "multiline" },
        width: 65,
        css: { "text-align": "center" },
      },
      {
        id: "total_other",
        header: { text: "Total\nOther", css: "multiline" },
        width: 65,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];
