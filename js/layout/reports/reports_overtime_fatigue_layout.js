let overtimeFatigueReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Overtime & Fatigue Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 15 },
      {
        view: "datepicker",
        id: "date_filter",
        name: "date_filter",
        width: 165,
        label: "Date",
        labelWidth: 40,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "combo",
        id: "time_filter",
        name: "time_filter",
        label: "Time",
        labelWidth: 40,
        width: 120,
        options: [
          { id: "08:00", value: "08:00" },
          { id: "18:00", value: "18:00" },
        ],
      },
      { width: 30 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 340,
        options: [],
      },
      { width: 30 },
      {
        view: "combo",
        id: "rank_filter",
        name: "rank_filter",
        label: "Rank",
        labelWidth: 40,
        width: 200,
        options: [
          { id: "All", value: "-- All Ranks --" },
          { id: "CMD", value: "CMD" },
          { id: "SO", value: "SO" },
          { id: "FF/SFF/COP/SCOP", value: "FF/SFF/COP/SCOP" },
          { id: "MOFF", value: "MOFF" },
        ],
      },
      { width: 30 },
      {
        view: "multicombo",
        id: "code_filter",
        name: "code_filter",
        label: "Code(s)",
        labelWidth: 60,
        width: 300,
        button: true,
        placeholder: "Select skill codes",
        tagMode: false,
        tagTemplate: function (values) {
          return values.length ? values.length + " skill(s) selected" : "";
        },
        options: { selectAll: true, data: [] },
      },
      {},
    ],
  },
  {
    cols: [
      { width: 6 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      { width: 30 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "overtime_fatigue_grid",
    headerRowHeight: 43,
    rowHeight: 23,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        width: 100,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "mobile_no",
        header: "Mobile No.",
        width: 100,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "rank",
        header: "Rank",
        width: 80,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "name",
        header: "Employee",
        minWidth: 200,
        adjust: true,
        sort: "string",
      },
      { id: "roster", header: "Roster", adjust: true },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "overtime_hours",
        header: { text: "Overtime\nHours", css: "multiline" },
        width: 90,
        css: { "text-align": "center", "font-weight": "bold" },
        sort: "int",
      },
      {
        id: "fatigue_hours",
        header: { text: "Fatigue\nHours", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "skill_codes",
        header: "Skill Codes",
        minWidth: 120,
        adjust: true,
        sort: "string",
      },
      {
        id: "logs",
        header: "View",
        css: { "text-align": "center" },
        template: "<span class = 'webix_icon fas fa-list'></span>",
        width: 60,
      },
    ],
    data: [],
  },
  { height: 10 },
];

function createOBreakWindow() {
  webix
      .ui({
        view: "window",
        id: "overtime-breakdown-window",
        modal: true,
        position: "center",
        fullscreen: false,
        height: 750,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "overtime-breakdown-label",
                  label:
                      "<span class='header_font'>Overtime Booking Details</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_overtime-breakdown_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              view: "template",
              id: "overtime-breakdown_details",
              template: "",
              autoheight: true,
              borderless: true,
              css: {
                "text-align": "center",
                color: "black",
                "font-size": "17px !important",
              },
            },
            {
              view: "datatable",
              id: "overtime-breakdown_grid",
              select: "row",
              height: 510,
              autowidth: true,
              columns: [
                {id: "booking_id", hidden: true},
                {
                  id: "code",
                  header: "Type",
                  width: 70,
                  css: {"text-align": "center"},
                  sort: "string",
                },
                {
                  id: "start_date",
                  header: "Overtime Date",
                  width: 140,
                  css: {"text-align": "center"},
                  sort: "date",
                },
                {
                  id: "roster",
                  header: "Roster",
                  minWidth: 90,
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "shift",
                  header: "Shift",
                  minWidth: 90,
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "location",
                  header: "Location",
                  minWidth: 90,
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "period",
                  header: "Period",
                  width: 70,
                  css: {"text-align": "center"},
                  sort: "int",
                },
                {
                  id: "hours",
                  header: "Hours",
                  width: 70,
                  css: {"text-align": "center"},
                  sort: "int",
                },
                {
                  id: "created_by",
                  header: "Created By",
                  width: 100,
                  css: {"text-align": "center"},
                  sort: "string",
                },
                {
                  id: "created_date",
                  header: "Created Date",
                  width: 140,
                  css: {"text-align": "center"},
                  sort: "date",
                },
              ],
              data: [],
            },
          ],
        },
      })
      .hide();
}
