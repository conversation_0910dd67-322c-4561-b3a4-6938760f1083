let deletionReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Bookings Deletion Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "deletion_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "roster",
        header: { text: "Roster", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "code",
        header: { text: "Code", css: { "text-align": "center" } },
        adjust: "data",
      },
      {
        id: "deleted_period",
        header: { text: "Deleted Period", css: { "text-align": "center" } },
        sort: "string",
        css: { "text-align": "center" },
        adjust: true,
      },
      {
        id: "total_hours",
        header: { text: "Hours", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "approved_start_date",
        header: "Start Date",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "approved_end_date",
        header: "End Date",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "deleted_by",
        header: { text: "Deleted By", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "deleted_date",
        header: { text: "Deleted Date", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "c21_deleted",
        header: { text: "C21 Deleted", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center", "background-color": "#ddebf7" },
      },
      {
        id: "wfr_comments",
        header: { text: "Comments", css: { "text-align": "center" } },
        width: 200,
        css: { "text-align": "center", "background-color": "#ddebf7" },
      },
      {
        id: "wfr_lve",
        header: { text: "Lve Book", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center", "background-color": "#ddebf7" },
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
