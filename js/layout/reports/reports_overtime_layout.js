let overtimeReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Overtime Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "richselect",
        id: "rosters",
        label: "Roster",
        width: 280,
        labelWidth: 50,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift",
        label: "Shift",
        width: 320,
        labelWidth: 45,
        options: [],
      },
      { width: 10 },
      {
        view: "checkbox",
        id: "include_otr",
        name: "include_otr",
        labelRight: "Inc OTR",
        width: 105,
        labelWidth: 20,
        value: 1,
        disabled: true,
      },
      {
        view: "checkbox",
        id: "include_commcen",
        name: "include_commcen",
        labelRight: "Inc CommCen",
        width: 140,
        labelWidth: 20,
        value: 1,
        disabled: true,
      },
      { width: 15 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "overtime_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Current Location", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "type",
        header: { text: "Type", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "activity",
        header: { text: "Activity", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "start_date",
        header: { text: "Start Date", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "start_time",
        header: { text: "Start Time", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "end_date",
        header: { text: "End Date", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "end_time",
        header: { text: "End Time", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "total_hours",
        header: { text: "Hours", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "comments",
        header: { text: "Comments", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "fin_admin",
        header: { text: "Fin Admin", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];

function createORerWindow() {
  webix
      .ui({
        view: "window",
        id: "overtime_report-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  label: "<span class='header_font'>Overtime Report Info</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_overtime_report_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 10},
            {
              view: "template",
              template:
                  "<div style='text-align: center; color: black; font-size: 13px; font-weight: 500'>Please confirm the Date and Time for the 'Overtime Summary'</br>report and enter in the Commander, Duty Officer and select</br>the email address to send the report to!</div>",
              align: "center",
              autoheight: true,
              borderless: true,
            },
            {
              view: "form",
              id: "overtime_export_info",
              borderless: true,
              rules: {
                overtime_export_time: webix.rules.isNotEmpty,
                overtime_export_commander: webix.rules.isNotEmpty,
                overtime_export_duty_officer: webix.rules.isNotEmpty,
              },
              elements: [
                {
                  view: "datepicker",
                  id: "overtime_export_date",
                  name: "overtime_export_date",
                  label: "Date",
                  timepicker: false,
                  labelWidth: 100,
                  width: 220,
                  format: "%d/%m/%Y",
                  value: new Date(),
                  stringResult: true,
                },
                {
                  view: "combo",
                  id: "overtime_export_time",
                  name: "overtime_export_time",
                  label: "Time",
                  labelWidth: 100,
                  width: 200,
                  options: [
                    {id: "08:00", value: "08:00"},
                    {id: "18:00", value: "18:00"},
                  ],
                },
                {
                  view: "text",
                  id: "overtime_export_commander",
                  name: "overtime_export_commander",
                  label: "Commander",
                  labelWidth: 100,
                  width: 400,
                },
                {
                  view: "text",
                  id: "overtime_export_duty_officer",
                  name: "overtime_export_duty_officer",
                  label: "Duty Officer",
                  labelWidth: 100,
                  width: 400,
                },
                {
                  view: "text",
                  id: "overtime_export_send_email",
                  name: "overtime_export_send_email",
                  label: "Send Email To",
                  labelWidth: 100,
                  width: 460,
                  suggest: [
                    {id: 1, value: "<EMAIL>"},
                    {id: 2, value: "<EMAIL>"},
                    {id: 3, value: "<EMAIL>"},
                    {id: 4, value: "<EMAIL>"},
                    {
                      id: 5,
                      value: "<EMAIL>",
                    },
                    {
                      id: 6,
                      value: "<EMAIL>",
                    },
                    {
                      id: 7,
                      value: "<EMAIL>",
                    },
                    {
                      id: 8,
                      value:
                          "<EMAIL>",
                    },
                    {id: 9, value: "<EMAIL>"},
                    {id: 10, value: "<EMAIL>"},
                  ],
                },
              ],
            },
            {height: 10},
            {
              cols: [
                {},
                {
                  view: "button",
                  id: "btn_export_overtime_report",
                  label: "Generate Report",
                  width: 150,
                  height: 32,
                },
                {width: 25},
                {
                  view: "button",
                  id: "btn_export_email_overtime_report",
                  label: "Generate & Email Report",
                  width: 200,
                  height: 32,
                },
                {},
              ],
            },
            {height: 15},
          ],
        },
      })
      .hide();
}

