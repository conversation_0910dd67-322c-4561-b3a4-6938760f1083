let covidVaxSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>COVID Vaccination Summary Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      { width: 170 },
      {
        view: "template",
        id: "covid_vax_records",
        template:
          "<div style='font-size: 17px; text-align: center'>Total Vaccine Records: 0</div>",
        borderless: true,
        autoheight: true,
        width: 730,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      { width: 40 },
      {
        view: "chart",
        id: "covid_vax_summary_chart",
        type: "bar",
        value: "#qty#",
        color: "#colour#",
        label: "#qty#",
        barWidth: 120,
        width: 1e3,
        minHeight: 440,
        data: [],
        radius: 2,
        gradient: "rising",
        yAxis: {},
        xAxis: { template: "#status#" },
      },
      {},
    ],
  },
  {},
];
