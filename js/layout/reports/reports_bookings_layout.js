let bookingsReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Bookings Creation Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "bookings_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "chris_21",
        header: { text: "Chris 21", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center", "background-color": "yellow" },
      },
      {
        id: "start_date",
        header: { text: "Start Date", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "date_number",
        header: { text: "", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "sequence_code",
        header: { text: "", css: { "text-align": "center" } },
        width: 160,
        css: { "text-align": "center", "background-color": "yellow" },
      },
      {
        id: "start_time",
        header: { text: "Start Time", css: { "text-align": "center" } },
        width: 100,
        cssFormat: reportTimeHighlight,
      },
      {
        id: "end_date",
        header: { text: "End Date", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "end_time",
        header: { text: "End Time", css: { "text-align": "center" } },
        width: 100,
        cssFormat: reportTimeHighlight,
      },
      {
        id: "code",
        header: { text: "Code", css: { "text-align": "center" } },
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "days",
        header: { text: "Days", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "days_check",
        header: { text: "", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_hours",
        header: { text: "Hours", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_hours_check",
        header: { text: "", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        adjust: true,
        cssFormat: reportLocationHighlight,
      },
      {
        id: "comments",
        header: { text: "Comments", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "approved_by",
        header: { text: "Approved By", css: { "text-align": "center" } },
        width: 110,
        css: { "text-align": "center" },
      },
      {
        id: "approved_date",
        header: { text: "Approved Date", css: { "text-align": "center" } },
        width: 140,
        css: { "text-align": "center" },
      },
      {
        id: "c21_booked",
        header: { text: "C21 Booked", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center", "background-color": "#ddebf7" },
      },
      {
        id: "wfr_comments",
        header: { text: "Comments", css: { "text-align": "center" } },
        width: 200,
        css: { "text-align": "center", "background-color": "#ddebf7" },
      },
      {
        id: "wfr_e_lve",
        header: { text: "E Lve Sheets", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center", "background-color": "#ddebf7" },
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
