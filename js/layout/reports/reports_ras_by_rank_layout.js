let rosterArrangementsByRankReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Roster Arrangements By Rank Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "roster_arrangement_by_rank_grid",
    rowHeight: 30,
    headerRowHeight: 45,
    css: "availability_report",
    columns: [
      { id: "date", header: "Date", width: 90 },
      {
        id: "period",
        header: { text: "Period", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_ras",
        header: { text: "Total\nAvailable", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "co_total",
        header: "CO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_co_total",
        header: "A/CO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "dco_total",
        header: "DCO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_dco_total",
        header: "A/DCO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "acfo_total",
        header: "ACFO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_acfo_total",
        header: "A/ACFO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "cmd_total",
        header: "CMD",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_cmd_total",
        header: "A/CMD",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "man_total",
        header: "MAN",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_man_total",
        header: "A/MAN",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "so_total",
        header: "SO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_so_total",
        header: "A/SO",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "sff_ff_total",
        header: "SFF/FF",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "moff_total",
        header: "MOFF",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_moff_total",
        header: "A/MOFF",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "coff_total",
        header: "COFF",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "act_coff_total",
        header: "A/COFF",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "scop_cop_total",
        header: "SCOP/COP",
        width: 80,
        css: { "text-align": "center" },
        sort: "int",
      },
    ],
    data: [],
  },
  { height: 10 },
];
