let skillCodesAvailabilityReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Skill Codes Availability Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "date_filter",
        name: "date_filter",
        width: 170,
        label: "Date",
        labelWidth: 40,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "richselect",
        id: "rosters",
        label: "Roster",
        width: 280,
        labelWidth: 50,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift",
        label: "Shift",
        width: 320,
        labelWidth: 45,
        options: [],
      },
      { width: 20 },
      {
        view: "combo",
        id: "code_filter_1",
        name: "code_filter_1",
        label: "Code(s)",
        labelWidth: 60,
        width: 145,
        options: [],
      },
      {
        view: "combo",
        id: "code_filter_2",
        name: "code_filter_2",
        label: "",
        width: 85,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    view: "datatable",
    id: "skill_codes_availability_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        minWidth: 220,
        adjust: true,
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "roster",
        header: { text: "Roster", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "skill_codes",
        header: { text: "Skill Codes", css: { "text-align": "center" } },
        width: 120,
        adjust: true,
      },
      {
        id: "shift_period",
        header: { text: "Shift Period", css: { "text-align": "center" } },
        minWidth: 110,
        css: { "text-align": "center" },
      },
      {
        id: "temp_ra",
        header: {
          text: "Temporary Roster Arrangement",
          css: { "text-align": "center" },
        },
        minWidth: 240,
        adjust: true,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
