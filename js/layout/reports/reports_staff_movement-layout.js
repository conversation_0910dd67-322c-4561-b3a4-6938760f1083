let staffMovementReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Staff Movement Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        hidden: true,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        hidden: true,
        options: [],
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 50,
        width: 180,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 30,
        width: 160,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  { height: 25 },
  {
    view: "datatable",
    id: "staff_movement_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        adjust: "header",
        css: { "text-align": "center" },
        sort: "int",
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      {
        id: "rank",
        header: "Rank",
        width: 60,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "roster",
        header: "Roster",
        css: { "text-align": "center" },
        adjust: true,
      },
      {
        id: "shift",
        header: "Shift",
        css: { "text-align": "center" },
        adjust: true,
        sort: "string",
      },
      {
        id: "location",
        header: "Location",
        css: { "text-align": "center" },
        adjust: true,
        sort: "string",
      },
      {
        id: "start_date",
        header: "Start Date",
        width: 120,
        css: { "text-align": "center" },
        sort: "date",
      },
      {
        id: "end_date",
        header: "End Date",
        width: 120,
        css: { "text-align": "center" },
        sort: "date",
      },
      {
        id: "hours",
        header: "Hours",
        css: { "text-align": "center" },
        width: 60,
      },
      { id: "type", header: "Type", adjust: true },
      { id: "from_station_id", header: "ID", width: 40, sort: "int" },
      { id: "from", header: "From", adjust: true, sort: "string" },
      { id: "to_station_id", header: "ID", width: 40, sort: "int" },
      { id: "to", header: "To", adjust: true, sort: "string" },
      {
        id: "prior_notice",
        header: "Prior Notice",
        width: 100,
        cssFormat: highlight_yes,
        sort: "string",
      },
      {
        id: "with_ppe",
        header: "With PPE",
        width: 85,
        cssFormat: highlight_yes,
        sort: "string",
      },
      { id: "comments", header: "Comments", adjust: true, sort: "string" },
      {
        id: "created_by",
        header: { text: "Created By", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "created_date",
        header: { text: "Date Created", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
