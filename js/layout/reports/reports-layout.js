let reportsLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        id: "reports-page",
        isolate: true,
        cols: [
          {
            view: "sidebar",
            id: "reports_sidebar",
            width: 300,
            scroll: "y",
            data: [],
            on: {
              onAfterSelect: function (id) {
                routes.navigate(id, { trigger: true });
              },
            },
          },
          {
            view: "multiview",
            cells: [
              {
                id: "reporting_bookings",
                isolate: true,
                rows: bookingsReportLayout,
              },
              {
                id: "reporting_leave_taken_breakdown",
                isolate: true,
                rows: leaveTakenBreakdownReportLayout,
              },
              {
                id: "reporting_overtime",
                isolate: true,
                rows: overtimeReportLayout,
              },
              {
                id: "reporting_overtime_summary",
                isolate: true,
                rows: overtimeSummaryReportLayout,
              },
              {
                id: "reporting_staff_overtime_summary",
                isolate: true,
                rows: staffOvertimeSummaryReportLayout,
              },
              {
                id: "reporting_reason_overtime_summary",
                isolate: true,
                rows: reasonOvertimeSummaryReportLayout,
              },
              {
                id: "reporting_staff_movement",
                isolate: true,
                rows: staffMovementReportLayout,
              },
              {
                id: "reporting_travel",
                isolate: true,
                rows: travelReportLayout,
              },
              {
                id: "reporting_sickness",
                isolate: true,
                rows: sicknessReportLayout,
              },
              {
                id: "reporting_sickness_summary",
                isolate: true,
                rows: sicknessSummaryReportLayout,
              },
              {
                id: "reporting_sickness_certificate",
                isolate: true,
                rows: sicknessCertificateReportLayout,
              },
              {
                id: "reporting_supplementary",
                isolate: true,
                rows: supplementaryReportLayout,
              },
              {
                id: "reporting_deletion",
                isolate: true,
                rows: deletionReportLayout,
              },
              {
                id: "reporting_skill_codes",
                isolate: true,
                rows: skillCodesReportLayout,
              },
              {
                id: "reporting_skill_codes_availability",
                isolate: true,
                rows: skillCodesAvailabilityReportLayout,
              },
              {
                id: "reporting_roster_arrangements",
                isolate: true,
                rows: rosterArrangementsReportLayout,
              },
              {
                id: "reporting_ra_summary",
                isolate: true,
                rows: rosterArrangementsSummaryReportLayout,
              },
              {
                id: "reporting_ras_by_rank",
                isolate: true,
                rows: rosterArrangementsByRankReportLayout,
              },
              {
                id: "reporting_shift_summary",
                isolate: true,
                rows: shiftSummaryReportLayout,
              },
              { id: "reporting_rrl", isolate: true, rows: rrlReportLayout },
              {
                id: "reporting_booking_errors",
                isolate: true,
                rows: bookingErrorsReportLayout,
              },
              {
                id: "reporting_covid_vax_status",
                isolate: true,
                rows: covidVaxStatusReportLayout,
              },
              {
                id: "reporting_covid_vax_summary",
                isolate: true,
                rows: covidVaxSummaryReportLayout,
              },
              {
                id: "reporting_covid_status_summary",
                isolate: true,
                rows: covidStatusSummaryReportLayout,
              },
              {
                id: "reporting_station_preference",
                isolate: true,
                rows: stationPreferencesReportLayout,
              },
              {
                id: "reporting_overtime_fatigue",
                isolate: true,
                rows: overtimeFatigueReportLayout,
              },
              {
                id: "reporting_overtime_distance_fatigue",
                isolate: true,
                rows: overtimeFatigueDistanceReportLayout,
              },
            ],
            animate: false,
          },
        ],
      };
    },
  };
})();
