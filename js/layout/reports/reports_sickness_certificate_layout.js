let sicknessCertificateReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Sickness Certificate Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 15 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 320,
        options: [],
      },
      {},
    ],
  },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "rosters",
        label: "Roster",
        width: 280,
        labelWidth: 50,
        options: [],
      },
      { width: 15 },
      {
        view: "richselect",
        id: "shift",
        label: "Shift",
        width: 315,
        labelWidth: 40,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    view: "datatable",
    id: "sickness_certificate_grid",
    select: "row",
    rowHeight: 23,
    footer: true,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
        footer: { text: "Total Records", colspan: 2 },
        sort: "int",
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        minWidth: 240,
        adjust: true,
        sort: "string",
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 90,
        css: { "text-align": "center" },
        footer: { text: "Total SICM", colspan: 3 },
        sort: "string",
      },
      {
        id: "sic_date",
        header: { text: "Date", css: { "text-align": "center" } },
        width: 135,
        css: { "text-align": "center" },
      },
      {
        id: "sic_type",
        header: { text: "Type", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "roster",
        header: { text: "Roster", css: { "text-align": "center" } },
        minWidth: 160,
        adjust: true,
        footer: { text: "Total FAML", colspan: 2 },
        sort: "string",
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        minWidth: 160,
        adjust: true,
        sort: "string",
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        width: 260,
        footer: { text: "Total SLUP", colspan: 1 },
        sort: "string",
      },
      {
        id: "cert_provided",
        header: { text: "Certificate\nProvided", css: "multiline" },
        width: 100,
        css: { "text-align": "center" },
        footer: { text: "Cert", colspan: 1 },
        sort: "string",
      },
      {
        id: "stat_dec_provided",
        header: { text: "Stat Dec\nProvided", css: "multiline" },
        width: 100,
        css: { "text-align": "center" },
        footer: { text: "Stat Dec", colspan: 1 },
        sort: "string",
      },
    ],
    data: [],
  },
  { height: 10 },
];
