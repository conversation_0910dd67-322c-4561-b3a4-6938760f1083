let staffOvertimeSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Overtime Staff Summary Report - (Recalls)</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        hidden: true,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        hidden: true,
        options: [],
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 50,
        width: 180,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 30,
        width: 160,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 30 },
      {
        view: "checkbox",
        id: "red_fatigue_filter",
        labelRight: "RED Fatigue Only",
        width: 140,
        labelWidth: 0,
        value: 0,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  { height: 25 },
  {
    view: "datatable",
    id: "staff_overtime_summary_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        adjust: "header",
        css: { "text-align": "center" },
        sort: "int",
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      {
        id: "rank",
        header: "Current Rank",
        width: 120,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "roster",
        header: "Current Roster",
        css: { "text-align": "center" },
        minWidth: 140,
        adjust: true,
      },
      {
        id: "shift",
        header: "Current Shift",
        css: { "text-align": "center" },
        minWidth: 140,
        adjust: true,
        sort: "string",
      },
      {
        id: "location",
        header: "Current Location",
        css: { "text-align": "center" },
        minWidth: 140,
        adjust: true,
        sort: "string",
      },
      {
        id: "total_hours",
        header: "Total Hours",
        css: { "text-align": "center" },
        width: 100,
        sort: "int",
      },
    ],
    data: [],
  },
  { height: 10 },
];
