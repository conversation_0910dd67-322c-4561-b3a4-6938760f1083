let skillCodesReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Skill Codes Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 340,
        options: [],
      },
      { width: 40 },
      {
        view: "multicombo",
        id: "code_filter",
        name: "code_filter",
        label: "Code(s)",
        labelWidth: 60,
        width: 520,
        button: true,
        placeholder: "Select skill codes",
        options: { selectAll: true, data: [] },
      },
      {},
    ],
  },
  {
    cols: [
      { width: 20 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "skill_codes_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        adjust: "header",
        css: { "text-align": "center" },
        sort: "int",
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      {
        id: "rank",
        header: "Rank",
        width: 80,
        css: { "text-align": "center" },
        sort: "string",
      },
      { id: "roster", header: "Roster", adjust: true },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      { id: "code", header: "Code", width: 80, sort: "string" },
      {
        id: "description",
        header: { text: "Description", css: { "text-align": "center" } },
        width: 100,
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "group",
        header: { text: "Group", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "expiry_date",
        header: "Expiry Date",
        width: 110,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];
