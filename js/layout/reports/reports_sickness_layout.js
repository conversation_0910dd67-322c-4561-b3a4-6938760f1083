let sicknessReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Sickness Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "richselect",
        id: "rosters",
        label: "Roster",
        width: 280,
        labelWidth: 50,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift",
        label: "Shift",
        width: 320,
        labelWidth: 45,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "sickness_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "start_date",
        header: { text: "Start Date", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "start_time",
        header: { text: "Start Time", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "end_date",
        header: { text: "End Date", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "end_time",
        header: { text: "End Time", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "code",
        header: { text: "Code", css: { "text-align": "center" } },
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "total_hours",
        header: { text: "Hours", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "description",
        header: { text: "Description", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "created_by",
        header: { text: "Created By", css: { "text-align": "center" } },
        width: 110,
        css: { "text-align": "center" },
      },
      {
        id: "created_date",
        header: { text: "Created Date", css: { "text-align": "center" } },
        width: 140,
        css: { "text-align": "center" },
      },
      { id: "had_SB", header: "", hidden: true },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
