let overtimeSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Overtime Summary Report - (Recalls)</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "richselect",
        id: "reason_filter",
        name: "reason_filter",
        label: "Reason",
        labelWidth: 60,
        width: 260,
        options: [
          { id: "All", value: "-- All --" },
          { id: "Rostered Rec Leave", value: "Rostered Rec Leave" },
          { id: "Sick Leave", value: "Sick Leave" },
          { id: "Booked Leave", value: "Booked Leave" },
          { id: "Covid", value: "Covid" },
          { id: "Industrial Action", value: "Industrial Action" },
          { id: "Other Leave", value: "Other Leave" },
          { id: "Course Instructor", value: "Course Instructor" },
          { id: "Course Participant", value: "Course Participant" },
          { id: "Deployment Interstate", value: "Deployment Interstate" },
          { id: "Deployment Intrastate", value: "Deployment Intrastate" },
          { id: "Out Duty", value: "Out Duty" },
          { id: "Special Leave", value: "Special Leave" },
          { id: "Unknown Reason", value: "Unknown Reason" },
        ],
      },
      { width: 20 },
      {
        view: "checkbox",
        id: "avg_filter",
        name: "avg_filter",
        labelRight: "Show Avg. Hours Columns",
        value: 0,
        labelWidth: 20,
        width: 220,
      },
      {},
    ],
  },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    view: "datatable",
    id: "overtime_summary_grid",
    rowHeight: 30,
    headerRowHeight: 45,
    css: "availability_report",
    columns: [
      { id: "location", header: "Location", width: 170 },
      {
        id: "cmd",
        header: { text: "CMD", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "cmd_hrs",
        header: { text: "CMD\nhours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "avg_cmd",
        header: { text: "CMD\navg. hours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "so",
        header: { text: "SO", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "so_hrs",
        header: { text: "SO\nhours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "avg_so",
        header: { text: "SO\navg. hours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        sort: sortWithExclusionSO,
        hidden: true,
      },
      {
        id: "sff_ff",
        header: { text: "SFF/FF", css: { "text-align": "center" } },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "sff_ff_hrs",
        header: { text: "SFF/FF\nhours", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "avg_sff_ff",
        header: { text: "SFF/FF\navg. hours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        sort: sortWithExclusionSFF,
        hidden: true,
      },
      {
        id: "coff",
        header: { text: "COFF", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "coff_hrs",
        header: { text: "COFF\nhours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "avg_coff",
        header: { text: "COFF\navg. hours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "scop_cop",
        header: { text: "SCOP/COP", css: { "text-align": "center" } },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "scop_cop_hrs",
        header: { text: "SCOP/COP\nhours", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "avg_scop_cop",
        header: { text: "SCOP/COP\navg. hours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "moff",
        header: { text: "MOFF", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "moff_hrs",
        header: { text: "MOFF\nhours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "avg_moff",
        header: { text: "MOFF\navg. hours", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "total",
        header: { text: "TOTAL", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];
