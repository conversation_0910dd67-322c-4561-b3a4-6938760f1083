let covidVaxStatusReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>COVID Vaccination Status Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "multicombo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 50,
        width: 420,
        placeholder: "Select status",
        tagMode: false,
        value: "1,2,3,4,5,6,7,8,9",
        tagTemplate: function (values) {
          return values.length ? values.length + " status selected" : "";
        },
        options: {
          data: [
            {
              id: 1,
              value: "COMPLETE - All current vaccination requirements met",
            },
            { id: 2, value: "NOT COMPLETE - No vaccinations (Not reported)" },
            { id: 3, value: "NOT COMPLETE - No vaccinations (Reported)" },
            { id: 4, value: "NOT COMPLETE - 2nd vaccination not received" },
            { id: 5, value: "NOT COMPLETE - Waiting on 2nd vaccine" },
            { id: 6, value: "NOT COMPLETE - Booster vaccination not received" },
            { id: 7, value: "NOT COMPLETE - Waiting on vax form" },
            { id: 8, value: "NOT COMPLETE - Waiting on digital cert" },
            {
              id: 9,
              value: "NOT COMPLETE - Waiting on digital cert & vax form",
            },
          ],
          body: { yCount: 9 },
        },
      },
      { width: 20 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      {},
      {
        view: "checkbox",
        id: "show_address",
        labelRight: "Show Address Column",
        labelWidth: 0,
        width: 190,
        value: 0,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_print",
        label:
          "<span class='webix_icon fas fa-print' style='color:black'></span><span class='text'> Print</span>",
        width: 100,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "covid_vax_status_grid",
    headerRowHeight: 32,
    rowHeight: 26,
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        adjust: "header",
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "mobile_no",
        header: "Mobile Number",
        adjust: "header",
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "rank",
        header: "Rank",
        width: 80,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: "Employee Name",
        minWidth: 140,
        adjust: "data",
        sort: "string",
      },
      {
        id: "home_address",
        header: "Home Address",
        minWidth: 200,
        adjust: "data",
        sort: "string",
        hidden: true,
      },
      {
        id: "roster",
        header: "Curr Roster",
        minWidth: 140,
        adjust: "data",
        sort: "string",
      },
      {
        id: "shift",
        header: "Curr Shift",
        minWidth: 140,
        adjust: "data",
        sort: "string",
      },
      {
        id: "location",
        header: "Curr Location",
        minWidth: 140,
        adjust: "data",
        sort: "string",
      },
      {
        id: "first_dose_date",
        header: "1st Dose Date",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "second_dose_date",
        header: "2nd Dose Date",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "booster_date",
        header: "Booster Date",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "status",
        header: "Status",
        minWidth: 120,
        adjust: "data",
        sort: "string",
      },
      {
        id: "exemption_date",
        header: "Exempt Expiry",
        width: 120,
        css: { "text-align": "center" },
        format: webix.Date.dateToStr("%d/%m/%Y"),
        sort: "date",
      },
      {
        id: "cert_received",
        header: "Cert. Received",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "form_received",
        header: "Form Received",
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "verified",
        header: "Verified",
        width: 80,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];
