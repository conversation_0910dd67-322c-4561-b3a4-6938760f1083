let bookingErrorsReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Bookings Errors Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  { height: 20 },
  {
    view: "datatable",
    id: "booking_errors_grid",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
        sort: "string",
      },
      {
        id: "ra_rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "roster",
        header: { text: "BK Roster", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        sort: "string",
      },
      {
        id: "shift",
        header: { text: "BK Shift", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        sort: "string",
      },
      {
        id: "location",
        header: { text: "BK Location", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        sort: "string",
      },
      {
        id: "leave_code",
        header: { text: "Code", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "ra_roster",
        header: { text: "RA Roster", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        sort: "string",
      },
      {
        id: "ra_shift",
        header: { text: "RA Shift", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        sort: "string",
      },
      {
        id: "ra_location",
        header: { text: "RA Location", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
        sort: "string",
      },
      {
        id: "start_date",
        header: { text: "Start Date", css: { "text-align": "center" } },
        width: 130,
        css: { "text-align": "center" },
        sort: "date",
      },
      {
        id: "end_date",
        header: { text: "End Date", css: { "text-align": "center" } },
        width: 130,
        css: { "text-align": "center" },
        sort: "date",
      },
      {
        id: "total_hours",
        header: { text: "Hours", css: { "text-align": "center" } },
        width: 70,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "comments",
        header: { text: "Comments", css: { "text-align": "center" } },
        adjust: true,
        sort: "string",
      },
      {
        id: "created_by",
        header: { text: "Created By", css: { "text-align": "center" } },
        width: 110,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "created_date",
        header: { text: "Created Date", css: { "text-align": "center" } },
        width: 140,
        css: { "text-align": "center" },
        sort: "date",
      },
    ],
    data: [],
  },
  { height: 10 },
];
