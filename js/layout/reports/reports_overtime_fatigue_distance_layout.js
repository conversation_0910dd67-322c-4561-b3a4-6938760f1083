let overtimeFatigueDistanceReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Overtime & Fatigue - Station Shortage by Distance Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 15 },
      {
        view: "datepicker",
        id: "date_filter",
        name: "date_filter",
        width: 185,
        label: "Date",
        labelWidth: 60,
        labelAlign: "right",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 30 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      {},
    ],
  },
  {
    cols: [
      { width: 15 },
      {
        view: "multicombo",
        id: "rank_filter",
        name: "rank_filter",
        label: "Rank(s)",
        labelWidth: 60,
        width: 300,
        button: true,
        placeholder: "Select ranks",
        tagMode: false,
        tagTemplate: function (values) {
          return values.length ? values.length + " rank(s) selected" : "";
        },
        options: {
          selectAll: true,
          data: [
            { id: "CMD", value: "CMD" },
            { id: "SO", value: "SO" },
            { id: "SFF", value: "SFF" },
            { id: "FF", value: "FF" },
            { id: "SCOP", value: "SCOP" },
            { id: "COP", value: "COP" },
          ],
        },
      },
      { width: 30 },
      {
        view: "multicombo",
        id: "code_filter",
        name: "code_filter",
        label: "Code(s)",
        labelWidth: 60,
        width: 300,
        button: true,
        placeholder: "Select skill codes",
        tagMode: false,
        tagTemplate: function (values) {
          return values.length ? values.length + " skill(s) selected" : "";
        },
        options: { selectAll: true, data: [] },
      },
      { width: 30 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "overtime_fatigue_distance_grid",
    headerRowHeight: 43,
    rowHeight: 23,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        width: 100,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "rank",
        header: "Rank",
        width: 70,
        css: { "text-align": "center" },
        sort: "string",
      },
      { id: "name", header: "Employee", width: 240, sort: "string" },
      { id: "skill_codes", header: "Skill Codes", minWidth: 120, adjust: true },
      {
        id: "kms_home_station",
        header: { text: "Distance From<br>Adelaide Station", css: "multiline" },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "kms_rel_station",
        header: { text: "Distance To<br>Relieving Station", css: "multiline" },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "kms_difference",
        header: { text: "Difference<br>(kms)", css: "multiline" },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "current_station",
        header: { text: "Current<br>Station", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "home_suburb",
        header: "Home Suburb",
        width: 180,
        css: { "text-align": "center" },
      },
      {
        id: "stat_1_pref",
        header: { text: "Station 1<br>Preference", css: "multiline" },
        width: 140,
      },
      {
        id: "stat_2_pref",
        header: { text: "Station 2<br>Preference", css: "multiline" },
        width: 140,
      },
      {
        id: "stat_3_pref",
        header: { text: "Station 3<br>Preference", css: "multiline" },
        width: 140,
      },
    ],
    data: [],
  },
  { height: 10 },
];
