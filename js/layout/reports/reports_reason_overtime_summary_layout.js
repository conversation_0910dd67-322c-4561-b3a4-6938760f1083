let reasonOvertimeSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label:
          "<span class='header_font'>Overtime Reason Summary Report - (Recalls & OTFC)</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from_date",
        name: "from_date",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        name: "to_date",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 30 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "rank_filter",
        name: "rank_filter",
        label: "Rank",
        labelWidth: 40,
        width: 130,
        options: [
          { id: "ALL", value: "-- All --" },
          { id: "CMD", value: "CMD" },
          { id: "SO", value: "SO" },
          { id: "SFF", value: "SFF" },
          { id: "FF", value: "FF" },
          { id: "MOFF", value: "MOFF" },
        ],
      },
      { width: 40 },
      {
        view: "radio",
        id: "type_filter",
        label: "Type",
        options: ["RC", "OTFC"],
        value: "RC",
        width: 190,
        labelWidth: 40,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    view: "datatable",
    id: "reason_overtime_summary_grid",
    rowHeight: 30,
    headerRowHeight: 45,
    css: "availability_report",
    columns: [
      { id: "date", header: "Date", width: 90 },
      {
        id: "rrl",
        header: { text: "RRL", css: { "text-align": "center" } },
        width: 65,
        css: { "text-align": "center" },
      },
      {
        id: "sick_leave",
        header: { text: "Sick\nLeave", css: "multiline" },
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "booked_leave",
        header: { text: "Booked\nLeave", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "covid",
        header: { text: "Covid", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "industrial_action",
        header: { text: "Industrial\nAction", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "other_leave",
        header: { text: "Other\nLeave", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "course_instructor",
        header: { text: "Course\nInstructor", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "course_participant",
        header: { text: "Course\nParticipant", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "deployment_interstate",
        header: { text: "Deployment\nInterstate", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "deployment_intrastate",
        header: { text: "Deployment\nIntrastate", css: "multiline" },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "out_duty",
        header: { text: "Out\nDuty", css: "multiline" },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "special_leave",
        header: { text: "Special\nLeave", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "unknown_reason",
        header: { text: "Unknown\nReason", css: "multiline" },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "day_total",
        header: { text: "TOTAL (Qty)", css: { "text-align": "center" } },
        width: 95,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];
