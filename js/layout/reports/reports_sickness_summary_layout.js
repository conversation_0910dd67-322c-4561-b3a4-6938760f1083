let sicknessSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Sickness Summary Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 15 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 35 },
      { view: "label", label: "OR", css: "report_label", width: 30 },
      { width: 30 },
      {
        view: "checkbox",
        id: "use_anniversary_date",
        name: "use_anniversary_date",
        label: "Anniversary to Date",
        labelWidth: 130,
        width: 160,
      },
      {},
    ],
  },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 320,
        options: [],
      },
      { width: 15 },
      {
        view: "richselect",
        id: "rosters",
        label: "Roster",
        width: 280,
        labelWidth: 50,
        options: [],
      },
      { width: 15 },
      {
        view: "richselect",
        id: "shift",
        label: "Shift",
        width: 315,
        labelWidth: 40,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      {},
      {
        view: "label",
        label:
          "<div style='color: red'>Note: You can click in the 'TOTAL' column of each entry to view a breakdown of the sick bookings!</div>",
        width: 610,
        align: "right",
      },
      { width: 60 },
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
    ],
  },
  {
    view: "datatable",
    id: "sickness_summary_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    select: "row",
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "period",
        header: { text: "Date Period", css: { "text-align": "center" } },
        width: 165,
        css: { "text-align": "center" },
      },
      {
        id: "days",
        header: { text: "Days", css: { "text-align": "center" } },
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "roster",
        header: { text: "Roster", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "total_BERE",
        header: { text: "BERE", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "total_CVPL",
        header: { text: "CVPL", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "total_CVUL",
        header: { text: "CVUL", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "total_FAML",
        header: { text: "FAML", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "total_SIC",
        header: { text: "SIC", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "total_SICM",
        header: { text: "SICM", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "total_SLUP",
        header: { text: "SLUP", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "total_SLUW",
        header: { text: "SLUW", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_WRSL",
        header: { text: "WRSL", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_CVSL",
        header: { text: "CVSL", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_CVFL",
        header: { text: "CVFL", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_CVIL",
        header: { text: "CVIL", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_CVWR",
        header: { text: "CVWR", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "total_ALL",
        header: { text: "TOTAL", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  { height: 10 },
];

function createRSWindow() {
  webix
      .ui({
        view: "window",
        id: "sickness-breakdown-window",
        modal: true,
        position: "center",
        fullscreen: false,
        height: 750,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "sickness-breakdown-label",
                  label: "<span class='header_font'>Booking Details</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_sickness-breakdown_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              view: "template",
              id: "sickness-breakdown_details",
              template: "",
              autoheight: true,
              borderless: true,
              css: {
                "text-align": "center",
                color: "black",
                "font-size": "17px !important",
              },
            },
            {
              cols: [
                {width: 310},
                {
                  view: "template",
                  template: "<div class='stat_dec_box'>XXXX</div>",
                  borderless: true,
                  width: 80,
                },
                {
                  view: "label",
                  label: "= statutory declaration was used",
                  width: 200,
                  css: "stat_dec_label",
                },
                {},
                {
                  view: "button",
                  id: "btn_export_excel",
                  label:
                      "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
                  width: 120,
                  align: "right",
                },
                {width: 5},
              ],
            },
            {
              view: "datatable",
              id: "sickness-breakdown_grid",
              select: "row",
              height: 510,
              autowidth: true,
              columns: [
                {id: "booking_id", hidden: true},
                {
                  id: "code",
                  header: "Type",
                  width: 70,
                  css: {"text-align": "center"},
                  sort: "string",
                },
                {
                  id: "start_date",
                  header: "Sickness Date",
                  width: 140,
                  css: {"text-align": "center"},
                  sort: "date",
                },
                {
                  id: "roster",
                  header: "Roster",
                  minWidth: 90,
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "shift",
                  header: "Shift",
                  minWidth: 90,
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "location",
                  header: "Location",
                  minWidth: 90,
                  adjust: true,
                  sort: "string",
                },
                {
                  id: "period",
                  header: "Period",
                  width: 70,
                  css: {"text-align": "center"},
                  sort: "int",
                },
                {
                  id: "hours",
                  header: "Hours",
                  width: 70,
                  css: {"text-align": "center"},
                  sort: "int",
                },
                {
                  id: "created_by",
                  header: "Created By",
                  width: 100,
                  css: {"text-align": "center"},
                  sort: "string",
                },
                {
                  id: "created_date",
                  header: "Created Date",
                  width: 140,
                  css: {"text-align": "center"},
                  sort: "date",
                },
                {id: "stat_dec_used", header: "", hidden: true},
              ],
              data: [],
            },
          ],
        },
      })
      .hide();
}
