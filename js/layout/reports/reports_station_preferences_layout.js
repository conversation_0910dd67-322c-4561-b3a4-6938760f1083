let stationPreferencesReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Station Preferences Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 380,
        options: [],
      },
      { width: 40 },
      {
        view: "richselect",
        id: "pref_filter",
        name: "pref_filter",
        label: "Preference",
        labelWidth: 80,
        width: 180,
        value: 99,
        options: [
          { id: 99, value: "-- All --" },
          { id: 1, value: "1st" },
          { id: 2, value: "2nd" },
          { id: 3, value: "3rd" },
        ],
      },
      {},
    ],
  },
  {
    cols: [
      { width: 20 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 45,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 20 },
  {
    cols: [
      { width: 50 },
      {
        view: "label",
        label:
          "<div style='color: red'>Note: The information contained in this report should be used as a guide only as the report may not always be accurate until the information is approved and entered into CHRIS 21</div>",
        width: 1080,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "station_preferences_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      {
        id: "pay_id",
        header: "Service No.",
        adjust: "header",
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "name",
        header: "Employee",
        minWidth: 200,
        adjust: "data",
        sort: "string",
      },
      {
        id: "rank",
        header: "Rank",
        width: 80,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "pref_no",
        header: "Preference",
        width: 100,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "roster",
        header: "Roster",
        minWidth: 140,
        adjust: "data",
        sort: "string",
      },
      {
        id: "shift",
        header: "Shift",
        minWidth: 140,
        adjust: "data",
        sort: "string",
      },
      {
        id: "location",
        header: "Location",
        minWidth: 140,
        adjust: "data",
        sort: "string",
      },
      {
        id: "updated_date",
        header: "Entered Date",
        width: 125,
        css: { "text-align": "center" },
        sort: "date",
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
      },
      { id: "sort_index", header: "", sort: "int", hidden: true },
    ],
    data: [],
  },
  { height: 10 },
];
