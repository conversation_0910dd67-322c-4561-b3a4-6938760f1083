let travelReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Travel Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 17 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 40 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 40 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 340,
        options: [],
      },
      {},
    ],
  },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 52,
        width: 160,
        options: [
          { id: 1, value: "All" },
          { id: 2, value: "Pending" },
          { id: 3, value: "Approved" },
        ],
      },
      { width: 50 },
      {
        view: "checkbox",
        id: "show_exported",
        name: "show_exported",
        label: "Show Exported",
        value: 0,
        labelWidth: 100,
        width: 130,
      },
      { width: 50 },
      {
        view: "checkbox",
        id: "show_negative",
        name: "show_negative",
        label: "Show Zero & Negative Distances",
        value: 0,
        labelWidth: 210,
        width: 240,
      },
      { width: 120 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "travel_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      { id: "db_id", header: "", hidden: true },
      { id: "travel_id", header: "", hidden: true },
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "roster",
        header: { text: "Roster", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "route",
        header: { text: "Route", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "travel_date",
        header: { text: "Travel Date", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      { id: "date_string", header: "Date String", hidden: true },
      {
        id: "distance",
        header: { text: "Distance", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "time_allowance",
        header: { text: "Time Allowance", css: { "text-align": "center" } },
        width: 125,
        css: { "text-align": "center" },
      },
      {
        id: "bk_code",
        header: { text: "Code", css: { "text-align": "center" } },
        minWidth: 80,
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "bk_desc",
        header: { text: "Description", css: { "text-align": "center" } },
        minWidth: 120,
        adjust: true,
      },
      {
        id: "time",
        header: { text: "Time", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "meals",
        header: { text: "Meals", css: { "text-align": "center" } },
        width: 80,
        cssFormat: travelMeals,
      },
      {
        id: "status",
        header: { text: "Status", css: { "text-align": "center" } },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "created_by",
        header: { text: "Created By", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "created_date",
        header: { text: "Date Created", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "equipment",
        header: { text: "Equipment", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "approved_by",
        header: { text: "Approved By", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "approved_denied_date",
        header: { text: "Date Approved", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "exported",
        header: { text: "Exported", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "cost_centre",
        header: { text: "Cost Centre", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
        hidden: true,
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
