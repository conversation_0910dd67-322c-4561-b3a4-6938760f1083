let supplementaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Supplementary Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    cols: [
      { width: 5 },
      {
        view: "datatable",
        id: "supplementary_grid",
        rowHeight: 22,
        headerRowHeight: 45,
        css: "supplementary_report_styling",
        columns: [
          {
            id: "pay_id",
            header: [
              "PAY ID",
              {
                text: "SUPPLEMENTARY",
                colspan: 13,
                css: "supplementary_report_header_bottom",
              },
            ],
            width: 70,
          },
          { id: "employee", header: "NAME", width: 140 },
          { id: "rank", header: "RANK", width: 60 },
          { id: "reason", header: "REASON", width: 70 },
          { id: "stn", header: "STN", width: 60 },
          {
            id: "higher_duties",
            header: { text: "HIGHER\nDUTY", css: "multiline" },
            width: 70,
          },
          {
            id: "left_from",
            header: { text: "LEFT\nFROM", css: "multiline" },
            width: 70,
          },
          {
            id: "gear_taken",
            header: { text: "GEAR\nTAKEN", css: "multiline" },
            width: 60,
          },
          {
            id: "booked_on",
            header: { text: "BOOKED\nON", css: "multiline" },
            width: 70,
          },
          { id: "on", header: "ON", width: 60 },
          { id: "off", header: "OFF", width: 60 },
          { id: "total", header: "TOTAL", width: 60 },
          { id: "fin_ad", header: "FIN.AD.", width: 70 },
        ],
        data: [],
      },
    ],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];


function createSRepWindow() {
  webix
      .ui({
        view: "window",
        id: "supplementary_report-popup",
        modal: true,
        position: "center",
        fullscreen: false,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  label:
                      "<span class='header_font'>Supplementary Report Info</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_supplementary_report_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 10},
            {
              view: "template",
              template:
                  "<div style='text-align: center; color: black; font-size: 13px; font-weight: 500'>Please confirm the Date for the Supplementary Report</br>and also enter in the 'Shift' and 'Commander'.</div>",
              align: "center",
              autoheight: true,
              borderless: true,
            },
            {
              view: "form",
              id: "supplementary_export_info",
              borderless: true,
              elements: [
                {
                  view: "datepicker",
                  id: "supplementary_export_date",
                  name: "supplementary_export_date",
                  label: "Date",
                  timepicker: false,
                  labelWidth: 90,
                  width: 220,
                  format: "%d/%m/%Y",
                  value: new Date(),
                  stringResult: true,
                },
                {
                  view: "richselect",
                  id: "supplementary_export_shift",
                  name: "supplementary_export_shift",
                  label: "Shift",
                  width: 280,
                  labelWidth: 90,
                  options: [
                    {id: 1, value: "A Shift"},
                    {id: 2, value: "B Shift"},
                    {id: 3, value: "C Shift"},
                    {id: 4, value: "D Shift"},
                    {id: 5, value: "Port Pirie A Shift"},
                    {id: 6, value: "Port Pirie B Shift"},
                    {id: 7, value: "Port Pirie C Shift"},
                    {id: 8, value: "Port Pirie D Shift"},
                    {id: 9, value: "Comms E1"},
                    {id: 10, value: "Comms E2"},
                    {id: 11, value: "Mount Gambier MG1"},
                    {id: 12, value: "Mount Gambier MG2"},
                    {id: 13, value: "OTR"},
                  ],
                },
                {
                  view: "text",
                  id: "supplementary_export_commander",
                  name: "supplementary_export_commander",
                  label: "Commander",
                  labelWidth: 90,
                  width: 400,
                },
              ],
            },
            {height: 15},
            {
              view: "button",
              id: "btn_supplementary_generate",
              label: "Generate",
              width: 120,
              align: "center",
            },
          ],
        },
      })
      .hide();
}
