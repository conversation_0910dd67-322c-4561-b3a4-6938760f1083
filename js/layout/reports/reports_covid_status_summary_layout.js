let covidStatusSummaryReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>COVID Status Summary Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      { width: 170 },
      {
        view: "template",
        id: "covid_status_records",
        template:
          "<div style='font-size: 17px; text-align: center'>Total Vaccine Records: 0</div>",
        borderless: true,
        autoheight: true,
        width: 730,
      },
      { width: 50 },
      {
        rows: [
          {
            view: "button",
            id: "btn_search",
            type: "icon",
            icon: "fas fa-search",
            label: "Search",
            align: "center",
            width: 100,
          },
        ],
      },
      {},
    ],
  },
  { height: 15 },
  {
    cols: [
      { width: 40 },
      {
        view: "chart",
        id: "covid_status_summary_chart",
        type: "barH",
        value: "#qty#",
        color: "#colour#",
        label: "#qty#",
        barWidth: 120,
        width: 1280,
        minHeight: 440,
        data: [],
        radius: 2,
        gradient: "rising",
        xAxis: {},
        yAxis: { template: "#status#" },
        padding: { left: 300, right: 60 },
      },
      {},
    ],
  },
  {},
];
