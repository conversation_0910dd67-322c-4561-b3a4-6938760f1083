let leaveTakenBreakdownReportLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Actual Leave Taken Report</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 410,
        options: [],
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 45,
        width: 170,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 25,
        width: 150,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 30 },
      {
        view: "radio",
        id: "display_filter",
        label: "Type",
        labelWidth: 40,
        width: 260,
        options: ["Summary", "Breakdown"],
        value: "Summary",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "richselect",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "shift_filter",
        name: "shift_filter",
        label: "Shift",
        labelWidth: 40,
        width: 320,
        options: [],
      },
      { width: 20 },
      {
        view: "richselect",
        id: "location_filter",
        name: "location_filter",
        label: "Location",
        labelWidth: 70,
        width: 330,
        options: [],
      },
      { width: 20 },
      {
        view: "multicombo",
        id: "leave_filter",
        name: "leave_filter",
        label: "Leave",
        labelWidth: 50,
        width: 240,
        button: true,
        placeholder: "Select leave type(s)",
        tagMode: false,
        tagTemplate: function (values) {
          return values.length ? values.length + " type(s) selected" : "";
        },
        options: { selectAll: true, data: [] },
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "leave_taken_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    columns: [
      { id: "booking_id", hidden: true },
      {
        id: "service_no",
        header: "Service No",
        width: 95,
        sort: "int",
        css: { "text-align": "center" },
      },
      { id: "name", header: "Employee", adjust: true, sort: "string" },
      { id: "rank", header: "Rank", adjust: true, sort: "string" },
      { id: "roster", header: "Roster", adjust: true },
      { id: "shift", header: "Shift", adjust: true, sort: "string" },
      { id: "location", header: "Location", adjust: true, sort: "string" },
      {
        id: "booking_first_date",
        header: "Start Date",
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "booking_last_date",
        header: "End Date",
        width: 130,
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "total_days",
        header: "Days",
        width: 65,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "total_hours",
        header: "Hours",
        width: 65,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "code",
        header: "Code",
        width: 60,
        sort: "string",
        css: { "text-align": "center" },
      },
      {
        id: "date_created",
        header: "Created Date",
        width: 130,
        css: { "text-align": "center" },
      },
      {
        id: "created_by",
        header: "Created By",
        width: 95,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "approved_denied_date",
        header: "Approved Date",
        width: 130,
        css: { "text-align": "center" },
      },
      {
        id: "approved_denied_by",
        header: "Approved By",
        width: 105,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "comments",
        header: "Comments",
        minWidth: 320,
        adjust: true,
        sort: "string",
      },
    ],
    data: [],
  },
];
