let createTravelLayout = [
    {
        view: "toolbar",
        id: "header",
        css: "main_header",
        cols: [
            {
                view: "label",
                label: "<span class='header_font'>Create Travel Claim(s) Form</span>",
                align: "left",
            },
            {},
        ],
    },
    {height: 10},
    {
        cols: [
            {
                view: "form",
                id: "formTravel",
                borderless: true,
                elements: [
                    {
                        cols: [
                            {
                                view: "combo",
                                id: "employees",
                                name: "employees",
                                label: "Employee",
                                width: 380,
                                labelWidth: 80,
                                options: [],
                                readonly: true,
                            },
                            {width: 30},
                            {
                                view: "text",
                                id: "employees_id",
                                name: "employees_id",
                                label: "Pay ID",
                                value: "2301234",
                                labelWidth: 50,
                                width: 140,
                                readonly: true,
                            },
                            {width: 30},
                            {
                                view: "text",
                                id: "employees_address",
                                name: "employees_address",
                                label: "Home Address",
                                labelWidth: 100,
                                width: 440,
                                value: "",
                                readonly: true
                            },
                            {width: 30},
                            {
                                view: "text",
                                id: "res_to_hs_dist",
                                name: "res_to_hs_dist",
                                label: "Res to HS",
                                width: 130,
                                labelWidth: 70,
                                value: "0",
                                inputAlign: "center",
                                readonly: true,
                            },
                            {view: "label", label: "Kms", width: 30, disabled: true},
                        ],
                    },
                    {
                        cols: [
                            {
                                view: "datepicker",
                                id: "travel_date",
                                name: "travel_date",
                                width: 230,
                                label: "Travel Date",
                                labelWidth: 80,
                                labelAlign: "left",
                                value: new Date(),
                                format: "%D, %d/%m/%Y",
                                stringResult: true,
                                timepicker: false,
                            },
                            {width: 30},
                            {
                                view: "combo",
                                id: "travel_reason",
                                name: "travel_reason",
                                label: "Reason",
                                labelWidth: 60,
                                width: 180,
                                options: [
                                    {id: 0, value: "Relief"},
                                    {id: 1, value: "Recall"},
                                    {id: 2, value: "Temp Transfer"},
                                ],
                            },
                            {width: 30},
                            {
                                view: "combo",
                                id: "travel_rel_stat",
                                name: "travel_rel_stat",
                                label: "To Station",
                                labelWidth: 100,
                                width: 250,
                                options: []
                            },
                            {width: 30},
                            {
                                view: "checkbox",
                                id: "acting_up",
                                name: "acting_up",
                                label: "Act Up",
                                value: 0,
                                labelWidth: 60,
                                width: 100
                            },
                        ],
                    },
                    {
                        cols: [
                            {
                                view: "checkbox",
                                id: "travel_with_ppe_1",
                                name: "travel_with_ppe_1",
                                label: "Travel With PPE",
                                value: 0,
                                labelWidth: 110,
                                width: 140
                            },
                            {width: 30},
                            {
                                view: "checkbox",
                                id: "prior_notice_1",
                                name: "prior_notice_1",
                                label: "Prior Notice Given",
                                value: 0,
                                labelWidth: 120,
                                width: 150
                            },
                            {width: 30},
                            {
                                view: "combo",
                                id: "route_from_1",
                                name: "route_from_1",
                                label: "Route From",
                                width: 230,
                                labelWidth: 80,
                                options: []
                            },
                            {width: 30},
                            {
                                view: "combo",
                                id: "route_to_1",
                                name: "route_to_1",
                                label: "Route To",
                                width: 220,
                                labelWidth: 70,
                                options: []
                            },
                            {width: 30},
                            {
                                view: "text",
                                id: "kms_1",
                                name: "kms_1",
                                label: "Kms",
                                width: 100,
                                labelWidth: 50,
                                readonly: true
                            },
                            {width: 30},
                            {
                                view: "text",
                                id: "travel_time_1",
                                name: "travel_time_1",
                                label: "Travel Time",
                                width: 120,
                                labelWidth: 160
                            },
                            {width: 30},
                            {
                                view: "text",
                                id: "comments",
                                name: "comments",
                                label: "Comments",
                                labelWidth: 80,
                                width: 400,
                            },
                        ],
                    },
                    {
                        cols: [
                            {
                                view: "checkbox",
                                id: "travel_confirm",
                                name: "travel_confirm",
                                label:
                                    "I certify that I am entitled for reimbursement for this travel in accordance the Firefighters Award 2007",
                                value: 0,
                                labelWidth: 740,
                                width: 770,
                            },
                            {width: 30},
                            {
                                view: "button",
                                id: "btn_save",
                                value: "Save",
                                width: 100,
                                disabled: true,
                            }
                        ]
                    }
                ],
                rules: {
                    employees: webix.rules.isNotEmpty,
                    route_to: webix.rules.isNotEmpty,
                    route_from: webix.rules.isNotEmpty,
                },
            },
            {},
        ],
    },
    {height: 10}
];
