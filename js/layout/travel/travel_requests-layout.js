let travelRequestsLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Travel Claims</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 30 },
      {
        view: "combo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 55,
        width: 160,
        options: [
          { id: 1, value: "All" },
          { id: 2, value: "Pending" },
          { id: 3, value: "Denied" },
          { id: 4, value: "Approved" },
        ],
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "from_date",
        label: "From",
        timepicker: false,
        labelWidth: 45,
        width: 170,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 30 },
      {
        view: "datepicker",
        id: "to_date",
        label: "To",
        timepicker: false,
        labelWidth: 25,
        width: 155,
        format: "%d/%m/%Y",
        value: new Date(),
        stringResult: true,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
      {
        view: "button",
        id: "btn_approve_claims",
        type: "icon",
        icon: "fas fa-thumbs-up",
        label: "Approve Claims",
        width: 150,
      },
      {},
      {
        view: "button",
        id: "btn_bulk_delete",
        type: "icon",
        icon: "fas fa-trash",
        label: "Bulk Delete",
        width: 130,
      },
      {},
      {
        view: "button",
        id: "btn_show_duplicates",
        type: "icon",
        icon: "fas fa-clone",
        label: "Show Duplicates",
        width: 150,
      },
      { width: 10 },
    ],
  },
  { height: 30 },
  {
    view: "datatable",
    id: "travel-requests_grid",
    select: "row",
    scroll: "xy",
    columns: [
      { id: "db_id", hidden: true },
      {
        id: "select",
        header: { content: "myMasterCheckbox", contentId: "master_select" },
        template: "{common.checkbox()}",
        width: 40,
      },
      { id: "pay_id", header: "Pay ID", width: 70, sort: "int" },
      { id: "employee", header: "Employee", width: 200, sort: "string" },
      {
        id: "rank",
        header: "Rank",
        width: 60,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "shift",
        header: "Shift",
        minWidth: 60,
        adjust: true,
        sort: "string",
      },
      {
        id: "travel_date",
        header: "Travel Date",
        width: 100,
        format: webix.Date.dateToStr("%d/%m/%Y"),
        sort: "date",
      },
      {
        id: "route",
        header: "Route",
        width: 65,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "distance",
        header: "Distance",
        width: 75,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "prior_notice",
        header: "Prior Notice",
        width: 100,
        css: { "text-align": "center" },
        sort: "string",
      },
      {
        id: "with_ppe",
        header: "With PPE",
        width: 85,
        css: { "text-align": "center" },
        sort: "string",
      },
      { id: "type", header: "Type", hidden: true },
      {
        id: "request_comments",
        header: "Comments",
        width: 160,
        css: { "text-align": "center" },
      },
      {
        id: "created_by",
        header: "Created By",
        adjust: "header",
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "created_date",
        header: "Date Created",
        width: 125,
        css: { "text-align": "center" },
        format: webix.Date.dateToStr("%d/%m/%Y %H:%i"),
        sort: "date",
      },
      {
        id: "status",
        header: "Status",
        width: 65,
        css: { "text-align": "center" },
      },
      {
        id: "edit",
        header: "",
        css: { "text-align": "center" },
        template: "<span class = 'webix_icon fas fa-edit'></span>",
        width: 50,
      },
      {
        id: "delete",
        header: "",
        css: { "text-align": "center" },
        template: "<span class = 'webix_icon fas fa-trash'></span>",
        width: 50,
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "search_count",
    template: "",
    borderless: true,
    autoheight: true,
    css: { "font-style": "italic" },
  },
  { height: 40 },
  {
    view: "template",
    id: "selected_request",
    template: "Selected Travel Claim: None",
    borderless: true,
    height: 50,
  },
  { view: "text", id: "request_id", label: "", hidden: true },
  {
    cols: [
      { width: 30 },
      {
        view: "text",
        id: "request_comments",
        label: "Comments",
        labelWidth: 90,
        width: 600,
      },
      { width: 10 },
      {
        rows: [
          {},
          { view: "button", id: "btn_approve", label: "Approve", width: 100 },
        ],
      },
      { width: 10 },
      {
        rows: [
          {},
          { view: "button", id: "btn_deny", label: "Deny", width: 100 },
        ],
      },
      {},
    ],
  },
  { height: 30 },
];
webix.ui.datafilter.myMasterCheckbox = webix.extend(
  {
    refresh: function (master, node, config) {
      node.onclick = function () {
        this.getElementsByTagName("input")[0].checked = config.checked =
          !config.checked;
        let column = master.getColumnConfig(config.columnId);
        let checked = config.checked ? column.checkValue : column.uncheckValue;
        let x = 0;
        master.data.each(function (obj) {
          x += 1;
          if (x <= 16) obj[config.columnId] = checked;
          master.callEvent("onCheck", [obj.id, config.columnId, checked]);
          this.callEvent("onStoreUpdated", [obj.id, obj, "save"]);
        });
        master.refresh();
      };
    },
  },
  webix.ui.datafilter.masterCheckbox,
);

function createTLogsWindow() {
  webix
      .ui({
        view: "window",
        id: "travel-logs-window",
        modal: true,
        position: "center",
        fullscreen: false,
        width: 700,
        height: 600,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "travel-logs-label",
                  label: "<span class='header_font'>Edit Travel Claim</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_travel-logs_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {
              view: "form",
              id: "travel_log_entries",
              height: 520,
              elements: [
                {
                  view: "text",
                  id: "travel_log_travel_id",
                  name: "travel_log_travel_id",
                  label: "Travel ID",
                  width: 200,
                  labelWidth: 80,
                  hidden: true,
                },
                {
                  view: "text",
                  id: "travel_log_pay_id",
                  name: "travel_log_pay_id",
                  label: "Pay ID",
                  width: 200,
                  labelWidth: 80,
                  disabled: true,
                },
                {
                  view: "text",
                  id: "travel_log_employee",
                  name: "travel_log_employee",
                  label: "Employee",
                  width: 380,
                  labelWidth: 80,
                  disabled: true,
                },
                {
                  view: "datepicker",
                  id: "travel_log_datepicker",
                  name: "travel_log_datepicker",
                  width: 230,
                  label: "Travel Date",
                  labelWidth: 80,
                  labelAlign: "left",
                  value: new Date(),
                  format: "%D, %d/%m/%Y",
                  stringResult: true,
                  timepicker: false,
                },
                {
                  view: "combo",
                  id: "travel_log_route_from",
                  name: "travel_log_route_from",
                  label: "Route From",
                  width: 380,
                  labelWidth: 80,
                  options: [],
                },
                {
                  view: "combo",
                  id: "travel_log_route_to",
                  name: "travel_log_route_to",
                  label: "Route To",
                  width: 380,
                  labelWidth: 80,
                  options: [],
                },
                {
                  cols: [
                    {
                      view: "text",
                      id: "travel_log_distance",
                      name: "travel_log_distance",
                      label: "Distance",
                      width: 170,
                      labelWidth: 80,
                    },
                    {width: 15},
                    {
                      view: "button",
                      id: "travel_log_btn_calculate",
                      value: "Auto Calculate",
                      width: 130,
                    },
                  ],
                },
                {
                  view: "checkbox",
                  id: "travel_log_prior_notice",
                  name: "travel_log_prior_notice",
                  label: "Prior Notice Given",
                  value: 0,
                  labelWidth: 120,
                  align: "center",
                },
                {
                  view: "checkbox",
                  id: "travel_log_with_ppe",
                  name: "travel_log_with_ppe",
                  label: "Travel With PPE",
                  value: 0,
                  labelWidth: 110,
                  align: "center",
                },
                {
                  view: "text",
                  id: "travel_log_comments",
                  name: "travel_log_comments",
                  label: "Comments",
                  labelWidth: 80,
                  width: 400,
                },
                {
                  view: "text",
                  id: "travel_log_status",
                  name: "travel_log_status",
                  label: "Status",
                  width: 300,
                  labelWidth: 80,
                  disabled: true,
                },
                {
                  view: "button",
                  id: "travel_log_btn_save",
                  value: "Update",
                  width: 100,
                  align: "right",
                },
              ],
              rules: {
                travel_log_status: webix.rules.isNotEmpty,
                route_to: webix.rules.isNotEmpty,
                route_from: webix.rules.isNotEmpty,
              },
            },
            {height: 5},
          ],
        },
      })
      .hide();
}


function createTDupsWindow() {
  webix
      .ui({
        view: "window",
        id: "travel-duplicates-window",
        modal: true,
        position: "center",
        fullscreen: false,
        height: 610,
        head: {
          view: "toolbar",
          elements: [
            {
              cols: [
                {
                  view: "label",
                  id: "travel-duplicates-label",
                  label:
                      "<span class='header_font'> Travel Claim Duplicates</span>",
                  align: "left",
                },
                {
                  view: "button",
                  id: "btn_travel-duplicates_close",
                  label: "X",
                  align: "right",
                  css: "webix_danger",
                  width: 40,
                },
              ],
            },
          ],
          css: "main_header",
        },
        body: {
          rows: [
            {height: 10},
            {
              view: "datatable",
              id: "travel-duplicates_grid",
              autowidth: true,
              columns: [
                {
                  id: "travel_date",
                  header: "Travel Date",
                  width: 100,
                  css: {"text-align": "center"},
                },
                {id: "db_id", header: "ID", width: 80, hidden: true},
                {id: "pay_id", header: "Pay ID", width: 80},
                {
                  id: "employee",
                  header: "Employee",
                  minWidth: 200,
                  adjust: true,
                },
                {
                  id: "route_from",
                  header: "Route From",
                  width: 65,
                  css: {"text-align": "center"},
                },
                {
                  id: "route_to",
                  header: "Route To",
                  width: 65,
                  css: {"text-align": "center"},
                },
                {
                  id: "distance",
                  header: "Distance",
                  width: 80,
                  css: {"text-align": "center"},
                },
                {
                  id: "with_ppe",
                  header: "With PPE",
                  width: 90,
                  css: {"text-align": "center"},
                },
                {
                  id: "prior_notice",
                  header: "Prior Notice",
                  width: 100,
                  css: {"text-align": "center"},
                },
                {
                  id: "type",
                  header: "Type",
                  width: 90,
                  css: {"text-align": "center"},
                },
                {
                  id: "status",
                  header: "Status",
                  width: 80,
                  css: {"text-align": "center"},
                },
                {
                  id: "comments",
                  header: "Comments",
                  minWidth: 160,
                  adjust: true,
                },
                {
                  id: "select",
                  header: "<span class='webix_icon fas fa-trash-alt'></span>",
                  template: "{common.checkbox()}",
                  css: {"text-align": "center"},
                  width: 60,
                },
                {id: "group_id", header: "Group ID", width: 80, hidden: true},
              ],
              data: [],
            },
            {height: 20},
            {
              view: "button",
              id: "btn_delete_duplicates",
              type: "icon",
              icon: "fas fa-trash",
              label: "Delete Selected Duplicates",
              width: 220,
              align: "center",
            },
            {height: 20},
          ],
        },
      })
      .hide();
}

