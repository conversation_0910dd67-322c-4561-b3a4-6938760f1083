let sickBookingsLogLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Sick Bookings Log (SICM/FAML)</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "datepicker",
        id: "from",
        name: "from",
        width: 170,
        label: "From",
        labelWidth: 45,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "datepicker",
        id: "to",
        name: "to",
        width: 150,
        label: "To",
        labelWidth: 25,
        labelAlign: "left",
        value: new Date(),
        format: "%d/%m/%Y",
        stringResult: true,
        timepicker: false,
      },
      { width: 20 },
      {
        view: "combo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 52,
        width: 160,
        options: [
          { id: 1, value: "All" },
          { id: 2, value: "Pending" },
          { id: 3, value: "Approved" },
          { id: 4, value: "Denied" },
        ],
      },
      { width: 20 },
      {
        view: "checkbox",
        id: "show_exported",
        name: "show_exported",
        label: "Show Exported",
        value: 0,
        labelWidth: 100,
        width: 130,
      },
      { width: 20 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  {
    cols: [
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Excel Export</span>",
        width: 120,
        align: "right",
      },
      { width: 10 },
    ],
  },
  {
    view: "datatable",
    id: "sick_bookings_grid",
    headerRowHeight: 23,
    rowHeight: 22,
    css: "report_styling",
    scheme: {
      $change: function (item) {
        if (item.status == "Approved") {
          item.$cellCss = { status: "approved_status" };
        } else if (item.status == "Denied") {
          item.$cellCss = { status: "denied_status" };
        } else {
          item.$cellCss = { status: "pending_status" };
        }
      },
    },
    columns: [
      { id: "db_id", header: "", hidden: true },
      {
        id: "pay_id",
        header: { text: "Service No.", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "employee",
        header: { text: "Employee Name", css: { "text-align": "center" } },
        adjust: true,
      },
      {
        id: "rank",
        header: { text: "Rank", css: { "text-align": "center" } },
        width: 60,
        css: { "text-align": "center" },
      },
      {
        id: "roster",
        header: { text: "Roster", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "shift",
        header: { text: "Shift", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "location",
        header: { text: "Location", css: { "text-align": "center" } },
        adjust: true,
        css: { "text-align": "center" },
      },
      {
        id: "start_date",
        header: { text: "Date", css: { "text-align": "center" } },
        width: 100,
        css: { "text-align": "center" },
      },
      {
        id: "hours",
        header: { text: "Hours", css: { "text-align": "center" } },
        width: 75,
        css: { "text-align": "center" },
      },
      {
        id: "leave_type",
        header: { text: "Type", css: { "text-align": "center" } },
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "status",
        header: { text: "Status", css: { "text-align": "center" } },
        width: 90,
        css: { "text-align": "center" },
      },
      {
        id: "doc_provided",
        header: { text: "Doc Provided", css: { "text-align": "center" } },
        width: 140,
        css: { "text-align": "center" },
      },
      {
        id: "approved_by",
        header: { text: "Appr/Deny By", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "approved_denied_date",
        header: { text: "Appr/Deny Date", css: { "text-align": "center" } },
        width: 120,
        css: { "text-align": "center" },
      },
      {
        id: "comments",
        header: { text: "Comments", css: { "text-align": "center" } },
        minWidth: 140,
        adjust: true,
      },
      {
        id: "exported",
        header: { text: "Exported", css: { "text-align": "center" } },
        width: 80,
        css: { "text-align": "center" },
      },
    ],
    data: [],
  },
  {
    view: "template",
    id: "records_count",
    template: "",
    borderless: true,
    css: { "font-style": "italic" },
    autoheight: true,
  },
  { height: 10 },
];
