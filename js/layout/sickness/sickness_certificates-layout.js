let sicknessCertificatesLayout = [
  {
    view: "toolbar",
    id: "header",
    css: "main_header",
    cols: [
      {
        view: "label",
        label: "<span class='header_font'>Sickness Certificates</span>",
        align: "left",
      },
      {},
    ],
  },
  { height: 10 },
  {
    cols: [
      { width: 10 },
      {
        view: "combo",
        id: "roster_filter",
        name: "roster_filter",
        label: "Roster",
        labelWidth: 50,
        width: 280,
        options: [],
      },
      { width: 30 },
      {
        view: "combo",
        id: "employee_filter",
        name: "employee_filter",
        label: "Employee",
        labelWidth: 70,
        width: 320,
        options: [],
      },
      { width: 30 },
      {
        view: "combo",
        id: "status_filter",
        name: "status_filter",
        label: "Status",
        labelWidth: 55,
        width: 160,
        options: [
          { id: 1, value: "Pending" },
          { id: 2, value: "Approved" },
          { id: 3, value: "Denied" },
        ],
      },
      { width: 30 },
      {
        view: "button",
        id: "btn_search",
        type: "icon",
        icon: "fas fa-search",
        label: "Search",
        width: 100,
      },
      {},
    ],
  },
  { height: 30 },
  {
    cols: [
      { width: 5 },
      {
        view: "button",
        id: "select_all",
        type: "icon",
        icon: "fas fa-check",
        label: "Select All",
        width: 110,
      },
      {},
      {
        view: "button",
        id: "btn_export_excel",
        label:
          "<span class='webix_icon fas fa-file-excel' style='color:green'></span><span class='text'> Export Elapsed</span>",
        width: 130,
        align: "right",
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_print",
        label:
          "<span class='webix_icon fas fa-print' style='color:black'></span><span class='text'> Print Elapsed</span>",
        width: 130,
        align: "right",
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_email",
        label:
          "<span class='webix_icon fas fa-envelope-square' style='color:darkred'></span><span class='text'> Email Elapsed</span>",
        width: 130,
        align: "right",
      },
      { width: 18 },
    ],
  },
  {
    view: "datatable",
    id: "sickness_pending_grid",
    scroll: "xy",
    select: "row",
    columns: [
      { id: "booking_id", hidden: true },
      { id: "service_no", header: "Pay ID", width: 80, sort: "int" },
      {
        id: "employee",
        header: "Employee",
        minWidth: 160,
        adjust: true,
        sort: "string",
      },
      { id: "rank", header: "Rank", width: 60, sort: "string" },
      {
        id: "roster",
        header: "Roster",
        minWidth: 120,
        adjust: "data",
        sort: "string",
      },
      {
        id: "shift",
        header: "Shift",
        minWidth: 120,
        adjust: "data",
        sort: "string",
      },
      {
        id: "location",
        header: "Location",
        minWidth: 120,
        adjust: "data",
        sort: "string",
      },
      {
        id: "type",
        header: "Type",
        width: 60,
        css: { "text-align": "center", "font-weight": "bold" },
        sort: "string",
      },
      {
        id: "start_date",
        header: "Date",
        width: 130,
        css: { "text-align": "center" },
      },
      {
        id: "date_string",
        header: "Date String",
        width: 130,
        css: { "text-align": "center" },
        hidden: true,
      },
      {
        id: "bk_period",
        header: "Period",
        width: 80,
        css: { "text-align": "center" },
      },
      {
        id: "elapsed",
        header: "Elapsed",
        width: 70,
        sort: "int",
        css: { "text-align": "center" },
      },
      {
        id: "status",
        header: "Status",
        width: 70,
        css: { "text-align": "center" },
      },
      {
        id: "select",
        header: "Select",
        template: "{common.checkbox()}",
        css: { "text-align": "center" },
        width: 60,
      },
      {
        id: "edit",
        header: "Edit",
        css: { "text-align": "center" },
        template: "<span class = 'webix_icon fas fa-edit'></span>",
        width: 60,
      },
      {
        id: "approved_date",
        header: { text: "Approved / Denied<br/>Date", css: "multiline" },
        width: 140,
        css: { "text-align": "center" },
      },
      {
        id: "approved_by",
        header: { text: "Approved / Denied<br/>By", css: "multiline" },
        width: 140,
        css: { "text-align": "center" },
        sort: "int",
      },
      {
        id: "emailed",
        header: "Email",
        css: { "text-align": "center" },
        template: "",
        width: 60,
      },
      {
        id: "work_cover",
        header: { text: "Work<br/>Related", css: "multiline" },
        css: { "text-align": "center" },
        width: 70,
        sort: "string",
      },
      { id: "email_address", header: "Email", width: 160, hidden: true },
      { id: "comments", header: "Comments", minWidth: 120, adjust: true },
    ],
    data: [],
  },
  { height: 10 },
  {
    view: "template",
    id: "selected_request",
    template: "Selected Sickness Bookings: None",
    borderless: true,
    css: "selected_employee",
    autoheight: true,
  },
  { height: 15 },
  {
    cols: [
      { width: 30 },
      {
        view: "textarea",
        id: "request_comments",
        label: "Comments",
        labelWidth: 80,
        height: 55,
        width: 500,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_approve_sicm",
        css: "green_button",
        label: "Certificate Provided</br>SICM or FAML",
        width: 140,
        disabled: true,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_change_to_sic",
        css: "yellow_button",
        label: "Certificate Not Provided</br>Changed to SIC",
        width: 160,
        disabled: true,
      },
      { width: 10 },
      {
        view: "button",
        id: "stat_dec_provided",
        css: "webix_primary",
        label: "Statutory Declaration<br>Provided",
        width: 140,
        disabled: true,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_deny",
        css: "webix_danger",
        label: "Certificate</br>Not Provided",
        width: 110,
        disabled: true,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_approve_cvil",
        css: "green_button",
        label: "Certificate Provided</br>CVIL",
        width: 140,
        disabled: true,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_approve_cvfl",
        css: "green_button",
        label: "Certificate Provided</br>CVFL",
        width: 140,
        disabled: true,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_approve_cvsl",
        css: "green_button",
        label: "Certificate Provided</br>CVSL",
        width: 140,
        disabled: true,
      },
      { width: 10 },
      {
        view: "button",
        id: "btn_approve_cvwr",
        css: "green_button",
        label: "Certificate Provided</br>CVWR",
        width: 140,
        disabled: true,
      },
      {},
    ],
  },
  { height: 50 },
];
