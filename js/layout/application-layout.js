let appPageLayout = (function () {
  "use strict";
  return {
    render: function () {
      return {
        view: "multiview",
        id: "views",
        cells: [
          {
            rows: [
              {
                view: "toolbar",
                id: "header",
                css: "main_header",
                cols: [
                  {
                    view: "label",
                    label:
                      "<div class='logo'><img src='resources/images/Sapphire_logo.png'></div>",
                    align: "left",
                    width: 32,
                  },
                  { width: 5 },
                  {
                    view: "label",
                    label:
                      "<span style='font-size: 17px; color: white'><strong>SAPPHIRE</strong> Rostering & Availability</span>",
                    align: "left",
                    width: 300,
                  },
                  {},
                  {
                    view: "label",
                    id: "logged_in_label",
                    label: "",
                    align: "right",
                    width: 500,
                  },
                  { width: 15 },
                  {
                    view: "button",
                    id: "btn_menu",
                    type: "icon",
                    icon: "fas fa-bars",
                    width: 40,
                    popup: "app_sub_menu",
                  },
                  { width: 10 },
                ],
              },
              {
                view: "toolbar",
                id: "main_toolbar",
                cols: [
                  {
                    view: "segmented",
                    id: "main_menu",
                    value: "1",
                    width: 820,
                    options: [
                      { id: "1", value: "Roster", disabled: false },
                      { id: "2", value: "Leave", disabled: false },
                      { id: "3", value: "Admin", disabled: false },
                      { id: "4", value: "Reports", disabled: false },
                      { id: "5", value: "Messaging", disabled: false },
                      { id: "6", value: "Applications", disabled: false },
                      { id: "7", value: "Respond 52", disabled: false },
                    ],
                  },
                  { width: 25 },
                  {
                    view: "checkbox",
                    id: "overtime_filter",
                    labelRight: "OT",
                    width: 50,
                    labelWidth: 0,
                    value: 1,
                  },
                  { width: 10 },
                  {
                    view: "button",
                    id: "btn_load_totals",
                    type: "icon",
                    label: "Totals",
                    icon: "fas fa-border-none",
                    css: "webix_primary",
                    width: 85,
                    hidden: true,
                  },
                  {
                    view: "button",
                    type: "icon",
                    id: "btn_ro_view",
                    icon: "fas fa-chart-bar",
                    label: "RO View",
                    css: "webix_primary",
                    width: 95,
                  },
                  {},
                  {
                    view: "label",
                    id: "todayDate",
                    label: "",
                    css: { "font-size": "14px" },
                    width: 150,
                    align: "right",
                  },
                  { width: 15 },
                ],
              },
              {
                view: "multiview",
                cells: [
                  scheduleLayout.render(),
                  adminLayout.render(),
                  bookingsLayout.render(),
                  reportsLayout.render(),
                  messagingLayout.render(),
                  applicationsLayout.render(),
                  respond52Layout.render()
                ],
                animate: false,
              },
            ],
          },
        ],
      };
    },
  };
})();
