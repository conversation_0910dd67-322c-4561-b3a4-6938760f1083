.main_header {
    background-color: #293B6B;
}

.logo img {
    float: left;
    margin-right: 10px;
    width: 30px;
}

.header_font_large {
    line-height: 10px !important;
    color: white;
    font-size: 22px;
}

.header_font_small {
    color: white !important;
    font-size: 12px !important;
}

.form_label .webix_el_box {
    font-size: 12px !important;
}

.header_font {
    color: white !important;
    font-size: 14px !important;
}

.schedule_header {
    line-height: 20px !important;
    padding-left: 7px !important;
    text-align: center !important;
    font-size: 12px !important;
}

.column_day_off {
    border-style: solid;
    background-color: #EAE9E9;
}

.column_working_shift {
    border-color: #B4B4B4;
    border-width: 1px;
    background-color: #C3EBFE;
}

.schedule_grid .webix_cell {
    font-size: 11px !important;
    padding-left: 7px !important;
    text-align: center;
}

.schedule_grid {
    border-left-width: 2px !important;
    border-color: gray !important;
    margin-left: 6px !important;
    margin-bottom: 5px !important;
}

.schedule_grid.webix_view.webix_dtable {
    border-width: 1px !important;
}

.column_staff_names {
    background-color: #F7F7F7;
}

.column_staff_names .webix_cell {
    border-color: grey !important;
}

.row_icons {
    background-color: #F7F7F7 !important;
}

.webix_cell.webix_cell_select {
    background-color: yellow !important;
}

.dbllist_items {
    float: right !important;
    line-height: 28px !important;
}

.functions_toolbar {
    background-color: #293B6B !important;
    color: white !important;
}

.webix_list_item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.webix_list_item.webix_selected {
    background-color: steelblue !important;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    color: white !important;
}

.functions_form .webix_inp_label {
    background-color: dimgrey !important;
    color: white !important;
}

.functions_form {
    background-color: dimgrey !important;
    color: white !important;
    margin-top: -12px !important;
}

.functions_form .webix_el_box {
    background-color: dimgrey !important;
    color: white !important;
    font-size: 14px;
}

.createdBy .webix_property_value {
    color: greenyellow !important;
}

.shift_period .webix_property_value {
    color: yellow !important;
}

.header_font .webix_el_box {
    color: yellow !important;
    font-size: 14px !important;
}


input[type=number] {
    -moz-appearance: textfield;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.loader {
    margin-top: 8px;
    border: 13px solid #f3f3f3;
    border-radius: 50%;
    border-top: 13px solid #15317E;
    width: 70px;
    height: 70px;
    -webkit-animation: spin 1s linear infinite; /* Safari */
    animation: spin 1s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.leave_logs .webix_view.webix_layout_line {
    background-color: dimgrey !important;

}

.functions_form .webix_property {
    padding-top: 12px !important;

}

.fail_validate {
    background-color: #FFDEDB !important;
}

.fail_validate.webix_cell.fail_validate.webix_row_select {
    background-color: #FFDEDB !important;
}

.rank_img {
    float: left;
    width: 26px;
    margin-top: 1px;
    padding-right: 5px;
}

.rank_img img {
    width: 100%;
}

.rank_label {
    width: 85%;
    text-align: left !important;
}

.overtime_dt_remove {
    color: dimgrey !important;
    background-color: #e8e8e8 !important;
    font-size: 18px !important;
}

.popup_radio .webix_label_right {
    color: white !important;
    background-color: dimgrey !important;
}

.popup_radio .webix_custom_radio {
    color: white !important;
    background-color: dimgrey !important;
}

.popup_radio .webix_custom_checkbox {
    color: white !important;
    background-color: dimgrey !important;
}

.popup_radio_disabled .webix_custom_checkbox {
    color: darkgrey !important;
    background-color: dimgrey !important;
}

.popup_radio_disabled .webix_inp_label {
    color: darkgrey !important;
    background-color: dimgrey !important;
}

.multiline {
    padding: 4px !important;
    line-height: 18px !important;
    text-align: center !important;
    white-space: pre-line !important;
}

.delete_request_true.fa-minus-circle {
    color: red !important;
    font-size: 21px !important;
    padding: 6px !important;
}

.day_delete_request_true.fa-minus-circle {
    color: darkorange !important;
    font-size: 21px !important;
    padding: 6px !important;
}

.delete_request_false.fa-minus-circle {
    color: lightgrey !important;
    font-size: 21px !important;
    padding: 6px !important;
}

.approved_false.fa-thumbs-down {
    color: red !important;
    font-size: 21px !important;
    padding: 8px !important;
}

.approved_true.fa-thumbs-up {
    color: limegreen !important;
    font-size: 21px !important;
    padding: 8px !important;
}

.approved_pending.fa-clock {
    color: steelblue !important;
    font-size: 20px !important;
    padding: 8px !important;
}

.approved_pending_partial.fa-clock {
    color: darkorange !important;
    font-size: 20px !important;
    padding: 8px !important;
}

.webix_hcell {
    background-color: grey;
    text-align: center;
    color: white;
}

.rrl_grid_group_1 {

    background-color: lightblue !important
}

.rrl_grid_group_2 {

    background-color: lightpink !important
}

.webix_hcell.rrl_swap_header {
    background-color: #949497 !important;
    text-align: center !important;
}

.delete_rrl_request_true.fa-minus-circle {
    color: red !important;
    font-size: 22px !important;
    padding: 6px !important;
}

.leave_count_grid_1 {
    background-color: #F5F5F5 !important;
}

.leave_count_grid_2 {
    background-color: #E8E8E8 !important;
}

.selected_employee {
    color: black !important;
    font-size: 14px !important;
}

.crew_info_popup {
    background-color: lightgoldenrodyellow !important;
    color: black !important;
    font-size: 14px !important;
    font-style: italic !important;
}

.overtime_dt_count {
    font-size: 15px !important;
    font-style: italic !important;
    color: white !important;
    background-color: dimgrey !important;
}

.webix_tree_item {
    font-size: 13px !important;
}

.login_button .webix_img_btn {
    font-size: 14px !important;
}

.leave_count_labels .webix_el_box {
    font-size: 12px !important;
    color: yellow !important;
    text-align: right !important;
    padding-right: 2px !important;
}

.standby_notice .webix_template {
    color: yellow !important;
    background: dimgrey !important;
    font-size: 14px !important;
    padding: 0 0 0 0 !important;
}

.port_pirie_grid .webix_cell {
    border-color: dimgrey !important;
    border-width: 1px !important;
    font-size: 11px !important;
    padding-left: 7px !important;
    text-align: center !important;
    line-height: 17px !important;
}

.port_pirie_grid {
    border-left-width: 3px !important;
    border-right-width: 3px !important;
    border-color: gray !important;
    margin-left: 5px !important;
    border-bottom-width: thin !important;

}

.mobiscroll_timepicker.webix_view {
    margin-top: 3px !important;
    height: 24px !important;
}

.mobiscroll_timepicker .webix_inp_static {
    font-family: Roboto, sans-serif;
    font-size: 13px !important;
    cursor: default !important;
    border: none !important;
}

.mobiscroll_timepicker .webix_el_box {
    background-color: white !important;
    border: none !important;
}

.mobiscroll_timepicker .webix_inp_label {
    background-color: white !important;
}

.mobiscroll_timepicker_2.webix_view {
    border: none !important;
}

.mobiscroll_timepicker_2 .webix_inp_static {
    font-family: Roboto, sans-serif;
    font-size: 13px !important;
    cursor: default !important;
}

.temp_ra .webix_custom_checkbox {
    color: white !important;
    /*background-color: dimgrey !important;*/
}

.temp_ra .webix_label_right {
    color: white !important;
    /*background-color: dimgrey !important;*/
}

.temp_ra_line {
    color: red !important;
}

.webix_message_area {
    top: initial !important;
    bottom: 60%;
    left: 60%;
    width: 280px !important;
}

.overtime_employees .webix_row_select {
    background-color: #DCDCDC !important;
}

.curr_day {
    background-color: #15317E !important;
    color: white !important;
    line-height: 20px !important;
    padding-left: 7px !important;
    text-align: center !important;
    font-size: 12px !important;
}

.ph_day {
    background-color: #ff5c4c !important;
    color: white !important;
    line-height: 20px !important;
    padding-left: 7px !important;
    text-align: center !important;
    font-size: 12px !important;
}

.ra_import .webix_inp_label {
    color: white !important;
    font-size: 14px !important;
}

.ra_import .webix_label_right {
    color: white !important;
    font-size: 14px !important;
}

.ra_import .webix_custom_radio {
    color: white !important;
    font-size: 14px !important;
}

.right_border {
    background-color: #FFFFFF !important;
}

.bk_scroller .webix_view {
    width: 3000px !important;
    background-color: white !important;
}

.report_styling .webix_hcell {
    background-color: #4d85ea;
    color: white;
    border-color: white !important;
}

.report_styling .webix_cell {
    border-color: black !important;
    color: black;
}

.report_styling .webix_last_row {
    border-color: black !important;
}

.report_styling .webix_column.webix_last > div {
    border-right-width: 1px !important;
}

.report_views .webix_button {
    color: white !important;
    background-color: #5D91C3 !important;
}

.report_views .webix_icon_btn {
    color: white !important;
}

.availability_report {
    color: black !important;
    font-weight: 500 !important;
}

.availability_report .webix_hcell {
    padding-left: 7px !important;
}

.availability_report .webix_cell {
    border-color: grey !important;
}

.excel_reports {
    font-size: 18px;
    font-weight: 500;
    color: black;
}

.travel_text .webix_popup_text {
    text-align: left !important;
    font-size: 14px !important;
    font-weight: 400 !important;
}

.unlock_button .webix_icon_btn {
    background-color: dimgrey !important;
    color: white !important;
    font-size: 14px !important;
    line-height: 21px !important;
    margin-left: -3px !important;
}

.unlock_button .webix_img_btn {
    background-color: dimgrey !important;
}

div.gantt_task_content {
    overflow: visible;
    color: black;
    background-color: lightskyblue;
    text-align: left;
    padding-left: 2px;
    font-weight: bold;
    line-height: 17px !important;
}

div.gantt_task_line.gantt_bar_task {
    border-color: black;
}

div.gantt_grid_head_cell {
    color: white !important;
    background-color: dimgrey !important;
    font-size: 13px !important;
    text-align: left !important;
    padding-left: 10px !important;
    border-color: white !important;
}

div.gantt_scale_cell {
    color: white !important;
    background-color: dimgrey !important;
    font-size: 13px !important;

}

.skill_codes {
    word-spacing: 3pt !important;
    background-color: #F7F7F7;
}

.skill_codes .webix_cell {
    border-color: grey !important;
}

.image_button .webix_image {
    max-width: inherit !important;
    margin: 1px 1px 1px 1px !important;
}

.webix_button.webix_img_btn {
    padding-top: 1px !important;
    padding-left: 4px !important;
}

.func_popup_toolbar .webix_list_item.webix_disabled {
    background-color: dimgrey !important;
    color: darkgrey !important;

}

.app_menu {
    background-color: #293B6B !important;
    color: white;
    font-size: 16px;
}

.app_menu .webix_list_item.webix_disabled {
    background-color: #293B6B !important;
    color: darkgrey !important;

}

.func_popup_toolbar {
    background-color: dimgrey !important;
    color: white;
    font-size: 16px;
}

.webix_fieldset legend {
    text-transform: unset;
}

.actup_fieldset .webix_fieldset_label {
    color: yellow !important;
}

.actup_fieldset {
    background-color: dimgrey;
    color: white;
    margin-bottom: -2px;
}

.login_page_message .webix_inp_textarea {
    font-size: 15px !important;
    color: red !important;
    font-weight: 500 !important;

}

.rrl_period_group_1 {

    background-color: #E0E0E0 !important
}

.rrl_period_group_2 {

    background-color: #F5F5F5 !important
}

.deleted_date {
    color: red !important;
    font-weight: bold !important;
}

.approved_status {
    color: #1BCE02 !important;
    font-weight: bold !important;
}

.denied_status {
    color: red !important;
    font-weight: bold !important;
}

.booster_status {
    color: orange !important;
    font-weight: bold !important;
}

.complete_status {
    color: darkgreen !important;
    font-weight: bold !important;
}

.pending_status {
    color: dimgrey !important;
    font-weight: bold !important;
}

.supplementary_report_header_bottom {
    font-size: 26px !important;
    font-weight: normal !important;
    background-color: white !important;
    color: black !important;
}

.supplementary_report_styling .webix_cell {
    border-color: black !important;
}

.supplementary_report_styling .webix_last_row {
    border-color: black !important;
}

.supplementary_report_styling .webix_column.webix_last > div {
    border-right-width: 1px !important;
}

.totals_row {
    background-color: dimgrey;
    color: white;
}

.sm_prompt_window .webix_popup_button {
    width: 140px;
}

.report_row_highlight {
    background-color: yellow;
}

.ra_report_row_highlight {
    background-color: #cffecc;
}

.sickness_report_row_highlight {
    background-color: red;
    color: white !important;
}

.msg_count .webix_el_box {
    font-style: italic;
    color: dimgrey !important;
    padding-top: 0 !important;
    font-size: 13px !important;
}

.msg_count {
    font-style: italic;
    color: dimgrey !important;
    padding-top: 0 !important;
    font-size: 13px !important;
    font-weight: 550 !important;
}

.msg_text_style.webix_el_textarea textarea {
    font-size: 15px !important;
}

.response_button .webix_button {
    font-size: 28px !important;
}

.grid_messages .webix_column > div.webix_row_select {
    background-color: dodgerblue;
    color: white;
}

.standby_request_note .webix_el_box {
    color: dimgrey !important;
    font-style: italic !important;
    font-weight: 400;
}

.approving_officer input {
    font-size: 20px !important;
}

.approving_officer label {
    font-size: 17px !important;
}

.sp90_form .webix_el_box {
    line-height: 80px !important;
}

.report_label .webix_el_box {
    font-size: 16px;
}

.green_button .webix_button {
    background-color: seagreen !important;
    color: white !important;
}

.emerald_button .webix_button {
    background-color: #46a783 !important;
    color: white !important;
}

.emerald_button .webix_icon_btn {
    color: white !important;
}

.green_button .webix_disabled_box .webix_button {
    color: #94A1B3 !important;
    background: #f4f5f9 !important;
    border-color: #f4f5f9 !important;
    text-shadow: none !important;
}

.yellow_button .webix_button {
    background-color: darkgoldenrod !important;
    color: white !important;
}

.orange_button .webix_button {
    background-color: orange !important;
    color: darkblue !important;
    font-size: 14px !important;
}

.orange_button .webix_icon_btn {
    color: darkblue !important;
    font-size: 19px !important;
}

.yellow_button .webix_disabled_box .webix_button {
    color: #94A1B3 !important;
    background: #f4f5f9 !important;
    border-color: #f4f5f9 !important;
    text-shadow: none !important;
}

.is_found input {
    color: green !important;
    font-weight: bold !important;
    font-size: 15px !important;
}

.not_found input {
    color: red !important;
    font-weight: bold !important;
    font-size: 15px !important;
}

.radio_spacing .webix_el_group {
    line-height: 37px !important;
}

.pin_code input {
    font-size: 28px !important;
}

.roster_grid_header {
    background-color: #15317E !important;
    color: white !important;
    font-weight: 500 !important;
    text-align: center !important;
    font-size: 10px !important;
}

.low_soil {
    background-color: red;
    color: white;
}

.enough_soil {
    background-color: darkgreen;
    color: white;
}

.soil_type_accum {
    color: darkgreen;
}

.soil_type_redeem {
    color: darkred;
}

.r52_grid_header div.webix_rotate {
    font-size: 15px !important;
    text-align: left !important;
}

.r52_grid_header.webix_hcell {
    font-size: 16px !important;
    line-height: 200px !important;
}

.r52_location_report .webix_cell {
    border-width: 1px !important;
    border-color: grey !important;
}

.r52_location_report.webix_view.webix_dtable {
    border-width: 1px !important;
    border-color: grey !important;
}

.r52_station_list .webix_list_item {
    font-size: 14px;
    font-weight: 500;
    color: dimgrey;
}

.r52_theme_white {
    text-align: center;
    font-weight: bold;
    background-color: white;
    color: dimgrey;
}

.r52_theme_dark_blue {
    text-align: center;
    font-weight: bold;
    background-color: #1c699d;
    color: white;
}

.r52_theme_yellow {
    text-align: center;
    font-weight: bold;
    background-color: #ffd21a;
    color: black;
}

.r52_theme_black {
    text-align: center;
    font-weight: bold;
    background-color: black;
    color: white;
}

.r52_theme_blue {
    text-align: center;
    font-weight: bold;
    background-color: #2f2dff;
    color: white;
}

.r52_theme_green {
    text-align: center;
    font-weight: bold;
    background-color: #238023;
    color: white;
}

.r52_theme_red {
    text-align: center;
    font-weight: bold;
    background-color: red;
    color: white;
}

.r52_theme_callsign {
    text-align: center;
    font-weight: 600;
    background-color: whitesmoke;
    color: red;
    font-size: 15px;
}

.r52_theme_orange {
    text-align: center;
    font-weight: bold;
    background-color: orange;
    color: white;
}

.r52_theme_purple {
    text-align: center;
    font-weight: bold;
    background-color: purple;
    color: white;
}

.r52_theme_pink {
    text-align: center;
    font-weight: bold;
    background-color: #FF67F6;
    color: white;
}

.r52_theme_teal {
    text-align: center;
    font-weight: bold;
    background-color: teal;
    color: white;
}

.r52_theme_wheat {
    text-align: center;
    font-weight: bold;
    background-color: wheat;
    color: black;
}

.r52_theme_commander {
    text-align: center;
    font-weight: bold;
    background-color: #1A3664;
    color: white;
}

.r52_theme_acfo {
    text-align: center;
    font-weight: bold;
    background-color: blue;
    color: white;
}

.r52_station_list .webix_unit_header.webix_unit_header {
    background-color: black;
    color: white;
    text-align: center;
    font-size: 13px;
    font-weight: 500;
}

.vax_status_red .webix_inp_static {
    background-color: red !important;
    color: white !important;
}

.vax_status_green .webix_inp_static {
    background-color: darkgreen !important;
    color: white !important;
}

.vax_status_exempt .webix_inp_static {
    background-color: #FFFF50 !important;
    color: black !important;
}

.webix_canvas_text.webix_axis_item_y {
    color: black !important;
}

.webix_canvas_text.webix_axis_item_x {
    color: black !important;
}

.sms_filter_radio .webix_inp_label {
    margin-top: -6px !important;
    line-height: 32px !important;
    height: 96px !important;
    text-align: right !important;
}

.sic_swap_button .webix_img_btn_top {
    line-height: 18px !important;
}

.sicm_swap_button .webix_img_btn_top {
    line-height: 18px !important;
}

.stat_dec_box {
    background-color: red;
    color: white;
    height: 30px;
    width: 65px;
    float: left;
    font-size: 14px;
    text-align: center;
    line-height: 30px;
}

.stat_dec_label .webix_el_box {
    font-style: italic;
    font-weight: 400 !important;
    color: black !important;
}

.day_work_schedule {
    text-align: center;
    color: black;
    font-weight: 400;
    font-size: 14px !important;
}

.wfr_fieldset.webix_fieldset > fieldset {
    border: 1px solid grey !important;
    padding-right: 1px !important;
}

.wfr_fieldset .webix_fieldset_label {
    color: black !important;
}

.fatigue_green {
    background-color: green;
    font-size: 13px !important;
    color: white;
}

.fatigue_red {
    background-color: red;
    font-size: 13px !important;
    color: white;
}

.fatigue_orange {
    background-color: darkorange;
    font-size: 13px !important;
    color: white;
}

.duplicate_group {
    background-color: #E8E8E8 !important;
}

.dbl_date_label .webix_inp_label {
    line-height: 16px !important;
    vertical-align: top !important;
}

.new_fieldset_label .webix_fieldset_label {
    font-weight: bold;
    font-size: 13px !important;
}
.new_fieldset_label > fieldset {
    border: 1px solid black !important;
}

.rrl_swap_grid .webix_row_select {
    background-color: yellow !important;
}
.webix_ss_footer .webix_hcell {
    font-size: 15px !important;
}

.webix_cal_icon_clear {
    display:none
}